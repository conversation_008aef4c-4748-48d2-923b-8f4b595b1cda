// Dashboard Configuration Constants

export const DASHBOARD_COLORS = {
  primary: '#1890ff',
  success: '#52c41a',
  warning: '#faad14',
  error: '#ff4d4f',
  purple: '#722ed1',
  cyan: '#13c2c2',
  orange: '#fa8c16',
  pink: '#eb2f96',
  lime: '#a0d911',
  gold: '#fadb14',
};

export const CHART_COLORS = {
  revenue: '#52c41a',
  claims: '#ff4d4f',
  companies: '#1890ff',
  target: '#faad14',
  app: '#722ed1',
  web: '#13c2c2',
  agency: '#fa8c16',
  other: '#eb2f96',
};

export const PERIOD_OPTIONS = [
  { label: 'Tuần', value: 'week' },
  { label: 'Tháng', value: 'month' },
  { label: 'Năm', value: 'year' },
];

export const INSURANCE_CATEGORIES = {
  AUTO: '<PERSON><PERSON><PERSON> hiểm xe ô tô',
  MOTORCYCLE: '<PERSON><PERSON><PERSON> hiểm xe máy',
  PERSONAL: '<PERSON><PERSON>o hiểm con người',
  PROPERTY: 'Bảo hiểm tài sản',
  TRAVEL: 'Bảo hiểm du lịch',
  OTHER: 'Bảo hiểm khác',
};

export const CHART_OPTIONS = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: true,
      position: 'bottom' as const,
    },
    tooltip: {
      enabled: true,
    },
  },
  scales: {
    y: {
      beginAtZero: true,
      grid: {
        color: 'rgba(0, 0, 0, 0.1)',
      },
    },
    x: {
      grid: {
        color: 'rgba(0, 0, 0, 0.1)',
      },
    },
  },
};

export const PIE_CHART_OPTIONS = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: true,
      position: 'right' as const,
    },
    tooltip: {
      enabled: true,
      callbacks: {
        label: function(context: any) {
          const label = context.label || '';
          const value = context.parsed || 0;
          const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
          const percentage = ((value / total) * 100).toFixed(1);
          return `${label}: ${percentage}%`;
        }
      }
    },
  },
};

export const DASHBOARD_REFRESH_INTERVAL = 5 * 60 * 1000; // 5 minutes

export const MOCK_DATA_CONFIG = {
  MONTHS: ['T1', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7', 'T8', 'T9', 'T10', 'T11', 'T12'],
  CURRENT_YEAR: 2025,
  PREVIOUS_YEAR: 2024,
};

/**
 * Tá<PERSON> dụng: Quản lý state và logic nghiệp vụ cho module danh mục bệnh viện
 */
import React, {memo, useCallback, useEffect, useState} from "react";
import {message} from "antd";

import {ReactQuery} from "@src/@types";
import {ACTION_CODE} from "@src/constants";
import {useCommonExecute} from "@src/services/react-queries";
import {defaultPaginationTableProps} from "@src/hooks";

import {DanhMucBenhVienContext} from "./index.context";
import {IDanhMucBenhVienProvider} from "./index.model";

interface IDanhMucBenhVienProviderProps {
  children: React.ReactNode;
}

const DanhMucBenhVienProviderComponent: React.FC<IDanhMucBenhVienProviderProps> = memo(({children}) => {
  //Danh sách bệnh viện hiển thị trong bảng chính
  const [listBenhVien, setListBenhVien] = useState<CommonExecute.Execute.IDanhMucBenhVien[]>([]);
  //Danh sách tỉnh thành cho dropdown filter và form
  const [listTinhThanh, setListTinhThanh] = useState<any[]>([]);
  //Danh sách quận huyện cho dropdown filter và form (phụ thuộc vào tỉnh được chọn)
  // const [listQuanHuyen, setListQuanHuyen] = useState<any[]>([]);
  //Danh sách ngân hàng cho dropdown filter và form
  const [listNganHang, setListNganHang] = useState<any[]>([]);
  //Danh sách chi nhánh ngân hàng cho dropdown filter và form (phụ thuộc vào ngân hàng được chọn)
  const [listChiNhanhNganHang, setListChiNhanhNganHang] = useState<any[]>([]);

  //Tổng số bản ghi để hiển thị phân trang
  const [tongSoDong, setTongSoDong] = useState<number>(0);
  //Tham số filter và phân trang hiện tại
  const [filterParams, setFilterParams] = useState<ReactQuery.ITimKiemPhanTrangDanhMucBenhVienParams>({
    nd_tim: "",
    nhom_bv: "",
    tinh_thanh: "",
    tk_ngan_hang: "",
    tk_chi_nhanh: "",
    trang_thai: "",
    trang: 1,
    so_dong: defaultPaginationTableProps.defaultPageSize,
  });

  //===== COMMON EXECUTE HOOK =====
  const {mutateAsync, isLoading: loading} = useCommonExecute();

  //Tác dụng: Load tất cả tỉnh thành để hiển thị trong dropdown filter và form modal
  const getListTinhThanh = useCallback(async () => {
    try {
      const response = await mutateAsync({
        ma: "",
        ten: "",
        trang_thai: "",
        trang: 1,
        so_dong: 100,
        actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_DANH_MUC_TINH_THANH
      } as any);
      
      const responseData = (response.data as any);
      setListTinhThanh(responseData.data || []);
    } catch (error) {
      setListTinhThanh([]);
    }
  }, [mutateAsync]);

  //Tác dụng: Load quận huyện theo tỉnh được chọn để hiển thị trong dropdown
  // const getListQuanHuyen = useCallback(async (params?: {ma_tinh?: string}) => {
  //   try {
  //     const response = await mutateAsync({
  //       ma: "",
  //       ten: "",
  //       trang_thai: "",
  //       ma_tinh: params?.ma_tinh || "",
  //       trang: 1,
  //       so_dong: 1000,
  //       actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_DANH_MUC_QUAN_HUYEN
  //     } as any);
      
  //     const responseData = (response.data as any);
  //     setListQuanHuyen(responseData.data || []);
  //   } catch (error) {
  //     setListQuanHuyen([]);
  //   }
  // }, [mutateAsync]);

  //
  //lấy danh sách ngân hàng
  const getListNganHang = useCallback(async () => {
    try {
      const response = await mutateAsync({
        ma: "",
        ten: "",
        trang_thai: "",
        trang: 1,
        so_dong: 100,
        actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_DANH_MUC_NGAN_HANG
      } as any);
      
      const responseData = (response.data as any);
      setListNganHang(responseData.data || []);
    } catch (error) {
      setListNganHang([]);
    }
  }, [mutateAsync]);

  //Tác dụng: Load chi nhánh ngân hàng theo ngân hàng được chọn để hiển thị trong dropdown
  const getListChiNhanhNganHang = useCallback(async (params?: {ma_ngan_hang?: string}) => {
    try {
      const response = await mutateAsync({
        ma: "",
        ten: "",
        trang_thai: "",
        ma_ngan_hang: params?.ma_ngan_hang || "",
        trang: 1,
        so_dong: 1000,
        actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_NGAN_HANG_CHI_NHANH
      } as any);
      
      const responseData = (response.data as any);
      setListChiNhanhNganHang(responseData.data || []);
    } catch (error) {
      setListChiNhanhNganHang([]);
    }
  }, [mutateAsync]);


  //Tác dụng: Load danh sách bệnh viện theo điều kiện filter và phân trang để hiển thị trong bảng
  const getListBenhVien = useCallback(
    async (params?: ReactQuery.ITimKiemPhanTrangDanhMucBenhVienParams) => {
      try {
        const searchParams = params || filterParams;
        
        const response = await mutateAsync({
          ...(searchParams as any),
          actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_DANH_MUC_BENH_VIEN,
        });

        //Response structure: data.data[] + data.tong_so_dong
        const responseData = response.data;
        if (responseData?.data && Array.isArray(responseData.data)) {
          setListBenhVien(responseData.data);
          setTongSoDong(responseData.tong_so_dong || responseData.data.length);
        }
      } catch (error) {
        message.error("Có lỗi xảy ra khi tải danh sách bệnh viện!");
      }
    },
    [mutateAsync, filterParams]
  );

  //Tác dụng: Lấy thông tin chi tiết của 1 bệnh viện khi user click vào row để hiển thị trong modal edit
  const getChiTietBenhVien = useCallback(
    async (data: {ma: string;}) => { 
      try {
        const response = await mutateAsync({
          ma: data.ma,
          actionCode: ACTION_CODE.CHI_TIET_DANH_MUC_BENH_VIEN,
        } as any);
        
        //Response structure: data.lke[0]
        const responseData = response.data as any;
        console.log("Full responseData:", responseData);

        // if (responseData?.lke && Array.isArray(responseData.lke) && responseData.lke.length > 0) {
        //   return responseData.lke[0] as CommonExecute.Execute.IDanhMucBenhVien;
        // }
        if (responseData?.lke && Array.isArray(responseData.lke) && responseData.lke.length > 0) {
        return responseData.lke[0] as CommonExecute.Execute.IDanhMucBenhVien;
      } else if (responseData && typeof responseData === 'object') {
        return responseData as CommonExecute.Execute.IDanhMucBenhVien;
      }

        
        return {} as CommonExecute.Execute.IDanhMucBenhVien;
      } catch (error) {
        message.error("Không thể tải chi tiết bệnh viện");
        return {} as CommonExecute.Execute.IDanhMucBenhVien;
      }
    },
    [mutateAsync],
  );

  //Tác dụng: Lưu thông tin bệnh viện mới hoặc cập nhật thông tin bệnh viện đã có
  const capNhatChiTietBenhVien = useCallback(
    async (params: ReactQuery.ICapNhatDanhMucBenhVienParams, isUpdate: boolean = false): Promise<any> => {
      try {
        const response = await mutateAsync({
          ...params,
          actionCode: ACTION_CODE.UPDATE_DANH_MUC_BENH_VIEN,
        } as any);
        
        //API trả về -1 là thành công
        if (response?.data === -1) {
          const successMessage = isUpdate ? "Cập nhật bệnh viện thành công!" : "Thêm mới bệnh viện thành công!";
          message.success(successMessage);
          return response.data;
        } else {
          const errorMessage = isUpdate ? "Có lỗi xảy ra khi cập nhật bệnh viện!" : "Có lỗi xảy ra khi thêm mới bệnh viện!";
          message.error(errorMessage);
          throw new Error(`API returned failure. Response data: ${response?.data}`);
        }
      } catch (error) {
        const errorMessage = isUpdate ? "Có lỗi xảy ra khi cập nhật bệnh viện!" : "Có lỗi xảy ra khi thêm mới bệnh viện!";
        message.error(errorMessage);
        throw error;
      }
    },
    [mutateAsync]
  );

  //===== EFFECTS =====
  //Load danh sách tỉnh thành khi component mount
  useEffect(() => {
    getListTinhThanh();
    // getListQuanHuyen();
    //Load danh sách ngân hàng khi component mount
    getListNganHang();
    //Load danh sách chi nhánh ngân hàng khi component mount
    getListChiNhanhNganHang();
  }, [getListTinhThanh, getListNganHang, getListChiNhanhNganHang]);

  //Load dữ liệu bệnh viện khi filterParams thay đổi
  useEffect(() => {
    getListBenhVien();
  }, [filterParams, getListBenhVien]);

  //===== CONTEXT VALUE =====
  const contextValue: IDanhMucBenhVienProvider = {
    listBenhVien,
    listTinhThanh,
    // listQuanHuyen,
    listNganHang,
    listChiNhanhNganHang,
    tongSoDong,
    loading,
    filterParams,
    getListBenhVien,
    getListTinhThanh,
    // getListQuanHuyen,
    getListNganHang,
    getListChiNhanhNganHang,
    getChiTietBenhVien,
    capNhatChiTietBenhVien,
    setFilterParams,
  };

  return <DanhMucBenhVienContext.Provider value={contextValue}>{children}</DanhMucBenhVienContext.Provider>;
});

DanhMucBenhVienProviderComponent.displayName = "DanhMucBenhVienProvider";
export default DanhMucBenhVienProviderComponent;

import {ArrowLeftOutlined, CheckOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {FormInput, HeaderModal, Popcomfirm} from "@src/components";
import {COLOR_PALETTE} from "@src/constants";
import {<PERSON>ton, Col, Flex, Form, Modal, Row} from "antd";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useState} from "react";
import {useChiTietDanhMucHangXeContext} from "../index.context";
import {FormChiTietDanhMucHangXe, NGHIEP_VU_HANG_XE, TRANG_THAI_HANG_XE} from "./index.configs";

const {ma, ten, trang_thai, stt, nv} = FormChiTietDanhMucHangXe;
interface Props {
  listDoiTac: Array<CommonExecute.Execute.IDoiTac>;
}

//định nghĩa c<PERSON>c hàm, biến mà modal sẽ expose để component bên ngoài sử dụng thông qua ref
export interface IModalChiTietDanhMuchangXeRef {
  open: (data?: CommonExecute.Execute.IChiTietDanhMucHangXe) => void;
}

const ModalChiTietDanhMucHangXeComponent = forwardRef<IModalChiTietDanhMuchangXeRef, Props>(({listDoiTac}: Props, ref) => {
  useImperativeHandle(ref, () => ({
    open: (dataDoiTac?: CommonExecute.Execute.IChiTietDanhMucHangXe) => {
      setIsOpen(true);
      if (dataDoiTac) setChiTietDanhMucHangXe(dataDoiTac);
    },
    close: () => setIsOpen(false),
  }));
  const [chiTietDanhMucHangXe, setChiTietDanhMucHangXe] = useState<CommonExecute.Execute.IChiTietDanhMucHangXe | null>(null);
  const [isOpen, setIsOpen] = useState(false);

  const [form] = Form.useForm();

  const {loading, onUpdateDanhMucHangXe} = useChiTietDanhMucHangXeContext();
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const formValues = Form.useWatch([], form);

  useEffect(() => {
    if (chiTietDanhMucHangXe) {
      const arrFormData = [];
      for (const key in chiTietDanhMucHangXe) {
        arrFormData.push({
          name: key as keyof CommonExecute.Execute.IChiTietDanhMucHangXe,
          value: chiTietDanhMucHangXe[key as keyof CommonExecute.Execute.IChiTietDanhMucHangXe],
        });
      }
      form.setFields(arrFormData);
    }
  }, [chiTietDanhMucHangXe]);

  //xử lý validate form
  useEffect(() => {
    form
      .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
      .then(() => {
        setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
      })
      .catch(() => {
        setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
      });
  }, [form, formValues]);

  const closeModal = useCallback(() => {
    setIsOpen(false);
    setChiTietDanhMucHangXe(null);
    form.resetFields();
  }, []);

  //Bấm Update
  const onConfirm = async () => {
    try {
      const values: ReactQuery.IUpdateDanhMuchangXeParams = form.getFieldsValue(); //lấy ra values của form
      values.stt = Number(values.stt);

      const params = {
        ...values,
      };

      const response = await onUpdateDanhMucHangXe(params);
      console.log("check respon", response);
      if (response === -1) {
        console.log("cập nhật thành công");
        closeModal();
      } else {
        console.log("cập nhật thất bại");
      }

      // closeModal();
    } catch (error) {
      console.log("onConfirm", error);
    }
  };

  // RENDER
  //get màu trạng thái sử dụng
  const getStatusColor = (status?: string) => {
    let color = COLOR_PALETTE.gray[70];
    if (status === "D") color = COLOR_PALETTE.green[100];
    else if (status === "K") color = COLOR_PALETTE.red[50];
    return color;
  };

  //FOOTER
  const renderFooter = () => {
    return (
      <Form.Item>
        <Button type="default" onClick={() => closeModal()} className="mr-2" icon={<ArrowLeftOutlined />}>
          Quay lại
        </Button>
        <Popcomfirm
          title="Thông báo"
          onConfirm={onConfirm}
          okText="Lưu"
          description="Bạn có chắc muốn lưu thông tin?"
          buttonTitle={"Lưu"}
          buttonDisable={disableSubmit}
          buttonClassName="w-40"
          buttonIcon={<CheckOutlined />}
          iconPosition="end"
          loading={loading}
        />
      </Form.Item>
    );
  };
  const renderFormInputColum = (props: any, span?: number) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  const renderForm = () => (
    <Form form={form} layout="vertical" initialValues={{trang_thai: TRANG_THAI_HANG_XE[0].ma}}>
      {/* MÃ */}
      <Row gutter={16}>
        {/* {renderFormColum({...ten_nv, options: listDoiTac}, 16)} */}
        {renderFormInputColum({...ma, disabled: chiTietDanhMucHangXe ? true : false}, 8)}
        {renderFormInputColum({...ten}, 16)}
      </Row>

      {/* gutter : khoảng cách giữa các ô */}
      <Row gutter={16}>
        {/* 3 cột có width = nhau  */}
        {renderFormInputColum({...nv, disabled: chiTietDanhMucHangXe, options: NGHIEP_VU_HANG_XE}, 8)}
        {renderFormInputColum({...stt}, 8)}
        {renderFormInputColum({...trang_thai, options: TRANG_THAI_HANG_XE}, 8)}
      </Row>
    </Form>
  );
  //Render
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        // styles={{
        //   body: {
        //     height: "60vh",
        //   },
        // }}
        title={
          <HeaderModal
            title={chiTietDanhMucHangXe ? `Chi tiết hãng xe ${chiTietDanhMucHangXe.ten}` : "Tạo mới hãng xe"}
            trang_thai_ten={chiTietDanhMucHangXe?.trang_thai_ten}
            trang_thai={chiTietDanhMucHangXe?.trang_thai}
          />
        }
        // centered
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width={{
          xs: "50%",
          sm: "50%",
          md: "50%",
          lg: "50%",
          xl: "50%",
          xxl: "50%",
        }}
        footer={renderFooter}>
        {renderForm()}
      </Modal>
    </Flex>
  );
});

ModalChiTietDanhMucHangXeComponent.displayName = "ModalChiTietBoMaNguyenTeComponent";
export const ModalChiTietDanhMucHangXe = memo(ModalChiTietDanhMucHangXeComponent, isEqual);

/**
 * Tác dụng: Đ<PERSON>nh nghĩa TypeScript interfaces cho module danh mục <PERSON>hu vực
 */
import {ReactQuery} from "@src/@types";

export interface IDanhMucKhuVucProvider {
  listKhuVuc: CommonExecute.Execute.IDanhMucKhuVuc[]; //Danh sách Khu vực hiển thị trong bảng
  listChauLuc: CommonExecute.Execute.IDanhMucChauLuc[]; //Danh sách tỉnh thành cho dropdown filter
  tongSoDong: number; //Tổng số bản ghi để hiển thị pagination
  loading: boolean; //Trạng thái loading cho UI
  filterParams: ReactQuery.ITimKiemPhanTrangDanhMucKhuVucParams & ReactQuery.IPhanTrang; //Tham số filter và phân trang
  searchKhuVuc: () => Promise<void>; //Tìm kiếm danh sách <PERSON>hu vực
  getChiTietKhuVuc: (data: {ma: string;}) => Promise<CommonExecute.Execute.IDanhMucKhuVuc>; //Lấy chi tiết một Khu vực
  capNhatChiTietKhuVuc: (data: ReactQuery.ICapNhatDanhMucKhuVucParams) => Promise<any>; //Tạo mới/cập nhật Khu vực
  setFilterParams: React.Dispatch<React.SetStateAction<ReactQuery.ITimKiemPhanTrangDanhMucKhuVucParams & ReactQuery.IPhanTrang>>; //Cập nhật tham số filter
}

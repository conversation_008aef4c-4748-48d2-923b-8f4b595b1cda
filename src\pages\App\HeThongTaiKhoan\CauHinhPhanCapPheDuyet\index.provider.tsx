import {ReactQuery} from "@src/@types";
import {ACTION_CODE} from "@src/constants";
import {CommonExecuteResponse, useCommonExecute} from "@src/services/react-queries";
import React, {PropsWithChildren, useCallback, useEffect, useMemo, useState} from "react";
import {CauHinhPhanCapPheDuyetContext} from "./index.context";
import {CauHinhPhanCapPheDuyetContextProps} from "./index.model";
import {message} from "antd";

/* 
  file Provider : Chứa logic quản lý state, gọi API, wrap context
*/
const CauHinhPhanCapPheDuyetProvider: React.FC<PropsWithChildren> = props => {
  const {children} = props;
  const mutateUseCommonExecute = useCommonExecute();
  const [danhSachTaiKhoanNguoiDung, setDanhSachTaiKhoanNguoiDung] = useState<Array<CommonExecute.Execute.IDanhSachTaiKhoanNguoiDungPhanTrang>>([]);
  const [danhSachCauHinhPhanCapPheDuyet, setDanhSachCauHinhPhanCapPheDuyet] = useState<Array<CommonExecute.Execute.ICauHinhPhanCapPheDuyet>>([]);
  const [danhSachCauHinhPhanCapPheDuyetCT, setDanhSachCauHinhPhanCapPheDuyetCT] = useState<Array<CommonExecute.Execute.ICauHinhPhanCapPheDuyetCT>>([]);
  const [tongSoDong, setTongSoDong] = useState<number>(0);
  const [listDoiTac, setListDoiTac] = useState<Array<CommonExecute.Execute.IDoiTac>>([]);
  const [listPhanCapNhom, setListPhanCapNhom] = useState<Array<CommonExecute.Execute.ICauHinhPhanCapNhom>>([]);
  const [listChiNhanh, setListChiNhanh] = useState<Array<CommonExecute.Execute.IChiNhanh>>([]);
  const [listSanPham, setListSanPham] = useState<Array<CommonExecute.Execute.IDanhMucSanPham>>([]);
  const defaultFormValue: ReactQuery.ILayDanhSachTaiKhoanNguoiDungPhanTrangParams = {
    ma_doi_tac: "",
    ma_chi_nhanh: "",
    phong: "",
    trang_thai: "",
    nd_tim: "",
    trang: 1,
    so_dong: 10,
  };
  useEffect(() => {
    // layDanhSachPhongBan()
    initData();
  }, []);
  // useEffect(()=>{
  //   setDanhSachCauHinhPhanCapPheDuyet
  // },[])
  const initData = () => {
    layDanhSachTaiKhoanNguoiDungPhanTrang(defaultFormValue);
    getListDoiTac();
    getListChiNhanh();
    getListPhanCapNhom();
  };
  /* ĐỐI TÁC */
  const getListDoiTac = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        actionCode: ACTION_CODE.LIET_KE_DANH_SACH_DOI_TAC,
      });
      setListDoiTac((response?.data as CommonExecute.Execute.IDoiTac[]).map(item => ({...item, ten: item.ma + " - " + item.ten_tat})));
    } catch (error) {
      console.log("getListDoiTac error ", error);
    }
  }, [mutateUseCommonExecute]);
  /* NHÓM PHÂN CẤP*/
  const getListPhanCapNhom = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        actionCode: ACTION_CODE.LIET_KE_CAU_HINH_PHAN_CAP_NHOM,
      });
      const data = (response?.data as CommonExecute.Execute.ICauHinhPhanCapNhom[]).map(item => ({...item, ten: item.ten}));
      setListPhanCapNhom(data);
    } catch (error) {
      console.log("getListDoiTac error ", error);
    }
  }, [mutateUseCommonExecute]);
  //DS phòng ban phân trang - Danh sách người dùng
  const layDanhSachTaiKhoanNguoiDungPhanTrang = useCallback(
    async (body: ReactQuery.ILayDanhSachTaiKhoanNguoiDungPhanTrangParams & ReactQuery.IPhanTrang) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.GET_DANH_SACH_TAI_KHOAN_NGUOI_DUNG,
        };
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        const data = response.data.data;
        setDanhSachTaiKhoanNguoiDung(data);
        setTongSoDong(response.data.tong_so_dong);
        console.log("tổng số dòng", tongSoDong);
        console.log("data danh sách tài khoản", data);
      } catch (error: any) {
        console.log("layDanhSachTaiKhoanNguoiDungPhanTrang error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );
  const layDanhSachCauHinhPhanCapPheDuyet = useCallback(
    async (body: ReactQuery.ILietKeCauHinhPhanCapPheDuyetParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.LIET_KE_CAU_HINH_PHAN_CAP_PHE_DUYET,
        };
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        const data = response.data;
        setDanhSachCauHinhPhanCapPheDuyet(data);
        console.log("data phân cấp theo nsd", data);
      } catch (error: any) {
        console.log("layDanhSachTaiKhoanNguoiDungPhanTrang error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );
  const layDanhSachCauHinhPhanCapPheDuyetCT = useCallback(
    async (body: ReactQuery.ILietKeCauHinhPhanCapPheDuyetCTParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.LIET_KE_CAU_HINH_PHAN_CAP_PHE_DUYET_CT,
        };
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        const data = response.data;
        setDanhSachCauHinhPhanCapPheDuyetCT(data);
        console.log("data phân cấp chi tiết", data);
      } catch (error: any) {
        console.log("Lấy danh sách cấu hình phê duyệt chi tiết error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );
  //Lấy chi tiết 1 tài khoản người dùng
  const layChiTietTaiKhoanNguoiDung = useCallback(
    async (item: ReactQuery.IChiTietTaiKhoanNguoiDungParams): Promise<CommonExecute.Execute.IChiTietNguoiSuDung | null> => {
      try {
        const params = {
          ...item,
          actionCode: ACTION_CODE.GET_CHI_TIET_TAI_KHOAN_NGUOI_DUNG,
        };
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        const response = responseData.data;

        return responseData.data.nsd as CommonExecute.Execute.IChiTietNguoiSuDung;
      } catch (error: any) {
        console.log("layChiTietTaiKhoanNguoiDung error ", error.message || error);
        return null;
      }
    },
    [mutateUseCommonExecute],
  );
  //Lấy chi tiết cấu hình phân cấp phê duyệt CT
  const layChiTietCauHinhPhanCapPheDuyetCT = useCallback(
    async (item: ReactQuery.IChiTietCauHinhPhanCapPheDuyetCTParams): Promise<CommonExecute.Execute.ICauHinhPhanCapPheDuyetCT | null> => {
      try {
        const params = {
          ...item,
          actionCode: ACTION_CODE.CHI_TIET_CAU_HINH_PHAN_CAP_PHE_DUYET_CT,
        };
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        const lke = (responseData.data as {lke?: any}).lke;
        const response = lke ? (Object.values(lke)[0] as CommonExecute.Execute.ICauHinhPhanCapPheDuyetCT) : null;
        return response;
      } catch (error: any) {
        console.log("layChiTietCauHinhPhanCapPheDuyetCT error ", error.message || error);
        return null;
      }
    },
    [mutateUseCommonExecute],
  );
  const getListChiNhanh = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        actionCode: ACTION_CODE.LIET_KE_DANH_SACH_CHI_NHANH,
      });
      // setListChiNhanh(response.data);
      setListChiNhanh((response.data as CommonExecute.Execute.IChiNhanh[]).map(item => ({...item, ten: item.ten_tat})));
      return response?.data || {data: []};
    } catch (error) {
      console.log("getListPhongBan error ", error);
      return {data: []};
    }
  }, [mutateUseCommonExecute]);
  const getListChiNhanhTheoDoiTac = useCallback(async () => {
    try {
      const response = await getListChiNhanh();
      const updatedData = [...response.data]; // Sao chép mảng để tránh mutate trực tiếp

      setListChiNhanh(updatedData.map(item => ({...item, ten_tat: item.ten_tat}))); // Cập nhật state nếu cần
      return {data: updatedData};
    } catch (error) {
      console.log("error", error);
      return {data: []}; // Trả về giá trị mặc định khi lỗi
    }
  }, [getListChiNhanh]);
  // //Cập nhật hoặc tạo mới 1 phòng ban
  const onUpdateCauHinhPhanCapPheDuyet = useCallback(
    async (body: ReactQuery.IUpdateCauHinhPhanCapPheDuyetParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.CAP_NHAT_CAU_HINH_PHAN_CAP_PHE_DUYET,
        };
        console.log("params cập nhật", params);

        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        if (responseData && (responseData.data as unknown as number) === -1) {
          console.log("responseData cập nhật", responseData);
          message.success("Cập nhật thông tin thành công!");
          initData();
          return responseData.data as unknown as number; // Chuyển đổi responseData.data thành number
        }
      } catch (error: any) {
        console.log("Cập nhật phân cấp phê duyệt error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );
  //cập nhataj cấu hình phân cấp phê duyệt chi tiết
  const onUpdateCauHinhPhanCapPheDuyetCT = useCallback(
    async (body: ReactQuery.IUpdateCauHinhPhanCapPheDuyetCTParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.CAP_NHAT_CAU_HINH_PHAN_CAP_PHE_DUYET_CT,
        };
        console.log("params cập nhật chi tiết", params);

        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        if (responseData && (responseData.data as unknown as number) === -1) {
          console.log("responseData cập nhật", responseData);
          message.success("Cập nhật thông tin thành công!");
          initData();
          return responseData.data as unknown as number; // Chuyển đổi responseData.data thành number
        }
      } catch (error: any) {
        console.log("Cập nhật phân cấp phê duyệt chi tiết error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );
  //Xóa cấu hình pahan cấp phê duyệt
  const onDeleteCauHinhPhanCapPheDuyet = useCallback(
    async (body: ReactQuery.IUpdateCauHinhPhanCapPheDuyetParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.XOA_CAU_HINH_PHAN_CAP_PHE_DUYET,
        };
        console.log("params Xóa", params);

        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        if (responseData && (responseData.data as unknown as number) === -1) {
          console.log("responseData Xóa", responseData);
          message.success("Xóa thông tin thành công!");
          initData();
          return responseData.data as unknown as number; // Chuyển đổi responseData.data thành number
        }
      } catch (error: any) {
        console.log("Xóa phân cấp phê duyệt error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );
  //Xóa cấu hình pahan cấp phê duyệt
  const onDeleteCauHinhPhanCapPheDuyetCT = useCallback(
    async (body: ReactQuery.IDeleteCauHinhPhanCapPheDuyetCTParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.XOA_CAU_HINH_PHAN_CAP_PHE_DUYET_CT,
        };
        console.log("params Xóa CT", params);

        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        if (responseData && (responseData.data as unknown as number) === -1) {
          console.log("responseData Xóa", responseData);
          message.success("Xóa thông tin thành công!");
          initData();
          return responseData.data as unknown as number; // Chuyển đổi responseData.data thành number
        }
      } catch (error: any) {
        console.log("Xóa phân cấp phê duyệt error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );
  const getListSanPham = useCallback(
    async (params: ReactQuery.ILietKeSanPhamParams) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...params,
          trang: 1,
          so_dong: 1000,
          actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_DS_SAN_PHAM,
        });
        console.log("Danh sách sản phẩm ", response);
        setListSanPham([...response.data.data]);
        return response?.data || {data: [], tong_so_dong: 0};
      } catch (error) {
        console.log("getListsanpham error ", error);
        return {data: [], tong_so_dong: 0};
      }
    },
    [mutateUseCommonExecute],
  );

  const getListSanPhamTheoDoiTac = useCallback(
    async (params: ReactQuery.ILietKeSanPhamParams) => {
      try {
        const response = await getListSanPham(params);
        const updatedData = [...response.data]; // Sao chép mảng để tránh mutate trực tiếp
        setListSanPham(updatedData); // Cập nhật state nếu cần
        console.log("list sản phẩm theo đối tác", listSanPham);
        return {data: updatedData};
      } catch (error) {
        console.log("error", error);
        return {data: [], tong_so_dong: 0}; // Trả về giá trị mặc định khi lỗi
      }
    },
    [getListSanPham],
  );
  //khai báo giá trị của context để truyền vào Component
  const value = useMemo<CauHinhPhanCapPheDuyetContextProps>(
    () => ({
      danhSachTaiKhoanNguoiDung: danhSachTaiKhoanNguoiDung,
      loading: mutateUseCommonExecute.isLoading,
      tongSoDong,
      defaultFormValue,
      listDoiTac,
      listChiNhanh,
      danhSachCauHinhPhanCapPheDuyet,
      danhSachCauHinhPhanCapPheDuyetCT,
      listSanPham,
      listPhanCapNhom,
      getListSanPhamTheoDoiTac,
      onUpdateCauHinhPhanCapPheDuyet,
      onDeleteCauHinhPhanCapPheDuyet,
      onDeleteCauHinhPhanCapPheDuyetCT,
      onUpdateCauHinhPhanCapPheDuyetCT,
      setDanhSachCauHinhPhanCapPheDuyetCT,
      layDanhSachCauHinhPhanCapPheDuyetCT,
      setDanhSachCauHinhPhanCapPheDuyet,
      layDanhSachCauHinhPhanCapPheDuyet,
      layChiTietTaiKhoanNguoiDung,
      setListChiNhanh,
      getListChiNhanhTheoDoiTac,
      getListDoiTac,
      getListPhanCapNhom,
      layDanhSachTaiKhoanNguoiDungPhanTrang,
      layChiTietCauHinhPhanCapPheDuyetCT,
    }),
    [
      danhSachTaiKhoanNguoiDung,
      mutateUseCommonExecute,
      listDoiTac,
      tongSoDong,
      defaultFormValue,
      listChiNhanh,
      danhSachCauHinhPhanCapPheDuyet,
      danhSachCauHinhPhanCapPheDuyetCT,
      listSanPham,
      listPhanCapNhom,
      getListPhanCapNhom,
      getListSanPhamTheoDoiTac,
      onUpdateCauHinhPhanCapPheDuyet,
      onDeleteCauHinhPhanCapPheDuyet,
      onDeleteCauHinhPhanCapPheDuyetCT,
      onUpdateCauHinhPhanCapPheDuyetCT,
      setDanhSachCauHinhPhanCapPheDuyetCT,
      layDanhSachCauHinhPhanCapPheDuyetCT,
      layDanhSachCauHinhPhanCapPheDuyet,
      setDanhSachCauHinhPhanCapPheDuyet,
      layChiTietTaiKhoanNguoiDung,
      setListChiNhanh,
      getListDoiTac,
      getListChiNhanhTheoDoiTac,
      layDanhSachTaiKhoanNguoiDungPhanTrang,
      layChiTietCauHinhPhanCapPheDuyetCT,
    ],
  );

  return <CauHinhPhanCapPheDuyetContext.Provider value={value}>{children}</CauHinhPhanCapPheDuyetContext.Provider>;
};

export default CauHinhPhanCapPheDuyetProvider;

/**
 * MODEL QUẢN LÝ TỈNH THÀNH
 * - Đ<PERSON><PERSON> nghĩa TypeScript interfaces cho module quản lý tỉnh thành
 * - <PERSON><PERSON> tả cấu trúc dữ liệu cho Context props
 * - Đảm bảo type safety cho toàn bộ module
 */
import {ReactQuery} from "@src/@types";
import {TableTinhThanhColumnDataType} from "./index.configs";

export interface IQuanLyTinhThanhContextProps {
  listTinhThanh: Array<CommonExecute.Execute.IDanhMucTinhThanh>; // Danh sách tỉnh thành hiển thị trong bảng
  tongSoDong: number; // Tổng số bản ghi để hiển thị pagination
  loading: boolean; // Trạng thái loading cho UI
  filterParams: ReactQuery.ITimKiemPhanTrangDanhMucTinhThanhParams & ReactQuery.IPhanTrang; // Tham số filter và phân trang
  getListTinhThanh: (params: ReactQuery.ITimKiemPhanTrangDanhMucTinhThanhParams & ReactQuery.IPhanTrang) => Promise<any>; // Tìm kiếm danh sách tỉnh thành
  getChiTietTinhThanh: (params: TableTinhThanhColumnDataType) => Promise<CommonExecute.Execute.IDanhMucTinhThanh>; // Lấy chi tiết một tỉnh thành
  capNhatChiTietTinhThanh: (params: ReactQuery.ICapNhatDanhMucTinhThanhParams) => Promise<any>; // Tạo mới/cập nhật tỉnh thành
  setFilterParams: React.Dispatch<React.SetStateAction<ReactQuery.ITimKiemPhanTrangDanhMucTinhThanhParams & ReactQuery.IPhanTrang>>; // Cập nhật tham số filter
}

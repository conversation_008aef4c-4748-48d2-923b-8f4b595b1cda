import {IFormInput} from "@src/@types";
import {defaultTableColumnsProps} from "@src/hooks";
import {TableProps} from "antd";

const ruleRequired = {
  required: true,
  message: "Thông tin bắt buộc",
};
//Thông tin chương trình bảo hiểm
export interface IFormChiTietChuongTrinhBaoHiemFieldsConfig {
  ma_doi_tac_ql: IFormInput;
  ma: IFormInput;
  ten: IFormInput;
  nv: IFormInput;
  stt: IFormInput;
  trang_thai: IFormInput;
  ngay_ad: IFormInput;
  mo_ta: IFormInput;
  ma_sp: IFormInput;
}
const FormChiTietChuongTrinhBaoHiem: IFormChiTietChuongTrinhBaoHiemFieldsConfig = {
  ma_doi_tac_ql: {
    component: "select",
    name: "ma_doi_tac_ql",
    label: "<PERSON><PERSON><PERSON> tác",
    placeholder: "Chọn đối tác",
    rules: [ruleRequired],
  },
  ma: {
    component: "input",
    name: "ma",
    label: "<PERSON>ã chương trình bảo hiểm",
    placeholder: "<PERSON>ã chương trình bảo hiểm",
    rules: [ruleRequired],
  },
  ten: {
    component: "input",
    name: "ten",
    label: "Tên chương trình bảo hiểm",
    placeholder: "Tên chương trình bảo hiểm",
    rules: [ruleRequired],
  },
  nv: {
    component: "select",
    name: "nv",
    label: "Nghiệp vụ",
    placeholder: "Chọn nghiệp vụ",
    rules: [ruleRequired],
  },
  ma_sp: {
    component: "select",
    name: "ma_sp",
    label: "Sản phẩm",
    placeholder: "Chọn sản phẩm",
    rules: [ruleRequired],
  },
  stt: {
    component: "input",
    name: "stt",
    label: "Thứ tự hiển thị",
    placeholder: "Thứ tự hiển thị",
    rules: [ruleRequired],
  },
  trang_thai: {
    component: "select",
    name: "trang_thai",
    label: "Trạng thái",
    placeholder: "Chọn trạng thái",
    rules: [ruleRequired],
  },
  ngay_ad: {
    component: "date-picker",
    name: "ngay_ad",
    label: "Ngày áp dụng",
    placeholder: "Chọn ngày áp dụng",
    rules: [ruleRequired],
  },
  mo_ta: {
    component: "textarea",
    name: "mo_ta",
    label: "Mô tả",
    placeholder: "Mô tả",
  },
};
export const TRANG_THAI_CHI_TIET_CTBH = [
  {ten: "Đang sử dụng", ma: "D"},
  {ten: "Ngừng sử dụng", ma: "K"},
];

export default FormChiTietChuongTrinhBaoHiem;
export interface Props {
  listDoiTac: Array<CommonExecute.Execute.IDoiTac>;
}

export interface IModalChiTietChuongTrinhBaoHiemRef {
  open: (data?: CommonExecute.Execute.IChuongTrinhBaoHiem) => void;
  close: () => void;
}
export type IFormChiTietChuongTrinhBaoHiemFieldsConfigKeys = keyof IFormChiTietChuongTrinhBaoHiemFieldsConfig;

//thông tin gói bảo hiểm

export interface TableGoiBaoHiemDataType {
  key: string;
  stt?: number;
  ma?: string;
  ten?: string;
  ma_sp?: string;
  ten_sp?: string;
  ma_ctbh?: string; // Thêm để lưu ma_ctbh của dòng
  ma_doi_tac_ql?: string;
  id_goi_bh?: number;
  nv?: string;
  hanh_dong?: () => JSX.Element | null; // Định nghĩa hanh_dong là hàm trả về JSX.Element;
}

export const goiBaoHiemColumns: TableProps<TableGoiBaoHiemDataType>["columns"] = [
  {...defaultTableColumnsProps, title: "STT", dataIndex: "stt", key: "stt", width: 30},
  {title: "Mã gói", dataIndex: "ma", key: "ma", width: 80, ...defaultTableColumnsProps},
  {title: "Tên gói", dataIndex: "ten", key: "ten", width: 100, ...defaultTableColumnsProps},
  {title: "Sản phẩm", dataIndex: "ten_sp", key: "ten_sp", width: 200, ...defaultTableColumnsProps},
  {title: "Mã sản phẩm", dataIndex: "ma_sp", key: "ma_sp", width: 60, ...defaultTableColumnsProps},
  {
    title: "Hành động",
    dataIndex: "hanh_dong",
    // key: "hanh_dong",
    width: 50,

    render: (_, record) => record.hanh_dong?.(),
    ...defaultTableColumnsProps,
  },
];
export type DataIndexGoi = keyof TableGoiBaoHiemDataType;
export interface TabThongTinGoiBaoHiemProps {
  // listGoiBaoHiem: CommonExecute.Execute.IGoiBaoHiem | null;
  filterValues: {ma?: string; ma_doi_tac_ql?: string};
  chiTietChuongTrinhBaoHiem: CommonExecute.Execute.IChuongTrinhBaoHiem | undefined;
}
//DanhSachs gói bảo hiểm thêm
export interface TableThemGoiBaoHiemDataType {
  key: string;
  chon?: string;
  ma?: string;
  ten?: string;
  ten_sp?: string;
  ma_doi_tac_ql?: string;
  nv?: string;
  id: number;
  ma_sp: string;
}

export const themGoiBaoHiemColumns: TableProps<TableThemGoiBaoHiemDataType>["columns"] = [
  {title: "Chọn", dataIndex: "chon", key: "chon", width: 20, ...defaultTableColumnsProps},
  {title: "Mã gói", dataIndex: "ma", key: "ma", width: 30, ...defaultTableColumnsProps},
  {title: "Tên gói", dataIndex: "ten", key: "ten", width: 50, ...defaultTableColumnsProps},
  {title: "Sản phẩm", dataIndex: "ten_sp", key: "ten_sp", width: 70, ...defaultTableColumnsProps},
  {title: "Mã sản phẩm", dataIndex: "ma_sp", key: "ma_sp", width: 40, ...defaultTableColumnsProps},
];
export interface IModalThemGoiBaoHiemRef {
  open: (data?: CommonExecute.Execute.IGoiBaoHiem) => void;
  close: () => void;
}
export type DataIndexThemGoi = keyof TableThemGoiBaoHiemDataType;
export interface ThemgoiProps {
  chiTietChuongTrinhBaoHiem: CommonExecute.Execute.IChuongTrinhBaoHiem | undefined;
  onDataChange?: (data: TableThemGoiBaoHiemDataType[]) => void;
}
export interface IFormTimKiemGoiBaoHiemFieldsConfig {
  nd_tim: IFormInput;
}
export const FormTimKiemGoiBaoHiem: IFormTimKiemGoiBaoHiemFieldsConfig = {
  nd_tim: {
    component: "input",
    name: "nd_tim",
    label: "Mã/tên gói bảo hiểm",
    placeholder: "Mã/tên gói bảo hiểm",
  },
};

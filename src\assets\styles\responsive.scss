@media (min-width: 992px) {
  .layout-dashboard .ant-layout-footer {
    margin: 0 0 20px 20px;
  }
  .layout-dashboard .ant-layout-sider.sider-primary {
    margin: 20px 0 0 20px;
    padding: 13px 20px;
    height: calc(100vh - 20px);
  }
  .layout-dashboard .ant-layout {
    width: 100%;
    flex-shrink: 1;
    margin-left: 0 !important;
  }
  .layout-dashboard .header-control .sidebar-toggler {
    display: none;
  }
}

@media (min-width: 768px) {
  .layout-dashboard .ant-layout-header {
    margin: 10px 20px;
  }
  .layout-dashboard .header-control {
    margin-top: 0;
  }
  .layout-dashboard .header-control .header-search {
    margin: 0 7px;
  }
  .layout-dashboard .header-control .btn-sign-in span {
    display: inline;
  }
  .profile-nav-bg {
    margin-top: -87.8px;
  }
  .card-profile-head {
    margin: -53px 24px 24px;
  }
}

@media (min-width: 992px) {
  .card-billing-info.ant-card .ant-card-body {
    display: flex;
  }
  .layout-dashboard-rtl {
    overflow: auto;
  }
  .layout-dashboard-rtl .ant-layout-sider.sider-primary {
    margin: 20px 20px 0 0;
    height: calc(100vh - 20px);
  }
  .layout-dashboard-rtl .ant-layout-sider.sider-primary {
    right: 0;
    left: auto;
  }
  .layout-dashboard-rtl .ant-layout-sider.sider-primary {
    right: 0;
    left: auto;
  }
  .layout-dashboard-rtl .ant-layout {
    margin-right: 270px;
    margin-left: 0;
  }
  .layout-dashboard-rtl .ant-layout-footer {
    margin: 0 20px 20px 0;
  }
}

@media (max-width: 768px) {
  .layout-dashboard.layout-dashboard-rtl .header-control {
    justify-content: flex-end;
  }
}

@media (min-width: 992px) {
  .layout-dashboard .ant-layout-sider.sider-primary {
    position: fixed !important;
    left: 0;
    top: 0;
    height: 100vh;
    padding: 0;
    margin: 0;
    z-index: 1000;
    transition: width 0.15s ease-in-out;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
    background: #fff !important;
    border-right: 1px solid #f0f0f0;
  }
  .layout-dashboard
    .ant-layout-sider.sider-primary
    > .ant-layout-sider-children {
    padding: 20px;
    transition: padding 0.15s ease-in-out;
    height: 100%;
    overflow-y: auto;
  }
}

.layout-dashboard .ant-layout-sider.sider-primary .brand span {
  vertical-align: middle;
  margin-left: 7px;
}

.layout-dashboard
  .ant-layout-sider.sider-primary
  .ant-menu-submenu
  .ant-menu-item-group-list
  .ant-menu-submenu-title,
.layout-dashboard
  .ant-layout-sider.sider-primary
  .ant-menu-submenu
  .ant-menu-item-group-list
  a {
  position: relative;
  margin-left: 4px;
  display: flex !important;
  align-items: center !important;
}

.layout-dashboard .ant-layout-sider.sider-primary .aside-footer {
  display: none;
  padding-top: 100px;
  padding-bottom: 33px;
}

// Content area fixed - always positioned as if sidebar is collapsed
@media (min-width: 992px) {
  .layout-dashboard .ant-layout {
    margin-left: 80px !important;
    width: calc(100% - 80px) !important;
    // No transitions - content stays fixed
  }
}

// Overlay sidebar styles - only apply on desktop
@media (min-width: 992px) {
  .layout-dashboard.sidebar-collapsed .ant-layout-sider.sider-primary {
    width: 80px !important;
    min-width: 80px !important;
    max-width: 80px !important;
  }

  .layout-dashboard.sidebar-expanded .ant-layout-sider.sider-primary {
    width: 350px !important;
    min-width: 350px !important;
    max-width: 350px !important;
  }

  .layout-dashboard.sidebar-collapsed .ant-layout-sider.sider-primary > .ant-layout-sider-children {
    padding: 20px 10px;
    overflow: hidden;
    text-align: center;
  }

  .layout-dashboard.sidebar-expanded .ant-layout-sider.sider-primary > .ant-layout-sider-children {
    padding: 20px;
    overflow-y: auto;
  }
}

// Mobile: Reset sidebar to normal behavior (not overlay)
@media (max-width: 991px) {
  .layout-dashboard .ant-layout-sider.sider-primary {
    position: relative !important;
    z-index: auto !important;
    box-shadow: none !important;
    height: auto !important;
    width: 350px !important;
  }

  .layout-dashboard .ant-layout {
    margin-left: 0 !important;
    width: 100% !important;
  }

  // Reset collapsed styles on mobile
  .layout-dashboard.sidebar-collapsed .ant-layout-sider.sider-primary,
  .layout-dashboard.sidebar-expanded .ant-layout-sider.sider-primary {
    width: 350px !important;
    position: relative !important;
  }

  .layout-dashboard.sidebar-collapsed .ant-layout-sider.sider-primary > .ant-layout-sider-children,
  .layout-dashboard.sidebar-expanded .ant-layout-sider.sider-primary > .ant-layout-sider-children {
    padding: 20px !important;
    text-align: left !important;
  }
}

// Brand styles when collapsed - only apply on desktop
@media (min-width: 992px) {
  .layout-dashboard.sidebar-collapsed .brand {
    justify-content: center;
    text-align: center;
  }

  .layout-dashboard.sidebar-collapsed .brand.collapsed span {
    display: none;
    opacity: 0;
    transition: opacity 0.15s ease-in-out;
  }

  // Menu text transitions
  .layout-dashboard .ant-menu-submenu-title .label {
    transition: opacity 0.15s ease-in-out;
  }

  .layout-dashboard.sidebar-collapsed .ant-menu-submenu-title .label.collapsed {
    opacity: 0;
    width: 0;
    overflow: hidden;
  }

  // Menu item spacing when collapsed - ensure perfect icon alignment
  // NOTE: Commented out to avoid conflict with Sidenav/index.default.scss
  // .layout-dashboard.sidebar-collapsed .ant-menu-submenu {
  //   text-align: center;
  //   margin: 0; // Remove margin for consistent spacing

  //   .ant-menu-submenu-title {
  //     padding: 12px 0 !important; // Same padding as brand for alignment
  //     margin: 0 !important;
  //     display: flex !important;
  //     justify-content: center !important;
  //     align-items: center !important;
  //     min-height: 56px; // Same height as brand for perfect alignment

  //     .anticon, .icon {
  //       font-size: 16px !important;
  //       width: 32px !important; // Same as logo for perfect alignment
  //       height: 32px !important; // Same as logo for perfect alignment
  //       display: inline-flex !important; // Use inline-flex to match original CSS
  //       justify-content: center !important;
  //       align-items: center !important;
  //       transition: background-color 0.15s ease-in-out !important; // Only animate background
  //       flex-shrink: 0; // Prevent icon from shrinking
  //       margin: 0 !important;
  //       border-radius: 6px; // Add border radius for consistency
  //       color: #333 !important; // Darker icon color

  //       // Ensure SVG icons are properly sized
  //       svg {
  //         width: 16px !important;
  //         height: 16px !important;
  //         fill: currentColor !important; // Use current color for SVG fill
  //       }

  //       // Hover state for darker color
  //       &:hover {
  //         color: #000 !important; // Even darker on hover
  //       }
  //     }
  //   }
  // }

  // NOTE: Commented out to avoid conflict with Sidenav/index.default.scss
  // .layout-dashboard.sidebar-collapsed .ant-menu-submenu-arrow {
  //   display: none !important;
  // }

  // // Ensure child menu items align with parent icons
  // .layout-dashboard.sidebar-collapsed .ant-menu-item-group-list .ant-menu-item {
  //   padding: 0 !important;
  //   margin: 0 !important; // Remove margin for consistent spacing
  //   text-align: center;

  //   a {
  //     padding: 10px 0 !important; // Slightly smaller padding for hierarchy
  //     display: flex !important;
  //     justify-content: center !important;
  //     align-items: center !important;
  //     min-height: 44px; // Slightly smaller than parent for hierarchy
  //     cursor: pointer; // Ensure clickable
  //     text-decoration: none; // Remove underline

  //     &:hover {
  //       background-color: rgba(0, 0, 0, 0.04); // Hover effect
  //     }

  //     .submenu-icon {
  //       font-size: 16px !important;
  //       font-weight: bold !important;
  //       color: #555 !important; // Darker color for better visibility
  //       width: 32px !important; // Same width as parent icons for perfect alignment
  //       height: 32px !important; // Same height as parent icons for perfect alignment
  //       display: inline-flex !important;
  //       justify-content: center !important;
  //       align-items: center !important;
  //       transition: color 0.15s ease-in-out !important; // Only animate color
  //       border-radius: 6px; // Match parent icons

  //       &:hover {
  //         color: #333 !important; // Even darker on hover
  //       }
  //     }
  //   }
  // }
}

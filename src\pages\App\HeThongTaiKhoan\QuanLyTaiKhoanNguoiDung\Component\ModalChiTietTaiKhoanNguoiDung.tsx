import {CheckOutlined, CopyOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {Button, FormInput, HeaderModal} from "@src/components";
import {usePhongBan} from "@src/hooks";

import {Col, Form, Modal, Row, Tabs} from "antd";
import {forwardRef, useCallback, useEffect, useImperativeHandle, useState} from "react";
import {useQuanLyTaiKhoanNguoiDungContext} from "../index.context";
import {FormChiTietTaiKhoanNguoiDung, initFormFields, TRANG_THAI_NGUOI_DUNG} from "./index.configs";
import TabPhanQuyenChucNangTheoVaiTro from "./TabPhanQuyenChucNangTheoVaiTro";
import TabPhanQuyenChucNangTheoNhom from "./TabPhanQuyenChucNangTheoNhom";
import TabDonViQuanLy from "./TabDonViQuanLy";
import dayjs from "dayjs";
import {hashPasswordWithAccount} from "@src/utils/password";

interface Props {
  danhSachTaiKhoanNguoiDung: Array<CommonExecute.Execute.IDanhSachTaiKhoanNguoiDungPhanTrang>;
}

export interface IModalChiTietTaiKhoanNguoiDungRef {
  open: (data?: CommonExecute.Execute.IChiTietNguoiSuDung) => void;
  close: () => void;
}

const ModalChiTietTaiKhoanNguoiDung = forwardRef<IModalChiTietTaiKhoanNguoiDungRef, Props>(({danhSachTaiKhoanNguoiDung}: Props, ref) => {
  const listPhongBan = usePhongBan();

  const {
    onUpdateTaiKhoanNguoiDung,
    listChucDanh,
    listDoiTac,
    getListChiNhanhTheoDoiTac,
    listChiNhanh,
    chucNangSelected,
    setChucNangNguoiSuDung,
    layChiTietTaiKhoanNguoiDung,
    setDonViQuanLyNguoiSuDung,
    setMenuNguoiSuDung,
    donViSelected,
    menuSelected,
  } = useQuanLyTaiKhoanNguoiDungContext();

  const {dthoai, ten, email, mat_khau, ma_chuc_danh, phong, ma_chi_nhanh, ma, ma_doi_tac, ngay_hl, ngay_kt, trang_thai} = FormChiTietTaiKhoanNguoiDung;
  const [formChiTietTaiKhoanNguoiDung] = Form.useForm();
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const formValues = Form.useWatch([], formChiTietTaiKhoanNguoiDung);
  const [chiTietNguoiSuDung, setChiTietNguoiSuDung] = useState<CommonExecute.Execute.IChiTietNguoiSuDung | null>(null);
  const [filteredPhongBan, setFilteredPhongBan] = useState<Array<CommonExecute.Execute.IDanhMucPhongBan>>([]);
  const [activeTab, setActiveTab] = useState("1");
  const [filteredChiNhanh, setFilteredChiNhanh] = useState<Array<CommonExecute.Execute.IChiNhanh>>([]);
  const [isEdit, setIsEdit] = useState<boolean>(false);
  // const [selectedDonViQuanLy, setSelectedDonViQuanLy] = useState<TableDonViQuanLyDataType[]>([]);
  // const [selectedMenu, setSelectedMenu] = useState<TableMenuNguoiDungDataType[]>([]);
  const [disableCoppy, setDisableCoppy] = useState<boolean>(false);
  // Hàm callback nhận dữ liệu từ TabDonViQuanLy
  const closeModal = useCallback(() => {
    setIsOpen(false);

    // setchitietMenu(null);
    // formThemMenuCha.resetFields();
    // setFilterParams({...filterParams});
    // console.log("filterparamss", filterParams);
  }, []);
  useImperativeHandle(ref, () => ({
    open: (dataChiTietNguoiSuDung?: CommonExecute.Execute.IChiTietNguoiSuDung) => {
      setIsOpen(true);
      setIsEdit(false);
      setDisableSubmit(true);

      if (!dataChiTietNguoiSuDung) {
        setMenuNguoiSuDung([]);
        setChucNangNguoiSuDung([]);
        setDonViQuanLyNguoiSuDung([]);
        setDisableCoppy(true);
      }
      if (dataChiTietNguoiSuDung) {
        setIsEdit(true);
        setChiTietNguoiSuDung(dataChiTietNguoiSuDung);
        setDisableCoppy(false);
      }
    },
    close: () => setIsOpen(false),
  }));
  // init form data gọi vào index.configs
  useEffect(() => {
    // console.log("chi tiết người sửa dung", chiTietNguoiSuDung);

    initFormFields(formChiTietTaiKhoanNguoiDung, chiTietNguoiSuDung);
  }, [chiTietNguoiSuDung]);

  // Lọc danh sách phòng ban theo mã chi nhánh
  useEffect(() => {
    if (chiTietNguoiSuDung?.ma_chi_nhanh_ql) {
      const filtered = listPhongBan.listPhongBan.filter(pb => pb.ma_chi_nhanh === chiTietNguoiSuDung.ma_chi_nhanh_ql);
      setFilteredPhongBan(filtered);
    } else {
      setFilteredPhongBan([]);
    }
  }, [chiTietNguoiSuDung?.ma_chi_nhanh_ql, listPhongBan.listPhongBan]);
  useEffect(() => {
    if (isOpen) {
      setActiveTab("1");
    }
  }, [isOpen]);
  //xử lý validate form
  useEffect(() => {
    formChiTietTaiKhoanNguoiDung
      .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
      .then(() => {
        setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
      })
      .catch(() => {
        setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
      });
  }, [formChiTietTaiKhoanNguoiDung, formValues]);

  //Bấm Update
  const onPressUpdateTaiKhoanNguoiDung = async () => {
    try {
      const values: ReactQuery.IUpdateTaiKhoanNguoiDungParams = formChiTietTaiKhoanNguoiDung.getFieldsValue(); //lấy ra values của form
      //mã hoá password
      console.log("values", values);
      console.log("ormChiTietTaiKhoanNguoiDung.getFieldValue(mat_khau)", formChiTietTaiKhoanNguoiDung.getFieldValue(mat_khau));
      if (values.mat_khau) {
        const passSHA256: string = hashPasswordWithAccount(values.ma, values.mat_khau);
        values.mat_khau = passSHA256;
      }
      const param: ReactQuery.IUpdateTaiKhoanNguoiDungParams = {
        ...values,
        mat_khau: values.mat_khau || "",
        ngay_hl: dayjs(values.ngay_hl).format("YYYYMMDD"),
        ngay_kt: dayjs(values.ngay_hl).format("YYYYMMDD"),
        qly: donViSelected.map(row => ({
          ma_doi_tac_ql: row.ma_doi_tac_ql,
          ma_chi_nhanh_ql: row.ma_chi_nhanh_ql,
        })),
        menu: menuSelected.map(row => ({
          ma: row.ma,
        })),
        quyen: chucNangSelected.map(row => ({
          ma: row.ma_chuc_nang,
        })),
      };
      console.log("params", param);
      const response = await onUpdateTaiKhoanNguoiDung(param);
      await layChiTietTaiKhoanNguoiDung({ma_doi_tac: values.ma_doi_tac, ma: values.ma});
      // if (response === -1) {
      //   console.log("cập nhật thành công");
      //   closeModal();
      // } else {
      //   console.log("cập nhật thất bại");
      // }
      // const dataChiTiet = await layChiTietTaiKhoanNguoiDung(values);
      // setChiTietNguoiSuDung(dataChiTiet);
    } catch (error: any) {
      console.log("onConfirm", error);
    }
  };

  const handleChangeDoiTac = (maDoiTac: string) => {
    console.log("hàm chang đối tác", maDoiTac);
    getListChiNhanhTheoDoiTac();
    console.log("listChiNhanh", listChiNhanh);
    const filtered = listChiNhanh.filter(pb => pb.ma_doi_tac === maDoiTac);
    setFilteredChiNhanh(filtered);
  };
  useEffect(() => {
    console.log("listChiNhanh efect", listChiNhanh);
  }, [listChiNhanh]);

  const optionsChucDanh = listChucDanh.map((item, idx) => ({
    ...item,
    value: item.ma,
    label: item.label || item.ten,
  }));

  // Xử lý khi chọn chi nhánh
  const handleChangeChiNhanh = (maChiNhanh: string) => {
    // Lọc danh sách phòng ban theo mã chi nhánh
    const filtered = listPhongBan.listPhongBan.filter(pb => pb.ma_chi_nhanh === maChiNhanh);
    setFilteredPhongBan(filtered);
    // Lấy giá trị phòng ban hiện tại từ chiTietNguoiSuDung
    const currentPhong = chiTietNguoiSuDung?.phong;
    // Kiểm tra phòng ban hiện tại có trong filtered không
    const found = filtered.find(pb => pb.value === currentPhong);
    if (found) {
      formChiTietTaiKhoanNguoiDung.setFieldsValue({phong: currentPhong}); // Nếu có thì set lại giá trị
    } else {
      formChiTietTaiKhoanNguoiDung.setFieldsValue({phong: null}); // Nếu không có thì reset
    }
  };
  const onCoppyTaiKhoan = () => {
    if (chiTietNguoiSuDung) {
      formChiTietTaiKhoanNguoiDung.setFieldsValue({
        ...chiTietNguoiSuDung,
        ma: "",
        mat_khau: "",
        ngay_hl: dayjs(),
        ngay_kt: dayjs(),
        trang_thai: "D",
        ma_doi_tac: undefined,
        ma_chi_nhanh: undefined,
        phong: undefined,
        ma_chuc_danh: undefined,
        ten: "",
        email: "",
        dthoai: "",
      });
      setIsEdit(false);
    }
  };
  const handleTabChange = async (activeKey: string) => {
    setActiveTab(activeKey);
  };
  //renderFooter Modal
  const renderFooter = () => {
    return (
      <div className="modal-footer-spaced">
        {/* nút coppy */}
        <Button type="default" onClick={onCoppyTaiKhoan} disabled={disableCoppy} className="mr-2" icon={<CopyOutlined />}>
          Coppy
        </Button>
        <Button type="primary" htmlType="submit" form="formUpdatePhongBan" disabled={disableSubmit} onClick={onPressUpdateTaiKhoanNguoiDung} className="mr-2" icon={<CheckOutlined />}>
          Lưu
        </Button>
      </div>
    );
  };
  //Render

  const renderFormInputColum = (props?: any, span = 4) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  const renderTabs = () => {
    const isEdit = !!chiTietNguoiSuDung;

    return (
      <Tabs activeKey={activeTab} onChange={handleTabChange}>
        <Tabs.TabPane key="1" tab="Đơn vị quản lý / Menu">
          <TabDonViQuanLy
            // filterValues={filterValues}
            chiTietNguoiSuDung={chiTietNguoiSuDung}
            // onDataChange={handleDonViQuanLyChange} // Truyền callback cho đơn vị quản lý
            // onDataChangeMenu={handleMenuChange}
          />
        </Tabs.TabPane>
        <Tabs.TabPane key="2" tab="Phân quyền chức năng theo vai trò">
          <TabPhanQuyenChucNangTheoVaiTro
            // filterValues={filterValues}
            chiTietNguoiSuDung={chiTietNguoiSuDung}
          />
        </Tabs.TabPane>
        <Tabs.TabPane key="3" tab="Phân quyền chức năng theo nhóm">
          <TabPhanQuyenChucNangTheoNhom
            // filterValues={filterValues}
            chiTietNguoiSuDung={chiTietNguoiSuDung}
          />
        </Tabs.TabPane>
      </Tabs>
    );
  };
  return (
    <Modal
      className="modal-chi-tiet-tai-khoan"
      title={
        <HeaderModal
          title={chiTietNguoiSuDung ? `Thông tin người dùng ${chiTietNguoiSuDung.ten}` : "Thêm người sử dụng"}
          trang_thai_ten={chiTietNguoiSuDung?.trang_thai_ten}
          trang_thai={chiTietNguoiSuDung?.trang_thai}
        />
      }
      centered
      open={isOpen}
      onOk={() => setIsOpen(false)}
      onCancel={() => {
        formChiTietTaiKhoanNguoiDung.resetFields();
        setIsOpen(false);
        setChiTietNguoiSuDung(null);
      }}
      footer={renderFooter}
      closable
      maskClosable={false}
      width="100vw"
      style={{
        top: 0,
        left: 0,
        padding: 0,
      }}
      styles={{
        body: {
          height: "76vh",
        },
      }}
      // className="custom-full-modal m-2"
    >
      <Form id="formUpdateTaiKhoanNguoiDung" onFinish={onPressUpdateTaiKhoanNguoiDung} form={formChiTietTaiKhoanNguoiDung} layout="vertical" autoComplete="on">
        <Row gutter={16}>
          {renderFormInputColum({...ma_doi_tac, options: listDoiTac, onChange: (value: string) => handleChangeDoiTac(value), disabled: isEdit ? true : false})}
          {renderFormInputColum({...ma_chi_nhanh, options: filteredChiNhanh, disabled: isEdit ? true : false, onChange: handleChangeChiNhanh})}
          {renderFormInputColum({...phong, options: filteredPhongBan, disabled: isEdit ? true : false, onChange: () => {}})}
          {renderFormInputColum({...ma, disabled: isEdit ? true : false})}
          {renderFormInputColum(ten)}
          {renderFormInputColum({...mat_khau})}
          {renderFormInputColum({...ngay_hl})}
          {renderFormInputColum({...ngay_kt})}
          {renderFormInputColum({...ma_chuc_danh, options: optionsChucDanh, onChange: () => {}})}
          {renderFormInputColum(dthoai)}
          {renderFormInputColum(email)}
          {renderFormInputColum({...trang_thai, options: TRANG_THAI_NGUOI_DUNG})}
        </Row>
      </Form>
      {renderTabs()}
    </Modal>
  );
});
ModalChiTietTaiKhoanNguoiDung.displayName = "ModalChiTietTaiKhoanNguoiDung";
export default ModalChiTietTaiKhoanNguoiDung;
// ModalChiTietTaiKhoanNguoiDungComponent.displayName = "ModalChiTietTaiKhoanNguoiDungComponent";
// export const ModalChiTietTaiKhoanNguoiDung = memo(ModalChiTietTaiKhoanNguoiDungComponent, isEqual);

/**
 * Format số với dấu phẩy phân cách hàng nghìn theo chuẩn Việt Nam
 * @param value - Giá trị số cần format
 * @param locale - Locale để format (mặc định là 'vi-VN')
 * @returns Chuỗi đã được format
 */
export const formatNumber = (value: number | string | null | undefined, locale: string = "vi-VN"): string => {
  // Xử lý các trường hợp null, undefined, empty string
  if (value == null || value === "") return "0";

  // Convert sang number
  let numValue: number;
  if (typeof value === "string") {
    // Loại bỏ tất cả dấu phẩy và dấu chấm phân cách hàng nghìn trước khi parse
    const cleanValue = value.replace(/[,.]/g, "");
    numValue = parseFloat(cleanValue);
  } else {
    numValue = value;
  }

  // Kiểm tra nếu không phải số hợp lệ
  if (isNaN(numValue)) return "0";

  // Format với locale cụ thể để đảm bảo nhất quán
  return numValue.toLocaleString(locale, {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  });
};

/**
 * Format số tiền với dấu phẩy phân cách hàng nghìn và 2 chữ số thập phân
 * @param value - Giá trị số cần format
 * @param locale - Locale để format (mặc định là 'vi-VN')
 * @returns Chuỗi đã được format
 */
export const formatCurrency = (value: number | string | null | undefined, locale: string): string => {
  // Xử lý các trường hợp null, undefined, empty string
  if (value == null || value === "") return "0";

  // Convert sang number
  let numValue: number;
  if (typeof value === "string") {
    // Loại bỏ tất cả dấu phẩy và dấu chấm phân cách hàng nghìn trước khi parse
    const cleanValue = value.replace(/[,.]/g, "");
    numValue = parseFloat(cleanValue);
  } else {
    numValue = value;
  }

  // Kiểm tra nếu không phải số hợp lệ
  if (isNaN(numValue)) return "0";

  // Format với locale cụ thể để đảm bảo nhất quán
  return numValue.toLocaleString(locale, {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  });
};

/**
 * Format số với dấu phẩy phân cách hàng nghìn theo chuẩn US (dùng dấu phẩy)
 * @param value - Giá trị số cần format
 * @returns Chuỗi đã được format
 */
export const formatNumberUS = (value: number | string | null | undefined): string => {
  return formatNumber(value, "en-US");
};

/**
 * Format số tiền với dấu phẩy phân cách hàng nghìn theo chuẩn US
 * @param value - Giá trị số cần format
 * @returns Chuỗi đã được format
 */
export const formatCurrencyUS = (value: number | string | null | undefined): string => {
  return formatCurrency(value, "en-US");
};

import {ClearOutlined, PlusCircleOutlined, SearchOutlined} from "@ant-design/icons";
import {IFormInput, ReactQuery} from "@src/@types";
import {Button, FormInput, Highlighter} from "@src/components";
import {COLOR_PALETTE, ID_PAGE} from "@src/constants";
import {Col, Form, Input, InputRef, Row, Table, TableColumnType, Tag, Tooltip} from "antd";
import {FilterDropdownProps} from "antd/es/table/interface";
import {memo, useCallback, useMemo, useRef, useState} from "react";
import {IModalChiTietBoMaNguyenTeRef, ModalChiTietBoMaNguyenTe} from "./Component/ModalChiTietBoMaNguyenTe";
import {BoMaNguyenTeColumns, FormTimKiemBoMaNguyenTe, optionTrangThaiHopDongSelect, radioItemTrangThaiHopDongTable, TableBoMaNguyenTeDataIndex, TableBoMaNguyenTeDataType} from "./index.configs";
import {useChiTietBoMaNguyenTeContext} from "./index.context";
import "./index.default.scss";
import {defaultPaginationTableProps, defaultTableProps, fillRowTableEmpty} from "@src/hooks";

type DataIndex = keyof TableBoMaNguyenTeDataType;

const HighlighterTextSearch = Highlighter as unknown as React.FC<any>; //tạm thời vượt qua lỗi 'Highlighter' cannot be used as a JSX component...
const BoMaNguyenteContent: React.FC = () => {
  const {listDoiTac} = useChiTietBoMaNguyenTeContext();

  const {danhSachBoMaNguyenTe, loading, layDanhSachBoMaNguyenTe, tongSoDong, layChiTietBoMaNguyenTe, defaultFormValue} = useChiTietBoMaNguyenTeContext();

  const [searchText, setSearchText] = useState("");
  const [searchedColumn, setSearchedColumn] = useState("");
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(defaultPaginationTableProps.defaultPageSize);

  const searchInput = useRef<InputRef>(null);
  const [searchParams, setSearchParams] = useState<ReactQuery.IlayChiTietBoMaNguyenTeParams>(defaultFormValue);
  const refModalChiTietBoMaNguyenTe = useRef<IModalChiTietBoMaNguyenTeRef>(null);

  const [searchTextTable, setSearchTextTable] = useState<string>(""); //lưu textsearch để hiển thị vào cell

  const refSearchInputTable = useRef<InputRef>(null);

  const {ma_doi_tac_ql, ma, trang_thai, ten} = FormTimKiemBoMaNguyenTe;

  // ĐỔ DỮ LIỆU CHO BẢNG
  const dataTableListBoMaNguyenTe = useMemo<Array<TableBoMaNguyenTeDataType>>(() => {
    try {
      const tableData = danhSachBoMaNguyenTe.map((item: any, index: number) => {
        return {
          stt: item.stt ?? index + 1,
          ma: item.ma,
          ma_doi_tac_ql: item.ma_doi_tac_ql,
          ten: item.ten,
          doi_tac: item.doi_tac,
          ngay_tao: item.ngay_tao,
          nguoi_tao: item.nguoi_tao,
          ngay_cap_nhat: item.ngay_cap_nhat,
          nguoi_cap_nhat: item.nguoi_cap_nhat,
          trang_thai: item.trang_thai,
          trang_thai_ten: item.trang_thai_ten,
          doi_tac_ql_ten_tat: item.doi_tac_ql_ten_tat,
          ma_doi_tac: item.ma_doi_tac,
          key: index.toString(),
        };
      });
      const arrEmptyRow: Array<TableBoMaNguyenTeDataType> = fillRowTableEmpty(tableData.length, defaultPaginationTableProps.defaultPageSize);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      console.log("dataTableListPhongBan error", error);
      return [];
    }
  }, [danhSachBoMaNguyenTe]);
  // RENDER
  //tạo cột tìm kiếm
  const getColumnSearchProps = (dataIndex: TableBoMaNguyenTeDataIndex, title: string): TableColumnType<TableBoMaNguyenTeDataType> => ({
    filterDropdown:
      dataIndex !== "trang_thai_ten"
        ? ({
            setSelectedKeys,
            selectedKeys,
            confirm,
            clearFilters,
            //  close
          }) => {
            return (
              <div onKeyDown={e => e.stopPropagation()} className="flex flex-row items-center p-2">
                {/* setSelectedKeys: thay đổi danh sách tìm kiếm */}
                {/* selectedKeys: danh sách giá trị hiện tại trong filter */}
                {/* confirm: hàm gọi khi áp dụng filter */}
                {/* clearFilters: hàm dùng để clear input filter */}
                {/* close: đóng dropdown filter */}
                <Input
                  ref={refSearchInputTable}
                  placeholder={`Nhập ${title}`}
                  value={selectedKeys[0]}
                  onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                  onPressEnter={() => handleSearch(selectedKeys as string[], confirm, dataIndex)}
                  className="mr-2 flex p-2"
                />
                <Tooltip title="Tìm kiếm">
                  <Button type="primary" shape="circle" icon={<SearchOutlined />} onClick={() => handleSearch(selectedKeys as string[], confirm, dataIndex)} className="mr-2" />
                </Tooltip>
                <Tooltip title="Xoá">
                  <Button type="primary" shape="circle" icon={<ClearOutlined />} onClick={() => clearFilters && handleReset(clearFilters, confirm, dataIndex)} />
                </Tooltip>
              </div>
            );
          }
        : undefined,
    filterIcon: (filtered: boolean) => {
      return <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} size={40} />;
    },

    onFilter: (value, record) => {
      return (
        record[dataIndex]
          ?.toString()
          .toLowerCase()
          .includes((value as string).toLowerCase()) || false
      );
    },
    //filterDropdownProps: thuộc tính tuỳ chỉnh hành vi dropdown
    filterDropdownProps: {
      // onOpenChange: callback khi dropdown mở/ tắt thì sẽ focus vào input
      onOpenChange(open) {
        if (open) {
          setTimeout(() => refSearchInputTable.current?.select(), 100);
        }
      },
    },

    filterSearch: true,
    filters: dataIndex === "trang_thai_ten" ? radioItemTrangThaiHopDongTable : undefined,
    render: (
      text,
      record,
      // index
    ) => {
      if (dataIndex === "trang_thai_ten") {
        // Xác định màu dựa vào text (trạng thái hiển thị)
        const color = text === "Đang sử dụng" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];
        if (record.key.toString().includes("empty")) return "";
        return (
          <Tag color={color} className="text-[11px]">
            {text}
          </Tag>
        );
      }
      return searchedColumn === dataIndex ? (
        <Highlighter searchWords={[searchText]} textToHighlight={text ? text.toString() : ""} />
      ) : text !== undefined ? (
        text
      ) : (
        <Tag color={"transparent"} className="!text-white text-[11px]">
          {"\u00A0"}
        </Tag>
      );
    },
  });

  const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: DataIndex) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);

  const handleReset = useCallback(
    (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: DataIndex) => {
      clearFilters();
      setSearchedColumn("");
      handleSearch([""], confirm, dataIndex);
    },
    [handleSearch],
  );

  //Bấm tìm kiếm
  const onSearchApi = (values: ReactQuery.ITimKiemPhanTrangPhuongThucKhaiThacParams & ReactQuery.IPhanTrang) => {
    console.log("values onpress tìm kiem", values);
    // đoạn này để sửa các giá trị undefined => '' trước khi request lên API
    // Ant Design Select không coi "" là một giá trị "chưa chọn", mà là một giá trị đã chọn hợp lệ (dù nó rỗng), nên nó sẽ không hiển thị placeholder nữa.
    const cleanedValues = {
      ...values,
      ma_doi_tac_ql: values.ma_doi_tac_ql ?? "",
      ma: values.ma ?? "",
      trang_thai: values.trang_thai ?? "",
      trang: values.trang ?? 1,
      so_dong: values.so_dong ?? 10,
    };
    setSearchParams(cleanedValues);
    setPage(1); // reset về trang 1 khi search mới
    layDanhSachBoMaNguyenTe({...cleanedValues, trang: 1, so_dong: pageSize});
  }; //form input
  //Gọi api khi chuyển trang
  const onChangePage = useCallback(
    (page: number, pageSize: number) => {
      setPage(page);
      setPageSize(pageSize);
      layDanhSachBoMaNguyenTe({...searchParams, trang: page, so_dong: pageSize});
    },
    [searchParams],
  );

  const renderFormInputColum = (props: IFormInput, span = 4) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );

  //render header table
  const renderHeaderTableBoMaNguyenTe = () => {
    return (
      <>
        <Form initialValues={{}} layout="vertical" className="[&_.ant-form-item]:mb-0" onFinish={onSearchApi}>
          <Row gutter={16} align={"bottom"}>
            {renderFormInputColum({...ma_doi_tac_ql, options: listDoiTac})}
            {renderFormInputColum({...ma})}
            {renderFormInputColum({...ten, options: listDoiTac})}
            {renderFormInputColum({...trang_thai, options: optionTrangThaiHopDongSelect})}
            <Col span={3}>
              <Button type="primary" htmlType="submit" loading={loading} icon={<SearchOutlined />} className="mb-0 w-full">
                Tìm kiếm
              </Button>
            </Col>
            <Col span={3}>
              <Button className="w-full" type="primary" icon={<PlusCircleOutlined />} onClick={() => refModalChiTietBoMaNguyenTe.current?.open()} loading={loading}>
                Tạo mới
              </Button>
            </Col>
          </Row>
        </Form>
      </>
    );
  };
  //render table
  return (
    <div id={ID_PAGE.BO_MA_NGUYEN_TE} className="[&_.ant-space]:w-full">
      <Table<TableBoMaNguyenTeDataType>
        {...defaultTableProps}
        loading={loading}
        onRow={(record, rowIndex) => {
          return {
            style: {cursor: loading ? "progress" : "pointer"}, //  thể hiện vị trí con trỏ là button(👈)
            onClick: async event => {
              const chiTietBoMaNguyenTe = await layChiTietBoMaNguyenTe({ma: record.ma, ma_doi_tac_ql: record.ma_doi_tac_ql});
              console.log("chiTietBoMaNguyenTe", chiTietBoMaNguyenTe);

              if (chiTietBoMaNguyenTe) {
                refModalChiTietBoMaNguyenTe.current?.open(chiTietBoMaNguyenTe);
              }
              if (record.key.toString().includes("empty")) return;
              const response = await layChiTietBoMaNguyenTe(record as ReactQuery.IlayChiTietBoMaNguyenTeParams);
            },
          };
        }}
        //tim kiem trong colums
        columns={(BoMaNguyenTeColumns || []).map(item => {
          // Đảm bảo item.key được định nghĩa, item.title là chuỗi và item.key không phải là "stt" trước khi thêm ô tìm kiếm
          return {
            ...item,
            ...(item.key && typeof item.title === "string" && item.key !== "stt" ? getColumnSearchProps(item.key as keyof TableBoMaNguyenTeDataType, item.title) : {}),
          };
        })} //định nghĩa của cột table
        dataSource={dataTableListBoMaNguyenTe}
        title={renderHeaderTableBoMaNguyenTe}
        pagination={{
          ...defaultPaginationTableProps,
          total: tongSoDong,
          onChange: (page, pageSize) => {
            onChangePage(page, pageSize);
          },
        }}
      />

      <ModalChiTietBoMaNguyenTe ref={refModalChiTietBoMaNguyenTe} listDoiTac={listDoiTac} />
    </div>
  );
};
export default memo(BoMaNguyenteContent);

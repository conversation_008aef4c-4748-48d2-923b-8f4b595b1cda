import {ReactQuery} from "@src/@types";

export interface IQuanLyChucDanhContextProps {
  listDoiTac: Array<CommonExecute.Execute.IChiTietChucDanh>;
  listChucDanh: Array<CommonExecute.Execute.IChiTietChucDanh>;
  tongSoDong: number;
  loading: boolean;
  filterParams: ReactQuery.ILayDanhSachChucDanhPhanTrangParams;
  searchChucDanh: () => Promise<void>;
  getChiTietChucDanh: (params: ReactQuery.IChiTietChucDanhParams) => Promise<CommonExecute.Execute.IChiTietChucDanh>;
  capNhatChiTietChucDanh: (params: ReactQuery.IUpdateChucDanhParams) => Promise<any>;
  setFilterParams: React.Dispatch<React.SetStateAction<ReactQuery.ILayDanhSachChucDanhPhanTrangParams & ReactQuery.IPhanTrang>>;
}

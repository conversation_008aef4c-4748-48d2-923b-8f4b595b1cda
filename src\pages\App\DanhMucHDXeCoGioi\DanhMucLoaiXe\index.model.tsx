import { ReactQuery } from "@src/@types";

/**
 * INTERFACE CHO CONTEXT CỦA MODULE DANH MỤC LOẠI XE
 */
export interface DanhMucLoaiXeContextProps {
  /** Danh sách dữ liệu loại xe hiện tại */
  danhSachDanhMucLoaiXe: Array<CommonExecute.Execute.IDanhSachDanhMucLoaiXePhanTrang>; 
  /** Trạng thái loading cho các API calls */
  loading: boolean;
  /** Tổng số dòng dữ liệu (để phân trang) */
  tongSoDong: number;
  /** Giá trị mặc định cho form tìm kiếm */
  defaultFormValue: ReactQuery.ITimKiemPhanTrangDanhMucLoaiXeParams & ReactQuery.IPhanTrang;
  /** 
   * Lấy danh sách loại xe với phân trang và tìm kiếm
   * @param params - Tham số tìm kiếm và phân trang
   */
  layDanhSachDanhMucLoaiXe: (
    params: ReactQuery.ITimKiemPhanTrangDanhMucLoaiXeParams & ReactQuery.IPhanTrang
  ) => Promise<void>;
  /** 
   * Lấy chi tiết một loại xe theo ID
   * @param params - Tham số chứa ID của loại xe
   */
  layChiTietDanhMucLoaiXe: (params: ReactQuery.IChiTietDanhMucLoaiXeParams) => Promise<CommonExecute.Execute.IChiTietDanhMucLoaiXe | null>;
  /** 
   * Cập nhật hoặc thêm mới loại xe
   * @param data - Dữ liệu loại xe cần cập nhật/thêm mới
   */
  onUpdateDanhMucLoaiXe: (data: ReactQuery.ICapNhatDanhMucLoaiXeParams) => Promise<boolean>;
}

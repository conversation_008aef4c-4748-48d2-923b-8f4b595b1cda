import {createContext, useContext} from "react";
import {DanhMucLoaiXeContextProps} from "./index.model";

const defaultContextValue: DanhMucLoaiXeContextProps = {
  danhSachDanhMucLoaiXe: [],
  loading: false,
  tongSoDong: 0,
  layDanhSachDanhMucLoaiXe: async () => {},
  layChiTietDanhMucLoaiXe: async () => null,
  onUpdateDanhMucLoaiXe: async () => false,
  defaultFormValue: {
    nv: "",
    ma: "",
    ten: "",
    trang_thai: "",
    trang: 1,
    so_dong: 13,
  },
};

export const DanhMucLoaiXeContext = createContext<DanhMucLoaiXeContextProps>(defaultContextValue);

export const useDanhMucLoaiXeContext = () => useContext(DanhMucLoaiXeContext);

import {CheckOutlined, CloseOutlined, EditOutlined, PlusOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {Button, HeaderModal} from "@src/components";
import {fillRowTableEmpty} from "@src/hooks";
import {Flex, Form, Input, Modal, Popover, Table, TableColumnType, Tag} from "antd";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useRef, useState} from "react";
import {useBaoHiemXeCoGioiContext} from "../../index.context";
import {IModalDanhGiaTonThatRef, tableDanhGiaTonThatColumns, TableDanhGiaTonThatDataType} from "./Constant";
import {ModalNhapHangMucTonThat} from "./Modal_NhapHangMucTonThat";
import MucDoTonThatPopoverContent from "./MucDoTonThat_PopoverContent";

const PAGE_SIZE = 15; // Số lượng item trên mỗi trang

const ModalDanhGiaTonThatComponent = forwardRef<IModalDanhGiaTonThatRef>((props, ref) => {
  const {layDanhSachHangMucTonThatTheoDoiTuongXe, loading, dataHangMucTonThatTheoDoiTuong, listMucDoTonThatXe, luuDanhGiaTonThatXe} = useBaoHiemXeCoGioiContext();

  useImperativeHandle(ref, () => ({
    open: (params: ReactQuery.IChiTietDoiTuongBaoHiemXeParams & ReactQuery.IChiTietHopDongXeParams) => {
      setIsOpen(true);
      setCurrentParams(params);
      layDanhSachHangMucTonThatTheoDoiTuongXe(params);
    },
    close: () => {
      setIsOpen(false);
      setDataSource([]);
      setCurrentParams(null);
      formTimKiem.resetFields();
    },
  }));

  const [formTimKiem] = Form.useForm();
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [dataSource, setDataSource] = useState<TableDanhGiaTonThatDataType[]>([]);
  const modalNhapHangMucTonThatRef = useRef<any>(null);
  const [openPopoverRowKey, setOpenPopoverRowKey] = useState<string | null>(null);
  const [openGhiChuPopoverRowKey, setOpenGhiChuPopoverRowKey] = useState<string | null>(null);
  const [tempGhiChu, setTempGhiChu] = useState<string>("");
  const [currentParams, setCurrentParams] = useState<(ReactQuery.IChiTietDoiTuongBaoHiemXeParams & ReactQuery.IChiTietHopDongXeParams) | null>(null);

  useEffect(() => {
    const initData = async () => {
      try {
        if (dataHangMucTonThatTheoDoiTuong) {
          const formattedData: TableDanhGiaTonThatDataType[] = dataHangMucTonThatTheoDoiTuong.map((item: any, index: number) => ({
            ...item,
            stt: index + 1,
            key: index.toString(),
          }));

          // Thêm empty rows để fill đủ PAGE_SIZE
          const arrEmptyRow: Array<TableDanhGiaTonThatDataType> = fillRowTableEmpty(formattedData.length, PAGE_SIZE);
          setDataSource([...formattedData, ...arrEmptyRow]);
        }
      } catch (error) {
        console.log("loadData error", error);
        setDataSource([]);
      }
    };

    initData();
  }, [dataHangMucTonThatTheoDoiTuong]);

  //Bấm button Thêm hạng mục
  const handleThemHangMuc = useCallback(() => {
    // Mở modal nhập hạng mục tổn thất
    if (modalNhapHangMucTonThatRef.current) {
      modalNhapHangMucTonThatRef.current.open();
    }
  }, []);

  //Đóng modal
  const handleClose = useCallback(() => {
    setIsOpen(false);
    setDataSource([]);
    setCurrentParams(null);
    formTimKiem.resetFields();
  }, [formTimKiem]);

  //Cập nhật mức độ tổn thất cho item trong bảng
  const handleSelectMucDoTonThat = async (ma_muc_do_tt: string) => {
    if (!openPopoverRowKey) return;
    // Tìm mức độ tổn thất theo mã
    const mucDoTonThat = listMucDoTonThatXe?.find(item => item.ma === ma_muc_do_tt);
    // Cập nhật dataSource với mức độ tổn thất được chọn
    const updatedDataSource = dataSource.map(item => {
      if (item.key === openPopoverRowKey) {
        return {
          ...item,
          ma_muc_do_tt: ma_muc_do_tt,
          ten_muc_do_tt: mucDoTonThat?.ten || "",
        };
      }
      return item;
    });
    setDataSource(updatedDataSource);
    setOpenPopoverRowKey(null); // Đóng popover sau khi chọn
  };

  //Cập nhật ghi chú cho item trong bảng
  const handleSaveGhiChu = () => {
    if (!openGhiChuPopoverRowKey) return;
    // Cập nhật dataSource với ghi chú mới
    const updatedDataSource = dataSource.map(item => {
      if (item.key === openGhiChuPopoverRowKey) {
        return {
          ...item,
          ghi_chu: tempGhiChu,
        };
      }
      return item;
    });
    setDataSource(updatedDataSource);
    setOpenGhiChuPopoverRowKey(null); // Đóng popover sau khi lưu
    setTempGhiChu(""); // Reset giá trị tạm
  };

  //Hàm lưu đánh giá tổn thất
  const handleLuuDanhGiaTonThat = useCallback(async () => {
    try {
      if (!currentParams?.so_id || !currentParams?.so_id_dt) {
        console.error("Missing required parameters: so_id or so_id_dt");
        return;
      }
      // Lọc các hạng mục tổn thất có dữ liệu (loại bỏ empty rows)
      const validItems = dataSource.filter(item => !item.key?.toString().startsWith("empty"));
      // Tạo params để truyền đi
      const params = {
        so_id: currentParams.so_id,
        so_id_dt: currentParams.so_id_dt,
        hm: validItems.map(item => ({
          ma_hang_muc: item.ma_hang_muc || "",
          ma_muc_do_tt: item.ma_muc_do_tt || "",
          ghi_chu: item.ghi_chu || "",
        })),
      };
      // Gọi API cập nhật đánh giá tổn thất
      const success = await luuDanhGiaTonThatXe(params);

      if (success) {
        // Nếu cập nhật thành công, lấy lại danh sách hạng mục tổn thất
        await layDanhSachHangMucTonThatTheoDoiTuongXe(currentParams);
        // handleClose();
      }
    } catch (error) {
      console.log("handleLuuDanhGiaTonThat error", error);
    }
  }, [handleClose, dataSource, currentParams, luuDanhGiaTonThatXe, layDanhSachHangMucTonThatTheoDoiTuongXe]);

  //Xoá dòng trong bảng
  const handleDeleteRow = useCallback(
    (key: string) => {
      console.log("Xoá hạng mục tổn thất", key);
      // Xóa hàng từ dataSource
      const filteredDataSource = dataSource.filter(item => item.key !== key);

      // Cập nhật lại STT và thêm empty rows
      const updatedDataSource = filteredDataSource
        .filter(item => !item.key?.toString().startsWith("empty"))
        .map((item, index) => ({
          ...item,
          stt: index + 1,
        }));

      // Thêm empty rows để fill đủ PAGE_SIZE
      const arrEmptyRow: Array<TableDanhGiaTonThatDataType> = fillRowTableEmpty(updatedDataSource.length, PAGE_SIZE);
      setDataSource([...updatedDataSource, ...arrEmptyRow]);
    },
    [dataSource],
  );

  //RENDER
  const renderColumn = (column: TableColumnType<TableDanhGiaTonThatDataType>) => {
    if (!("dataIndex" in column)) return column;

    //nút xoá
    if (column.dataIndex === "action") {
      return {
        ...column,
        render: (_: any, record: TableDanhGiaTonThatDataType, index: number) => {
          if (record.key && record.key.toString().startsWith("empty")) {
            return (
              <Tag color={"transparent"} className="!text-white text-[11px]">
                {"\u00A0"}
              </Tag>
            );
          }
          return <Button icon={<CloseOutlined />} type="link" onClick={() => handleDeleteRow(record.key)} className="custom-link-button button_xoa"></Button>;
        },
      };
    }
    if (column.dataIndex === "ghi_chu") {
      return {
        ...column,
        render: (_: any, record: TableDanhGiaTonThatDataType, index: number) => {
          if (record.key && record.key.toString().startsWith("empty")) return null;

          const handleOpenGhiChuPopover = () => {
            setOpenGhiChuPopoverRowKey(record.key);
            setTempGhiChu(record.ghi_chu || "");
          };

          const hasContent = record.ghi_chu && record.ghi_chu.trim() !== "";
          const iconColor = hasContent ? "#1890ff" : "gray";

          return (
            <Popover
              title="Ghi chú"
              content={<GhiChuPopoverContent tempGhiChu={tempGhiChu} setTempGhiChu={setTempGhiChu} onCancel={() => setOpenGhiChuPopoverRowKey(null)} onSave={handleSaveGhiChu} />}
              trigger="click"
              open={record.key === openGhiChuPopoverRowKey}
              onOpenChange={visible => {
                if (!visible) {
                  setOpenGhiChuPopoverRowKey(null);
                  setTempGhiChu("");
                }
              }}
              placement="left">
              <Button icon={<EditOutlined style={{color: iconColor}} />} type="link" onClick={handleOpenGhiChuPopover} className="custom-link-button"></Button>
            </Popover>
          );
        },
      };
    }
    if (column.dataIndex === "ten_muc_do_tt") {
      return {
        ...column,
        render: (text: string, record: TableDanhGiaTonThatDataType) => {
          if (record.key && record.key.toString().startsWith("empty")) return null;
          const buttonText = !text || text.trim() === "" ? "Chưa xác định" : text;
          const textColor = !text || text.trim() === "" ? "red" : "rgb(24, 144, 255)";
          return (
            <Popover
              title="Mức độ tổn thất"
              content={
                <MucDoTonThatPopoverContent
                  onSelect={handleSelectMucDoTonThat}
                  onClose={() => setOpenPopoverRowKey(null)}
                  selectedMucDo={record.ma_muc_do_tt} // Truyền mã mức độ tổn thất hiện tại
                />
              }
              trigger="click"
              open={record.key === openPopoverRowKey}
              onOpenChange={visible => {
                setOpenPopoverRowKey(visible ? record.key : null);
              }}
              placement="left">
              <Button type="link" className="custom-link-button" onClick={() => setOpenPopoverRowKey(record.key)} style={{color: textColor}}>
                {buttonText}
              </Button>
            </Popover>
          );
        },
      };
    }

    return column;
  };

  // Render footer
  const renderFooter = () => {
    return (
      <Form.Item className="mb-0">
        <Button type="default" onClick={handleClose} className="mr-2" icon={<CloseOutlined />}>
          Đóng
        </Button>
        <Button type="primary" onClick={handleLuuDanhGiaTonThat} icon={<CheckOutlined />}>
          Lưu
        </Button>
      </Form.Item>
    );
  };

  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal centered className="custom-full-modal" title={<HeaderModal title="Đánh giá tổn thất" />} open={isOpen} onCancel={handleClose} width={"62vw"} maskClosable={false} footer={renderFooter}>
        <div className="mb-[8px]">
          <Button type="primary" icon={<PlusOutlined />} onClick={handleThemHangMuc}>
            Thêm hạng mục tổn thất
          </Button>
        </div>
        {/* {renderFormTimKiem()} */}
        <Table<TableDanhGiaTonThatDataType>
          className="no-header-border-radius"
          columns={tableDanhGiaTonThatColumns?.map(renderColumn)}
          dataSource={dataSource}
          loading={loading}
          bordered
          pagination={false}
          scroll={dataSource.length > PAGE_SIZE ? {x: "max-content", y: 460} : {x: "max-content"}}
          size="small"
        />
      </Modal>

      {/* Modal nhập hạng mục tổn thất */}
      <ModalNhapHangMucTonThat ref={modalNhapHangMucTonThatRef} />
    </Flex>
  );
});

ModalDanhGiaTonThatComponent.displayName = "ModalDanhGiaTonThatComponent";
export const ModalDanhGiaTonThat = memo(ModalDanhGiaTonThatComponent, isEqual);

// Component for Ghi Chú Popover Content
interface GhiChuPopoverContentProps {
  tempGhiChu: string;
  setTempGhiChu: (value: string) => void;
  onCancel: () => void;
  onSave: () => void;
}

const GhiChuPopoverContent: React.FC<GhiChuPopoverContentProps> = ({tempGhiChu, setTempGhiChu, onCancel, onSave}) => {
  return (
    <div className="w-[300px]">
      <Input.TextArea placeholder="Nhập ghi chú" value={tempGhiChu} onChange={e => setTempGhiChu(e.target.value)} />
      <div className="mt-[10px] text-right">
        <Button size="small" onClick={onCancel} className="mr-2" icon={<CloseOutlined />}>
          Huỷ
        </Button>
        <Button type="primary" size="small" onClick={onSave} icon={<CheckOutlined />}>
          Lưu
        </Button>
      </div>
    </div>
  );
};

import {ReactQuery} from "@src/@types";
import {TableDanhMucChauLucColumnDataType} from "./index.configs";

export interface IQuanLyDanhMucChauLucContextProps {
  listDanhMucChauLuc: Array<CommonExecute.Execute.IDanhMucChauLuc>;
  tongSoDong: number;
  loading: boolean;
  filterParams: ReactQuery.ITimKiemPhanTrangDanhMucChauLucParams & ReactQuery.IPhanTrang;
  getListDanhMucChauLuc: (params?: ReactQuery.ITimKiemPhanTrangDanhMucChauLucParams & ReactQuery.IPhanTrang) => Promise<void>;
  getChiTietDanhMucChauLuc: (params: TableDanhMucChauLucColumnDataType) => Promise<CommonExecute.Execute.IDanhMucChauLuc>;
  capNhatChiTietDanhMucChauLuc: (params: ReactQuery.ICapNhatDanhMucChauLucParams) => Promise<any>;
  setFilterParams: React.Dispatch<React.SetStateAction<ReactQuery.ITimKiemPhanTrangDanhMucChauLucParams & ReactQuery.IPhanTrang>>;
}

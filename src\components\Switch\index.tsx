import React, {memo} from "react";
import {Space, Switch as AntSwitch, SwitchProps} from "antd";
import {twMerge} from "tailwind-merge";

import {Typography} from "..";
import {isEqual} from "lodash";

const SwitchComponent: React.FC<
  SwitchProps & {
    labelDirection?: "horizontal" | "vertical";
    containerClassName?: string;
  }
> = props => {
  const {className = "", title, checked, onChange, labelDirection = "vertical", containerClassName, ...etc} = props;
  return (
    <Space direction={labelDirection} className={containerClassName}>
      {title && <Typography type="text">{title}</Typography>}
      <AntSwitch className={twMerge("custom-switch", className)} checked={checked} onChange={onChange} {...etc} />
    </Space>
  );
};

const Switch = memo(SwitchComponent, isEqual);

export default memo(Switch);

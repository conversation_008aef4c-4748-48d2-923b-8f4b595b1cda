// import equals from "lodash";

export const onChangeAlias = (value: string) => {
  let str = value + "";
  str = str.toLowerCase();
  str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, "a");
  str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, "e");
  str = str.replace(/ì|í|ị|ỉ|ĩ/g, "i");
  str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, "o");
  str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, "u");
  str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, "y");
  str = str.replace(/đ/g, "d");
  str = str.replace(/[!@%^*()+=<>?/,.':;"&#[\]~$_`\-{|}\\ ]/g, " ");
  str = str.replace(/ + /g, " ");
  str = str.replace(/\s/g, "");
  str = str.trim();
  return str;
};

// export const onChangeAliasArr = (value: Array<string>): Array<string> => {
//   const newArr: Array<string> = [];

//   value.map((st, i) => {
//     let str = st;
//     // str = str.title.toLowerCase();
//     str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, "a");
//     str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, "e");
//     str = str.replace(/ì|í|ị|ỉ|ĩ/g, "i");
//     str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, "o");
//     str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, "u");
//     str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, "y");
//     str = str.replace(/đ/g, "d");
//     str = str.replace(/!|@|%|\^|\*|\(|\)|\+|\=|\<|\>|\?|\/|,|\.|\:|\;|\'|\"|\&|\#|\[|\]|~|\$|_|`|-|{|}|\||\\/g, " ");
//     str = str.replace(/ + /g, " ");
//     str = str.replace(/\s/g, "");
//     str = str.trim();
//     newArr.push(str);
//   });
//   return newArr;
// };

export const replaceAllISO8859 = (value: string) => {
  //https://www.htmlhelp.com/reference/charset/iso192-223.html
  let str = value + "";
  str = str.toLowerCase();
  str = str.replace(/&#192;|&#224;/g, "à"); //À
  str = str.replace(/&#193;|&#225;/g, "á"); //Á
  str = str.replace(/&#194;|&#226;/g, "â"); //Â
  str = str.replace(/&#195;|&#227;/g, "ã"); //Ã
  str = str.replace(/&#200;|&#232;/g, "ã"); //È
  str = str.replace(/&#201;|&#233;/g, "è"); //É
  str = str.replace(/&#202;|&#234;/g, "à"); //Ê
  str = str.replace(/&#204;|&#236;/g, "à"); //Ì
  str = str.replace(/&#205;|&#237;/g, "à"); //Í
  str = str.replace(/&#208;|&#240;/g, "à"); //Đ
  str = str.replace(/&#210;|&#242;/g, "à"); //Ò
  str = str.replace(/&#211;|&#243;/g, "à"); //Ó
  str = str.replace(/&#212;|&#244;/g, "à"); //Ô
  str = str.replace(/&#213;|&#245;/g, "à"); //Õ
  str = str.replace(/&#217;|&#249;/g, "à"); //Ù
  str = str.replace(/&#218;|&#250;/g, "à"); //Ú
  str = str.replace(/&#221;|&#253;/g, "à"); //Ý
  str = str.trim();
  return str;
};

export const replaceAll = (source = "", textReplace = "", textInstead = "") => {
  return source.split(textReplace).join(textInstead);
};

export const removeHtmlTag = (source = "") => {
  return source.replace(/<\/?[^>]+(>|$)/g, "");
};

// export const compareValue = (val1: any, val2: any) => {
// return equals(val1, val2);
// };

export const removeChar = (source = "") => {
  return source.replace(/[^0-9]/g, "");
};

// export const getIdYoutube = (url: string) => {
//   let ID = "";
//   url = `${url}`.replace(/(>|<)/gi, "").split(/(vi\/|v=|\/v\/|youtu\.be\/|\/embed\/)/);
//   if (url[2] !== undefined) {
//     ID = url[2].split(/[^0-9a-z_\-]/i);
//     ID = ID[0];
//   } else {
//     ID = url;
//   }
//   return ID + "";
// };

/**
 * Validate url
 * @param {String} str url
 * @returns
 */
// export const isValidURL = str => {
//   let pattern = new RegExp(
//     "^(https?:\\/\\/)?" + // protocol
//       "((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.)+[a-z]{2,}|" + // domain name
//       "((\\d{1,3}\\.){3}\\d{1,3}))" + // OR ip (v4) address
//       "(\\:\\d+)?(\\/[-a-z\\d%_.~+]*)*" + // port and path
//       "(\\?[;&a-z\\d%_.~+=-]*)?" + // query string
//       "(\\#[-a-z\\d_]*)?$",
//     "i",
//   ); // fragment locator
//   return !!pattern.test(str);
// };

/**
 * slice address ellipsizeMode middle
 * @address {String} str address
 * @number of slice
 */
// export const ellipsizeAddressMiddle = (address, number = 0) => {
//   if (!address) return "";
//   if (typeof number !== "number") return address;

//   const str = address.toString();
//   const start = str.slice(0, number);
//   const end = str.slice(-number);
//   return `${start}...${end}`;
// };

// export const getPercentageValue = inputVal => {
//   let outputVal = "0";
//   if (+inputVal > 100) {
//     outputVal = "100";
//   } else outputVal = inputVal;
//   return outputVal;
// };

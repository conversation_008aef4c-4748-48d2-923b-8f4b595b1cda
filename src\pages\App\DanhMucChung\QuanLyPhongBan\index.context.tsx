import { createContext, useContext } from "react";
import { QuanLyPhongBanContextProps } from "./index.model";

//createContext : khai báo 1 context mà component có thể cung cấp hoặc đọc
export const QuanLyPhongBanContext = createContext<QuanLyPhongBanContextProps>({
  //khởi tạo các giá trị mặc định, trong context này có giá trị là onSubmit
  danhSachPhongBan: [],
  loading: false,
  onUpdate: () => Promise.resolve(false),
  layDanhSachPhongBanPhanTrang: () => Promise.resolve(),
  tongSoDong: 0,
  layChiTietPhongBan: () => Promise.resolve(null),
  defaultFormValue: {}
});

//useContext(someContext): là React Hook cho phép bạn đọc và subscribe context từ component của mình.
// file .context muốn truy câp context thì sử dụng useLoginContext : const {onSubmit} = useLoginContext();
export const useQuanLyPhongBanContext = () => useContext(QuanLyPhongBanContext);

import React, {PropsWithChildren, useCallback, useEffect, useMemo, useState} from "react";

import {QuanLyDanhMucNhomMaBenhContext} from "./index.context";
import {IQuanLyDanhMucNhomMaBenhContextProps} from "./index.model";
import {ACTION_CODE} from "@src/constants";
import {ReactQuery} from "@src/@types";
import {TableDanhMucNhomMaBenhColumnDataType} from "./index.configs";
import {useCommonExecute} from "@src/services/react-queries";
import {message} from "antd";

const QuanLyDanhMucNhomMaBenhProvider: React.FC<PropsWithChildren> = props => {
  const {children} = props;
  const mutateUseCommonExecute = useCommonExecute();

  const [listDanhMucNhomMaBenh, setListDanhMucNhomMaBenh] = useState<Array<CommonExecute.Execute.IDanhMucNhomMaBenh>>([]);
  const [tongSoDong, setTongSoDong] = useState<number>(0);
  const [filterParams, setFilterParams] = useState<ReactQuery.ITimKiemPhanTrangDanhMucNhomMaBenhParams & ReactQuery.IPhanTrang>({ma: "", ten: "", trang_thai: "", trang: 1, so_dong: 20});
  
  //khởi tạo dữ liệu ban đàu
  useEffect(() => {//nè
    initData();
  }, []);

  const getChiTietDanhMucNhomMaBenh = useCallback(
    async (data: TableDanhMucNhomMaBenhColumnDataType) => {
      try {
        console.log("[Provider] getChiTietDanhMucNhomMaBenh được gọi với data:", data);
        
        if (!data.ma) {
          console.log("[Provider] Không có mã để tìm kiếm, data.ma =", data.ma);
          return {} as CommonExecute.Execute.IDanhMucNhomMaBenh;
        }
        
        console.log("[Provider] Gọi API với params:", {
          ma: data.ma,
          actionCode: ACTION_CODE.CHI_TIET_DANH_MUC_NHOM_MA_BENH
        });
        
        const response = await mutateUseCommonExecute.mutateAsync({
          ma: data.ma,
          actionCode: ACTION_CODE.CHI_TIET_DANH_MUC_NHOM_MA_BENH,
        } as any);
        
        console.log("[Provider] Raw response từ API:", response);
        console.log("[Provider] Response.data:", response.data);
        
        // Xử lý structure response.data.lke
        const responseData = response.data as any; // Type casting để truy cập lke
        if (responseData && responseData.lke && Array.isArray(responseData.lke) && responseData.lke.length > 0) {
          const result = responseData.lke[0] as CommonExecute.Execute.IDanhMucNhomMaBenh;
          console.log("[Provider] Trả về data từ lke[0]:", result);
          return result;
        } else {
          console.log("[Provider] Không tìm thấy data trong response.data.lke");
          return {} as CommonExecute.Execute.IDanhMucNhomMaBenh;
        }
      } catch (error) {
        console.log("[Provider] getChiTietDanhMucNhomMaBenh error:", error);
        return {} as CommonExecute.Execute.IDanhMucNhomMaBenh;
      }
    },
    [mutateUseCommonExecute],
  );

  const getListDanhMucNhomMaBenh = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        ...filterParams,
        actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_DANH_MUC_NHOM_MA_BENH,
      } as any);
      if (response.data) {
        setListDanhMucNhomMaBenh(response.data.data);
        setTongSoDong(response.data.tong_so_dong);
      }
    } catch (error) {
      console.log("getListDanhMucNhomMaBenh error ", error);
    }
  }, [mutateUseCommonExecute]);

  useEffect(() => {
    getListDanhMucNhomMaBenh();
  }, [filterParams]);

  const capNhatChiTietDanhMucNhomMaBenh = useCallback(
    async (data: ReactQuery.ICapNhatDanhMucNhomMaBenhParams, isEditMode: boolean = false) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...data,
          actionCode: ACTION_CODE.UPDATE_DANH_MUC_NHOM_MA_BENH,
        } as any);
        message.success(isEditMode ? "Cập nhật nhóm mã bệnh thành công" : "Thêm mới nhóm mã bệnh thành công");
        return response.data;
      } catch (error) {
        message.error(isEditMode ? "Có lỗi xảy ra khi cập nhật nhóm mã bệnh" : "Có lỗi xảy ra khi thêm mới nhóm mã bệnh");
        console.log("capNhatChiTietDanhMucNhomMaBenh err", error);
        // return {} as CommonExecute.Execute.IDanhMucNhomMaBenh;
      }
    },
    [mutateUseCommonExecute],
  );

  //initData chỉ dùng trong useEffect 1 lần, không truyền đi đâu cả, nên không cần memo hoá
  const initData = () => {};

  const value = useMemo<IQuanLyDanhMucNhomMaBenhContextProps>(
    () => ({
      listDanhMucNhomMaBenh,
      tongSoDong,
      loading: mutateUseCommonExecute.isLoading,
      filterParams,
      getListDanhMucNhomMaBenh,
      getChiTietDanhMucNhomMaBenh,
      capNhatChiTietDanhMucNhomMaBenh,
      setFilterParams,
    }),
    [listDanhMucNhomMaBenh, tongSoDong, mutateUseCommonExecute, filterParams, getListDanhMucNhomMaBenh, getChiTietDanhMucNhomMaBenh, capNhatChiTietDanhMucNhomMaBenh],
  );

  return <QuanLyDanhMucNhomMaBenhContext.Provider value={value}>{children}</QuanLyDanhMucNhomMaBenhContext.Provider>;
};

export default QuanLyDanhMucNhomMaBenhProvider;

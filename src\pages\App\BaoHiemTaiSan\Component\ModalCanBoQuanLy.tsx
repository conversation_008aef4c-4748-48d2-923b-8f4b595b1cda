import {CheckOutlined, ClearOutlined, CloseOutlined, SearchOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {Button, FormInput, Highlighter} from "@src/components";
import {COLOR_PALETTE} from "@src/constants";
import {defaultPaginationTableProps, fillRowTableEmpty, useDoiTac} from "@src/hooks";
import {useTableNoHover} from "@src/utils/tableNoHoverUtils";
import {Col, Empty, Form, Input, InputRef, Modal, Row, Table, TableColumnType, Tag, Tooltip} from "antd";
import {forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState} from "react";
import {useBaoHiemTaiSanContext} from "../index.context";
import {
  defaultFormTimKiemPhanTrangCanBoQuanLy,
  defaultFormTimKiemPhanTrangDaiLyKhaiThac,
  FormTimKiemPhanTrangCanBoQuanLy,
  listCanBoColumns,
  radioItemTrangThaiDaiLy,
  TableCanBoQuanLyDataType,
  TRANG_THAI_CHI_TIET_DAI_LY,
} from "./Constant";
interface Props {
  onSelectCanBo: (dly_kt: CommonExecute.Execute.IDoiTacNhanVien | null) => void;
}
export interface IModalCanBoQuanLyRef {
  open: (ma_chi_nhanh_ql?: string, ma_doi_tac_ql?: string) => void;
  close: () => void;
}
const {ma_doi_tac_ql, ma_chi_nhanh_ql, ma, ten, nd_tim, phong_ql, cmt, dthoai, email, trang_thai} = FormTimKiemPhanTrangCanBoQuanLy;
type DataIndex = keyof TableCanBoQuanLyDataType;

const PAGE_SIZE = 10; //khai báo khác default do modal nhỏ hơn

const ModalCanBoQuanLy = forwardRef<IModalCanBoQuanLyRef, Props>(({onSelectCanBo}: Props, ref) => {
  const {getTableClassName: getNoHoverTableClassName} = useTableNoHover({
    activeRowColor: "#96bf49", // Màu xanh lá cho row được chọn
    styleId: "modal-dai-ly-khai-thac-table-styles", // ID riêng cho modal này
  });

  const {listDoiTac} = useDoiTac();

  const {loading, tongSoDongCanBoQuanLy, listChiNhanh, listPhongBan, getListCanBoQuanLy, listCanBo} = useBaoHiemTaiSanContext();
  const [formCanBoQuanLy] = Form.useForm();
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const [chiTietCanBoQuanLy, setChiTietCanBoQuanLy] = useState<CommonExecute.Execute.IDoiTacNhanVien | null>(null);
  const [searchParams, setSearchParams] = useState<ReactQuery.ITimKiemPhanTrangCanBoQuanLyParams>(defaultFormTimKiemPhanTrangDaiLyKhaiThac);
  const [page, setPage] = useState(1);
  const [searchText, setSearchText] = useState("");
  const [searchedColumn, setSearchedColumn] = useState("");
  const [listPhongBanFiltered, setListPhongBanFiltered] = useState<Array<CommonExecute.Execute.IPhongBan>>([]);
  const [listChiNhanhFiltered, setListChiNhanhFiltered] = useState<Array<CommonExecute.Execute.IChiNhanh>>([]);

  const searchInput = useRef<InputRef>(null);

  useImperativeHandle(ref, () => ({
    open: (ma_chi_nhanh_ql?: string, doiTacSelected?: string) => {
      setIsOpen(true);
      initModalData(doiTacSelected || "", ma_chi_nhanh_ql || "");
      //đoạn này để set dữ liệu vào form và cho tìm kiếm với mã đối tác, mã chi nhánh từ ngoài truyền vào
      formCanBoQuanLy.setFieldsValue({
        ma_doi_tac_ql: doiTacSelected,
      });
      const bodySearch = {
        ...defaultFormTimKiemPhanTrangCanBoQuanLy,
        ma_doi_tac_ql: doiTacSelected,
      };
      setSearchParams(bodySearch);
      getListCanBoQuanLy(bodySearch);
      if (doiTacSelected && doiTacSelected !== "") {
        onChangeMaDoiTac(doiTacSelected);
      }
    },
    close: () => setIsOpen(false),
  }));

  //MAP VALUE CỦA LIST VÀO TABLE
  const dataTableListCanBoQuanLy = useMemo<Array<TableCanBoQuanLyDataType>>(() => {
    try {
      const tableData = listCanBo.map((item: any, index: number) => {
        return {
          ...item,
          key: index.toString(),
        };
      });
      const arrEmptyRow: Array<TableCanBoQuanLyDataType> = fillRowTableEmpty(tableData.length, PAGE_SIZE);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      console.log("dataTableListDaiLy error", error);
      return [];
    }
  }, [listCanBo]);

  //xử lý validate form
  useEffect(() => {
    setDisableSubmit(!chiTietCanBoQuanLy); // Nếu có activeKey -> enable nút Lưu, ngược lại -> disable
  }, [chiTietCanBoQuanLy]);

  const initModalData = (maDoiTac: string, maChiNhanh: string) => {
    if (listPhongBan.length > 0 && maDoiTac !== "") {
      const filteredPhongBanData = listPhongBan.filter(item => item.ma_doi_tac === maDoiTac && item.ma_chi_nhanh === maChiNhanh);
      setListPhongBanFiltered(filteredPhongBanData);
    } else {
      setListPhongBanFiltered([]);
    }
  };

  const onChangeMaDoiTac = (maDoiTac: string) => {
    formCanBoQuanLy.setFieldValue("ma_chi_nhanh_ql", undefined);
    formCanBoQuanLy.setFieldValue("phong_ql", undefined);
    // const maChiNhanh = form.getFieldValue("ma_chi_nhanh_ql");
    setListChiNhanhFiltered(listChiNhanh.filter(item => item.ma_doi_tac === maDoiTac));
    setListPhongBanFiltered([]);
  };
  const onChangeMaChiNhanh = (maChiNhanh: string) => {
    formCanBoQuanLy.setFieldValue("phong_ql", undefined);
    const maDoiTac = formCanBoQuanLy.getFieldValue("ma_doi_tac_ql");
    setListPhongBanFiltered(listPhongBan.filter(item => item.ma_chi_nhanh === maChiNhanh && item.ma_doi_tac === maDoiTac));
  };

  //Bấm tìm kiếm
  const onTimKiemPhanTrangCanBoQuanLy = (values: ReactQuery.ITimKiemPhanTrangCanBoQuanLyParams & ReactQuery.IPhanTrang) => {
    // đoạn này để sửa các giá trị undefined => '' trước khi request lên API
    // Ant Design Select không coi "" là một giá trị "chưa chọn", mà là một giá trị đã chọn hợp lệ (dù nó rỗng), nên nó sẽ không hiển thị placeholder nữa.
    const cleanedValues = {
      ...values,
      ma_doi_tac_ql: values.ma_doi_tac_ql ?? "",
      ma_chi_nhanh_ql: values.ma_chi_nhanh_ql ?? "",
      phong_ql: values.phong_ql ?? "",
      ma: values.ma ?? "",
      ten: values.ten ?? "",
      cmt: values.cmt ?? "",
      dthoai: values.dthoai ?? "",
      email: values.email ?? "",
      nd_tim: values.nd_tim ?? "",
      trang_thai: values.trang_thai ?? "",
      trang: values.trang ?? 1,
    };
    setSearchParams(cleanedValues);
    setPage(1); // reset về trang 1 khi search mới
    getListCanBoQuanLy({...cleanedValues, trang: 1, so_dong: PAGE_SIZE});
  };

  //bấm huỷ / để sau xoá dữ liệu của modal khi nào mở thì gọi lại
  const onPressDeSau = () => {
    setIsOpen(false);
    setChiTietCanBoQuanLy(null);
  };

  //bấm xác nhận
  const handleSelectCanBoQuanLy = () => {
    if (chiTietCanBoQuanLy && onSelectCanBo) {
      onSelectCanBo(chiTietCanBoQuanLy); // Gọi callback để truyền khách hàng ra ngoài
      setIsOpen(false); // Đóng modal sau khi chọn
    }
  };

  //renderFooter Modal
  const renderFooter = () => {
    return (
      <Form.Item className="mb-0">
        <Button type="default" htmlType="reset" form="formSelectDaiLy" onClick={onPressDeSau} className="mr-2" icon={<CloseOutlined />}>
          Đóng
        </Button>
        <Button disabled={disableSubmit} type="primary" form="formSelectDaiLy" onClick={handleSelectCanBoQuanLy} icon={<CheckOutlined />}>
          Chọn
        </Button>
      </Form.Item>
    );
  };
  //Render

  //search client
  const handleSearch = (selectedKeys: string[], confirm: () => void, dataIndex: DataIndex) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  };

  const handleReset = (clearFilters: () => void, confirm: () => void, dataIndex: DataIndex) => {
    clearFilters();
    confirm();
    setSearchText("");
    setSearchedColumn(dataIndex);
  };

  //Gọi api khi chuyển trang
  const onChangePage = useCallback(
    (page: number, pageSize: number) => {
      setPage(page);
      getListCanBoQuanLy({...searchParams, trang: page, so_dong: pageSize});
    },
    [searchParams],
  );

  const renderFormInputColum = (props?: any) => (
    <Col span={4}>
      <FormInput {...props} />
    </Col>
  );

  const getColumnSearchProps = (dataIndex: DataIndex, title: string): TableColumnType<TableCanBoQuanLyDataType> => ({
    filterDropdown:
      dataIndex !== "trang_thai_ten"
        ? ({setSelectedKeys, selectedKeys, confirm, clearFilters, close}) => (
            <div style={{padding: 8}} onKeyDown={e => e.stopPropagation()} className="flex flex-row items-center p-2">
              <Input
                ref={searchInput}
                placeholder={`Tìm theo ${title}`}
                value={selectedKeys[0]}
                onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                onPressEnter={() => handleSearch(selectedKeys as string[], confirm, dataIndex)}
                style={{display: "block", marginRight: 8}}
              />
              <Tooltip title="Tìm kiếm">
                <Button type="primary" shape="circle" icon={<SearchOutlined />} onClick={() => handleSearch(selectedKeys as string[], confirm, dataIndex)} className="mr-2" />
              </Tooltip>
              <Tooltip title="Xoá">
                <Button type="primary" shape="circle" icon={<ClearOutlined />} onClick={() => clearFilters && handleReset(clearFilters, confirm, dataIndex)} />
              </Tooltip>
            </div>
          )
        : null,
    filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} />,
    onFilter: (value, record) =>
      record[dataIndex]
        ? record[dataIndex]
            .toString()
            .toLowerCase()
            .includes((value as string).toLowerCase())
        : false,
    filterDropdownProps: {
      onOpenChange(open) {
        if (open) {
          setTimeout(() => searchInput.current?.select(), 100);
        }
      },
    },
    filters: dataIndex === "trang_thai_ten" ? radioItemTrangThaiDaiLy : undefined,
    render: (text, record, index) => {
      if (dataIndex === "trang_thai_ten") {
        // Xác định màu dựa vào text (trạng thái hiển thị)
        const color = text === "Đang sử dụng" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];
        return <Tag color={color}>{text}</Tag>;
      }
      return searchedColumn === dataIndex ? (
        <Highlighter searchWords={[searchText]} textToHighlight={text ? text.toString() : ""} />
      ) : text !== undefined ? (
        text
      ) : (
        <Tag color={"transparent"} className="!text-white text-[11px]">
          {"\u00A0"}
        </Tag>
      );
    },
  });

  // render modal
  return (
    <div>
      <Modal
        title={"Thông tin cán bộ quản lý"}
        open={isOpen}
        onOk={() => setIsOpen(false)}
        onCancel={() => {
          formCanBoQuanLy.resetFields();
          setIsOpen(false);
          setChiTietCanBoQuanLy(null);
        }}
        footer={renderFooter}
        closable
        maskClosable={false}
        width="95vw"
        style={{
          top: "4vh",
          left: 0,
          padding: 0,
        }}
        styles={{
          body: {
            height: "75vh",
            overflow: "hidden",
          },
        }}
        className="custom-full-modal">
        <Form id="form" onFinish={onTimKiemPhanTrangCanBoQuanLy} form={formCanBoQuanLy} layout="vertical" autoComplete="on" className="mt-1">
          <div className="flex w-full flex-wrap items-end">
            <Row gutter={16} align="bottom">
              {renderFormInputColum({...ma_doi_tac_ql, options: listDoiTac, onChange: (value: any) => onChangeMaDoiTac(value)})}
              {renderFormInputColum({...ma_chi_nhanh_ql, options: listChiNhanhFiltered, onChange: (value: any) => onChangeMaChiNhanh(value)})}
              {renderFormInputColum({...phong_ql, options: listPhongBanFiltered})}
              {renderFormInputColum({...ma})}
              {renderFormInputColum({...ten})}
              {renderFormInputColum({...cmt})}
              {renderFormInputColum({...dthoai})}
              {renderFormInputColum({...email})}
              {renderFormInputColum({...nd_tim})}
              {renderFormInputColum({...trang_thai, options: TRANG_THAI_CHI_TIET_DAI_LY})}
              <Col>
                <Form.Item className="!mb-2">
                  <Button type="primary" htmlType="submit" loading={loading} icon={<SearchOutlined />} className="w-full">
                    Tìm kiếm
                  </Button>
                </Form.Item>
              </Col>
            </Row>
          </div>
        </Form>
        {dataTableListCanBoQuanLy.length > 0 ? (
          <div
            style={{
              overflow: "auto",
            }}>
            <Table<TableCanBoQuanLyDataType>
              className={getNoHoverTableClassName("custom-table no-header-border-radius")}
              sticky
              scroll={{y: "100vh"}}
              loading={loading}
              style={{cursor: "pointer"}}
              onRow={(record, rowIndex) => {
                if (record.key.toString().includes("empty")) return {};
                return {
                  onClick: async event => {
                    setChiTietCanBoQuanLy(record as CommonExecute.Execute.IDoiTacNhanVien);
                  },
                  onDoubleClick: async event => {
                    await setChiTietCanBoQuanLy(record as CommonExecute.Execute.IDoiTacNhanVien);
                    handleSelectCanBoQuanLy();
                  },
                };
              }}
              rowClassName={record => (chiTietCanBoQuanLy?.ma && record.ma === chiTietCanBoQuanLy?.ma ? "table-row-active" : "")} // ✅ Set class cho dòng được chọn
              columns={(listCanBoColumns || []).map(item => {
                // Đảm bảo item.key được định nghĩa, item.title là chuỗi và item.key không phải là "stt" trước khi thêm ô tìm kiếm
                return {
                  ...item,
                  ...(item.key && typeof item.title === "string" && item.key !== "sott" ? getColumnSearchProps(item.key as keyof TableCanBoQuanLyDataType, item.title) : {}),
                };
              })} //định nghĩa cột của table
              dataSource={dataTableListCanBoQuanLy}
              // title={'HHIhi'}
              pagination={{
                ...defaultPaginationTableProps,
                defaultPageSize: PAGE_SIZE,
                total: tongSoDongCanBoQuanLy, //quan trọng - để hiển thị toàn bộ số lượng dòng dữ liệu phục vụ việc phân trang
                current: page, //set current page
                pageSize: PAGE_SIZE, //Số lượng mục dữ liệu trên mỗi trang
                //được gọi khi page size change
                onChange: (page, pageSize) => {
                  onChangePage(page, pageSize);
                },
                locale: {
                  jump_to: "Tới trang",
                  page: "",
                },
              }}
              bordered
            />
          </div>
        ) : (
          <Empty description="Không có dữ liệu" />
        )}
      </Modal>
    </div>
  );
});

ModalCanBoQuanLy.displayName = "ModalCanBoQuanLy";
export default ModalCanBoQuanLy;

import React from "react";
import ReactApex<PERSON>hart from "react-apexcharts";
import {ApexOptions} from "apexcharts";

interface IPieChartProps {
  data: {
    labels: string[];
    values: number[];
  };
  height?: number;
  title?: string;
  showLegend?: boolean;
}

const PieChart: React.FC<IPieChartProps> = ({data, height = 300, title, showLegend = true}) => {
  const options: ApexOptions = {
    chart: {
      type: "donut",
      height: height,
    },
    title: {
      text: title,
      align: "center",
      style: {
        fontSize: "16px",
        fontWeight: "600",
        color: "#262626",
      },
    },
    labels: data.labels,
    colors: ["#52c41a", "#722ed1", "#fa8c16", "#f5222d"],
    dataLabels: {
      enabled: true,
      formatter: function (val: number) {
        return val.toFixed(1) + "%";
      },
      style: {
        fontSize: "12px",
        fontWeight: "600",
        colors: ["#fff"],
      },
    },
    plotOptions: {
      pie: {
        donut: {
          size: "60%",
          labels: {
            show: true,
            total: {
              show: true,
              label: "Tổng",
              fontSize: "14px",
              fontWeight: "600",
              color: "#262626",
              formatter: function (w) {
                const total = w.globals.seriesTotals.reduce((a: number, b: number) => a + b, 0);
                return new Intl.NumberFormat("vi-VN", {
                  notation: "compact",
                  compactDisplay: "short",
                }).format(total);
              },
            },
            value: {
              show: true,
              fontSize: "20px",
              fontWeight: "700",
              color: "#262626",
              formatter: function (val) {
                return new Intl.NumberFormat("vi-VN", {
                  notation: "compact",
                  compactDisplay: "short",
                }).format(parseInt(val));
              },
            },
          },
        },
      },
    },
    legend: {
      show: showLegend,
      position: "right",
      fontSize: "12px",
      markers: {
        width: 8,
        height: 8,
        radius: 4,
      },
      formatter: function (seriesName, opts) {
        const value = opts.w.globals.series[opts.seriesIndex];
        const percentage = ((value / opts.w.globals.seriesTotals.reduce((a: number, b: number) => a + b, 0)) * 100).toFixed(1);
        return `${seriesName}: ${percentage}%`;
      },
    },
    tooltip: {
      y: {
        formatter: function (value) {
          return new Intl.NumberFormat("vi-VN", {
            style: "currency",
            currency: "VND",
          }).format(value);
        },
      },
    },
    responsive: [
      {
        breakpoint: 768,
        options: {
          chart: {
            height: 250,
          },
          legend: {
            position: "bottom",
          },
        },
      },
    ],
  };

  return (
    <div className="pie-chart-container">
      <ReactApexChart options={options} series={data.values} type="donut" height={height} />
    </div>
  );
};

export default PieChart;

import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState} from "react";
import FormChiTietDanhMucDaiLy, {
  cayTyLeHoaHongColumns,
  DataIndexTyLeHoaHong,
  IModalChiTietCauHinhTyLeHoaHongRef,
  Props,
  TableCayTyLeHoaHongDataType,
  TableTyLeHoaHongDataType,
  TRANG_THAI,
  tyLeHoaHongColumns,
} from "./index.configs";
import {Col, Flex, Form, InputRef, message, Modal, Row, Table, TableColumnType} from "antd";
import {useCauHinhTyLeHoaHongContext} from "../index.context";
import {isEqual} from "lodash";
import {Button, FormInput, HeaderModal, Highlighter, InputCellTable, TableFilterDropdown} from "@src/components";
import dayjs from "dayjs";
import isSameOrBefore from "dayjs/plugin/isSameOrBefore";
import {defaultTableProps, fillRowTableEmpty} from "@src/hooks";
import {FilterDropdownProps} from "antd/es/table/interface";
import {CheckOutlined, CloseOutlined, PlusCircleOutlined} from "@ant-design/icons";
import {useTableNoHover} from "@src/utils/tableNoHoverUtils";

dayjs.extend(isSameOrBefore);
const {ma_doi_tac_ql, ma, ma_chi_nhanh_ql} = FormChiTietDanhMucDaiLy;

const ModalChiTietCauHinhTyLeHoaHongComponent = forwardRef<IModalChiTietCauHinhTyLeHoaHongRef, Props>(({}: Props, ref) => {
  useImperativeHandle(ref, () => ({
    open: (dataDanhMucDaiLy?: CommonExecute.Execute.IChiTietDanhMucDaiLy) => {
      setIsOpen(true);
      console.log("dataDanhMucDaiLy", dataDanhMucDaiLy);
      if (dataDanhMucDaiLy) {
        layDanhSachTyLeHoaHong({ma_dai_ly: dataDanhMucDaiLy.dl[0].ma, ma_doi_tac_ql: dataDanhMucDaiLy.dl?.ma_doi_tac});
        setChiTietDanhMucDaiLy(dataDanhMucDaiLy);
      }
    },
    close: () => {
      setIsOpen(false);
      setChiTietDanhMucDaiLy(null);
      setCayTyLeHoaHong([]);
    },
  }));
  // const refModalThemTyLeHoaHong = useRef<IModalThemTyLeHoaHongRef>(null);
  // const {ngay_ad,tlhh,tlhh_ct} = FormThemNgayApDung;
  const [chiTietDanhMucDaiLy, setChiTietDanhMucDaiLy] = useState<CommonExecute.Execute.IChiTietDanhMucDaiLy | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  const [pageSize, setPageSize] = useState(7);
  const {
    loading,
    filterParams,
    listDoiTac,
    setFilterParams,
    danhSachTyLeHoaHong,
    listChiNhanh,
    getListChiNhanh,
    onUpdateCauHinhTyLeHoaHong,
    layDanhSachTyLeHoaHong,
    setDanhSachTyLeHoaHong,
    cayTyLeHoaHong,
    layCayTyLeHoaHong,
    setCayTyLeHoaHong,
  } = useCauHinhTyLeHoaHongContext();
  const [searchedColumn, setSearchedColumn] = useState("");
  const [searchText, setSearchText] = useState("");
  const searchInput = useRef<InputRef>(null);
  const [form] = Form.useForm();
  const formValues = Form.useWatch([], form);
  const [formThemNgayApDung] = Form.useForm();
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const [ngayAdMoiTao, setNgayAdMoiTao] = useState<string | null>(null);
  const [selectedCauHinhTyLeHoaHong, setSelectedCauHinhTyLeHoaHong] = useState<string>("");
  const [dataSource, setDataSource] = useState<Array<TableTyLeHoaHongDataType>>([]);
  const [inputRowKey, setInputRowKey] = useState<string | null>(null);
  const [inputRowKeys, setInputRowKeys] = useState<string[]>([]);

  useEffect(() => {
    if (chiTietDanhMucDaiLy) {
      const arrFormData = [];
      for (const key in chiTietDanhMucDaiLy.dl) {
        arrFormData.push({
          name: key as keyof CommonExecute.Execute.IChiTietDanhMucDaiLy,
          value: chiTietDanhMucDaiLy[key as keyof CommonExecute.Execute.IChiTietDanhMucDaiLy],
        });
      }
      form.setFields(arrFormData);
    }
    console.log("chi tiết danh mục đại lý", chiTietDanhMucDaiLy);
  }, [chiTietDanhMucDaiLy, form]);
  // lấy chi nhánh theo đối tác
  useEffect(() => {
    if (chiTietDanhMucDaiLy?.dl?.ma_doi_tac) {
      getListChiNhanh();
    }
  }, [chiTietDanhMucDaiLy?.dl?.ma_doi_tac]);
  // useEffect(() => {
  //   if (chiTietDanhMucDaiLy?.ma) {
  //     layDanhSachCauHoiApDung({ma_sp: chiTietDanhMucDaiLy.ma, ma_doi_tac_ql: chiTietDanhMucDaiLy.ma_doi_tac_ql, nv: chiTietDanhMucDaiLy.nv});
  //   }
  // }, [chiTietDanhMucDaiLy]);

  //   useEffect(() => {
  //     if (selecteCauHoiApDung !== null) {
  //       setDisableSubmit(false);
  //     } else {
  //       setDisableSubmit(true);
  //     }
  //   }, [selecteCauHoiApDung]);
  // Tự động chọn dòng cuối cùng khi danh sách thay đổi
  useEffect(() => {
    if (Array.isArray(danhSachTyLeHoaHong) && danhSachTyLeHoaHong.length > 0) {
      let selected;
      if (ngayAdMoiTao) {
        // Tìm ngày vừa tạo
        //selected gồm ngày áp dụng , mã đối tác quản lý và mã chi nhánh quản lý
        const selected = danhSachTyLeHoaHong.find(
          item => dayjs(item.ngay_ad, "DD/MM/YYYY").isSame(dayjs(ngayAdMoiTao, "DD/MM/YYYY")) && item.ma_doi_tac_ql === item.ma_doi_tac_ql && item.ma_chi_nhanh_ql === item.ma_chi_nhanh_ql,
        );
        // selected = danhSachTyLeHoaHong.find(item =>
        // {
        //   //gồm ngày áp dụng , mã đối tác quản lý và mã chi nhánh quản lý
        //   return (
        //   ngay_ad:dayjs(item.ngay_ad, "DD/MM/YYYY").isSame(dayjs(ngayAdMoiTao, "DD/MM/YYYY"));
        //   // ma_dai_ly: chiTietDanhMucDaiLy?.dl?.ma,
        //   ma_doi_tac_ql: item.ma_doi_tac_ql
        //   )

        // });
        setNgayAdMoiTao(null); // Reset lại sau khi đã select
      } else {
        // Tìm ngày gần hôm nay nhất (không vượt quá hôm nay)
        const today = dayjs();
        const validDates = danhSachTyLeHoaHong.filter(
          item =>
            dayjs(item.ngay_ad, "DD/MM/YYYY").isBefore(today) ||
            (dayjs(item.ngay_ad, "DD/MM/YYYY").isSame(today) && item.ma_doi_tac_ql === item.ma_doi_tac_ql && item.ma_chi_nhanh_ql === item.ma_chi_nhanh_ql),
        );

        if (validDates.length > 0) {
          selected = validDates.reduce((prev, curr) => {
            const prevDiff = today.diff(dayjs(prev.ngay_ad, "DD/MM/YYYY"), "day");
            const currDiff = today.diff(dayjs(curr.ngay_ad, "DD/MM/YYYY"), "day");
            return currDiff < prevDiff ? curr : prev;
          });
        } else {
          // Fallback: nếu không có ngày nào <= hôm nay, chọn ngày gần nhất
          selected = danhSachTyLeHoaHong.reduce((prev, curr) => {
            const prevDiff = Math.abs(today.diff(dayjs(prev.ngay_ad, "DD/MM/YYYY"), "day"));
            const currDiff = Math.abs(today.diff(dayjs(curr.ngay_ad, "DD/MM/YYYY"), "day"));
            return currDiff < prevDiff ? curr : prev;
          });
        }
      }
      if (selected && selected.ngay_ad !== undefined && selected.ngay_ad !== null) {
        setSelectedCauHinhTyLeHoaHong(String(selected.ngay_ad));
        layCayTyLeHoaHong({
          ngay_ad: Number(dayjs(selected.ngay_ad, "DD/MM/YYYY").format("YYYYMMDD")),
          ma_dai_ly: chiTietDanhMucDaiLy?.dl?.ma,
          ma_doi_tac_ql: selected.ma_doi_tac_ql,
          // ma_chi_nhanh_ql: selected.ma_chi_nhanh_ql,
        });
      }
    } else {
      setSelectedCauHinhTyLeHoaHong("");
      setCayTyLeHoaHong([]);
    }
  }, [danhSachTyLeHoaHong]);
  const closeModal = () => {
    setIsOpen(false);
    setChiTietDanhMucDaiLy(null);
    setCayTyLeHoaHong([]);
    setDanhSachTyLeHoaHong([]);
    form.resetFields();
    setFilterParams(filterParams);
  };

  // Dữ liệu bảng câu hỏi áp dụng(bên trái)
  const dataTableListTyLeHoaHong = useMemo<Array<TableTyLeHoaHongDataType>>(() => {
    try {
      const mappedData = Array.isArray(danhSachTyLeHoaHong)
        ? danhSachTyLeHoaHong
            .filter(item => item)
            .map((item: any, index: number) => ({
              key: index.toString(),
              ngay_ad: item.ngay_ad || "",
              tlhh: Number(item.tlhh) || 0,
              tlhh_ct: Number(item.tlhh_ct) || 0,
              ma_doi_tac_ql: item.ma_doi_tac_ql,
              ma_chi_nhanh_ql: item.ma_chi_nhanh_ql,
              // hanh_dong: () => renderDeleteButton(item.ngay_ad),
            }))
        : [];
      console.log("mappedData", mappedData);
      const arrEmptyRow = fillRowTableEmpty(mappedData.length, pageSize);

      setDataSource([...mappedData, ...arrEmptyRow]);

      return [...mappedData, ...arrEmptyRow];
    } catch (error) {
      console.error("Lỗi khi xử lý dữ liệu bảng ngày áp dụng:", error);
      return [];
    }
  }, [danhSachTyLeHoaHong, pageSize]);
  const dataTableCayTyLeHoaHong = useMemo<Array<TableCayTyLeHoaHongDataType>>(() => {
    try {
      const mappedData = Array.isArray(cayTyLeHoaHong)
        ? cayTyLeHoaHong
            .filter(item => item) // loại bỏ undefined/null
            .map((item: any, index: number) => {
              const cap = item.cap || 0; // lấy cấp từ dữ liệu, nếu có

              const prefix =
                Array(cap * 3)
                  .fill("\u00A0")
                  .join("") + Array(cap).fill("+").join("");

              return {
                key: index.toString(),
                ngay_ad: item.ngay_ad || "",
                tlhh: Number(item.tlhh) || 0,
                tlhh_ct: Number(item.tlhh_ct) || 0,
                ten_dai_ly: prefix + (item.ten_dai_ly || ""),
                ma_dai_ly: item.ma,
                stt: index + 1,
                cap, // lưu lại cấp nếu cần dùng cho style
              };
            })
        : [];
      const arrEmptyRow = fillRowTableEmpty(mappedData.length, pageSize);
      return [...mappedData, ...arrEmptyRow];
    } catch (error) {
      console.error("Lỗi khi xử lý dữ liệu bảng cây tỷ lệ hoa hồng:", error);
      return [];
    }
  }, [cayTyLeHoaHong, pageSize]);

  const renderFormInputColum = (props?: any, span = 5) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: DataIndexTyLeHoaHong) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);
  const handleReset = useCallback(
    (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: DataIndexTyLeHoaHong) => {
      clearFilters();
      setSearchedColumn("");
      handleSearch([""], confirm, dataIndex);
    },
    [handleSearch],
  );

  // const handleDelete = async (bt: number) => {
  //   try {
  //     await onDeleteCauHoiApDung({
  //       bt: Number(bt),
  //     });

  //     // Reset selection sau khi xóa
  //     setSelectedCauHoiApDung(null);
  //     //  setDanhSachCauHinhPhanCapPheDuyetCT([]);

  //     // Refresh danh sách
  //     if (chiTietDanhMucDaiLy?.ma && chiTietDanhMucDaiLy.nv && chiTietDanhMucDaiLy.ma_doi_tac_ql) {
  //       layDanhSachCauHoiApDung({ma_sp: chiTietDanhMucDaiLy.ma, nv: chiTietDanhMucDaiLy.nv, ma_doi_tac_ql: chiTietDanhMucDaiLy.ma_doi_tac_ql});
  //     }
  //   } catch (error) {
  //     console.log("Lỗi khi xóa ngày áp dụng:", error);
  //   }
  // };

  const handleTyLeHoaHongRowClick = async (record: TableTyLeHoaHongDataType) => {
    // Nếu là hàng trống (key chứa "empty" hoặc không có bt), set null
    if (record.key?.toString().includes("empty") || !record.ngay_ad || record.ngay_ad.trim() === "") {
      // Không set selectedCauHinhTyLeHoaHong nữa
      return;
    }
    setSelectedCauHinhTyLeHoaHong(record.ngay_ad);
    try {
      const cayTyLeHoaHongData = await layCayTyLeHoaHong({
        ngay_ad: Number(dayjs(record.ngay_ad, "DD/MM/YYYY").format("YYYYMMDD")),
        ma_dai_ly: chiTietDanhMucDaiLy?.dl?.ma,
        ma_doi_tac_ql: record.ma_doi_tac_ql,
        // ma_chi_nhanh_ql: record.ma_chi_nhanh_ql,
      });
      setCayTyLeHoaHong(cayTyLeHoaHongData);
    } catch (error) {
      setCayTyLeHoaHong([]);
    }
  };
  const handleAddRow = () => {
    // Tìm index dòng trống đầu tiên
    const emptyRowIndex = dataSource.findIndex(item => item.key.includes("empty"));
    const newData = [...dataSource];
    if (emptyRowIndex !== -1) {
      newData.splice(emptyRowIndex, 1);
    }
    // Tìm vị trí cuối cùng của dữ liệu gốc (dòng có ma_cau_hoi hoặc trường phân biệt)
    const lastDataIndex = newData.reduce((lastIdx, item, idx) => (item.ngay_ad ? idx : lastIdx), -1);
    // Tạo dòng dữ liệu mới trống
    const newKey = `new-${Date.now()}`;
    const newRow = {
      key: newKey,
      ngay_ad: "",
      // ma_doi_tac_ql:.ma_doi_tac_ql,
      // ma_chi_nhanh_ql: chiTietDanhMucDaiLy?.dl?.ma_chi_nhanh,
      tlhh: 0,
      tlhh_ct: 0,
    };

    // Thêm dòng mới vào ngay sau dòng dữ liệu cuối cùng
    newData.splice(lastDataIndex + 1, 0, newRow);

    setDataSource(newData);
    setInputRowKey(newKey);
    setInputRowKeys(prev => [...prev, newKey]);
  };
  const handleInputChange = (index: number, dataIndex: string, value: string) => {
    setDataSource(prev => {
      const next = [...prev];
      next[index] = {...next[index], [dataIndex]: value};

      return next;
    });
  };
  // Sử dụng useTableNoHover hook để tự động inject CSS và cung cấp utility functions
  const {getTableClassName: getNoHoverTableClassName, getRowClassName} = useTableNoHover({
    activeRowColor: "#96bf49", // Màu xanh lá cho row được chọn
    styleId: "modal-cau-hinh-ty-le-hoa-hong-table-styles", // ID riêng cho modal này
  });
  const onConfirm = async () => {
    try {
      const validRows = dataSource.filter(row => !row.key.includes("empty") && row.ngay_ad && row.ngay_ad.trim() !== "");

      const finalValues = {
        ma_dai_ly: chiTietDanhMucDaiLy?.dl?.ma,
        ma_doi_tac_ql: chiTietDanhMucDaiLy?.dl?.ma_doi_tac,
        dl: validRows.map(row => ({
          ngay_ad: Number(dayjs(row.ngay_ad, "DD/MM/YYYY").format("YYYYMMDD")),
          tlhh: Number(row.tlhh),
          tlhh_ct: Number(row.tlhh_ct),
        })),
      };

      console.log("check list dai ly", danhSachTyLeHoaHong);

      // Kiểm tra trùng lặp trong danh sách mới (validRows)
      const ngayAdCounts: {[key: string]: number} = {};

      for (const row of validRows) {
        const ngayAd = row.ngay_ad;
        ngayAdCounts[ngayAd] = (ngayAdCounts[ngayAd] || 0) + 1;

        // Nếu có nhiều hơn 2 ngày áp dụng giống nhau
        if (ngayAdCounts[ngayAd] >= 2) {
          message.error(`Ngày áp dụng ${ngayAd} đã tồn tại!`);
          return;
        }
      }

      console.log("finalValues", finalValues);
      await onUpdateCauHinhTyLeHoaHong(finalValues);

      layDanhSachTyLeHoaHong({ma_dai_ly: chiTietDanhMucDaiLy?.dl?.ma, ma_doi_tac_ql: chiTietDanhMucDaiLy?.dl?.ma_doi_tac});
      // closeModal();
    } catch (error) {
      console.log("onConfirm", error);
    }
  };
  const getColumnSearchTyLeHoaHongProps = (dataIndex: DataIndexTyLeHoaHong, title: string): TableColumnType<DataIndexTyLeHoaHong> => ({
    filterDropdown: ({setSelectedKeys, selectedKeys, confirm, clearFilters}) => (
      <TableFilterDropdown
        ref={searchInput}
        title={title}
        selectedKeys={selectedKeys}
        dataIndex={dataIndex}
        setSelectedKeys={setSelectedKeys}
        handleSearch={handleSearch}
        confirm={confirm}
        clearFilters={clearFilters}
        handleReset={handleReset}
      />
    ),
    // filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} />,
    onFilter: (value, record) =>
      record[dataIndex as keyof DataIndexTyLeHoaHong]
        ? record[dataIndex as keyof DataIndexTyLeHoaHong]
            .toString()
            .toLowerCase()
            .includes((value as string).toLowerCase())
        : false,
    filterDropdownProps: {
      onOpenChange(open) {
        if (open) {
          setTimeout(() => searchInput.current?.select(), 100);
        }
      },
    },
    // filters: dataIndex === "trang_thai_ten" ? radioItemTrangThaiNhomTable : undefined,
    render: (text, record, index) => {
      //   if (dataIndex === "trang_thai_ten") {
      //     // Xác định màu dựa vào text (trạng thái hiển thị)
      //     const color = text === "Đang sử dụng" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];
      //     if (record.key.toString().includes("empty")) return "";
      //     return <Tag color={color}>{text}</Tag>;
      //   }
      // if (record.key.toString().includes("empty")) return <div style={{height: 20}} />;

      return searchedColumn === dataIndex ? (
        <Highlighter searchWords={[searchText]} textToHighlight={text ? text.toString() : ""} />
      ) : text !== undefined ? (
        text
      ) : (
        <Button type="link" className="h-auto p-0" style={{color: "transparent"}} icon={<CloseOutlined style={{visibility: "hidden"}} />}></Button>
      );
    },
  });
  const renderColumn = (column: any, index: number) => {
    if (column.dataIndex === "tlhh" || column.dataIndex === "tlhh_ct") {
      return {
        ...column,
        render: (_: any, record: TableTyLeHoaHongDataType, index: number) => {
          const isEmptyRow = record.key.includes("empty");
          // Nếu là dòng trống đang được chọn để nhập liệu
          if (inputRowKeys.includes(record.key)) {
            return (
              <div className="custom-checkbox-cell">
                <InputCellTable
                  className="text-right"
                  component="input"
                  key={`${record.key}-${column.dataIndex}`}
                  value={record[column.dataIndex as keyof TableTyLeHoaHongDataType] ?? ""}
                  index={index}
                  dataIndex={column.dataIndex}
                  onChange={handleInputChange}
                  autoFocus
                />
                {/* <span className="ml-1">%</span> */}
              </div>
            );
          }
          // Các dòng trống khác chỉ để trống
          // if (isEmptyRow) return <div style={{height: 22}} />;
          // Dòng thường thì render như cũ
          if (isEmptyRow) {
            return (
              <div className="custom-checkbox-cell">
                <FormInput className="!mb-0" component="date-picker" disabled style={{display: "none"}} />
              </div>
            );
          }
          return (
            <div className="custom-checkbox-cell">
              <InputCellTable
                className="text-right"
                component="input"
                key={`${record.key}-${column.dataIndex}`}
                value={record[column.dataIndex as keyof TableTyLeHoaHongDataType] ?? ""}
                index={index}
                dataIndex={column.dataIndex}
                onChange={handleInputChange}
                autoFocus
              />
              {/* <span className="ml-1">%</span> */}
            </div>
          );
        },
      };
    }
    if (column.dataIndex === "ngay_ad") {
      return {
        ...column,
        render: (text: any, record: TableTyLeHoaHongDataType, index: number) => {
          const isEmptyRow = record.key.includes("empty");

          // Trường hợp dòng trống: hiển thị ô trống
          if (isEmptyRow) {
            return "";
          }
          // const value = text ? dayjs(text, "DD/MM/YYYY") : undefined;
          // Trường hợp dòng có dữ liệu: hiển thị date-picker
          return (
            <FormInput
              className="!mb-0"
              component="date-picker"
              value={text && dayjs(text, "DD/MM/YYYY", true).isValid() ? (dayjs(text, "DD/MM/YYYY") as any) : undefined}
              format="DD/MM/YYYY"
              onChange={(value: any) => {
                const dateString = value ? dayjs(value).format("DD/MM/YYYY") : "";
                handleInputChange(index, "ngay_ad", dateString);
              }}
            />
          );
        },
      };
    }
    return {
      ...column,
      ...(column.key && typeof column.title === "string" && column.key !== "stt" ? getColumnSearchTyLeHoaHongProps(column.key, column.title) : {}),
      render: (text: string) => text || "", // Đảm bảo hiển thị chuỗi rỗng nếu text undefined
    };
  };

  const renderForm = () => (
    <Form form={form} layout="vertical" initialValues={{trang_thai: TRANG_THAI[0].ma}}>
      <Row gutter={16}>
        {renderFormInputColum({...ma_doi_tac_ql, options: listDoiTac, disabled: true}, 6)}
        {/* {renderFormInputColum({...ma_chi_nhanh_ql, options: listChiNhanh, disabled: true}, 6)} */}
        {renderFormInputColum({...ma, disabled: true}, 4)}
      </Row>
    </Form>
  );
  const renderTableTyLeHoaHong = () => {
    return (
      <Table<TableTyLeHoaHongDataType>
        className={getNoHoverTableClassName("table-vai-tro no-header-border-radius")}
        {...defaultTableProps}
        style={{cursor: "pointer"}}
        onRow={(record, rowIndex) => {
          return {
            style: {
              cursor: "pointer",
              // background: record.ngay_ad && record.ngay_ad === selectedCauHinhTyLeHoaHong ? "#96BF49" : undefined,
            },
            onClick: () => handleTyLeHoaHongRowClick(record), // Xử lý nhấp vào hàng
          };
        }}
        rowClassName={record => getRowClassName(selectedCauHinhTyLeHoaHong === record.ngay_ad)}
        title={null}
        pagination={false}
        columns={(tyLeHoaHongColumns || []).map(
          renderColumn,
          //   item => {
          //   // Đảm bảo item.key được định nghĩa, item.title là chuỗi và item.key không phải là "stt" trước khi thêm ô tìm kiếm
          //   return {
          //     ...item,
          //     ...(item.key && typeof item.title === "string" && item.key !== "stt" ? getColumnSearchTyLeHoaHongProps(item.key as keyof TableTyLeHoaHongDataType, item.title) : {}),
          //   };
          // }
        )}
        dataSource={dataSource}
        bordered
        scroll={dataSource.length > pageSize ? {y: 285} : undefined}
      />
    );
  };
  const renderTableCayTyLeHoaHong = () => {
    return (
      <Table<TableCayTyLeHoaHongDataType>
        className="table-vai-tro no-header-border-radius"
        {...defaultTableProps}
        style={{cursor: "pointer"}}
        title={null}
        pagination={false}
        columns={(cayTyLeHoaHongColumns || []).map(item => {
          if ("dataIndex" in item && item.dataIndex === "ten_dai_ly") {
            return {
              ...item,
              render: (text, record) => {
                if (record.key.includes("empty")) return <div style={{height: 32}} />;
                let className = "";

                if (record.ma_dai_ly == chiTietDanhMucDaiLy?.dl?.ma) className = "font-bold";
                // else if (record.cap === 2) className = "italic";
                return (
                  // <div className="custom-checkbox-cell">
                  <span className={className} style={{height: 32}}>
                    {text}
                  </span>
                  // </div>
                );
              },
            };
          }
          // Đảm bảo item.key được định nghĩa, item.title là chuỗi và item.key không phải là "stt" trước khi thêm ô tìm kiếm
          //render dòng trống

          return item;
        })}
        dataSource={dataTableCayTyLeHoaHong}
        bordered
        scroll={dataTableCayTyLeHoaHong.length > pageSize ? {y: 285} : undefined}
      />
    );
  };
  const renderTableTyLeHoaHongFooter = () => {
    return (
      <Form.Item style={{marginTop: 16, marginBottom: 0, textAlign: "end"}}>
        <Button
          className=""
          type="primary"
          icon={<PlusCircleOutlined />}
          onClick={
            handleAddRow
            // () => refModalThemTyLeHoaHong.current?.open()
          }
          loading={loading}
          disabled={disableSubmit}>
          Thêm tỷ lệ hoa hồng
        </Button>
        <Button className="ml-2" type="primary" icon={<CheckOutlined />} iconPosition="end" onClick={onConfirm} loading={loading} disabled={disableSubmit}>
          Lưu
        </Button>
      </Form.Item>
    );
  };
  const renderTable = () => {
    return (
      <Row>
        <Col span={15} style={{paddingRight: 16}}>
          {renderTableTyLeHoaHong()}
          {renderTableTyLeHoaHongFooter()}
        </Col>
        <Col span={9}>{renderTableCayTyLeHoaHong()}</Col>
      </Row>
    );
  };
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        title={
          <HeaderModal
            title={chiTietDanhMucDaiLy ? `Chi tiết tỷ lệ hoa hồng của đại lý ${chiTietDanhMucDaiLy.dl?.ten}` : ""}
            trang_thai_ten={chiTietDanhMucDaiLy?.dl?.trang_thai_ten}
            trang_thai={chiTietDanhMucDaiLy?.dl?.trang_thai}
          />
        }
        maskClosable={false}
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width="80vw"
        footer={null}
        className="modal-ty-le-hoa-hong [&_.ant-space]:w-full">
        {renderForm()}
        {renderTable()}
        {/* <ModalThemCauHoi ref={refModalThemCauHoi} CauHoiApDung={selecteCauHoiApDung}></ModalThemCauHoi> */}
      </Modal>
    </Flex>
  );
});
ModalChiTietCauHinhTyLeHoaHongComponent.displayName = "ModalChiTietCauHinhTyLeHoaHongComponent";
export const ModalChiTietCauHinhTyLeHoaHong = memo(ModalChiTietCauHinhTyLeHoaHongComponent, isEqual);

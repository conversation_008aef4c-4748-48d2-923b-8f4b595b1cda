import React, {PropsWithChildren, useCallback, useEffect, useMemo, useState} from "react";
import {ReactQuery} from "@src/@types";
import {ACTION_CODE} from "@src/constants";
import {defaultPaginationTableProps} from "@src/hooks";
import {useCommonExecute} from "@src/services/react-queries";
import {message} from "antd";
import {TableNhomHangMucXeColumnDataType} from "./index.configs";
import {NhomHangMucXeContext} from "./index.context";
import {INhomHangMucXeContextProps} from "./index.model";

/**
 * Component này wrap các component con và cung cấp context với state và methods
 */
const NhomHangMucXeProvider: React.FC<PropsWithChildren> = props => {
  const {children} = props;
  const mutateUseCommonExecute = useCommonExecute(); // Hook để gọi API chung

  const [listNhomHangMucXe, setListNhomHangMucXe] = useState<Array<CommonExecute.Execute.INhomHangMucXe>>([]); // Danh sách nhóm hạng mục xe
  const [tongSoDong, setTongSoDong] = useState<number>(0); // Tổng số dòng từ API
  const [filterParams, setFilterParams] = useState<ReactQuery.ITimKiemPhanTrangNhomHangMucXeParams & ReactQuery.IPhanTrang>({
    // Khởi tạo filter params với giá trị mặc định
    ma: "",
    ten: "",
    nv: "",
    trang_thai: "",
    trang: 1,
    so_dong: defaultPaginationTableProps.defaultPageSize,
  });

  // ===== KHỞI TẠO DỮ LIỆU BAN ĐẦU =====
  useEffect(() => {
    initData(); 
  }, []);

  /**
   * @param data - Dữ liệu row được click chứa mã và nghiệp vụ
   * @returns Chi tiết nhóm hạng mục xe
   */
  //lấy chi tiết khi click vào 1 hàng
  const getChiTietNhomHangMucXe = useCallback(
    async (data: TableNhomHangMucXeColumnDataType) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ma: data.ma, 
          nv: data.nv, 
          actionCode: ACTION_CODE.CHI_TIET_NHOM_HANG_MUC_XE,
        });
        
        // XỬ LÝ RESPONSE 
        const responseData = response.data as any;
        if (responseData?.lke && Array.isArray(responseData.lke) && responseData.lke.length > 0) {
          return responseData.lke[0] as CommonExecute.Execute.INhomHangMucXe;
        }
        
        return {} as CommonExecute.Execute.INhomHangMucXe;
      } catch (error) {
        console.log("getChiTietNhomHangMucXe err", error);
        message.error("Không thể tải chi tiết nhóm hạng mục xe");
        return {} as CommonExecute.Execute.INhomHangMucXe;
      }
    },
    [mutateUseCommonExecute],
  );

  /**
   * TÌM KIẾM VÀ LOAD DANH SÁCH NHÓM HẠNG MỤC XE THEO FILTER PARAMETERS 
   * Hàm này được gọi khi filter params thay đổi hoặc cần refresh data
   */
  const getListNhomHangMucXe = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        ...filterParams, 
        actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_NHOM_HANG_MUC_XE, 
      });
      
      // XỬ LÝ RESPONSE
      const responseData = response.data;
      console.log("[NhomHangMucXe] Raw response:", JSON.stringify(responseData, null, 2));
      
      if (responseData?.data && Array.isArray(responseData.data)) {
        setListNhomHangMucXe(responseData.data); // Cập nhật danh sách
        setTongSoDong(responseData.tong_so_dong || responseData.data.length); 
      }
    } catch (error) {
      console.log("getListNhomHangMucXe error ", error);
      message.error("Không thể tải danh sách nhóm hạng mục xe");
    }
  }, [mutateUseCommonExecute, filterParams]);

  // ===== Tự tải lại KHI FILTER PARAMS THAY ĐỔI =====
  useEffect(() => {
    getListNhomHangMucXe(); // Tự động gọi API khi filter params thay đổi
  }, [filterParams]);

  /**
   * @param data - Dữ liệu form từ modal
   * @returns Response từ API
   */
  //Tạo-sửa
  const capNhatChiTietNhomHangMucXe = useCallback(
    async (data: ReactQuery.ICapNhatNhomHangMucXeParams) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...data, 
          actionCode: ACTION_CODE.UPDATE_NHOM_HANG_MUC_XE, 
        });
        
        console.log("[NhomHangMucXe - capNhatChiTietNhomHangMucXe] Raw response:", JSON.stringify(response, null, 2));
        
        // Hiển thị thông báo thành công
        message.success(data.ma ? "Cập nhật nhóm hạng mục xe thành công" : "Thêm mới nhóm hạng mục xe thành công");
        return response.data;
      } catch (error) {
        console.log("capNhatChiTietNhomHangMucXe err", error);
        message.error(data.ma ? "Có lỗi xảy ra khi cập nhật nhóm hạng mục xe" : "Có lỗi xảy ra khi thêm mới nhóm hạng mục xe");
      }
    },
    [mutateUseCommonExecute],
  );

  /**
   INIT FUNCTION - KHỞI TẠO DỮ LIỆU BAN ĐẦU KHI COMPONENT MOUNT
   */
  const initData = () => {
  };

  // ===== CONTEXT VALUE - TẠO CONTEXT VALUE VỚI MEMOIZATION ĐỂ TỐI ƯU PERFORMANCE =====
  const value = useMemo<INhomHangMucXeContextProps>(
    () => ({
      // State values
      listNhomHangMucXe,
      tongSoDong,
      loading: mutateUseCommonExecute.isLoading, // Loading state từ API hook
      filterParams,
      // API functions
      getListNhomHangMucXe,
      getChiTietNhomHangMucXe,
      capNhatChiTietNhomHangMucXe,
      // State setters
      setFilterParams,
    }),
    [listNhomHangMucXe, tongSoDong, mutateUseCommonExecute, filterParams, getListNhomHangMucXe, getChiTietNhomHangMucXe, capNhatChiTietNhomHangMucXe],
  );

  return <NhomHangMucXeContext.Provider value={value}>{children}</NhomHangMucXeContext.Provider>;
};

export {NhomHangMucXeProvider};
export default NhomHangMucXeProvider;

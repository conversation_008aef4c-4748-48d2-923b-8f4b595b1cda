import {createContext, useContext} from "react";

import {IQuanLyDanhMucChauLucContextProps} from "./index.model";

//khởi tạo giá trị mặc định
export const QuanLyDanhMucChauLucContext = createContext<IQuanLyDanhMucChauLucContextProps>({
  listDanhMucChauLuc: [],
  tongSoDong: 0,
  loading: false,
  filterParams: {},
  getListDanhMucChauLuc: async () => Promise.resolve(),
  getChiTietDanhMucChauLuc: async () => Promise.resolve({} as CommonExecute.Execute.IDanhMucChauLuc),
  capNhatChiTietDanhMucChauLuc: async () => Promise.resolve(),
  setFilterParams: () => {},
});

export const useQuanLyDanhMucChauLucContext = () => useContext(QuanLyDanhMucChauLucContext);

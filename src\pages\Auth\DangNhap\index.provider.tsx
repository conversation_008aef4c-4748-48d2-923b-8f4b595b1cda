import {Authen} from "@src/@types/Authentication";
import {ACTION_CODE, AUTHEN_KEYS} from "@src/constants";
import {useProfile} from "@src/hooks";
import {useMenuNguoiDung} from "@src/hooks/menuNguoiDungStore";
import {useDangNhap} from "@src/services/react-queries";
import {CommonExecuteResponse, useCommonExecute} from "@src/services/react-queries";
import {navigateTo} from "@src/utils";
import {message} from "antd";
import Cookies from "js-cookie";
import {KJUR} from "jsrsasign";
import React, {PropsWithChildren, useCallback, useMemo} from "react";
import {DangNhapContext} from "./index.context";
import {ILoginContextProps} from "./index.model";

/* 
  file Provider : Chứa logic quản lý state, gọi API, wrap context
*/
const DangNhapProvider: React.FC<PropsWithChildren> = props => {
  const {children} = props;
  const {setProfile, setPw} = useProfile(); // để update profile sau khi login thành công
  const {setMenuNguoiDung} = useMenuNguoiDung();
  const mutateUseCommonExecute = useCommonExecute();

  const mutateDangNhap = useDangNhap();

  const onSubmit = useCallback(
    async (values: Authen.Login.IFormFieldsValue) => {
      try {
        // LOGIN
        //mã hoá password
        const md = new KJUR.crypto.MessageDigest({alg: "sha256", prov: "cryptojs"});
        md.updateString(values.tai_khoan + values.mat_khau);
        const passSHA256: string = md.digest();
        const response: {data: Authen.Profile.IMetaData; output: any} = await mutateDangNhap.mutateAsync({tai_khoan: values.tai_khoan, mat_khau: passSHA256, actionCode: ACTION_CODE.DANG_NHAP});
        message.success("Đăng nhập thành công");
        setProfile(response.data);
        setPw(passSHA256);
        Cookies.set(AUTHEN_KEYS.PW, passSHA256);
        // LẤY MENU
        const responseMenu: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync({
          nhom: "CLIENT",
          actionCode: ACTION_CODE.LAY_DANH_SACH_MENU_NGUOI_DUNG_THEO_NHOM,
        });
        console.log("responseMenu", responseMenu);
        setMenuNguoiDung(responseMenu.data);
        navigateTo("/");
      } catch (error) {
        message.error("Đăng nhập không thành công");
      }
    },
    [setProfile, mutateDangNhap, mutateUseCommonExecute, setMenuNguoiDung, setPw],
  );

  //khai báo giá trị của context để truyền vào Component
  const value = useMemo<ILoginContextProps>(
    () => ({
      onSubmit,
      isSuccess: mutateDangNhap.isSuccess,
      isError: mutateDangNhap.isError,
      error: mutateDangNhap.error,
      isLoading: mutateDangNhap.isLoading,
    }),
    [onSubmit, mutateDangNhap],
  );

  return <DangNhapContext.Provider value={value}>{children}</DangNhapContext.Provider>;
};

export default DangNhapProvider;

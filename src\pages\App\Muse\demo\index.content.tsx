// import React, {memo, useEffect, useState} from "react";
// import {VirtualList} from "@src/components";
// import {isEqual} from "lodash";
// import {useGetDogList} from "@src/services/react-queries";

// const fakeDataUrl = "https://randomuser.me/api/?results=20&inc=name,gender,email,nat,picture&noinfo";

// interface UserItem {
//   email: string;
//   gender: string;
//   name: {
//     first: string;
//     last: string;
//     title: string;
//   };
//   nat: string;
//   picture: {
//     large: string;
//     medium: string;
//     thumbnail: string;
//   };
// }

// const DemoContent: React.FC = memo(() => {
//   const {data, loading} = useGetDogList();
//   console.log("data", data);
//   // const [data, setData] = useState<UserItem[]>([]);
//   const renderItem = (item: UserItem, index: number) => {
//     return <p key={index}>{item.name.first}</p>;
//   };

//   const appendData = () => {
//     fetch(fakeDataUrl)
//       .then(res => res.json())
//       .then(body => {
//         // setData(data.concat(body.results));
//       });
//   };

//   const onScroll = () => {
//     console.log("REACHED END");
//     // appendData();
//   };

//   useEffect(() => {
//     // appendData();
//   }, []);

//   return <div className="bg-green-10">{/* <VirtualList<UserItem> itemKey={"email"} data={data} renderItem={renderItem} height={400} itemHeight={47} onScroll={onScroll} /> */}</div>;
// }, isEqual);

// DemoContent.displayName = "DemoContent";

// export default DemoContent;

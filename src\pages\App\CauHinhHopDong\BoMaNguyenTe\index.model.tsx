import {ReactQuery} from "@src/@types";

//khai báo interface props Context của login
export interface BoMaNguyenteProps {
  listDoiTac: Array<CommonExecute.Execute.IDoiTac>;
  danhSachBoMaNguyenTe: Array<CommonExecute.Execute.IDanhSachBoMaNguyenTe>;
  loading: boolean;
  layDanhSachBoMaNguyenTe: (Params: ReactQuery.ITimKiemPhanTrangBoMaNguyenTeParams) => void;
  tongSoDong: number;
  layChiTietBoMaNguyenTe: (Params: ReactQuery.IlayChiTietBoMaNguyenTeParams) => Promise<CommonExecute.Execute.IChiTietBoMaNguyenTe | null>;
  defaultFormValue: object;
  updateBoMaNguyenTe: (Params: ReactQuery.IUpdateBoMaNguyenTeParams) => void;
  getListDoiTac: () => Promise<void>;
}

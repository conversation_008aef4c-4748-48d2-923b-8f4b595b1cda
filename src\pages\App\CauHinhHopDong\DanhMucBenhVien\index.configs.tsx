import React from "react";
import {TableColumnsType, TableProps} from "antd";

import {IFormInput, ReactQuery} from "@src/@types";
import {colWidthByKey, defaultTableColumnsProps, ruleInputMessage, validationRules} from "@src/hooks";

//Kiểm tra value có phải là empty hay không (null, undefined, chuỗi rỗng)
const isEmptyValue = (value: any): boolean => {
  return value === null || value === undefined || value === '' || (typeof value === 'string' && value.trim() === '');
};

//===== FORM TÌM KIẾM =====
export interface IFormTimKiemBenhVienFieldsConfig {
  nd_tim: IFormInput;
  nhom_bv: IFormInput;
  tinh_thanh: IFormInput;
  tk_ngan_hang: IFormInput;
  tk_chi_nhanh: IFormInput;
  trang_thai: IFormInput;
}

//C<PERSON><PERSON> hình form tìm kiếm bệnh viện ở header table
export const FormTimKiemDanhMucBenhVien: IFormTimKiemBenhVienFieldsConfig = {
  nd_tim: {
    component: "input",
    name: "nd_tim",
    label: "Từ khóa",
    placeholder: "Nhập mã/tên bệnh viện",
    className: "!mb-0",
  },
  nhom_bv: {
    component: "select",
    name: "nhom_bv",
    label: "Nhóm bệnh viện",
    placeholder: "Chọn nhóm bệnh viện",
    className: "!mb-0",
  },
  tinh_thanh: {
    component: "select",
    name: "tinh_thanh",
    label: "Tỉnh thành",
    placeholder: "Chọn tỉnh thành",
    className: "!mb-0",
  },
  tk_ngan_hang: {
    component: "select",
    name: "tk_ngan_hang",
    label: "Tài khoản ngân hàng",
    placeholder: "Chọn ngân hàng",
    className: "!mb-0",
  },
  tk_chi_nhanh: {
    component: "select",
    name: "tk_chi_nhanh",
    label: "Tài khoản chi nhánh",
    placeholder: "Chọn chi nhánh ngân hàng",
    className: "!mb-0",
  },
  trang_thai: {
    component: "select",
    name: "trang_thai",
    label: "Trạng thái",
    placeholder: "Chọn trạng thái",
    className: "!mb-0",
  },
};

//===== FORM TẠO MỚI/CHỈNH SỬA =====
export interface IFormTaoMoiBenhVienFieldsConfig {
  ma: IFormInput;
  ten: IFormInput;
  mst: IFormInput;
  dia_chi: IFormInput;
  dthoai: IFormInput;
  email: IFormInput;
  nhom_bv: IFormInput;
  loai: IFormInput;
  tinh_thanh: IFormInput;
  // quan_huyen: IFormInput;
  ad_bhyt: IFormInput;
  bl_nt: IFormInput;
  bl_gt: IFormInput;
  bl_ra: IFormInput;
  tk_ngan_hang: IFormInput;
  tk_chi_nhanh: IFormInput;
  tk_so: IFormInput;
  tk_ten: IFormInput;
  nguoi_lhe: IFormInput;
  dthoai_lhe: IFormInput;
  email_lhe: IFormInput;
  stt: IFormInput;
  trang_thai: IFormInput;
}

//Cấu hình form tạo mới/chỉnh sửa bệnh viện trong modal
export const FormTaoMoiBenhVien: IFormTaoMoiBenhVienFieldsConfig = {
  ma: {
    component: "input",
    label: "Mã bệnh viện",
    name: "ma",
    placeholder: "Nhập mã bệnh viện",
   // rules: [ruleInputMessage.required],
  },
  ten: {
    component: "input",
    label: "Tên bệnh viện",
    name: "ten",
    placeholder: "Nhập tên bệnh viện",
    rules: [ruleInputMessage.required],
  },
  mst: {
    component: "input",
    label: "Mã số thuế",
    name: "mst",
    placeholder: "Nhập mã số thuế",
  },
  dia_chi: {
    component: "input",
    label: "Địa chỉ",
    name: "dia_chi",
    placeholder: "Nhập địa chỉ",
  },
  dthoai: {
    component: "input",
    label: "Điện thoại",
    name: "dthoai",
    placeholder: "Nhập số điện thoại",
  },
  email: {
    component: "input",
    label: "Email",
    name: "email",
    placeholder: "Nhập email",
  },
  nhom_bv: {
    component: "select",
    label: "Nhóm bệnh viện",
    name: "nhom_bv",
    placeholder: "Chọn nhóm bệnh viện",
  },
  loai: {
    component: "select",
    label: "Loại bệnh viện",
    name: "loai",
    placeholder: "Chọn loại bệnh viện",
  },
  tinh_thanh: {
    component: "select",
    label: "Tỉnh thành",
    name: "tinh_thanh",
    placeholder: "Chọn tỉnh thành",
    rules: [ruleInputMessage.required],
  },
  // quan_huyen: {
  //   component: "select",
  //   label: "Quận huyện",
  //   name: "quan_huyen",
  //   placeholder: "Chọn quận huyện",
  // },
  ad_bhyt: {
    component: "select",
    label: "Áp dụng BHYT",
    name: "ad_bhyt",
    placeholder: "Chọn áp dụng BHYT",
  },
  bl_nt: {
    component: "select",
    label: "Bội lệ ngoại trú",
    name: "bl_nt",
    placeholder: "Chọn bội lệ ngoại trú",
  },
  bl_gt: {
    component: "select",
    label: "Bội lệ giường",
    name: "bl_gt",
    placeholder: "Chọn bội lệ giường",
  },
  bl_ra: {
    component: "select",
    label: "Bội lệ ra viện",
    name: "bl_ra",
    placeholder: "Chọn bội lệ ra viện",
  },
  tk_ngan_hang: {
    component: "input",
    label: "Ngân hàng",
    name: "tk_ngan_hang",
    placeholder: "Chọn ngân hàng",
    // rules: [ruleInputMessage.required],
  },
  tk_chi_nhanh: {
    component: "input",
    label: "Chi nhánh",
    name: "tk_chi_nhanh",
    placeholder: "Chọn chi nhánh",
    // rules: [ruleInputMessage.required],
  },
  tk_so: {
    component: "input",
    label: "Số tài khoản",
    name: "tk_so",
    placeholder: "Nhập số tài khoản",
  },
  tk_ten: {
    component: "input",
    label: "Tên tài khoản",
    name: "tk_ten",
    placeholder: "Nhập tên tài khoản",
  },
  nguoi_lhe: {
    component: "input",
    label: "Người liên hệ",
    name: "nguoi_lhe",
    placeholder: "Nhập tên người liên hệ",
  },
  dthoai_lhe: {
    component: "input",
    label: "ĐT liên hệ",
    name: "dthoai_lhe",
    placeholder: "Nhập số điện thoại liên hệ",
  },
  email_lhe: {
    component: "input",
    label: "Email liên hệ",
    name: "email_lhe",
    placeholder: "Nhập email liên hệ",
  },
  stt: {
    component: "input",
    label: "Số thứ tự",
    name: "stt",
    placeholder: "Nhập số thứ tự",
    rules: [ruleInputMessage.required],
  },
  trang_thai: {
    component: "select",
    label: "Trạng thái",
    name: "trang_thai",
    placeholder: "Chọn trạng thái",
    rules: [ruleInputMessage.required],
  },
};

//===== TABLE COLUMNS =====
export interface TableBenhVienColumnDataType {
  key?: string;
  sott?: number;
  ma?: string;
  ten?: string;
  mst?: string;
  dia_chi?: string;
  dthoai?: string;
  email?: string;
  nhom_bv?: string;
  ten_nhom_bv?: string;
  loai?: string;
  ten_loai?: string;
  tinh_thanh?: string;
  // quan_huyen?: string;
  ad_bhyt?: string;
  bl_nt?: string;
  bl_gt?: string;
  bl_ra?: string;
  tk_ngan_hang?: string;
  tk_chi_nhanh?: string;
  tk_so?: string;
  tk_ten?: string;
  nguoi_lhe?: string;
  dthoai_lhe?: string;
  email_lhe?: string;
  stt?: number;
  trang_thai?: string;
  trang_thai_ten?: string;
  ngay_tao?: string;
  nguoi_tao?: string;
  ngay_cap_nhat?: string;
  nguoi_cap_nhat?: string;
}

export type TableBenhVienColumnDataIndex = keyof TableBenhVienColumnDataType;

export const tableBenhVienColumn: TableColumnsType<TableBenhVienColumnDataType> = [
  {
    ...defaultTableColumnsProps,
    title: "STT",
    dataIndex: "sott",
    key: "sott",
    width: colWidthByKey.sott,
    align: "center",
  },
  {
    ...defaultTableColumnsProps,
    title: "Mã BV",
    dataIndex: "ma",
    key: "ma",
    width: 150,
  },
  {
    ...defaultTableColumnsProps,
    title: "Tên bệnh viện",
    dataIndex: "ten",
    key: "ten",
    width: 200,
  },
  {
    ...defaultTableColumnsProps,
    title: "MST",
    dataIndex: "mst",
    key: "mst",
    width: 120,
  },
  {
    ...defaultTableColumnsProps,
    title: "Địa chỉ",
    dataIndex: "dia_chi",
    key: "dia_chi",
    width: 250,
  },
  {
    ...defaultTableColumnsProps,
    title: "Điện thoại",
    dataIndex: "dthoai",
    key: "dthoai",
    width: 120,
  },
  {
    ...defaultTableColumnsProps,
    title: "Email",
    dataIndex: "email",
    key: "email",
    width: 120,
  },
  {
    ...defaultTableColumnsProps,
    title: "Nhóm BV",
    dataIndex: "ten_nhom_bv",
    key: "ten_nhom_bv",
    width: 120,
  },
  {
    ...defaultTableColumnsProps,
    title: "Loại BV",
    dataIndex: "ten_loai",
    key: "ten_loai",
    width: 120,
  },
  {
    ...defaultTableColumnsProps,
    title: "Tỉnh thành",
    dataIndex: "ten_tinh_thanh",
    key: "tinh_thanh",
    width: 120,
  },
  {
    ...defaultTableColumnsProps,
    title: "Ngày tạo",
    dataIndex: "ngay_tao",
    key: "ngay_tao",
    width: colWidthByKey.ngay_tao,
  },
  {
    ...defaultTableColumnsProps,
    title: "Người tạo",
    dataIndex: "nguoi_tao",
    key: "nguoi_tao",
    width: colWidthByKey.nguoi_tao,
  },
  {
    title: "Ngày cập nhật",
    dataIndex: "ngay_cap_nhat",
    key: "ngay_cap_nhat",
    width: colWidthByKey.ngay_cap_nhat,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Người cập nhật",
    dataIndex: "nguoi_cap_nhat",
    key: "nguoi_cap_nhat",
    width: colWidthByKey.nguoi_cap_nhat,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    ...defaultTableColumnsProps,
    title: "Trạng thái",
    dataIndex: "trang_thai_ten",
    key: "trang_thai_ten",
    width: 120,
  }
];

export const radioItemTrangThaiBenhVienSelect = [
  {ma: "", ten: "Tất cả"},
  {ma: "D", ten: "Đang sử dụng"},
  {ma: "K", ten: "Ngừng sử dụng"},
];

export const radioItemTrangThaiBenhVienTable = [
  {ma: "D", ten: "Đang sử dụng"},
  {ma: "K", ten: "Ngừng sử dụng"},
];

// NHOM_BV: C- BV cong, T - BV tu (comment trong DB: BV - Benh vien, NT - Nha thuoc)
export const radioItemNhomBenhVienSelect = [
  {ma: "", ten: "Tất cả"},
  {ma: "BV", ten: "Bệnh viện"},
  {ma: "NT", ten: "Nhà thuốc"},
];

export const radioItemLoaiBenhVienSelect = [
  {ma: "", ten: "Tất cả"},
  {ma: "C", ten: "BV/NT công"},
  {ma: "T", ten: "BV/NT tư"},
];

// AD_BHYT: C- Co ap dung BHYT, K - Khong ap dung BHYT
export const radioItemApDungBHYTSelect = [
  {ma: "", ten: "Chọn"},
  {ma: "C", ten: "Có áp dụng BHYT"},
  {ma: "K", ten: "Không áp dụng BHYT"},
];

// BL_NT, BL_GT, BL_RA: C hoặc K
export const radioItemBoiLeSelect = [
  {ma: "", ten: "Chọn"},
  {ma: "C", ten: "Có"},
  {ma: "K", ten: "Không"},
];

import {ReactQuery} from "@src/@types";

//khai báo interface props context của login
export interface DanhMucHangXeProps {
  listDoiTac: Array<CommonExecute.Execute.IDoiTac>;
  danhSachDanhMucHangXe: Array<CommonExecute.Execute.IDanhMucHangXe>;
  loading: boolean;
  layDanhSachDanhMucHangXe: (Params: ReactQuery.ITimKiemPhanTrangDanhMucHangXeParams) => void;
  tongSoDong: number;
  layChiTietDanhMucHangXe: (Params: ReactQuery.ILayChiTietDanhMucHangXeParams) => Promise<CommonExecute.Execute.IChiTietDanhMucHangXe | null>;
  defaultFormValue: object;
  onUpdateDanhMucHangXe: (Params: ReactQuery.IUpdateDanhMuchangXeParams) => Promise<number | null | undefined>;
  getListDoiTac: () => Promise<void>;
}

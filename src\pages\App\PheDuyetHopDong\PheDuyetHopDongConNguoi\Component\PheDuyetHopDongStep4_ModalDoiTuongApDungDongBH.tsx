import {CheckOutlined, CloseOutlined, SearchOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {Button, FormInput, HeaderModal, InputCellTable, Popcomfirm} from "@src/components";
import {defaultPaginationTableProps, fillRowTableEmpty} from "@src/hooks";
import {Checkbox, Col, Flex, Form, Modal, Row, Table, TableColumnType} from "antd";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState} from "react";
import {defaultParamsTimKiemPhanTrangNguoiDuocBaoHiem, FormTimKiemNguoiDuocBaoHiem, tableDoiTuongApDungDongBHColumn, TableDoiTuongApDungColumnDataType, IModalDoiTuongApDungRef} from "./Constant";
import {useHopDongConNguoiContext} from "@src/pages/App/BaoHiemConNguoi/HopDongConNguoi/index.context";

const PAGE_SIZE = 5; // khai báo khác default cho vừa màn hình
const {nd_tim, dong_tai} = FormTimKiemNguoiDuocBaoHiem;

const ModalDoiTuongApDungDongComponent = forwardRef<IModalDoiTuongApDungRef, {disabled?: boolean}>(({disabled = false}, ref) => {
  useImperativeHandle(ref, () => ({
    open: (donViDong?: CommonExecute.Execute.IDongBaoHiem, listDoiTuongDaDuocApDungDongBH?: []) => {
      setIsOpen(true);
      if (donViDong) setChiTietDonViDong(donViDong);
      setSelectedItems(listDoiTuongDaDuocApDungDongBH || []);
      // // Gọi API tìm kiếm với dữ liệu mặc định
      const so_id = chiTietHopDong?.so_id;
      if (so_id) {
        // const defaultParams = {
        //   ...defaultFormValueTimKiemDoiTuongBaoHiemXe,
        //   so_id,
        //   trang: 1,
        //   ma_dvi_dong_tai: donViDong?.ma_dvi_dong,
        //   dong_tai: "",
        //   so_dong: PAGE_SIZE,
        // };
        // timKiemPhanTrangNguoiDuocBaoHiem(defaultParams);
        setTimKiemPhanTrangNguoiDuocBaoHiemParams({...defaultParamsTimKiemPhanTrangNguoiDuocBaoHiem, so_id});
      }
    },
    close: () => setIsOpen(false),
  }));

  const {loading, tongSoDongNguoiDuocBaoHiem, listNguoiDuocBaoHiem, chiTietHopDong, timKiemPhanTrangNguoiDuocBaoHiemParams, setTimKiemPhanTrangNguoiDuocBaoHiemParams, updateDoiTuongApDungTyLeDongBH} =
    useHopDongConNguoiContext();

  const [formTimKiemPhanTrangDoiTuongBaoHiemConNguoi] = Form.useForm();
  const [disableSubmit, setDisableSubmit] = useState<boolean>(true);
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [page, setPage] = useState(1);
  const [dataTableListDoiTuongBaoHiemConNguoi, setDataTableListDoiTuongBaoHiemConNguoi] = useState<Array<TableDoiTuongApDungColumnDataType>>([]);
  const [checkboxTyleChecked, setCheckboxTyleChecked] = useState<boolean>(true);
  const [selectedItems, setSelectedItems] = useState<Array<TableDoiTuongApDungColumnDataType>>([]);
  const [chiTietDonViDong, setChiTietDonViDong] = useState<CommonExecute.Execute.IDongBaoHiem | null>(null);

  //sử dụng ref để state mới cập nhật sử dụng được luôn (checkboxTyleChecked, selectedItems)
  const checkboxTyleCheckedRef = useRef(checkboxTyleChecked);
  const selectedItemsRef = useRef(selectedItems);

  useEffect(() => {
    checkboxTyleCheckedRef.current = checkboxTyleChecked;
  }, [checkboxTyleChecked]);

  useEffect(() => {
    selectedItemsRef.current = selectedItems;
  }, [selectedItems]);

  //Init data khi mở modal
  const initDataTable = useMemo(() => {
    try {
      //match mảng đối tượng đã được áp dụng với mảng data phân trang
      const selectedSoIdDtMap = new Map(selectedItems.map(item => [String(item.so_id_dt), item])); // Đưa về kiểu String để so sánh dữ liệu API vì API trả về string so_id_dt
      const tableData = listNguoiDuocBaoHiem.map((item: any, index: number) => {
        const selectedItem = selectedSoIdDtMap.get(String(item.so_id_dt));
        return {
          ...item,
          ten: item.ten + " / " + item.so_cmt + " / " + item.dthoai,
          key: index.toString(),
          ap_dung: selectedItem ? "C" : "K",
          tl_dong: selectedItem ? selectedItem.tl_dong : item.tl_dong,
        };
      });
      const arrEmptyRow: Array<TableDoiTuongApDungColumnDataType> = fillRowTableEmpty(tableData.length, PAGE_SIZE);
      setDataTableListDoiTuongBaoHiemConNguoi([...tableData, ...arrEmptyRow]);
    } catch (error) {
      setDataTableListDoiTuongBaoHiemConNguoi([]);
      setSelectedItems([]);
    }
  }, [listNguoiDuocBaoHiem, selectedItems]);

  //xử lý validate form
  useEffect(() => {
    if (selectedItems.length <= 0) {
      setDisableSubmit(true);
      return;
    } else setDisableSubmit(false);
  }, [selectedItems]);

  // Hàm xử lý thay đổi giá trị input của bảng
  const handleTableInputChange = (index: number, dataIndex: string, value: any) => {
    if (dataIndex === "tl_dong") {
      const numericValue = value.replace(/[^0-9]/g, "");
      if (numericValue.length > 3) return;
      const numValue = parseInt(numericValue) || 0;
      const finalValue = numValue > 100 ? "100" : numericValue;

      setDataTableListDoiTuongBaoHiemConNguoi(prev => {
        const newData = [...prev];
        // Sử dụng ref để lấy giá trị mới nhất
        if (checkboxTyleCheckedRef.current && selectedItemsRef.current.length > 0) {
          // Cập nhật tất cả các item được chọn
          selectedItemsRef.current.forEach(selectedItem => {
            const itemIndex = newData.findIndex(item => item.key === selectedItem.key);
            if (itemIndex !== -1) {
              newData[itemIndex] = {
                ...newData[itemIndex],
                [dataIndex]: finalValue,
              } as TableDoiTuongApDungColumnDataType;
            }
          });

          // Cập nhật selectedItems state để đồng bộ
          const updatedSelectedItems = selectedItemsRef.current.map(item => ({
            ...item,
            [dataIndex]: finalValue,
          }));
          setSelectedItems(updatedSelectedItems);
        } else {
          // Cập nhật chỉ item tại index được truyền vào
          if (index >= 0 && index < newData.length) {
            newData[index] = {
              ...newData[index],
              [dataIndex]: finalValue,
            } as TableDoiTuongApDungColumnDataType;

            // Cập nhật selectedItems nếu item này đang được chọn
            const currentItem = newData[index];
            // Chỉ cập nhật item trong selectedItems nếu nó đã được chọn (ap_dung === "C")
            if (currentItem.ap_dung === "C") {
              setSelectedItems(prevSelectedItems => prevSelectedItems.map(item => (item.key === currentItem.key ? {...item, [dataIndex]: finalValue} : item)));
            }
          }
        }
        return newData;
      });
    }
  };

  // Handle change checkbox Áp dụng
  const handleCheckboxChange = (key: string, checked: boolean) => {
    const item = dataTableListDoiTuongBaoHiemConNguoi.find(i => i.key === key);
    if (!item) return;
    const updatedItem = {...item, ap_dung: checked ? "C" : "K"};

    setSelectedItems(prev => {
      const index = prev.findIndex(i => String(i.so_id_dt) === String(updatedItem.so_id_dt));
      if (checked) {
        // Nếu check thì thêm/cập nhật vào mảng
        if (index !== -1) {
          // Cập nhật
          const newArr = [...prev];
          newArr[index] = {...newArr[index], ap_dung: "C"};
          return newArr;
        } else {
          // Thêm mới
          return [...prev, updatedItem];
        }
      } else {
        // Nếu uncheck thì loại bỏ khỏi mảng
        if (index !== -1) {
          const newArr = [...prev];
          newArr.splice(index, 1);
          return newArr;
        }
        return prev;
      }
    });
  };

  //Bấm Update
  const onPressLuuDoiTuongApDung = async () => {
    try {
      const so_id = chiTietHopDong?.so_id;
      const ma_dvi_dong = chiTietDonViDong?.ma_dvi_dong;
      const gcn =
        selectedItems.length > 0
          ? selectedItems.map(item => ({
              so_id_dt: Number(item.so_id_dt || 0),
              tl_dong: Number(item.tl_dong || 0),
            }))
          : [];

      if (!so_id || !ma_dvi_dong) return;
      const params = {so_id, ma_dvi_dong, gcn};
      const response = await updateDoiTuongApDungTyLeDongBH(params);
      if (response) {
        onCloseModal();
        return;
      }
    } catch (error) {
      console.log("onConfirm", error);
    }
  };

  // Gộp hàm xử lý tìm kiếm và chuyển trang
  const handleSearchAndPagination = useCallback(
    (values: ReactQuery.ITimKiemPhanTrangNguoiDuocBaoHiemHopDongConNguoi) => {
      const so_id = chiTietHopDong?.so_id;
      // Process search with form values
      const params = {
        ...timKiemPhanTrangNguoiDuocBaoHiemParams,
        ...values,
        so_id,
        trang: 1, // Reset to first page when searching
        so_dong: PAGE_SIZE,
      };
      setTimKiemPhanTrangNguoiDuocBaoHiemParams(params);
      setPage(1);
    },
    [chiTietHopDong, timKiemPhanTrangNguoiDuocBaoHiemParams],
  );

  //bấm huỷ / để sau xoá dữ liệu của modal khi nào mở thì gọi lại
  const onCloseModal = () => {
    setIsOpen(false);
    // Reset form tìm kiếm
    formTimKiemPhanTrangDoiTuongBaoHiemConNguoi.resetFields();
    // Reset các state
    setSelectedItems([]);
    setCheckboxTyleChecked(true);
    setPage(1);
    setChiTietDonViDong(null);
    setDataTableListDoiTuongBaoHiemConNguoi([]);
  };

  // Handler change checkbox tỷ lệ
  const handleCheckboxTyleChange = (checked: boolean) => {
    setCheckboxTyleChecked(checked);
  };

  //RENDER
  const renderFormInputColum = (props?: any, span = 10) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );

  // Clone columns và thay title cột 'Tỷ lệ' thành Checkbox
  const columnsWithCheckbox = useMemo(() => {
    return (tableDoiTuongApDungDongBHColumn || []).map((col: any) => {
      if ("dataIndex" in col && col.dataIndex === "tl_dong") {
        return {
          ...col,
          title: (
            <span className="flex w-full items-center justify-center gap-1">
              <Checkbox checked={checkboxTyleChecked} onChange={e => handleCheckboxTyleChange(e.target.checked)} />
              Tỷ lệ
            </span>
          ),
        };
      }
      return col;
    });
  }, [checkboxTyleChecked]);

  //render cột
  const renderColumn = (column: TableColumnType<TableDoiTuongApDungColumnDataType>) => {
    if (!("dataIndex" in column)) return column;

    //nhập tỷ lệ
    if (["tl_dong"].includes(column.dataIndex as string)) {
      return {
        ...column,
        render: (value: any, record: TableDoiTuongApDungColumnDataType, index: number) =>
          record.key && record.key.toString().startsWith("empty") ? null : (
            <InputCellTable
              component="input"
              key={`${record.key}-${column.dataIndex}`}
              value={value || ""}
              index={index}
              dataIndex={column.dataIndex as string}
              onChange={handleTableInputChange}
              disabled={record.ap_dung !== "C"}
              maxLength={3}
            />
          ),
      };
    }
    //check box
    if (column.dataIndex === "ap_dung") {
      return {
        ...column,
        render: (_: any, record: TableDoiTuongApDungColumnDataType) =>
          record.key && record.key.toString().startsWith("empty") ? null : (
            <FormInput className="!mb-0" component="checkbox" checked={record.ap_dung === "C"} onChange={e => handleCheckboxChange(record.key, e.target.checked)} />
          ),
      };
    }

    return column;
  };

  //render header table đối tượng
  const renderHeaderTableDoiTuongBaoHiemConNguoi = () => {
    return (
      <Form form={formTimKiemPhanTrangDoiTuongBaoHiemConNguoi} layout="vertical" className="mt-5 [&_.ant-form-item]:mb-2" onFinish={handleSearchAndPagination}>
        <Row gutter={16}>
          {renderFormInputColum({...nd_tim})}
          {renderFormInputColum({...dong_tai}, 10)}
          <Col span={4}>
            <Form.Item>
              <Button type="primary" htmlType="submit" loading={loading} icon={<SearchOutlined />} className="w-full">
                Tìm kiếm
              </Button>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    );
  };

  //renderFooter Modal
  const renderFooter = () => {
    return (
      <Form.Item className="mb-0">
        <Button type="default" htmlType="reset" form="formNhapKyThanhToan" onClick={onCloseModal} className="mr-2" icon={<CloseOutlined />}>
          Đóng
        </Button>
        <Popcomfirm
          title="Thông báo"
          onConfirm={onPressLuuDoiTuongApDung}
          htmlType="submit"
          okText="Lưu"
          description="Bạn có chắc chắn muốn lưu thông tin đối tượng áp dụng không?"
          buttonTitle={"Lưu"}
          buttonDisable={disableSubmit || disabled}
          buttonIcon={<CheckOutlined />}
          loading={loading}
        />
      </Form.Item>
    );
  };
  //Render
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        className="modal-doi-tuong-ap-dung-dong-bh"
        title={<HeaderModal title={"Đối tượng áp dụng"} />}
        // centered
        open={isOpen}
        onOk={() => setIsOpen(false)}
        onCancel={onCloseModal}
        width={"45vw"}
        maskClosable={false}
        footer={renderFooter}>
        {renderHeaderTableDoiTuongBaoHiemConNguoi()}
        <Table<TableDoiTuongApDungColumnDataType>
          className="table-doi-tuong-ap-dung-dong-bh hide-scrollbar"
          columns={columnsWithCheckbox.map(renderColumn)}
          dataSource={dataTableListDoiTuongBaoHiemConNguoi as TableDoiTuongApDungColumnDataType[]}
          scroll={{y: 300}}
          bordered
          title={() => <></>}
          pagination={{
            ...defaultPaginationTableProps,
            total: tongSoDongNguoiDuocBaoHiem,
            size: "small",
            pageSize: PAGE_SIZE,
            current: page, //set current page
            onChange: (newPage, newPageSize) => {
              setPage(newPage);
              if (newPageSize && newPageSize !== PAGE_SIZE) {
                setPage(1); // Reset về trang 1 khi thay đổi pageSize
              }
              setTimKiemPhanTrangNguoiDuocBaoHiemParams({
                ...timKiemPhanTrangNguoiDuocBaoHiemParams,
                so_id: chiTietHopDong?.so_id,
                trang: newPageSize && newPageSize !== PAGE_SIZE ? 1 : newPage,
                so_dong: newPageSize || PAGE_SIZE,
              });
            },
            locale: {
              jump_to: "Tới trang",
              page: "",
            },
          }}
        />
      </Modal>
    </Flex>
  );
});
ModalDoiTuongApDungDongComponent.displayName = "ModalDoiTuongApDungDongComponent";
export const ModalDoiTuongApDungDongBH = memo(ModalDoiTuongApDungDongComponent, isEqual);

import {IFormInput} from "@src/@types";
import {defaultTableColumnsProps, ruleInputMessage} from "@src/hooks";
import {TableProps} from "antd";

//ĐỊNH NGHĨA CẤU HÌNH TABLE ĐỐI TÁC GỒM NHỮNG CỘT NÀO, THỨ TỰ CỘT SẼ HIỂN THỊ THEO THỨ TỰ KHAI BÁO Ở DƯỚI
export interface TableChucDanhColumnDataType {
  key: string;
  sott?: number;
  ten?: string; 
  ngay_tao?: string; 
  nguoi_tao?: string; 
  ngay_cap_nhat?: string; 
  nguoi_cap_nhat?: string; 
  doi_tac_ql_ten_tat?: string;
  trang_thai_ten?: string;
}

// ĐỊNH NGHĨA Column RENDER RA TỪNG Ô TRONG CỘT
//đọc https://ant.design/components/table#column để biết thêm về column
export const tableChucDanhColumn: TableProps<TableChucDanhColumnDataType>["columns"] = [
  {
    title: "STT",
    dataIndex: "sott",
    key: "sott",
    align: "center",
    width: 60,
    ...defaultTableColumnsProps,
  },
  {
    title: "Mã",
    dataIndex: "ma",
    key: "ma",
    align: "center",
    width: 80,
    ...defaultTableColumnsProps,
  },
  {
    title: "Tên",
    dataIndex: "ten",
    key: "ten",
    width: 200,
    ...defaultTableColumnsProps,
  },
  {
    title: "Đối tác quản lý",
    dataIndex: "doi_tac_ql_ten_tat",
    key: "doi_tac_ql_ten_tat",
    width: 150,
    ...defaultTableColumnsProps,
  },
{
    title: "Ngày tạo",
    dataIndex: "ngay_tao",
    key: "ngay_tao",
    width: 120,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Người tạo",
    dataIndex: "nguoi_tao",
    key: "nguoi_tao",
    width: 110,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Ngày cập nhật",
    dataIndex: "ngay_cap_nhat",
    key: "ngay_cap_nhat",
    width: 120,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Người cập nhật",
    dataIndex: "nguoi_cap_nhat",
    key: "nguoi_cap_nhat",
    width: 120,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Trạng thái",
    dataIndex: "trang_thai_ten",
    key: "trang_thai_ten",
    width: 130,
    align: "center",
    ...defaultTableColumnsProps,
  },
];

//keyof: return ra key của inteface TableChucDanhColumnDataType;
export type TableChucDanhColumnDataIndex = keyof TableChucDanhColumnDataType;

//radio trong table
export const radioItemTrangThaiChucDanhTable = [
  {value: "Đang sử dụng", text: "Đang sử dụng"},
  {value: "Ngưng sử dụng", text: "Ngưng sử dụng"},
];

//form tìm kiếm
//radio trong search
export const radioItemTrangThaiChucDanhSelect: Array<{ma: string; ten: string}> = [
  {ma: "D", ten: "Đang sử dụng"},
  {ma: "K", ten: "Ngưng sử dụng"},
];
export interface IFormTimKiemChucDanhFieldsConfig {
  ten: IFormInput;
  ma_doi_tac_ql: IFormInput;
  trang_thai: IFormInput;
}
export const FormTimKiemQuanLyChucDanh: IFormTimKiemChucDanhFieldsConfig = {
  ten: {
    component: "input",
    name: "ten",
    label: "Tên chức danh",
    placeholder: "Nhập tên chức danh",
    className: "!mb-0",
  },
  ma_doi_tac_ql: {component: "select", name: "ma_doi_tac_ql", label: "Đối tác quản lý", placeholder: "Chọn đối tác quản lý", className: "!mb-0"},
  trang_thai: {component: "select", name: "trang_thai", label: "Trạng thái", className: "!mb-0", placeholder: "Chọn trạng thái"},
};

//form update / create
export interface IFormTaoMoiChucDanhFieldsConfig {
  ma: IFormInput;
  ten: IFormInput;
  trang_thai: IFormInput;
  stt: IFormInput;
  ma_doi_tac_ql: IFormInput;
}

export const FormTaoMoiChucDanh: IFormTaoMoiChucDanhFieldsConfig = {
  stt: {
    component: "input",
    label: "Số thứ tự",
    name: "stt",
    placeholder: "Nhập số thứ tự",
    rules: [ruleInputMessage.required],
  },
  ma: {
    component: "input",
    label: "Mã",
    name: "ma",
    placeholder: "Mã đơn vị / chi nhánh",
    rules: [ruleInputMessage.required],
  },
  ten: {
    component: "input",
    label: "Tên",
    name: "ten",
    placeholder: "Tên đơn vị / chi nhánh",
    rules: [ruleInputMessage.required],
  },
  trang_thai: {
    component: "select",
    label: "Trạng thái",
    name: "trang_thai",
    placeholder: "Chọn trạng thái",
    rules: [ruleInputMessage.required],
  },
  ma_doi_tac_ql: {
    component: "select",
    label: "Đối tác",
    name: "ma_doi_tac_ql",
    placeholder: "Chọn đối tác",
    rules: [ruleInputMessage.required],
  },
};

export const TRANG_THAI_TAO_MOI_CHUC_DANH = [
  {ten: "Đang sử dụng", ma: "D"},
  {ten: "Ngừng sử dụng", ma: "K"},
];

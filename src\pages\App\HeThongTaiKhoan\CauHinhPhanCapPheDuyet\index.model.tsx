import {ReactQuery} from "@src/@types";

// khai báo interface props Context của Login
export interface CauHinhPhanCapPheDuyetContextProps {
  danhSachTaiKhoanNguoiDung: Array<CommonExecute.Execute.IDanhSachTaiKhoanNguoiDungPhanTrang>;
  loading: boolean;
  tongSoDong: number;
  listDoiTac: Array<CommonExecute.Execute.IDoiTac>;
  listPhanCapNhom: Array<CommonExecute.Execute.ICauHinhPhanCapNhom>;
  listSanPham: Array<CommonExecute.Execute.IDanhMucSanPham>;
  defaultFormValue: object;
  listChiNhanh: Array<CommonExecute.Execute.IChiNhanh>;
  danhSachCauHinhPhanCapPheDuyet: Array<CommonExecute.Execute.ICauHinhPhanCapPheDuyet>;
  danhSachCauHinhPhanCapPheDuyetCT: Array<CommonExecute.Execute.ICauHinhPhanCapPheDuyetCT>;
  onUpdateCauHinhPhanCapPheDuyet: (item: ReactQuery.IUpdateCauHinhPhanCapPheDuyetParams) => void;
  onDeleteCauHinhPhanCapPheDuyet: (item: ReactQuery.IUpdateCauHinhPhanCapPheDuyetParams) => void;
  onDeleteCauHinhPhanCapPheDuyetCT: (item: ReactQuery.IDeleteCauHinhPhanCapPheDuyetCTParams) => void;
  onUpdateCauHinhPhanCapPheDuyetCT: (item: ReactQuery.IUpdateCauHinhPhanCapPheDuyetCTParams) => void;
  setDanhSachCauHinhPhanCapPheDuyetCT: (data: Array<CommonExecute.Execute.ICauHinhPhanCapPheDuyetCT>) => void;
  layDanhSachCauHinhPhanCapPheDuyetCT: (params: ReactQuery.ILietKeCauHinhPhanCapPheDuyetCTParams) => void;
  setDanhSachCauHinhPhanCapPheDuyet: (data: Array<CommonExecute.Execute.ICauHinhPhanCapPheDuyet>) => void;
  layDanhSachCauHinhPhanCapPheDuyet: (params: ReactQuery.ILietKeCauHinhPhanCapPheDuyetParams) => void;
  getListChiNhanhTheoDoiTac: () => void;
  getListSanPhamTheoDoiTac: (params: ReactQuery.ILietKeSanPhamParams) => void;
  layDanhSachTaiKhoanNguoiDungPhanTrang: (params: ReactQuery.ILayDanhSachTaiKhoanNguoiDungPhanTrangParams) => void;
  getListDoiTac: () => Promise<void>;
  layChiTietTaiKhoanNguoiDung: (params: ReactQuery.IChiTietTaiKhoanNguoiDungParams) => Promise<CommonExecute.Execute.IChiTietNguoiSuDung | null>;
  layChiTietCauHinhPhanCapPheDuyetCT: (params: ReactQuery.IChiTietCauHinhPhanCapPheDuyetCTParams) => Promise<CommonExecute.Execute.ICauHinhPhanCapPheDuyetCT | null>;
}

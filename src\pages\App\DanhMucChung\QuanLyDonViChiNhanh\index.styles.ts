import {createStyles} from "antd-style";

/* 
  MỘT CÁCH KHÁC ĐỂ STYLE MÀ KHÔNG CẦN TẠO THÊM CLASS GỌI LÀ CSS-in-JS  với thư viện antd-style kết hợp cùng React.
  <PERSON><PERSON> thể hơ<PERSON>, đây là phương pháp tạo và quản lý styles trong JavaScript thay vì viết CSS riêng biệt trong file .css hoặc .scss.
Phương pháp này sử dụng:
createStyles: Một hàm từ antd-style giúp tạo styles với cú pháp CSS-in-JS. Nó cho phép bạn viết styles giống như bạn viết CSS thông thường,
 nhưng trong JavaScript hoặc TypeScript, đồng thời có thể sử dụng các giá trị từ token như màu sắc, k<PERSON><PERSON> thướ<PERSON>, và các đặc tính thiết kế khác mà Ant Design cung cấp.
css: <PERSON><PERSON> một hàm từ createStyles (cung cấp từ antd-style) để bạn viết các đoạn mã CSS.
 Đoạn CSS này sẽ được biến thành các className mà bạn có thể áp dụng trực tiếp vào các thành phần trong React.

 Ưu điểm của CSS-in-JS:
  Tính động: Bạn có thể sử dụng các biến, tham số từ JavaScript hoặc React state vào trong styles.
  Tính mô-đun: Mỗi style chỉ tồn tại trong phạm vi của component, giúp giảm thiểu sự xung đột về className.
  Tích hợp tốt với React: Việc thay đổi styles dựa trên state hoặc props trở nên rất dễ dàng.
  
  Hạn chế:
  Hiệu suất: Nếu không tối ưu, việc sử dụng CSS-in-JS có thể gặp vấn đề về hiệu suất, vì các styles sẽ được tính toán lại mỗi khi component render lại.
  Khó duy trì: Đối với những dự án lớn, việc quản lý tất cả các styles trong JavaScript có thể trở nên phức tạp.
*/
const antTableCls = ".ant-table"; //class name prefix
export const useTableStyle = createStyles(({css, token}) => {
  return {
    customTable: css`
      ${antTableCls} {
        ${antTableCls}-container {
          ${antTableCls}-header {
            ${antTableCls}-cell {
              background-color: #96bf49;
              color: #fff;
              font-weight: bold;
              text-align: center;
            }
          }
        }
      }
    `,
  };
});

import {CommonExecuteResponse, useCommonExecute} from "@src/services/react-queries";
import {PropsWithChildren, useCallback, useEffect, useMemo, useState} from "react";
import {defaultFormValue} from "./index.configs";
import {ReactQuery} from "@src/@types";
import {ACTION_CODE} from "@src/constants";
import {message} from "antd";
import {DanhMucHieuXeProps} from "./index.model";
import {DanhMucHieuXeContext} from "./index.context";

const DanhMucHieuXeProvider: React.FC<PropsWithChildren> = props => {
  const {children} = props;
  const mutateUseCommonExecute = useCommonExecute();
  const [loading, setLoading] = useState<boolean>(false);
  const [danhSachDanhMucHieuXe, setDanhSachDanhMucHieuXe] = useState<Array<CommonExecute.Execute.IDanhMucHieuXe>>([]);
  const [tongSoDong, setTongSoDong] = useState<number>(0);
  const [listHangXe, setListHangXe] = useState<Array<CommonExecute.Execute.IDoiTac>>([]);

  useEffect(() => {
    initData();
  }, []);
  const initData = () => {
    getListHangXe();
    layDanhSachDanhMucHieuXe(defaultFormValue);
  };

  /* ĐỐI TÁC */
  const getListHangXe = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        actionCode: ACTION_CODE.LIET_KE_HANG_XE,
      });

      setListHangXe(response?.data?.map(item => ({...item, ten: item.ma + " - " + item.ten})));
    } catch (error) {}
  }, [mutateUseCommonExecute]);
  //TÌM KIẾM PHÂN TRANG DANH MỤC HIỆU XE
  const layDanhSachDanhMucHieuXe = useCallback(
    async (body: ReactQuery.ITimKiemPhanTrangDanhMucHieuXeParams & ReactQuery.IPhanTrang) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_DANH_MUC_HIEU_XE,
        };

        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);

        const data = response.data.data;
        setDanhSachDanhMucHieuXe(data);
        setTongSoDong(response.data.tong_so_dong);
      } catch (error: any) {
        console.log("layDanhSachTimKiemPhanTrangDanhMucHieuXe error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );
  //LẤY CHI TIẾT DANH MỤC HIỆU XE
  const layChiTietDanhMucHieuXe = useCallback(
    async (item: ReactQuery.ILayChiTietDanhMucHieuXeParams): Promise<CommonExecute.Execute.IChiTietDanhMucHieuXe | null> => {
      try {
        const params = {
          ...item,
          actionCode: ACTION_CODE.CHI_TIET_DANH_MUC_HIEU_XE,
        };

        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);

        // Type cast để truy cập property lke
        const dataArray = (responseData.data as any).lke;
        const data = Array.isArray(dataArray) && dataArray.length > 0 ? dataArray[0] : null;

        return data as CommonExecute.Execute.IChiTietDanhMucHieuXe;
      } catch (error: any) {
        console.log("laychitietchucnang error", error.message || error);
        return null;
      }
    },
    [mutateUseCommonExecute],
  );
  // UPDATE DANH MỤC HIỆU XE
  const onUpdateDanhMucHieuXe = useCallback(async (body: ReactQuery.IUpdateDanhMucHieuXeParams) => {
    try {
      setLoading(true);
      if (!body.hang_xe) {
        message.error(" Hãng xe là bắt buộc!");
        return null;
      }
      if (!body.ma) {
        message.error("Mã hiệu xe là bắt buộc!");
        return null;
      }
      if (!body.nv) {
        message.error("Nghiệp vụ là bắt buộc!");
        return null;
      }
      if (!body.stt) {
        message.error("Thứ tự hiển thị là bắt buộc!");
        return null;
      }
      if (!body.ten) {
        message.error("Tên hiệu xe là bắt buộc!");
        return null;
      }
      if (!body.trang_thai) {
        message.error("Trạng thái là bắt buộc!");
        return null;
      }
      const params = {
        ...body,
        actionCode: ACTION_CODE.UPDATE_DANH_MUC_HIEU_XE,
      };

      const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
      if (responseData.data === -1) {
        // Xác định đây là thêm mới hay chỉnh sửa
        const isEdit = params.ma && params.ma.trim() !== "";
        message.success(`${isEdit ? "Cập nhật" : "Thêm mới"} hiệu xe thành công`);
        initData();
        // Reload lại danh sách để hiển thị data mới nhất
        layDanhSachDanhMucHieuXe(defaultFormValue);

        //chuyển đổi responData thành number
        return responseData.data as unknown as number;
      }

      return responseData.data;
    } catch (error: any) {
      console.log("updateDanhMucHieuXe", error.message || error);
      return null;
    }
  }, []);
  const value = useMemo<DanhMucHieuXeProps>(
    () => ({
      listHangXe,

      getListHangXe,

      danhSachDanhMucHieuXe,
      loading: mutateUseCommonExecute.isLoading,
      tongSoDong,
      layDanhSachDanhMucHieuXe,
      layChiTietDanhMucHieuXe,
      defaultFormValue,
      onUpdateDanhMucHieuXe,
    }),
    [listHangXe, danhSachDanhMucHieuXe, mutateUseCommonExecute, tongSoDong, getListHangXe, layDanhSachDanhMucHieuXe, onUpdateDanhMucHieuXe],
  );
  return <DanhMucHieuXeContext.Provider value={value}>{children}</DanhMucHieuXeContext.Provider>;
};

export default DanhMucHieuXeProvider;

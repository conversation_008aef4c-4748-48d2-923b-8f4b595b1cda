import {createContext, useContext} from "react";

import {IQuanLyDoiTacContextProps} from "./index.model";

//khởi tạo giá trị mặc định
export const QuanLyDoiTacContext = createContext<IQuanLyDoiTacContextProps>({
  listDoiTac: [],
  tongSoDong: 0,
  loading: false,
  filterParams: {},
  getListDoiTac: async () => Promise.resolve(),
  getChiTietDoiTac: async () => Promise.resolve({} as CommonExecute.Execute.IDoiTac),
  capNhatChiTietDoiTac: async () => Promise.resolve(),
  setFilterParams: () => {},
});

export const useQuanLyDoiTacContext = () => useContext(QuanLyDoiTacContext);

import {ClearOutlined, PlusCircleOutlined, SearchOutlined} from "@ant-design/icons";
import {Button, FormInput, Highlighter} from "@src/components";
import {COLOR_PALETTE, ID_PAGE} from "@src/constants";
import {Col, Form, Input, InputRef, Row, Table, TableColumnType, Tag, Tooltip} from "antd";
import {FilterDropdownProps} from "antd/es/table/interface";
import {useCallback, useMemo, useRef, useState} from "react";

import {defaultPaginationTableProps, defaultTableProps, fillRowTableEmpty} from "@src/hooks";
import {DanhMucHieuXeColumns, FormTimKiemDanhMucHieuXe, optionTrangThaiHopDongSelect, radioItemTrangThaiHopDongTable, TableDanhMucHieuXeDataIndex, TableDanhMucHieuXeDataType} from "./index.configs";
import {useChiTietDanhMucHieuXeContext} from "./index.context";
import "./index.default.scss";
import {IFormInput, ReactQuery} from "@src/@types";
import {IModalChiTietDanhMuchieuXeRef, ModalChiTietDanhMucHieuXe} from "./Component/ModalChiTietDanhMucHieuXe";
import {NGHIEP_VU_HIEU_XE} from "./Component/index.configs";

type DataIndex = keyof TableDanhMucHieuXeDataType;
defaultPaginationTableProps.defaultPageSize = 13;
const DanhMucHieuXeContent: React.FC = () => {
  const {listHangXe} = useChiTietDanhMucHieuXeContext();

  const {danhSachDanhMucHieuXe, loading, layDanhSachDanhMucHieuXe, tongSoDong, layChiTietDanhMucHieuXe, defaultFormValue} = useChiTietDanhMucHieuXeContext();

  const [searchText, setSearchText] = useState("");
  const [searchedColumn, setSearchedColumn] = useState("");
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(defaultPaginationTableProps.defaultPageSize);

  const searchInput = useRef<InputRef>(null);
  const [searchParams, setSearchParams] = useState<ReactQuery.ILayChiTietDanhMucHieuXeParams>(defaultFormValue);
  const refModalChiTietDanhMucHieuXe = useRef<IModalChiTietDanhMuchieuXeRef>(null);

  const [searchTextTable, setSearchTextTable] = useState<string>(""); //lưu textsearch để hiển thị vào cell

  const refSearchInputTable = useRef<InputRef>(null);

  const {ma, trang_thai, ten, hang_xe, nv} = FormTimKiemDanhMucHieuXe;

  // ĐỔ DỮ LIỆU CHO BẢNG
  const dataTableListDanhMucHieuXe = useMemo<Array<TableDanhMucHieuXeDataType>>(() => {
    try {
      const tableData = danhSachDanhMucHieuXe.map((item: any, index: number) => {
        return {
          key: index.toString(),
          ma: item.ma,
          ten: item.ten,
          sott: item.sott,
          trang_thai: item.trang_thai,
          ngay_tao: item.ngay_tao,
          nguoi_tao: item.nguoi_tao,
          ngay_cap_nhat: item.ngay_cap_nhat,
          nguoi_cap_nhat: item.nguoi_cap_nhat,
          trang: item.trang,
          so_dong: item.so_dong,
          trang_thai_ten: item.trang_thai_ten,
          hang_xe: item.hang_xe,
          nv: item.nv,
        };
      });
      const arrEmptyRow: Array<TableDanhMucHieuXeDataType> = fillRowTableEmpty(tableData.length, defaultPaginationTableProps.defaultPageSize);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      return [];
    }
  }, [danhSachDanhMucHieuXe]);
  // RENDER
  //tạo cột tìm kiếm
  const getColumnSearchProps = (dataIndex: TableDanhMucHieuXeDataIndex, title: string): TableColumnType<TableDanhMucHieuXeDataType> => ({
    filterDropdown:
      dataIndex !== "trang_thai_ten"
        ? ({
            setSelectedKeys,
            selectedKeys,
            confirm,
            clearFilters,
            //  close
          }) => {
            return (
              <div onKeyDown={e => e.stopPropagation()} className="flex flex-row items-center p-2">
                <Input
                  ref={refSearchInputTable}
                  placeholder={`Nhập ${title}`}
                  value={selectedKeys[0]}
                  onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                  onPressEnter={() => handleSearch(selectedKeys as string[], confirm, dataIndex)}
                  className="mr-2 flex p-2"
                />
                <Tooltip title="Tìm kiếm">
                  <Button type="primary" shape="circle" icon={<SearchOutlined />} onClick={() => handleSearch(selectedKeys as string[], confirm, dataIndex)} block />
                </Tooltip>
                <Tooltip title="Xoá">
                  <Button type="primary" shape="circle" icon={<ClearOutlined />} onClick={() => clearFilters && handleReset(clearFilters, confirm, dataIndex)} block />
                </Tooltip>
              </div>
            );
          }
        : undefined,
    filterIcon: (filtered: boolean) => {
      return <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} size={40} />;
    },

    onFilter: (value, record) => {
      return (
        record[dataIndex]
          ?.toString()
          .toLowerCase()
          .includes((value as string).toLowerCase()) || false
      );
    },
    //filterDropdownProps: thuộc tính tuỳ chỉnh hành vi dropdown
    filterDropdownProps: {
      // onOpenChange: callback khi dropdown mở/ tắt thì sẽ focus vào input
      onOpenChange(open) {
        if (open) {
          setTimeout(() => refSearchInputTable.current?.select(), 100);
        }
      },
    },

    filterSearch: true,
    filters: dataIndex === "trang_thai_ten" ? radioItemTrangThaiHopDongTable : undefined,
    render: (
      text,
      record,
      // index
    ) => {
      if (dataIndex === "trang_thai_ten") {
        // Xác định màu dựa vào text (trạng thái hiển thị)
        const color = text === "Đang sử dụng" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];
        if (record.key.toString().includes("empty")) return "";
        return (
          <Tag color={color} className="text-[11px]">
            {text}
          </Tag>
        );
      }
      return searchedColumn === dataIndex ? (
        <Highlighter searchWords={[searchText]} textToHighlight={text ? text.toString() : ""} />
      ) : text !== undefined ? (
        text
      ) : (
        <Tag color={"transparent"} className="!text-white text-[11px]">
          {"\u00A0"}
        </Tag>
      );
    },
  });

  const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: DataIndex) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);

  const handleReset = useCallback(
    (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: DataIndex) => {
      clearFilters();
      setSearchedColumn("");
      handleSearch([""], confirm, dataIndex);
    },
    [handleSearch],
  );
  // bấm tìm kiếm
  const onSearchApi = (values: ReactQuery.ITimKiemPhanTrangDanhMucHieuXeParams & ReactQuery.IPhanTrang) => {
    const cleanedValues = {
      ...values,
      ma: values.ma ?? "",
      trang_thai: values.trang_thai ?? "",
      trang: values.trang ?? 1,
      so_dong: values.so_dong ?? pageSize,
      ten: values.ten ?? "",
      hang_xe: values.hang_xe ?? "",
      nv: values.nv ?? "",
    };
    // setSearchParams(cleanedValues);
    setPage(1); //reset về trang 1 khi search mới
    layDanhSachDanhMucHieuXe({...cleanedValues, trang: 1, so_dong: pageSize});
  };
  // Gọi api khi chuyển trang
  const onChangePage = useCallback(
    (page: number, pageSize: number) => {
      setPage(page);
      setPageSize(pageSize);
      layDanhSachDanhMucHieuXe({...searchParams, trang: page, so_dong: pageSize});
    },
    [searchParams],
  );
  // RENDER
  const renderFormInputColum = (props: IFormInput, span = 4) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  // render header table
  const renderHeaderTableDanhMucHieuXe = () => {
    return (
      <div>
        <Form initialValues={{}} layout="vertical" className="[&_.ant-form-item]:!mb-0" onFinish={onSearchApi}>
          <Row gutter={16} align={"bottom"}>
            {renderFormInputColum({...ma})}
            {renderFormInputColum({
              ...ten,
            })}
            {renderFormInputColum({...nv, options: NGHIEP_VU_HIEU_XE})}
            {renderFormInputColum({...hang_xe, options: listHangXe})}
            {renderFormInputColum({...trang_thai, options: optionTrangThaiHopDongSelect})}
            <Col span={2}>
              <Button type="primary" htmlType="submit" loading={loading} icon={<SearchOutlined />} block>
                Tìm kiếm
              </Button>
            </Col>
            <Col span={2}>
              <Button type="primary" icon={<PlusCircleOutlined />} onClick={() => refModalChiTietDanhMucHieuXe.current?.open()} loading={loading} block>
                Tạo mới
              </Button>
            </Col>
          </Row>
        </Form>
      </div>
    );
  };
  // render table
  return (
    <div id={ID_PAGE.DANH_MUC_HIEU_XE} className="[&_.ant-space]:w-full">
      <Table<TableDanhMucHieuXeDataType>
        {...defaultTableProps}
        loading={loading}
        onRow={(record, rowIndex) => {
          return {
            style: {cursor: loading ? "progress" : "pointer"}, //  thể hiện vị trí con trỏ là button(👈)
            onClick: async event => {
              if (record.key.toString().includes("empty")) return;
              const chiTietDanhMucHieuXe = await layChiTietDanhMucHieuXe({ma: record.ma});
              console.log("xxxxxxxx", chiTietDanhMucHieuXe);

              if (chiTietDanhMucHieuXe) {
                refModalChiTietDanhMucHieuXe.current?.open(chiTietDanhMucHieuXe);
              }

              //  response = await layChiTietDanhMucHieuXe(record as ReactQuery.ILayChiTietDanhMucHieuXeParams);
            },
          };
        }}
        //tim kiem trong colums
        columns={(DanhMucHieuXeColumns || []).map(item => {
          // Đảm bảo item.key được định nghĩa, item.title là chuỗi và item.key không phải là "stt" trước khi thêm ô tìm kiếm
          return {
            ...item,
            ...(item.key && typeof item.title === "string" && item.key !== "stt" ? getColumnSearchProps(item.key as keyof TableDanhMucHieuXeDataType, item.title) : {}),
          };
        })} //định nghĩa của cột table
        dataSource={dataTableListDanhMucHieuXe}
        title={renderHeaderTableDanhMucHieuXe}
        pagination={{
          ...defaultPaginationTableProps,
          total: tongSoDong,
          onChange: (page, pageSize) => {
            onChangePage(page, pageSize);
          },
        }}
        bordered
      />
      <ModalChiTietDanhMucHieuXe ref={refModalChiTietDanhMucHieuXe} listHangXe={listHangXe} />
    </div>
  );
};
export default DanhMucHieuXeContent;

import {But<PERSON>} from "@src/components";
import {useRef} from "react";
import {ModalPheDuyetHopDongConNguoi, IModalPheDuyetHopDongRef} from "./Component";
import HopDongConNguoiProvider from "@src/pages/App/BaoHiemConNguoi/HopDongConNguoi/index.provider";
import PheDuyetHopDongConNguoiProvider from "./index.provider";

const PheDuyetHopDongConNguoiDemo = () => {
  const refModal = useRef<IModalPheDuyetHopDongRef>(null);

  const handleOpenModal = () => {
    // Mock data để test modal
    const mockData = {
      so_id: 123,
      so_hd: "HD001",
      ten_kh: "Nguyễn Văn A",
      ma_doi_tac_ql: "DT001",
      ten_doi_tac_ql: "Đối tác ABC",
      ngay_hl: "2024-01-01",
      ngay_kt: "2024-12-31",
      ten_sp: "<PERSON>ảo hiểm sức khỏe",
      tong_phi: 1000000,
      vip: "VIP",
    };

    refModal.current?.open(mockData as any);
  };

  return (
    <div style={{padding: 20}}>
      <h2>Demo Modal Phê duyệt Hợp đồng con người</h2>
      <Button type="primary" onClick={handleOpenModal}>
        Mở Modal Phê duyệt
      </Button>

      <HopDongConNguoiProvider>
        <PheDuyetHopDongConNguoiProvider>
          <ModalPheDuyetHopDongConNguoi ref={refModal} />
        </PheDuyetHopDongConNguoiProvider>
      </HopDongConNguoiProvider>
    </div>
  );
};

export default PheDuyetHopDongConNguoiDemo;

import React, {PropsWithChildren, useMemo} from "react";

import {NotFoundPageContext} from "./index.context";
import {INotFoundPageContextProps} from "./index.model";

const NotFoundPageProvider: React.FC<PropsWithChildren> = (props) => {
  const {children} = props;

  const value = useMemo<INotFoundPageContextProps>(() => ({}), []);

  return <NotFoundPageContext.Provider value={value}>{children}</NotFoundPageContext.Provider>;
};

export default NotFoundPageProvider;

import {Button} from "@src/components";
import {Col, Divider, Row, ColorPicker} from "antd";
import {isEqual} from "lodash";
import {memo} from "react";

const TestScreen = () => {
  console.log("TestScreen");
  return (
    <>
      {/* LƯỚI CƠ BẢN */}
      <Row>
        <Col span={24}>
          <Button block>col</Button>
        </Col>
      </Row>
      <Row>
        <Col span={12}>
          <Button block>col-12</Button>
        </Col>
        <Col span={12}>
          <Button block>col-12</Button>
        </Col>
      </Row>

      <Row>
        <Col span={8}>
          <Button block>col-8</Button>
        </Col>
        <Col span={8}>
          <Button block>col-8</Button>
        </Col>
        <Col span={8}>
          <Button block>col-8</Button>
        </Col>
      </Row>
      <Divider />

      {/* Grid Gutter */}
      <Row gutter={{xs: 8, sm: 16, md: 24, lg: 32}}>
        <Col className="gutter-row" span={6}>
          <div style={style}>col-6</div>
        </Col>
        <Col className="gutter-row" span={6}>
          <div style={style}>col-6</div>
        </Col>
        <Col className="gutter-row" span={6}>
          <div style={style}>col-6</div>
        </Col>
        <Col className="gutter-row" span={6}>
          <div style={style}>col-6</div>
        </Col>
      </Row>
      <Divider />
      <Row gutter={[16, {xs: 8, sm: 16, md: 24, lg: 32}]}>
        <Col className="gutter-row" span={6}>
          <div style={style}>col-6</div>
        </Col>
        <Col className="gutter-row" span={6}>
          <div style={style}>col-6</div>
        </Col>
        <Col className="gutter-row" span={6}>
          <div style={style}>col-6</div>
        </Col>
        <Col className="gutter-row" span={6}>
          <div style={style}>col-6</div>
        </Col>
        <Col className="gutter-row" span={6}>
          <div style={style}>col-6</div>
        </Col>
        <Col className="gutter-row" span={6}>
          <div style={style}>col-6</div>
        </Col>
        <Col className="gutter-row" span={6}>
          <div style={style}>col-6</div>
        </Col>
        <Col className="gutter-row" span={6}>
          <div style={style}>col-6</div>
        </Col>
      </Row>
      <ColorPicker defaultValue="#1677ff" />
    </>
  );
};

const style: React.CSSProperties = {background: "#0092ff", padding: "8px 0"};

const Test = memo(TestScreen, isEqual);
export default Test;

import {ReactQuery} from "@src/@types";
import {colWidthByKey, defaultTableColumnsProps} from "@src/hooks";
import {TableProps, TabsProps} from "antd";

/* MODAL CẤU HÌNH MÃ BỆNH */
export const PAGE_SIZE = 10; // Số dòng mỗi trang

export interface IModalCauHinhMaBenhRef {
  open: (soId?: number, soIdDt?: number) => void;
  close: () => void;
}

export interface ModalCauHinhMaBenhProps {}

/* CONSTANTS CHO LOẠI ÁP DỤNG */
export const LOAI_AP_DUNG = {
  WHITELIST: "WL" as const,
  BLACKLIST: "BL" as const,
};

/* INTERFACES CHO API - HOP DONG CON NGUOI */
export interface ITimKiemMaBenhParams extends ReactQuery.IPhanTrang {
  so_id: number; // ID hợp đồng
  so_id_dt: number; // ID đối tượng được bảo hiểm
  loai_ad: "WL" | "BL"; // Loại áp dụng: WL = Whitelist, BL = Blacklist
  ten?: string; // Tên mã bệnh để tìm kiếm
  actionCode: string;
}

export interface ILayDanhSachMaBenhDaLuuParams {
  so_id: number; // ID hợp đồng
  so_id_dt: number; // ID đối tượng được bảo hiểm
  loai_ad: "WL" | "BL"; // Loại áp dụng
  actionCode: string;
}

export interface ILuuCauHinhMaBenhParams {
  so_id: number; // ID hợp đồng
  so_id_dt: number; // ID đối tượng được bảo hiểm
  loai_ad: "WL" | "BL"; // Loại áp dụng
  bvien: Array<{
    ma: string; // Mã bệnh
    hinh_thuc_ad: string; // Hình thức áp dụng
  }>;
  actionCode: string;
}

/* INTERFACES CHO DỮ LIỆU MÃ BỆNH */
export interface IMaBenh {
  id?: number;
  ma?: string;
  ten?: string;
  dia_chi?: string;
  dien_thoai?: string;
  email?: string;
  trang_thai?: string;
  trang_thai_ten?: string;
  is_selected?: boolean; // Để track checkbox selection
  hinh_thuc_ap_dung?: string; // Hình thức áp dụng
}

/* TABLE INTERFACES */
export interface TableMaBenhDataType {
  key: string;
  stt?: number;
  id?: number | string;
  ma?: string;
  ten?: string;
  dia_chi?: string;
  dien_thoai?: string;
  email?: string;
  trang_thai?: string;
  trang_thai_ten?: string;
  is_selected?: boolean;
  hinh_thuc_ap_dung?: string;
}

export interface TableMaBenhProps {
  soId?: number;
  soIdDt?: number;
  loaiApDung: "WL" | "BL";
  onDataChange?: (data: TableMaBenhDataType[]) => void;
}

export interface ITableMaBenhWhitelistRef {
  getData: () => TableMaBenhDataType[];
  setData: (data: TableMaBenhDataType[]) => void;
  refreshData: () => void;
  resetSelections: () => void;
}

export interface ITableMaBenhBlacklistRef {
  getData: () => TableMaBenhDataType[];
  setData: (data: TableMaBenhDataType[]) => void;
  refreshData: () => void;
  resetSelections: () => void;
}

/* OPTIONS VÀ CONSTANTS */
export const HINH_THUC_AP_DUNG_OPTIONS = [
  {ten: "Áp dụng toàn bộ", ma: ""},
  {ten: "Áp dụng cho hồ sơ bảo lãnh", ma: "BL"},
  {ten: "Áp dụng cho hồ sơ trực tiếp", ma: "TT"},
];

/* TABLE COLUMNS */
export const maBenhColumns: TableProps<TableMaBenhDataType>["columns"] = [
  {
    ...defaultTableColumnsProps,
    title: "STT",
    dataIndex: "stt",
    key: "stt",
    width: colWidthByKey.sott,
    align: "center",
    render: (_: any, record: TableMaBenhDataType, index: number) => {
      if (record.key.includes("empty")) return "\u00A0";
      return index + 1;
    },
  },
  {
    ...defaultTableColumnsProps,
    title: "Chọn",
    dataIndex: "is_selected",
    key: "is_selected",
    width: 80,
    align: "center",
  },
  {
    ...defaultTableColumnsProps,
    title: "Mã bệnh",
    dataIndex: "ma",
    key: "ma",
    width: 120,
  },
  {
    ...defaultTableColumnsProps,
    title: "Tên mã bệnh",
    dataIndex: "ten",
    key: "ten",
    align: "left",
  },
  {
    ...defaultTableColumnsProps,
    title: "Hình thức áp dụng",
    dataIndex: "hinh_thuc_ap_dung",
    key: "hinh_thuc_ap_dung",
    width: 250,
    align: "center",
  },
];

/* TAB CONFIGURATION */
export const tabsCauHinhMaBenh: TabsProps["items"] = [
  {
    key: "1",
    label: "Mã bệnh white list",
  },
  {
    key: "2",
    label: "Mã bệnh black list",
  },
];

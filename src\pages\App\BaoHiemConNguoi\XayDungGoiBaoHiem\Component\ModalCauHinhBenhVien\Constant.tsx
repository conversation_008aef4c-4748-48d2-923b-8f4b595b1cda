import {ReactQuery} from "@src/@types";
import {colWidthByKey, defaultTableColumnsProps} from "@src/hooks";
import {TableProps, TabsProps} from "antd";

/* MODAL CẤU HÌNH BỆNH VIỆN */

export const PAGE_SIZE = 10; // Số dòng mỗi trang
export interface IModalCauHinhBenhVienRef {
  open: (goiBaoHiemId?: number) => void;
  close: () => void;
}

export interface ModalCauHinhBenhVienProps {}

/* INTERFACES CHO API */
export interface ITimKiemBenhVienParams extends ReactQuery.IPhanTrang {
  id: number; // ID gói bảo hiểm
  loai_ad: "WL" | "BL"; // Loại áp dụng: WL = Whitelist, BL = Blacklist
  ten?: string; // Tên bệnh viện để tìm kiếm
  actionCode: string;
}

export interface ILayDanhSachBenhVienDaLuuParams {
  id: number; // ID gói bảo hiểm
  loai_ad: "WL" | "BL"; // Loại áp dụng
  actionCode: string;
}

/* INTERFACES CHO DỮ LIỆU BỆNH VIỆN */
export interface IBenhVien {
  id?: number;
  ma?: string;
  ten?: string;
  dia_chi?: string;
  dien_thoai?: string;
  email?: string;
  trang_thai?: string;
  trang_thai_ten?: string;
  is_selected?: boolean; // Để track checkbox selection
  hinh_thuc_ap_dung?: string; // Hình thức áp dụng
}

/* TABLE DATA TYPES */
export interface TableBenhVienDataType {
  key: string;
  stt?: number;
  id?: number;
  ma?: string;
  ten?: string;
  dia_chi?: string;
  dien_thoai?: string;
  email?: string;
  trang_thai?: string;
  trang_thai_ten?: string;
  is_selected?: boolean;
  hinh_thuc_ap_dung?: string;
}

/* CONSTANTS CHO HÌNH THỨC ÁP DỤNG */
export const HINH_THUC_AP_DUNG_OPTIONS = [
  {ten: "Áp dụng toàn bộ", ma: ""},
  {ten: "Áp dụng cho hồ sơ bảo lãnh", ma: "BL"},
  {ten: "Áp dụng cho hồ sơ trực tiếp", ma: "TT"},
];

/* CONSTANTS CHO LOẠI ÁP DỤNG */
export const LOAI_AP_DUNG = {
  WHITELIST: "WL" as const,
  BLACKLIST: "BL" as const,
};

/* TAB CONFIGURATION */
export const tabsCauHinhBenhVien: TabsProps["items"] = [
  {
    key: "1",
    label: "Bệnh viện white list",
  },
  {
    key: "2",
    label: "Bệnh viện black list",
  },
];

/* COLUMN DEFINITIONS */
export const benhVienColumns: TableProps<TableBenhVienDataType>["columns"] = [
  {
    ...defaultTableColumnsProps,
    title: "STT",
    dataIndex: "stt",
    key: "stt",
    width: colWidthByKey.sott,
    align: "center",
    render: (_: any, record: TableBenhVienDataType, index: number) => {
      if (record.key.includes("empty")) return "\u00A0";
      return index + 1;
    },
  },
  {
    ...defaultTableColumnsProps,
    title: "Chọn",
    dataIndex: "is_selected",
    key: "is_selected",
    width: 80,
    align: "center",
  },
  {
    ...defaultTableColumnsProps,
    title: "Mã bệnh viện",
    dataIndex: "ma",
    key: "ma",
    width: 160,
    align: "center",
  },
  {
    ...defaultTableColumnsProps,
    title: "Tên bệnh viện",
    dataIndex: "ten",
    key: "ten",
    align: "left",
  },
  {
    ...defaultTableColumnsProps,
    title: "Hình thức áp dụng",
    dataIndex: "hinh_thuc_ap_dung",
    key: "hinh_thuc_ap_dung",
    width: 250,
    align: "center",
  },
];

/* REF INTERFACES CHO TABLE COMPONENTS */
export interface ITableBenhVienWhitelistRef {
  getData: () => TableBenhVienDataType[]; // Trả về tất cả items được chọn từ globalSelections
  setData: (data: TableBenhVienDataType[]) => void;
  refreshData: () => void; // Refresh data sau khi lưu
  resetSelections: () => void; // Reset tất cả selections tạm thời, giữ nguyên data từ API
}

export interface ITableBenhVienBlacklistRef {
  getData: () => TableBenhVienDataType[]; // Trả về tất cả items được chọn từ globalSelections
  setData: (data: TableBenhVienDataType[]) => void;
  refreshData: () => void; // Refresh data sau khi lưu
  resetSelections: () => void; // Reset tất cả selections tạm thời, giữ nguyên data từ API
}

/* PROPS CHO TABLE COMPONENTS */
export interface TableBenhVienProps {
  goiBaoHiemId?: number;
  loaiApDung: "WL" | "BL";
  onDataChange?: (data: TableBenhVienDataType[]) => void;
}

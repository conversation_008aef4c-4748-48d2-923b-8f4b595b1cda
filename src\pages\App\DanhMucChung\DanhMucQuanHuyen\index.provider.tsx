/**
 * T<PERSON><PERSON> dụng: Q<PERSON>ản lý state và logic nghiệp vụ cho module danh mục quận huyện
 */
import React, {PropsWithChildren, useCallback, useEffect, useMemo, useState} from "react";
import {message} from "antd";
import {isEqual} from "lodash";

import {ReactQuery} from "@src/@types";
import {ACTION_CODE} from "@src/constants";
import {useCommonExecute} from "@src/services/react-queries";
import {defaultPaginationTableProps} from "@src/hooks";

import DanhMucQuanHuyenContext from "./index.context";
import {IDanhMucQuanHuyenProvider} from "./index.model";

const DanhMucQuanHuyenProvider: React.FC<PropsWithChildren> = props => {
  const {children} = props;

  const mutateUseCommonExecute = useCommonExecute();
  const [filterParams, setFilterParams] = useState<ReactQuery.ITimKiemPhanTrangDanhMucQuanHuyenParams & ReactQuery.IPhanTrang>({
    ma: "",
    ten: "",
    ma_tinh: "",
    ngay_ad: "",
    trang_thai: "",
    trang: 1,
    so_dong: defaultPaginationTableProps.defaultPageSize,
  });
  const [tongSoDong, setTongSoDong] = useState<number>(0);
  const [listQuanHuyen, setListQuanHuyen] = useState<Array<CommonExecute.Execute.IDanhMucQuanHuyen>>([]);
  const [listTinhThanh, setListTinhThanh] = useState<Array<CommonExecute.Execute.IDanhMucTinhThanh>>([]);

  useEffect(() => {
    initData();
  }, []);

  //Tác dụng: Lấy danh sách tỉnh thành để hiển thị trong dropdown filter
  const getListTinhThanh = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        ma: "",
        ten: "",
        trang_thai: "",
        trang: 1,
        so_dong: 100,
        actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_DANH_MUC_TINH_THANH,
      } as any);
      
      if (response.data) {
        //Xử lý response structure để đảm bảo tương thích
        if (Array.isArray(response.data)) {
          setListTinhThanh(response.data);
        } else if (response.data && typeof response.data === "object") {
        const responseData = response.data as any;
        if (responseData.data && Array.isArray(responseData.data)) {
            setListTinhThanh(responseData.data);
        } else {
            setListTinhThanh([]);
          }
        }
      }
    } catch (error) {
      setListTinhThanh([]);
    }
  }, [mutateUseCommonExecute]);

  //Tác dụng: Lấy thông tin chi tiết của một quận huyện khi click vào row
  const getChiTietQuanHuyen = useCallback(
    async (data: {ma_tinh: string; ma: string; ngay_ad: number}) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ma_tinh: data.ma_tinh,
          ma: data.ma,
          ngay_ad: data.ngay_ad,
          actionCode: ACTION_CODE.CHI_TIET_DANH_MUC_QUAN_HUYEN,
        }as any);
        
        //Response structure confirmed: data.lke[0]
        const responseData = response.data as any;
        
        if (responseData?.lke && Array.isArray(responseData.lke) && responseData.lke.length > 0) {
          return responseData.lke[0] as CommonExecute.Execute.IDanhMucQuanHuyen;
        }
        
        return {} as CommonExecute.Execute.IDanhMucQuanHuyen;
      } catch (error) {
        message.error("Không thể tải chi tiết quận huyện");
        return {} as CommonExecute.Execute.IDanhMucQuanHuyen;
      }
    },
    [mutateUseCommonExecute],
  );

  //Tác dụng: Tìm kiếm và load danh sách quận huyện theo filter parameters
  const searchQuanHuyen = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        ...filterParams,
        actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_DANH_MUC_QUAN_HUYEN,
      }as any);
      
      //Response structure confirmed: data.data[] + data.tong_so_dong
      const responseData = response.data;
      if (responseData?.data && Array.isArray(responseData.data)) {
        setListQuanHuyen(responseData.data);
        setTongSoDong(responseData.tong_so_dong || responseData.data.length);
      }
        } catch (error) {
      message.error("Không thể tải danh sách quận huyện");
    }
  }, [mutateUseCommonExecute, filterParams]);

  useEffect(() => {
    searchQuanHuyen();
  }, [filterParams]);

  //Tác dụng: Tạo mới hoặc cập nhật thông tin quận huyện
  const capNhatChiTietQuanHuyen = useCallback(
    async (data: ReactQuery.ICapNhatDanhMucQuanHuyenParams) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...data,
              actionCode: ACTION_CODE.UPDATE_DANH_MUC_QUAN_HUYEN,
        }as any);
        message.success(data.ma ? "Cập nhật quận huyện thành công" : "Thêm mới quận huyện thành công");
        return response.data;
        } catch (error) {
        message.error(data.ma ? "Có lỗi xảy ra khi cập nhật quận huyện" : "Có lỗi xảy ra khi thêm mới quận huyện");
        return {} as CommonExecute.Execute.IDanhMucQuanHuyen;
      }
    },
    [mutateUseCommonExecute],
  );

  //Tác dụng: Khởi tạo dữ liệu ban đầu khi component mount
  const initData = () => {
    getListTinhThanh();
  };

  const value = useMemo<IDanhMucQuanHuyenProvider>(
    () => ({
      listTinhThanh,
    listQuanHuyen,
    tongSoDong,
      loading: mutateUseCommonExecute.isLoading,
      searchQuanHuyen,
    getChiTietQuanHuyen,
    capNhatChiTietQuanHuyen,
      filterParams,
    setFilterParams,
    }),
    [listQuanHuyen, listTinhThanh, tongSoDong, mutateUseCommonExecute, searchQuanHuyen, getChiTietQuanHuyen, capNhatChiTietQuanHuyen, filterParams],
  );

  return <DanhMucQuanHuyenContext.Provider value={value}>{children}</DanhMucQuanHuyenContext.Provider>;
};

export default DanhMucQuanHuyenProvider;

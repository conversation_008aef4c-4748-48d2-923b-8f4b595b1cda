/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_BASE_URL: string;
  readonly VITE_SECRET_KEY: string;
  readonly VITE_E_PARTNER_CODE: string;
  readonly VITE_E_AUTH_TOKEN_PROD: string;
  readonly VITE_SOCKET_URL: string;
  readonly VITE_PORT: string;
  readonly VITE_IS_PRODUCTION: string;
  readonly VITE_APP_ENV: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}

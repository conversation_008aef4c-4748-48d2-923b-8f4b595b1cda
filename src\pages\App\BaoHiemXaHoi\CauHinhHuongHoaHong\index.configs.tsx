import {IFormInput} from "@src/@types";
import {colWidthByKey, defaultTableColumnsProps, ruleInputMessage} from "@src/hooks";
import {TableProps} from "antd";

//ĐỊNH NGHĨA CẤU HÌNH TABLE ĐỐI TÁC GỒM NHỮNG CỘT NÀO, THỨ TỰ CỘT SẼ HIỂN THỊ THEO THỨ TỰ KHAI BÁO Ở DƯỚI
export interface TableTaiKhoanDonViThuHoColumnDataType {
  key: string;
  sott?: number;
  ma_dvi?: string;
  ma?: string;
  ten?: string;
  mat_khau?: string;
  dthoai?: string;
  email?: string;
  ngay_hl?: number | string;
  ngay_kt?: number | string;
  trang_thai?: string;
  ngay_tao?: string;
  nguoi_tao?: string;
  ngay_cap_nhat?: string;
  nguoi_cap_nhat?: string;
  trang_thai_ten?: string;
}

// ĐỊNH NGHĨA Column RENDER RA TỪNG Ô TRONG CỘT
//đọc https://ant.design/components/table#column để biết thêm về column
export const tableTaiKhoanDonViThuHoColumn: TableProps<TableTaiKhoanDonViThuHoColumnDataType>["columns"] = [
  {
    title: "STT",
    dataIndex: "sott",
    key: "sott",
    align: "center",
    width: colWidthByKey.sott,
    ...defaultTableColumnsProps,
  },
  {
    title: "Mã",
    dataIndex: "ma",
    key: "ma",
    align: "center",
    width: 100,
    ...defaultTableColumnsProps,
  },
  {
    title: "Tên tài khoản đơn vị thu hộ ",
    dataIndex: "ten",
    key: "ten",
    width: 180,
    ...defaultTableColumnsProps,
  },
  {
    title: "Mã đơn vị",
    dataIndex: "ma_dvi",
    key: "ma_dvi",
    width: 100,
    ...defaultTableColumnsProps,
  },
  //   {
  //     title: "Mật khẩu",
  //     dataIndex: "mat_khau",
  //     key: "mat_khau",
  //     width: 120,
  //     ...defaultTableColumnsProps,
  //   },
  {
    title: "Email",
    dataIndex: "email",
    key: "email",
    width: 120,
    ...defaultTableColumnsProps,
  },
  {
    title: "Điện thoại",
    dataIndex: "dthoai",
    key: "dthoai",
    width: 120,
    ...defaultTableColumnsProps,
  },
  {
    title: "Ngày tạo",
    dataIndex: "ngay_tao",
    key: "ngay_tao",
    width: colWidthByKey.ngay_tao,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Người tạo",
    dataIndex: "nguoi_tao",
    key: "nguoi_tao",
    width: colWidthByKey.nguoi_tao,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Ngày cập nhật",
    dataIndex: "ngay_cap_nhat",
    key: "ngay_cap_nhat",
    width: colWidthByKey.ngay_cap_nhat,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Người cập nhật",
    dataIndex: "nguoi_cap_nhat",
    key: "nguoi_cap_nhat",
    width: colWidthByKey.nguoi_cap_nhat,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Trạng thái",
    dataIndex: "trang_thai_ten",
    key: "trang_thai_ten",
    width: 130,
    align: "center",
    ...defaultTableColumnsProps,
  },
];

//keyof: return ra key của inteface TableTaiKhoanDonViThuHoColumnDataType;
export type TableTaiKhoanDonViThuHoColumnDataIndex = keyof TableTaiKhoanDonViThuHoColumnDataType;

//radio trong table
export const radioItemTrangThaiTaiKhoanDonViThuHoTable = [
  {value: "Đang sử dụng", text: "Đang sử dụng"},
  {value: "Ngưng sử dụng", text: "Ngưng sử dụng"},
];

//form tìm kiếm
//radio trong search
export const radioItemTrangThaiTaiKhoanDonViThuHoSelect: Array<{ma: string; ten: string}> = [
  {ma: "D", ten: "Đang sử dụng"},
  {ma: "K", ten: "Ngưng sử dụng"},
];
export interface IFormTimKiemTaiKhoanDonViThuHoFieldsConfig {
  ten: IFormInput;
  ma: IFormInput;
  ma_dvi: IFormInput;
  trang_thai: IFormInput;
}
export const FormTimKiemQuanLyTaiKhoanDonViThuHo: IFormTimKiemTaiKhoanDonViThuHoFieldsConfig = {
  ten: {
    component: "input",
    name: "ten",
    label: "Tên tài khoản đơn vị thu hộ ",
    placeholder: "Nhập tên tài khoản đơn vị thu hộ ",
    className: "!mb-0",
  },
  ma: {
    component: "input",
    name: "ma",
    label: "Mã tài khoản đơn vị thu hộ ",
    placeholder: "Nhập mã tài khoản đơn vị thu hộ ",
    className: "!mb-0",
  },
  ma_dvi: {
    component: "select",
    name: "ma_dvi",
    label: "Mã đơn vị",
    placeholder: "Chọn mã đơn vị",
    className: "!mb-0",
  },
  trang_thai: {
    component: "select",
    name: "trang_thai",
    label: "Trạng thái",
    placeholder: "Chọn trạng thái",
    className: "!mb-0",
  },
};

//form update / create
export interface IFormTaoMoiTaiKhoanDonViThuHoFieldsConfig {
  ma: IFormInput;
  ten: IFormInput;
  ma_dvi: IFormInput;
  mat_khau: IFormInput;
  email: IFormInput;
  dthoai: IFormInput;
  ngay_hl: IFormInput;
  ngay_kt: IFormInput;
  stt: IFormInput;
  trang_thai: IFormInput;
}

// const ruleRequired = {
//   required: true,
//   message: "Thông tin bắt buộc",
// };

export const FormTaoMoiTaiKhoanDonViThuHo: IFormTaoMoiTaiKhoanDonViThuHoFieldsConfig = {
  ma: {
    component: "input",
    label: "Mã",
    name: "ma",
    placeholder: "Nhập mã Tài khoản đơn vị thu hộ ",
    rules: [ruleInputMessage.required],
  },
  ten: {
    component: "input",
    label: "Tên",
    name: "ten",
    placeholder: "Nhập tên Tài khoản đơn vị thu hộ ",
    rules: [ruleInputMessage.required],
  },
  ma_dvi: {
    component: "select",
    label: "Mã đơn vị",
    name: "ma_dvi",
    placeholder: "Chọn mã đơn vị",
    rules: [ruleInputMessage.required],
  },
  mat_khau: {
    component: "input",
    label: "Mật khẩu",
    name: "mat_khau",
    placeholder: "Nhập mật khẩu",
    rules: [ruleInputMessage.required],
  },
  email: {
    component: "input",
    label: "Email",
    name: "email",
    placeholder: "Nhập email",
    rules: [ruleInputMessage.required, ruleInputMessage.email],
  },
  dthoai: {
    component: "input",
    label: "Điện thoại",
    name: "dthoai",
    placeholder: "Nhập điện thoại",
    rules: [ruleInputMessage.required, ruleInputMessage.phone],
  },
  ngay_hl: {
    component: "date-picker",
    label: "Ngày hiệu lực",
    name: "ngay_hl",
    placeholder: "Chọn ngày hiệu lực",
    rules: [ruleInputMessage.required],
  },
  ngay_kt: {
    component: "date-picker",
    label: "Ngày kết thúc",
    name: "ngay_kt",
    placeholder: "Chọn ngày kết thúc",
    rules: [ruleInputMessage.required],
  },
  stt: {
    component: "input",
    label: "Số thứ tự",
    placeholder: "Nhập số thứ tự",
    name: "stt",
    rules: [ruleInputMessage.required],
  },
  trang_thai: {
    component: "select",
    label: "Trạng thái",
    name: "trang_thai",
    placeholder: "Chọn trạng thái",
    rules: [ruleInputMessage.required],
  },
};

export const TRANG_THAI_TAO_MOI_TAI_KHOAN_DON_VI_THU_HO = [
  {ten: "Đang sử dụng", ma: "D"},
  {ten: "Ngừng sử dụng", ma: "K"},
];

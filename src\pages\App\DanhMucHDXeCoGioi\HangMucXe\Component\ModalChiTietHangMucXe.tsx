import React, {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useState, useContext, useMemo} from "react";
import {Button, Col, Flex, Form, Modal, Row} from "antd";
import {ArrowLeftOutlined, CheckOutlined} from "@ant-design/icons";

import {FormInput, HeaderModal, Popcomfirm} from "@src/components";
import {IFormInput, ReactQuery} from "@src/@types";

import HangMucXeContext from "../index.context";
import {FormTaoMoiHangMucXe, TRANG_THAI_TAO_MOI_HANG_MUC_XE, DANH_SACH_NGHIEP_VU_HANG_MUC_XE, DANH_SACH_VI_TRI, DANH_SACH_LOAI} from "../index.configs";

// ===== INTERFACE ĐỊNH NGHĨA REF VÀ PROPS CHO MODAL =====
export interface IModalChiTietHangMucXeRef {
  open: (data?: CommonExecute.Execute.IHangMucXe) => void;
}

interface IModalChiTietHangMucXeProps {
  onAfterSave?: () => void;
  danhSachNhomHangMucXe?: Array<CommonExecute.Execute.INhomHangMucXe>; // Cập nhật để nhận đầy đủ thông tin nghiệp vụ
}

/**
 * Modal này dùng để tạo mới và chỉnh sửa thông tin hạng mục xe, có validation real-time và kiểm tra trùng mã
 */
const ModalChiTietHangMucXeComponent = forwardRef<IModalChiTietHangMucXeRef, IModalChiTietHangMucXeProps>(({onAfterSave, danhSachNhomHangMucXe = []}, ref) => {
  // ===== CONTEXT & FORM - LẤY DỮ LIỆU VÀ METHODS TỪ CONTEXT =====
  const {capNhatChiTietHangMucXe, listHangMucXe, loading} = useContext(HangMucXeContext);
  const [form] = Form.useForm();

  // ===== STATE MANAGEMENT - QUẢN LÝ TRẠNG THÁI MODAL VÀ FORM =====
  const [isOpen, setIsOpen] = useState<boolean>(false); // Trạng thái mở/đóng modal
  const [chiTietHangMucXe, setChiTietHangMucXe] = useState<CommonExecute.Execute.IHangMucXe | null>(null); // Data để edit
  const [disableSubmit, setDisableSubmit] = useState<boolean>(true); // Disable submit button khi form chưa valid

  // ===== FORM VALUES WATCHING - THEO DÕI THAY ĐỔI FORM REAL-TIME =====
  const formValues = Form.useWatch([], form);
  const selectedNghiepVu = Form.useWatch("nv", form); // Theo dõi nghiệp vụ được chọn

  // ===== FORM CONFIGURATIONS - CẤU HÌNH CÁC FIELD TRONG FORM =====
  const {nv, ma, ten, loai, nhom, vi_tri, stt, trang_thai} = FormTaoMoiHangMucXe;

  // ===== DROPDOWN OPTIONS - CHUẨN BỊ DỮ LIỆU CHO CÁC DROPDOWN =====
  // Filter nhóm hạng mục xe theo nghiệp vụ được chọn
  const nhomHangMucXeOptions = useMemo(() => {
    if (!danhSachNhomHangMucXe || !Array.isArray(danhSachNhomHangMucXe)) {
      return [];
    }

    let filteredOptions = danhSachNhomHangMucXe.filter(item => {
      // Lọc ra những item hợp lệ
      const hasValidData = item && item.ma && item.ten;
      if (!hasValidData) return false;

      const trangThai = item.trang_thai?.toString();
      const isActive = trangThai === "1" || trangThai === "D" || trangThai === "Đang sử dụng" || !trangThai;

      return hasValidData && isActive;
    });

    // Filter theo nghiệp vụ nếu đã chọn nghiệp vụ
    if (selectedNghiepVu) {
      filteredOptions = filteredOptions.filter(item => item.nv === selectedNghiepVu);
    }

    // Map để tạo options cho dropdown với format chuẩn
    const mappedOptions = filteredOptions.map(item => ({
      ma: item.ma?.toString() || "",
      ten: item.ten?.toString() || "",
      nv: item.nv?.toString() || "",
    }));

    console.log("[ModalChiTietHangMucXe] Filter nhóm theo nghiệp vụ:", {
      selectedNghiepVu,
      originalCount: danhSachNhomHangMucXe.length,
      filteredCount: mappedOptions.length,
      filteredOptions: mappedOptions.slice(0, 3), // Show first 3 for debugging
    });

    return mappedOptions;
  }, [danhSachNhomHangMucXe, selectedNghiepVu]);

  const trangThaiOptions = TRANG_THAI_TAO_MOI_HANG_MUC_XE;
  const nghiepVuOptions = DANH_SACH_NGHIEP_VU_HANG_MUC_XE;
  const viTriOptions = DANH_SACH_VI_TRI;
  const loaiOptions = DANH_SACH_LOAI;

  // ===== EFFECT: RESET NHÓM KHI NGHIỆP VỤ THAY ĐỔI =====
  useEffect(() => {
    // Chỉ reset nhóm khi modal đang mở và không phải lần đầu load data
    if (isOpen && selectedNghiepVu !== undefined) {
      const currentNhom = form.getFieldValue("nhom");

      // Kiểm tra nếu nhóm hiện tại không thuộc nghiệp vụ được chọn
      if (currentNhom) {
        const isNhomValid = nhomHangMucXeOptions.some(option => option.ma === currentNhom);
        if (!isNhomValid) {
          form.setFieldValue("nhom", undefined);
          // console.log("[ModalChiTietHangMucXe] Reset nhóm do nghiệp vụ thay đổi");
        }
      }
    }
  }, [selectedNghiepVu, nhomHangMucXeOptions, form, isOpen]);

  // ===== IMPERATIVE HANDLE - EXPOSE METHODS CHO PARENT COMPONENT =====
  useImperativeHandle(ref, () => ({
    open: (data?: CommonExecute.Execute.IHangMucXe) => {
      setChiTietHangMucXe(data || null);
      setIsOpen(true);
    },
  }));

  // ===== EFFECT: KHỞI TẠO FORM DATA KHI MODAL MỞ =====
  useEffect(() => {
    if (chiTietHangMucXe && isOpen) {
      // Chỉnh sửa - load data vào form
      const arrFormData = [];
      for (const key in chiTietHangMucXe) {
        arrFormData.push({
          name: key,
          value: chiTietHangMucXe[key as keyof CommonExecute.Execute.IHangMucXe],
        });
      }
      form.setFields(arrFormData);
    } else if (!chiTietHangMucXe && isOpen) {
      // Tạo mới - set giá trị mặc định
      form.setFields([
        {name: "nv", value: undefined},
        {name: "ma", value: ""},
        {name: "ten", value: ""},
        {name: "loai", value: undefined},
        {name: "nhom", value: undefined},
        {name: "vi_tri", value: undefined},
        {name: "stt", value: ""},
        {name: "trang_thai", value: "D"},
      ]);
    }
  }, [chiTietHangMucXe, isOpen, form]);

  // ===== EFFECT: VALIDATION REAL-TIME CHO FORM =====
  useEffect(() => {
    if (!isOpen) return;

    form
      .validateFields({validateOnly: true})
      .then(() => {
        setDisableSubmit(false);
      })
      .catch(() => {
        setDisableSubmit(true);
      });
  }, [form, formValues, isOpen]);

  // ===== HANDLER: ĐÓNG MODAL VÀ RESET FORM =====
  const closeModal = useCallback(() => {
    setIsOpen(false);
    setChiTietHangMucXe(null);
    form.resetFields();
  }, [form]);

  // ===== HANDLER: XỬ LÝ SUBMIT FORM VÀ GỌI API =====
  const onConfirm = async () => {
    try {
      const values: ReactQuery.ICapNhatHangMucXeParams = form.getFieldsValue();

      // Validate mã hạng mục xe không trùng khi tạo mới
      // if (!chiTietHangMucXe) {
      //   for (let i = 0; i < listHangMucXe.length; i++) {
      //     if (listHangMucXe[i].ma === values.ma && listHangMucXe[i].nv === values.nv) {
      //       form.setFields([
      //         {
      //           name: "ma",
      //           errors: ["Mã hạng mục xe đã tồn tại trong nghiệp vụ này!"],
      //         },
      //       ]);
      //       return;
      //     }
      //   }
      // }

      await capNhatChiTietHangMucXe(values);
      closeModal();
      onAfterSave?.();
    } catch (error) {
      console.log("onConfirm error", error);
    }
  };

  // ===== RENDER FUNCTION: FOOTER CỦA MODAL =====
  const renderFooter = () => {
    return (
      <div>
        <Button type="default" onClick={() => closeModal()} className="mr-2" icon={<ArrowLeftOutlined />}>
          Quay lại
        </Button>
        <Popcomfirm
          title={disableSubmit ? "Vui lòng nhập đầy đủ thông tin" : ""}
          onConfirm={onConfirm}
          okText="Lưu"
          description="Bạn có chắc muốn lưu thông tin?"
          buttonTitle={"Lưu"}
          buttonDisable={disableSubmit}
          buttonClassName="w-40"
          buttonIcon={<CheckOutlined />}
          iconPosition="end"
          loading={loading}
        />
      </div>
    );
  };

  // ===== RENDER FUNCTION: RENDER TỪNG COLUMN CỦA FORM =====
  const renderFormColumn = (props: IFormInput, span = 8) => {
    // Debug log để kiểm tra props của trang_thai
    if (props.name === "trang_thai") {
      console.log("[ModalChiTietHangMucXe] trang_thai props:", props);
    }

    return (
      <Col span={span}>
        <FormInput {...props} />
      </Col>
    );
  };

  // ===== RENDER FUNCTION: LAYOUT FORM VỚI CÁC FIELD =====
  const renderForm = () => (
    <Form form={form} layout="vertical">
      <Row gutter={16}>
        {renderFormColumn({...ma, disabled: chiTietHangMucXe ? true : false}, 6)}
        {renderFormColumn({...ten}, 10)}
        {renderFormColumn({...nv, disabled: chiTietHangMucXe ? true : false, options: nghiepVuOptions}, 8)}
      </Row>

      <Row gutter={16}>
        {renderFormColumn({...loai, options: loaiOptions}, 6)}
        {renderFormColumn({...nhom, options: nhomHangMucXeOptions}, 18)}
      </Row>

      <Row gutter={16}>
        {renderFormColumn({...vi_tri, options: viTriOptions}, 6)}
        {renderFormColumn({...stt}, 6)}
        {renderFormColumn(
          {
            ...trang_thai,
            options: trangThaiOptions,
            fieldNames: {label: "label", value: "value"},
          },
          12,
        )}
      </Row>
    </Form>
  );

  // ===== MAIN RENDER - RENDER MODAL VỚI TẤT CẢ COMPONENTS =====
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        title={<HeaderModal title={chiTietHangMucXe ? `${chiTietHangMucXe.ten}` : "Tạo mới hạng mục xe"} trang_thai_ten={chiTietHangMucXe?.trang_thai_ten} trang_thai={chiTietHangMucXe?.trang_thai} />}
        maskClosable={false}
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width={780}
        styles={{
          body: {
            paddingTop: "8px",
            paddingBottom: "16px",
          },
        }}
        footer={renderFooter()}
        destroyOnClose={true}>
        {renderForm()}
      </Modal>
    </Flex>
  );
});

ModalChiTietHangMucXeComponent.displayName = "ModalChiTietHangMucXe";

const ModalChiTietHangMucXe = memo(ModalChiTietHangMucXeComponent);

export default ModalChiTietHangMucXe;

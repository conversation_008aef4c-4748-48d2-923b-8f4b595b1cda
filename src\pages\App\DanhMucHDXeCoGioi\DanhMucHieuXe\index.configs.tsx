import {IFormInput, ReactQuery} from "@src/@types";
import {colWidthByKey, defaultTableColumnsProps} from "@src/hooks";
import {TableProps} from "antd";
export interface TableDanhMucHieuXeDataType {
  key: number | string;
  sott?: number;
  ma?: string;
  ten?: string;
  trang_thai_ten?: string;
  ngay_tao?: string;
  nguoi_tao?: string;
  ngay_cap_nhat?: string;
  nguoi_cap_nhat?: string;
  nv?: string;
  hang_xe?: string;
}

export const DanhMucHieuXeColumns: TableProps<TableDanhMucHieuXeDataType>["columns"] = [
  {...defaultTableColumnsProps, title: "STT", dataIndex: "sott", width: colWidthByKey.sott},
  {...defaultTableColumnsProps, title: "Mã hiệu xe", dataIndex: "ma", key: "ma", width: 150},
  {...defaultTableColumnsProps, title: "Tên hiệu xe", dataIndex: "ten", key: "ten", width: 200},
  {...defaultTableColumnsProps, title: "Hãng xe", dataIndex: "hang_xe", key: "hang_xe", width: 150},
  {...defaultTableColumnsProps, title: "Nghiệp vụ", dataIndex: "nv", key: "nv", width: 130},
  {...defaultTableColumnsProps, title: "Ngày tạo", dataIndex: "ngay_tao", key: "ngay_tao", width: colWidthByKey.ngay_tao},
  {...defaultTableColumnsProps, title: "Người tạo", dataIndex: "nguoi_tao", key: "nguoi_tao", width: colWidthByKey.nguoi_tao},
  {...defaultTableColumnsProps, title: "Ngày cập nhật", dataIndex: "ngay_cap_nhat", key: "Ngay_cap_nhat", width: colWidthByKey.ngay_cap_nhat},
  {...defaultTableColumnsProps, title: "Người cập nhật", dataIndex: "nguoi_cap_nhat", key: "nguoi_cap_nhat", width: colWidthByKey.nguoi_cap_nhat},
  {...defaultTableColumnsProps, title: "Trạng thái", dataIndex: "trang_thai_ten", key: "trang_thai_ten", width: 130},
];

export const setFormFields = (form: any, chiTietHopDong: any) => {
  if (chiTietHopDong) {
    form.setFields([
      {
        name: "ten",
        value: chiTietHopDong.ten || "",
      },
      {
        name: "ma",
        value: chiTietHopDong.ma || "",
      },
      {
        name: "loai",
        value: chiTietHopDong.loai || "",
      },
      {
        name: "kieu_ad",
        value: chiTietHopDong.kieu_ad || "",
      },
    ]);
  }
};

export const radioItemTrangThaiHopDongTable = [
  {value: "Đang sử dụng", text: "Đang sử dụng"},
  {value: "Ngưng sử dụng", text: "Ngưng sử dụng"},
];

//option select trạng thái
export const optionTrangThaiHopDongSelect = [
  {ma: "D", ten: "Đang sử dụng"},
  {ma: "K", ten: "Ngưng sử dụng"},
];
//Form
export interface IFormTimKiemDanhMucHieuXeFieldsConfig {
  ma: IFormInput;
  ten: IFormInput;
  trang_thai: IFormInput;
  hang_xe: IFormInput;
  nv: IFormInput;
}

export const FormTimKiemDanhMucHieuXe: IFormTimKiemDanhMucHieuXeFieldsConfig = {
  ma: {
    component: "input",
    name: "ma",
    label: "Mã hiệu xe",
    placeholder: "Chọn mã hiệu xe",
  },
  ten: {
    component: "input",
    name: "ten",
    label: "Tên",
    placeholder: "Chọn tên",
  },
  trang_thai: {
    component: "select",
    name: "trang_thai",
    label: "Trạng thái",
    placeholder: "Chọn trạng thái",
  },
  hang_xe: {
    component: "select",
    name: "hang_xe",
    label: "Hãng xe",
    placeholder: "Chọn hãng xe",
  },
  nv: {
    component: "select",
    name: "nv",
    label: "Nghiệp vụ",
    placeholder: "Chọn nghiệp vụ",
  },
};
// keyof: return ra kay của interface TableBoMaNguyenTeColumDataType;
export type TableDanhMucHieuXeDataIndex = keyof TableDanhMucHieuXeDataType;
// defaultFormValue tìm kiếm phân trang bộ mã nguyên tệ
export const defaultFormValue: ReactQuery.ITimKiemPhanTrangDanhMucHieuXeParams = {
  ma: "",
  ten: "",
  trang_thai: "",
  trang: 1,
  so_dong: 13,
};
export const TRANG_THAI_CHI_TIET_DANH_MUC_HIEU_XE = [
  {ten: "Đang sử dụng", ma: "D"},
  {ten: "Ngưng sử dụng", ma: "K"},
];

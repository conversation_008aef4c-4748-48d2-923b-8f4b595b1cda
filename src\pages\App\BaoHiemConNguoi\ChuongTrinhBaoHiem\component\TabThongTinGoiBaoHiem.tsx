import {useCallback, useEffect, useMemo, useRef, useState} from "react";
import {Table, TableColumnType, InputRef, Form, Button, Flex, Input, Tooltip, Tag} from "antd";
import {ClearOutlined, CloseOutlined, PlusCircleOutlined, SearchOutlined} from "@ant-design/icons";
import {defaultTableProps, fillRowTableEmpty} from "@src/hooks";
import {TableGoiBaoHiemDataType, DataIndexGoi, goiBaoHiemColumns, TabThongTinGoiBaoHiemProps, IModalThemGoiBaoHiemRef} from "./index.configs";
import {useChuongTrinhBaoHiemContext} from "../index.context";
import {Highlighter, Popcomfirm} from "@src/components";
import {ModalThemGoiBaoHiem} from "./ModalThemGoiBaoHiem";
import {ReactQuery} from "@src/@types";
import {FilterDropdownProps} from "antd/es/table/interface";

const TabThongTinGoiBaoHiem: React.FC<TabThongTinGoiBaoHiemProps> = (props: TabThongTinGoiBaoHiemProps) => {
  const {chiTietChuongTrinhBaoHiem, filterValues} = props;
  const refModalThemGoiBaoHiem = useRef<IModalThemGoiBaoHiemRef>(null);
  const searchInput = useRef<InputRef>(null);
  const {loading, listGoiBaoHiem, onXoaGoiBaoHiemKhoiCTBH, getListGoiBaoHiemTheoCTBH} = useChuongTrinhBaoHiemContext();
  const [searchedColumn, setSearchedColumn] = useState("");
  const [searchText, setSearchText] = useState("");
  useEffect(() => {
    if (chiTietChuongTrinhBaoHiem && chiTietChuongTrinhBaoHiem.ma && chiTietChuongTrinhBaoHiem.ma_doi_tac_ql) {
      const values = {
        ma_ctbh: chiTietChuongTrinhBaoHiem.ma,
        ma_doi_tac_ql: chiTietChuongTrinhBaoHiem.ma_doi_tac_ql,
      };
    }
  }, [chiTietChuongTrinhBaoHiem]);

  const dataTableListGoiBaoHiem = useMemo<Array<TableGoiBaoHiemDataType>>(() => {
    try {
      let tableData = Array.isArray(listGoiBaoHiem) ? listGoiBaoHiem : [];
      if (filterValues.ma || filterValues.ma_doi_tac_ql) {
        tableData = listGoiBaoHiem.filter(item => {
          const matchMa = !filterValues.ma || item.ma_ctbh === filterValues.ma;
          const matchMaDoiTacQl = !filterValues.ma_doi_tac_ql || item.ma_doi_tac_ql === filterValues.ma_doi_tac_ql;
          return matchMa && matchMaDoiTacQl;
        });
      }

      const mappedData = tableData.map((item: any, index: number) => ({
        stt: item.stt ?? index + 1,
        ma: item.ma_goi_bh,
        ten: item.ten_goi_bh,
        ten_sp: item.ten_sp,
        ma_ctbh: item.ma_ctbh,
        id_goi_bh: item.id,
        nv: chiTietChuongTrinhBaoHiem?.nv,
        ma_sp: chiTietChuongTrinhBaoHiem?.ma_sp,
        ma_doi_tac_ql: chiTietChuongTrinhBaoHiem?.ma_doi_tac_ql,
        hanh_dong: () => renderDeleteButton(item.ma_ctbh, chiTietChuongTrinhBaoHiem?.ma_doi_tac_ql, item.id_goi_bh, chiTietChuongTrinhBaoHiem?.nv, chiTietChuongTrinhBaoHiem?.ma_sp),
        key: index.toString(),
      }));

      console.log("mapdata", mappedData);
      const arrEmptyRow: Array<TableGoiBaoHiemDataType> = fillRowTableEmpty(mappedData.length, 10);
      return [...mappedData, ...arrEmptyRow];
    } catch (error) {
      console.log("dataTableListGoiBaoHiem error", error);
      return [];
    }
  }, [listGoiBaoHiem, filterValues]);
  const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: DataIndexGoi) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);
  const handleReset = useCallback(
    (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: DataIndexGoi) => {
      clearFilters();
      setSearchedColumn("");
      handleSearch([""], confirm, dataIndex);
    },
    [handleSearch],
  );
  const getColumnSearchProps = (dataIndex: DataIndexGoi, title: string): TableColumnType<TableGoiBaoHiemDataType> => ({
    filterDropdown: ({setSelectedKeys, selectedKeys, confirm, clearFilters, close}) => (
      <div style={{padding: 8}} onKeyDown={e => e.stopPropagation()} className="flex flex-row items-center p-2">
        <Input
          ref={searchInput}
          placeholder={`Tìm theo ${title}`}
          value={selectedKeys[0]}
          onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
          onPressEnter={() => handleSearch(selectedKeys as string[], confirm, dataIndex)}
          style={{display: "block", marginRight: 8}}
        />
        <Tooltip title="Tìm kiếm">
          <Button type="primary" shape="circle" icon={<SearchOutlined />} onClick={() => handleSearch(selectedKeys as string[], confirm, dataIndex)} className="mr-2" />
        </Tooltip>
        <Tooltip title="Xoá">
          <Button type="primary" shape="circle" icon={<ClearOutlined />} onClick={() => clearFilters && handleReset(clearFilters, confirm, dataIndex)} />
        </Tooltip>
      </div>
    ),
    filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} />,
    onFilter: (value, record) =>
      record[dataIndex]
        ? record[dataIndex]
            .toString()
            .toLowerCase()
            .includes((value as string).toLowerCase())
        : false,
    filterDropdownProps: {
      onOpenChange(open) {
        if (open) {
          setTimeout(() => searchInput.current?.select(), 100);
        }
      },
    },
    render: (text, record, index) => {
      return searchedColumn === dataIndex ? (
        <Highlighter searchWords={[searchText]} textToHighlight={text ? text.toString() : ""} />
      ) : text !== undefined ? (
        text
      ) : (
        <Tag color={"transparent"} className="!text-white text-[11px]">
          {"\u00A0"}
        </Tag>
      );
    },
  });
  const onDelete = async (ma_ctbh: string, ma_doi_tac_ql: string, id_goi_bh?: number, nv?: string, ma_sp?: string) => {
    try {
      const params: ReactQuery.IXoaGoiBaoHiemKhoiCTBHParams = {
        ma_ctbh: ma_ctbh,
        ma_doi_tac_ql: ma_doi_tac_ql,
        id_goi_bh: id_goi_bh,
        nv: nv,
        ma_sp: ma_sp,
      };
      console.log("params", params);
      await onXoaGoiBaoHiemKhoiCTBH(params);
      getListGoiBaoHiemTheoCTBH({ma_ctbh: ma_ctbh, ma_doi_tac_ql: ma_doi_tac_ql, nv: nv});
    } catch (error) {
      console.log("onDelete error", error);
    }
  };
  //render
  const renderFooter = () => {
    return (
      <Form.Item className="h-300 float-right mb-0">
        <Flex wrap="wrap" gap="small" className="w-full">
          <Button className="" type="primary" icon={<PlusCircleOutlined />} onClick={() => refModalThemGoiBaoHiem.current?.open()} loading={loading}>
            Thêm gói bảo hiểm
          </Button>
        </Flex>
      </Form.Item>
    );
  };

  const renderDeleteButton = (ma_ctbh?: string, ma_doi_tac_ql?: string, id_goi_bh?: number, nv?: string, ma_sp?: string) => {
    if (!ma_ctbh || !ma_doi_tac_ql) return null;
    return (
      <Popcomfirm
        title="Thông báo"
        onConfirm={() => onDelete(ma_ctbh, ma_doi_tac_ql, id_goi_bh, nv, ma_sp)}
        htmlType="button"
        okText="Xóa"
        description="Bạn có chắc muốn xóa gói bảo hiểm?"
        buttonTitle={""}
        buttonColor="red"
        okButtonProps={{
          style: {
            backgroundColor: "white",
            borderColor: "red",
            color: "red",
          },
        }}
        variant="text"
        className="h-auto"
        icon={<CloseOutlined />}
        buttonIcon
      />
    );
  };
  return (
    <div className="[&_.ant-space]:w-full">
      <Table<TableGoiBaoHiemDataType>
        {...defaultTableProps}
        style={{
          borderBottom: "1px solid #f0f0f0",
          marginBottom: "1rem",
        }}
        loading={loading}
        columns={(goiBaoHiemColumns || []).map(item => ({
          ...item,
          ...(item.key && typeof item.title === "string" && item.key !== "stt" ? getColumnSearchProps(item.key as keyof TableGoiBaoHiemDataType, item.title) : {}),
        }))}
        dataSource={dataTableListGoiBaoHiem}
        pagination={false}
      />

      {renderFooter()}
      <ModalThemGoiBaoHiem ref={refModalThemGoiBaoHiem} chiTietChuongTrinhBaoHiem={chiTietChuongTrinhBaoHiem ?? undefined} />
    </div>
  );
};

export default TabThongTinGoiBaoHiem;

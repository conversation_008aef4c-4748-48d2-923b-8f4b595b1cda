import {IFormInput} from "@src/@types";

export const initFormFields = (form: any, chiTietNhomChucNang: any) => {
  if (!chiTietNhomChucNang) return;
  const fields = Object.entries(chiTietNhomChucNang.nhom_cn).map(([name, value]) => ({
    name,
    value: value ?? "",
  }));
  form.setFields(fields);
};

const ruleRequired = {
  required: true,
  message: "Thông tin bắt buộc",
};

export const radioItemTrangThaiChucNangTable = [
  {ma: "Đang sử dụng", ten: "Đang sử dụng"},
  {ma: "Ngưng sử dụng", ten: "Ngưng sử dụng"},
];
export const dataOptionLoaiKhachHang = [
  {ma: "C", ten: "Cá nhân"},
  {ma: "T", ten: "Tổ chức"},
];

export interface TableKhachHangDataType {
  stt?: number;
  ma?: string;
  ten?: string;
  ten_nhom?: string;
  sl_chuc_nang?: number;
  ngay_tao?: string;
  nguoi_tao?: string;
  ngay_cap_nhat?: string;
  nguoi_cap_nhat?: string;
  trang_thai_ten?: string;
  doi_tac_ql_ten_tat?: string;
}
export interface IFormChiTietDanhMucHieuXeFieldsConfig {
  ma: IFormInput;
  // ten_nv: IFormInput;
  stt: IFormInput;
  ten: IFormInput;
  trang_thai: IFormInput;
  nv: IFormInput;
  hang_xe: IFormInput;
}

export const FormChiTietDanhMucHieuXe: IFormChiTietDanhMucHieuXeFieldsConfig = {
  ma: {
    component: "input",
    label: "Mã",
    name: "ma",
    placeholder: "Nhập mã ",
    rules: [ruleRequired],
  },
  ten: {
    component: "input",
    label: "Tên",
    name: "ten",
    placeholder: "Nhập tên",
    rules: [ruleRequired],
  },
  nv: {
    component: "select",
    label: "Nghiệp vụ ",
    name: "nv",
    placeholder: "Chọn nghiệp vụ",
    rules: [ruleRequired],
  },
  stt: {
    component: "input",
    label: "Thứ tự hiển thị",
    name: "stt",
    placeholder: "Nhập thứ tự hiển thị",
    rules: [ruleRequired],
  },
  trang_thai: {
    component: "select",
    label: "Trạng thái",
    name: "trang_thai",
    placeholder: "Chọn trạng thái",
    rules: [ruleRequired],
  },
  hang_xe: {
    component: "select",
    label: "Hãng xe",
    name: "hang_xe",
    placeholder: "Chọn hãng xe",
    rules: [ruleRequired],
  },
};

export const TRANG_THAI_HIEU_XE = [
  {ten: "Đang sử dụng", ma: "D"},
  {ten: "Ngừng sử dụng", ma: "K"},
];
export const NGHIEP_VU_HIEU_XE = [
  {ten: "Bảo hiểm ô tô", ma: "XE"},
  {ten: "Bảo hiểm xe máy", ma: "XE_MAY"},
];

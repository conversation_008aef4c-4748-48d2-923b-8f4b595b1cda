import React, {PropsWithChildren} from "react";
import {notification} from "antd";
import {NotificationInstance} from "antd/es/notification/interface";

export interface INotiContextProps {
  noti: NotificationInstance;
}

export const NotiContext = React.createContext<INotiContextProps>({} as any);

export const useNotiContext = () => React.useContext<INotiContextProps>(NotiContext);

export const NotiProvider: React.FC<PropsWithChildren> = props => {
  const {children} = props;
  const [api, contextHolder] = notification.useNotification();

  return (
    <NotiContext.Provider value={{noti: api}}>
      {contextHolder}
      {children}
    </NotiContext.Provider>
  );
};

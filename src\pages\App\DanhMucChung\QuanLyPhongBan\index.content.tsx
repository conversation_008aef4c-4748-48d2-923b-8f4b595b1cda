import {ClearOutlined, PlusCircleOutlined, SearchOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {Button, FormInput, Highlighter} from "@src/components";
import {ACTION_CODE, COLOR_PALETTE, ID_PAGE} from "@src/constants";
import {fillRowTableEmpty, defaultTableProps, defaultPaginationTableProps, useChiNhanh, useDoiTac} from "@src/hooks";
import {Col, Form, Input, InputRef, Row, Table, TableColumnType, Tag, Tooltip} from "antd";
import type {FilterDropdownProps} from "antd/es/table/interface";
import {isEqual} from "lodash";
import React, {memo, useCallback, useMemo, useRef, useState} from "react";
import ModalChiTietPhongBan, {IModalChiTietPhongBanRef} from "./Component/ModalChiTietPhongBan";
import {FormTimKiemPhanTrangPhongBan, optionTrangThaiPhongBanSelect, phongBanColumns, radioItemTrangThaiPhongBanTable, TablePhongBanDataType} from "./index.configs";
import {useQuanLyPhongBanContext} from "./index.context"; // file này lưu biến về state
import "./index.dark.scss";
import "./index.default.scss";

type DataIndex = keyof TablePhongBanDataType;
/*
file LoginContent : xử lý phần UI của màn hình
React.Fc : khai báo rằng type LoginContent là 1 React Function
*/

const {ten, ma, ma_doi_tac, ma_chi_nhanh, trang_thai} = FormTimKiemPhanTrangPhongBan;

const QuanLyPhongBanContent: React.FC = () => {
  const {listDoiTac} = useDoiTac();
  const {listChiNhanh} = useChiNhanh();
  const [formTimKiemPhongBan] = Form.useForm();
  const {danhSachPhongBan, loading, layDanhSachPhongBanPhanTrang, tongSoDong, layChiTietPhongBan, defaultFormValue} = useQuanLyPhongBanContext();
  const [searchText, setSearchText] = useState("");
  const [searchedColumn, setSearchedColumn] = useState("");
  const searchInput = useRef<InputRef>(null);
  const [searchParams, setSearchParams] = useState<ReactQuery.ILayDanhSachPhongBanPhanTrangParams>(defaultFormValue);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(defaultPaginationTableProps.defaultPageSize);
  const refModalChiTietPhongBan = useRef<IModalChiTietPhongBanRef>(null);

  const watchMaDoiTac = Form.useWatch("ma_doi_tac", formTimKiemPhongBan);

  const dataTableListPhongBan = useMemo<Array<TablePhongBanDataType>>(() => {
    try {
      const tableData = danhSachPhongBan.map((item, index) => {
        return {
          ten: item.ten,
          ma_doi_tac: item.ma_doi_tac,
          ma_chi_nhanh: item.ma_chi_nhanh,
          ma: item.ma,
          stt: item.sott,
          ngay_tao: item.ngay_tao,
          nguoi_tao: item.nguoi_tao,
          ngay_cap_nhat: item.ngay_cap_nhat,
          nguoi_cap_nhat: item.nguoi_cap_nhat,

          trang_thai_ten: item.trang_thai_ten,
          trang_thai: item.trang_thai,
          chi_nhanh_ten_tat: item.chi_nhanh_ten_tat,
          doi_tac_ten_tat: item.doi_tac_ten_tat,
          key: index.toString(),
        };
      });
      const arrEmptyRow: Array<TablePhongBanDataType> = fillRowTableEmpty(tableData.length, defaultTableProps.defaultPageSize);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      console.log("dataTableListPhongBan error", error);
      return [];
    }
  }, [danhSachPhongBan]);

  const listChiNhanhFilterTheoDoiTac = useMemo(() => {
    return listChiNhanh.filter(item => item.ma_doi_tac === watchMaDoiTac);
  }, [listChiNhanh, watchMaDoiTac]);

  //tìm kiếm client-side
  const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: DataIndex) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);

  const handleReset = useCallback(
    (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: DataIndex) => {
      clearFilters();
      setSearchedColumn("");
      handleSearch([""], confirm, dataIndex);
    },
    [handleSearch],
  );

  const getColumnSearchProps = (dataIndex: DataIndex, title: string): TableColumnType<TablePhongBanDataType> => ({
    filterDropdown:
      dataIndex !== "trang_thai_ten"
        ? ({setSelectedKeys, selectedKeys, confirm, clearFilters, close}) => (
            <div style={{padding: 8}} onKeyDown={e => e.stopPropagation()} className="flex flex-row items-center p-2">
              <Input
                ref={searchInput}
                placeholder={`Tìm theo ${title}`}
                value={selectedKeys[0]}
                onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                onPressEnter={() => handleSearch(selectedKeys as string[], confirm, dataIndex)}
                style={{display: "block", marginRight: 8}}
              />
              <Tooltip title="Tìm kiếm">
                <Button type="primary" shape="circle" icon={<SearchOutlined />} onClick={() => handleSearch(selectedKeys as string[], confirm, dataIndex)} className="mr-2" />
              </Tooltip>
              <Tooltip title="Xoá">
                <Button type="primary" shape="circle" icon={<ClearOutlined />} onClick={() => clearFilters && handleReset(clearFilters, confirm, dataIndex)} />
              </Tooltip>
            </div>
          )
        : null,
    filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} />,
    onFilter: (value, record) =>
      record[dataIndex]
        ? record[dataIndex]
            .toString()
            .toLowerCase()
            .includes((value as string).toLowerCase())
        : false,
    filterDropdownProps: {
      onOpenChange(open) {
        if (open) {
          setTimeout(() => searchInput.current?.select(), 100);
        }
      },
    },
    filters: dataIndex === "trang_thai_ten" ? radioItemTrangThaiPhongBanTable : undefined,
    render: (text, record, index) => {
      if (dataIndex === "trang_thai_ten") {
        const color = record.trang_thai === "D" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];
        return <Tag color={color}>{text}</Tag>;
      }
      return searchedColumn === dataIndex ? <Highlighter searchWords={[searchText]} textToHighlight={text ? text.toString() : ""} /> : text;
    },
  });

  //Bấm tìm kiếm
  const onSearchApi = (values: ReactQuery.ILayDanhSachPhongBanPhanTrangParams & ReactQuery.IPhanTrang) => {
    // đoạn này để sửa các giá trị undefined => '' trước khi request lên API
    // Ant Design Select không coi "" là một giá trị "chưa chọn", mà là một giá trị đã chọn hợp lệ (dù nó rỗng), nên nó sẽ không hiển thị placeholder nữa.
    const cleanedValues = {
      ...values,
      ma_doi_tac: values.ma_doi_tac ?? "",
      ma_chi_nhanh: values.ma_chi_nhanh ?? "",
      trang_thai: values.trang_thai ?? "",
      ma: values.ma ?? "",
      ten: values.ten ?? "",
    };
    setSearchParams(cleanedValues);
    setPage(1); // reset về trang 1 khi search mới
    layDanhSachPhongBanPhanTrang({...cleanedValues, trang: 1, so_dong: pageSize});
  };

  //Gọi api khi chuyển trang
  const onChangePage = useCallback(
    (page: number, pageSize: number) => {
      setPage(page);
      setPageSize(pageSize);
      layDanhSachPhongBanPhanTrang({...searchParams, trang: page, so_dong: pageSize});
    },
    [searchParams],
  );

  const renderFormInputColum = (props?: any, span = 4) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );

  // RENDER
  const renderHeaderTableQuanLyPhongBan = () => {
    return (
      <div>
        <Form form={formTimKiemPhongBan} layout="vertical" className="[&_.ant-form-item]:mb-0" onFinish={onSearchApi}>
          <Row gutter={16} align={"bottom"}>
            {renderFormInputColum({...ma_doi_tac, options: listDoiTac, onChange: () => formTimKiemPhongBan.setFieldValue("ma_chi_nhanh", undefined)})}
            {renderFormInputColum({...ma_chi_nhanh, options: listChiNhanhFilterTheoDoiTac})}
            {renderFormInputColum({...ma})}
            {renderFormInputColum({...ten})}
            {renderFormInputColum({...trang_thai, options: optionTrangThaiPhongBanSelect})}
            <Col span={2}>
              <Button type="primary" htmlType="submit" loading={loading} icon={<SearchOutlined />} className="w-full">
                Tìm kiếm
              </Button>
            </Col>

            <Col span={2}>
              <Button className="w-full" type="primary" icon={<PlusCircleOutlined />} onClick={() => refModalChiTietPhongBan.current?.open()}>
                Tạo mới
              </Button>
            </Col>
          </Row>
        </Form>
      </div>
    );
  };

  return (
    <div id={ID_PAGE.QUAN_LY_PHONG_BAN}>
      <Table<TablePhongBanDataType>
        {...defaultTableProps}
        loading={loading}
        onRow={(record, rowIndex) => {
          return {
            style: {cursor: "pointer"}, //  thể hiện vị trí con trỏ là button(👈)
            onClick: async event => {
              if (!record || record.key?.toString().includes("empty")) return;
              const response: CommonExecute.Execute.IPhongBan | null = await layChiTietPhongBan({...record, actionCode: ACTION_CODE.CHI_TIET_PHONG_BAN});
              if (response?.ma) refModalChiTietPhongBan.current?.open(response);
            }, // click row
            // onDoubleClick: event => { }, // double click row
            // onContextMenu: event => { }, // right button click row
            // onMouseEnter: event => { }, // mouse enter row
            // onMouseLeave: event => { }, // mouse leave row
          };
        }}
        title={renderHeaderTableQuanLyPhongBan}
        pagination={{
          total: tongSoDong, //quan trọng - để hiển thị toàn bộ số lượng dòng dữ liệu phục vụ việc phân trang
          current: page, //set current page
          pageSize: pageSize, //Số lượng mục dữ liệu trên mỗi trang
          // responsive : Nếu size không được chỉ định, Pagination sẽ thay đổi kích thước theo chiều rộng của cửa sổ
          size: "default", //size của pagination : default / small
          //được gọi khi page size change
          onChange: (page, pageSize) => {
            onChangePage(page, pageSize);
          },
          showTotal: (total, range) => {
            return `${range[0]} - ${range[1]} của ${total} bản ghi`;
          },
          showQuickJumper: true, // Xác định xem bạn có thể nhảy trực tiếp đến các trang hay không
          /* showSizeChanger : Xác định xem có hiển thị pageSize select hay không, nó sẽ đúng khi tổng > 50,
                 kết hợp với pageSizeOptions, cái này có thể tuỳ chỉnh được nữa. xem DOC
          */
          showSizeChanger: false,
          position: ["bottomCenter"], //vị trí của pagination
          hideOnSinglePage: false, //ẩn pagination khi chỉ có 1 page
          pageSizeOptions: [10, 20, 50, 100], //Chỉ định các tùy chọn sizeChanger, kết hợp với showSizeChanger
          locale: {
            jump_to: "Tới trang",
            page: "",
          },
        }}
        // pagination={false}
        columns={(phongBanColumns || []).map(item => {
          // Đảm bảo item.key được định nghĩa, item.title là chuỗi và item.key không phải là "stt" trước khi thêm ô tìm kiếm
          return {
            ...item,
            ...(item.key && typeof item.title === "string" && item.key !== "stt" ? getColumnSearchProps(item.key as keyof TablePhongBanDataType, item.title) : {}),
          };
        })} //định nghĩa cột của table
        dataSource={dataTableListPhongBan}
        bordered
      />

      <ModalChiTietPhongBan ref={refModalChiTietPhongBan} danhSachPhongBan={danhSachPhongBan} />
    </div>
  );
};

QuanLyPhongBanContent.displayName = "QuanLyPhongBanContent"; //Được sử dụng trong các thông báo gỡ lỗi

export default memo(QuanLyPhongBanContent, isEqual);

// Modal T<PERSON><PERSON><PERSON>
// <PERSON><PERSON> thủ convention của codebase với SCSS

// Modal container - xoá radius header
.modal-trinh-duyet-hop-dong {
  // Remove header border radius
  .fix-textarea-height textarea.ant-input {
    height: 60px !important;
    min-height: 0 !important;
  }
  .no-header-border-radius .ant-table-thead > tr > th {
    border-radius: 0 !important;
  }

  // Highlight row được chọn
  .table-row-active {
    background-color: #96bf49 !important;

    td {
      background-color: #96bf49 !important;
      color: #fff !important;
    }
  }

  // Override hover effect cho row được chọn
  .table-row-active:hover td {
    background-color: #96bf49 !important;
    color: #fff !important;
  }
}

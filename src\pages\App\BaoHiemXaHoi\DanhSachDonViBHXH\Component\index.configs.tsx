import {IFormInput} from "@src/@types";
import {REGUlAR_EXPRESSION} from "@src/constants";
import {colWidthByKey, defaultTableColumnsProps} from "@src/hooks";
import {TableProps} from "antd";

const ruleRequired = {
  required: true,
  message: "Thông tin bắt buộc",
};

export interface IFormChiTietDonViBHXHFieldsConfig {
  ma: IFormInput;
  ten: IFormInput;
  ten_tat: IFormInput;
  ten_e: IFormInput;
  mst: IFormInput;
  dchi: IFormInput;
  dthoai: IFormInput;
  logo: IFormInput;
  stt: IFormInput;
  ma_nh: IFormInput;
  ten_nh: IFormInput;
  so_tk: IFormInput;
  ten_tk: IFormInput;
  trang_thai: IFormInput;
  trang_thai_ten: IFormInput;
  ma_ct: IFormInput;
}
export const FormChiTietDonViBHXH: IFormChiTietDonViBHXHFieldsConfig = {
  ma: {
    component: "input",
    label: "Mã đơn vị",
    name: "ma",
    placeholder: "Mã đơn vị",
    rules: [ruleRequired],
  },
  ten: {
    component: "input",
    label: "Tên đơn vị",
    name: "ten",
    placeholder: "Tên đơn vị",
    rules: [ruleRequired],
  },
  ten_tat: {
    component: "input",
    label: "Tên tắt",
    name: "ten_tat",
    placeholder: "Tên tắt",
    // rules: [ruleRequired],
  },
  ten_e: {
    component: "input",
    label: "Tên tiếng anh",
    name: "ten_e",
    placeholder: "Tên tiếng anh",
    // rules: [ruleRequired],
  },
  dchi: {
    component: "input",
    label: "Địa chỉ",
    name: "dchi",
    placeholder: "Địa chỉ",
    // rules: [ruleRequired],
  },
  dthoai: {
    component: "input",
    label: "Điện thoại",
    name: "dthoai",
    placeholder: "Điện thoại",
    rules: [
      ruleRequired,
      {
        pattern: REGUlAR_EXPRESSION.REG_PHONE,
        message: "Số điện thoại sai định dạng",
      },
    ],
  },
  mst: {
    component: "input",
    label: "Mã số thuế",
    name: "mst",
    placeholder: "Mã số thuế",
    // rules: [ruleRequired],
  },
  ma_nh: {
    component: "input",
    label: "Mã ngân hàng",
    name: "ma_nh",
    placeholder: "Mã ngân hàng",
    // rules: [ruleRequired],
  },
  ten_nh: {
    component: "select",
    label: "Tên ngân hàng",
    name: "ten_nh",
    placeholder: "Tên ngân hàng",
    // rules: [ruleRequired],
  },
  so_tk: {
    component: "input",
    label: "Số tài khoản",
    name: "so_tk",
    placeholder: "Số tài khoản",
    // rules: [ruleRequired],
  },
  ten_tk: {
    component: "input",
    label: "Tên tài khoản",
    name: "ten_tk",
    placeholder: "Tên tài khoản",
    // rules: [ruleRequired],
  },
  logo: {
    component: "input",
    label: "Logo",
    name: "logo",
    placeholder: "Logo",
    // rules: [ruleRequired],
  },
  stt: {
    component: "input",
    label: "Thứ tự hiển thị",
    name: "stt",
    placeholder: "Thứ tự hiển thị",
    rules: [ruleRequired],
  },
  trang_thai: {
    component: "select",
    label: "Trạng thái",
    name: "trang_thai",
    placeholder: "Chọn trạng thái",
    rules: [ruleRequired],
  },
  trang_thai_ten: {
    component: "select",
    label: "Trạng thái",
    name: "trang_thai_ten",
    placeholder: "Chọn trạng thái",
  },
  ma_ct: {
    component: "select",
    label: "Đại lý cấp trên",
    name: "ma_ct",
    placeholder: "Chọn đơn vị cấp trên",
    showSearch: false,
  },
};

export interface ChiTietDonViBHXHProps {
  listDoiTac: Array<CommonExecute.Execute.IDoiTac>;
  danhSachDonViBHXH: Array<CommonExecute.Execute.IDanhSachDonViBHXH>;
  //   listDonViBHXH: Array<CommonExecute.Execute.IDanhSachDonViBHXH>;
}
//định nghĩa các hàm, biến mà modal sẽ expose để component bên ngoài sử dụng thông qua ref
export interface IModalChiTietDanhSachDonViBHXHRef {
  open: (data?: CommonExecute.Execute.IDanhSachDonViBHXH) => void;
  close: () => void;
}
export interface IModalThemDonViChaRef {
  open: (data?: CommonExecute.Execute.IDanhSachDonViBHXH) => void;
  close: () => void;
}

//Danh mục đơn vị cha
export interface TableDonViBHXHChaDataType {
  key: string;
  stt?: number;
  ma?: string;
  ten?: string;
  ten_tat?: string;
  ten_e?: string;
  mst?: string;
  dchi?: string;
  dthoai?: string;
  logo?: string;
  trang_thai?: string;
  ma_ct?: string;
  cap?: number;
}

// ĐỊNH NGHĨA Column RENDER RA TỪNG Ô TRONG CỘT
//đọc https://ant.design/components/table#column để biết thêm về column
export const danhSachDonViBHXHChaColumns: TableProps<TableDonViBHXHChaDataType>["columns"] = [
  {title: "STT", dataIndex: "stt", key: "stt", width: colWidthByKey.sott, align: "center", ...defaultTableColumnsProps},
  {title: "Mã", dataIndex: "ma", key: "ma", width: 100, align: "center", ...defaultTableColumnsProps},
  {...defaultTableColumnsProps, title: "Tên", dataIndex: "ten", key: "ten", width: 200, align: "left"},
  {title: "Tên tắt", dataIndex: "ten_tat", key: "ten_tat", width: 120, align: "center", ...defaultTableColumnsProps},
  {title: "Tên tiếng anh", dataIndex: "ten_e", key: "ten_e", width: 120, align: "center", ...defaultTableColumnsProps},
  {title: "MST", dataIndex: "mst", key: "mst", width: 120, align: "center", ...defaultTableColumnsProps},
  {...defaultTableColumnsProps, title: "Địa chỉ", dataIndex: "dchi", key: "dchi", width: 200, align: "left"},
  {title: "Điện thoại", dataIndex: "dthoai", key: "dthoai", width: 120, align: "center", ...defaultTableColumnsProps},
  {title: "Logo", dataIndex: "logo", key: "logo", width: 120, align: "center", ...defaultTableColumnsProps},
];
// export type DataIndex = keyof TableDonViBHXHDataType;
export type DataIndexCha = keyof TableDonViBHXHChaDataType;
//keyof: return ra key của inteface TableKhachHangColumnDataType;
export type TableDonViBHXHChaColumnDataIndex = keyof TableDonViBHXHChaDataType;
export interface DonViBHXHChaProps {
  onSelectDonViBHXHCha: (ma_ct: CommonExecute.Execute.IDanhSachDonViBHXH | null) => void;
  chiTietDonViBHXH: CommonExecute.Execute.IDanhSachDonViBHXH | null;
}
export interface IDonViBHXHSelected {
  ma?: string;
  ten?: string;
}

import {create} from "zustand";

export interface IListChiNhanhStore {
  listChiNhanh: Array<CommonExecute.Execute.IChiNhanh>;
  setListChiNhanh: (listChiNhanh: Array<CommonExecute.Execute.IChiNhanh>) => void;
}

export const useChiNhanh = create<IListChiNhanhStore>(set => ({
  listChiNhanh: [],
  setListChiNhanh: listChiNhanh => set({listChiNhanh}),
}));

//create : hàm tạo store
// export const useChiNhanh = create(
//   //persist : middileware để lưu trạng thái vào localStorage
//   persist<IListChiNhanhStore, [], [], Pick<IListChiNhanhStore, "listChiNhanh">>(
//     //set, get phải đúng thứ tự này, nếu không sẽ báo lỗi vì sử dụng nhầm cách get/set
//     (set, get) => ({
//       //khởi tạo state menuNguoiDung từ cookie + localStorage
//       listChiNhanh: get()?.listChiNhanh || [],
//       setListChiNhanh: (listChiNhanh: Array<CommonExecute.Execute.IChiNhanh>) => set(() => ({listChiNhanh: [...listChiNhanh]})),
//     }),
//     //cấu hình persist
//     {
//       name: LOCAL_STORAGE_KEY.CHI_NHANH, //key để lưu trong localStorate
//       storage: createJSONStorage(() => localStorage), //nơi lưu trữ là localStorage
//       //chỉ định những phần nào trong state sẽ được lưu, tránh lưu những thứ k cần thiết như các hàm set
//       partialize: state => ({listChiNhanh: state.listChiNhanh}),
//     },
//   ),
// );

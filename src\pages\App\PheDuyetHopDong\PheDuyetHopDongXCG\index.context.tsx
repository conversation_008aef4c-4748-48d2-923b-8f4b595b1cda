import {createContext, useContext} from "react";

import {IPheDuyetHopDongXCGContextProps} from "./index.model";

//khởi tạo giá trị mặc định
export const HopDongTrinhDuyetContext = createContext<IPheDuyetHopDongXCGContextProps>({
  danhSachHopDongTrinhDuyetXCG: [],
  tongSoDong: 0,
  loading: false,
  timKiemPhanTrangHopDongTrinhDuyetXCG: () => Promise.resolve(),
  xemChiTietHopDongTrinhDuyet: () => Promise.resolve({}),
  handlePheDuyetHopDong: () => Promise.resolve(false),
  handleGoDuyetHopDong: () => Promise.resolve(false),
  handleTuChoiDuyetHopDong: () => Promise.resolve(false),
});

export const useHopDongTrinhDuyetContext = () => useContext(HopDongTrinhDuyetContext);

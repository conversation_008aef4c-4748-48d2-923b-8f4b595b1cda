import {ReactQuery} from "@src/@types";
import {TableMucDoTonThatXeColumnDataType} from "./index.configs";

export interface IQuanLyMucDoTonThatXeContextProps {
  listMucDoTonThatXe: Array<CommonExecute.Execute.IMucDoTonThatXe>;
  tongSoDong: number;
  loading: boolean;
  filterParams: ReactQuery.ITimKiemPhanTrangMucDoTonThatXeParams & ReactQuery.IPhanTrang;
  getListMucDoTonThatXe: (params?: ReactQuery.ITimKiemPhanTrangMucDoTonThatXeParams & ReactQuery.IPhanTrang) => Promise<void>;
  getChiTietMucDoTonThatXe: (params: TableMucDoTonThatXeColumnDataType) => Promise<CommonExecute.Execute.IMucDoTonThatXe>;
  capNhatChiTietMucDoTonThatXe: (params: ReactQuery.ICapNhatMucDoTonThatXeParams) => Promise<any>;
  setFilterParams: React.Dispatch<React.SetStateAction<ReactQuery.ITimKiemPhanTrangMucDoTonThatXeParams & ReactQuery.IPhanTrang>>;
}

import {CommonExecuteResponse, useCommonExecute} from "@src/services/react-queries";
import React, {PropsWithChildren, useEffect, useMemo, useCallback} from "react";
import {useState} from "react";
import {DanhMucNghiepVuContextProps} from "./index.model";
import {DanhMucNghiepVuContext} from "./index.context";
import {ACTION_CODE} from "@src/constants";
import {ReactQuery} from "@src/@types";
// import {defaultFormValue} from "./index.configs";
import {message} from "antd";

const DanhMucNghiepVuProvider: React.FC<PropsWithChildren> = props => {
  const {children} = props;

  const mutateUseCommonExecute = useCommonExecute();
  const [listDoiTac, setListDoiTac] = useState<Array<CommonExecute.Execute.IDoiTac>>([]);
  const [tongSoDong, setTongSoDong] = useState<number>(0);
  const [danhSachNghiepVuPhanTrang, setDanhSachNghiepVu] = useState<Array<CommonExecute.Execute.IDanhMucNghiepVu>>([]);
  const [filterParams, setFilterParams] = useState<ReactQuery.ITimKiemPhanTrangDanhSachNghiepVuParams & ReactQuery.IPhanTrang>({
    ma: "",
    ten: "",

    trang_thai: "",
    // trang: 1,
    // so_dong: 20,
  });
  useEffect(() => {
    initData();
  }, []);
  const initData = () => {
    layDanhSachNghiepVuPhanTrang(filterParams);
    getListDoiTac();
  };
  useEffect(() => {
    layDanhSachNghiepVuPhanTrang(filterParams);
  }, [filterParams]);
  /* ĐỐI TÁC */
  const getListDoiTac = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        actionCode: ACTION_CODE.LIET_KE_DANH_SACH_DOI_TAC,
      });
      setListDoiTac(response?.data?.map(item => ({...item, ten: item.ma + " - " + item.ten})));
    } catch (error) {
      console.log("getListDoiTac error ", error);
    }
  }, [mutateUseCommonExecute]);

  const layDanhSachNghiepVuPhanTrang = useCallback(
    async (body: ReactQuery.ITimKiemPhanTrangDanhSachNghiepVuParams & ReactQuery.IPhanTrang) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_DS_NGHIEP_VU,
        };

        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);

        const data = response.data.data;
        console.log("data", data);
        setDanhSachNghiepVu(data);
        setTongSoDong(response.data.tong_so_dong);
      } catch (error: any) {
        console.log("Lấy danh sách nghiệp vụ error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );
  //Lấy chi tiết 1 nghiệp vụ
  const layChiTietNghiepVu = useCallback(
    async (item: ReactQuery.IChiTietDanhMucNghiepVuParams): Promise<CommonExecute.Execute.IDanhMucNghiepVu | null> => {
      try {
        const params = {
          ma: item.ma,
          actionCode: ACTION_CODE.CHI_TIET_NGHIEP_VU,
        };
        console.log("params", params);
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        // console.log("responseData", responseData.data.lke);
        const lke = (responseData.data as {lke?: any}).lke;
        const response = lke ? (Object.values(lke)[0] as CommonExecute.Execute.IDanhMucNghiepVu) : null;
        return response;
        // return responseData.data.lke as CommonExecute.Execute.IDanhMucNghiepVu;
      } catch (error: any) {
        console.log("layChiTietNghiepVu error ", error.message || error);
        return null;
      }
    },
    [mutateUseCommonExecute],
  );
  const onUpdateDanhMucNghiepVu = useCallback(
    async (body: ReactQuery.IUpdateDanhMucNghiepVuParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.CAP_NHAT_NGHIEP_VU,
        };
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        if (responseData && (responseData.data as unknown as number) === -1) {
          message.success("Cập nhật thông tin thành công!");
          initData();
          return responseData.data as unknown as number;
        }
      } catch (error: any) {
        console.log("onUpdateDanhMucNghiepVu error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );
  //khai báo giá trị của context để truyền vào Component
  const value = useMemo<DanhMucNghiepVuContextProps>(
    () => ({
      loading: mutateUseCommonExecute.isLoading,
      tongSoDong,
      filterParams,
      danhSachNghiepVuPhanTrang,
      listDoiTac,
      setFilterParams,
      layDanhSachNghiepVuPhanTrang,
      layChiTietNghiepVu,
      onUpdateDanhMucNghiepVu,
      getListDoiTac,
    }),
    [
      danhSachNghiepVuPhanTrang,
      tongSoDong,
      mutateUseCommonExecute,
      filterParams,
      listDoiTac,
      setFilterParams,
      layDanhSachNghiepVuPhanTrang,
      layChiTietNghiepVu,
      onUpdateDanhMucNghiepVu,
      getListDoiTac,
    ],
  );

  return <DanhMucNghiepVuContext.Provider value={value}>{children}</DanhMucNghiepVuContext.Provider>;
};
export default DanhMucNghiepVuProvider;

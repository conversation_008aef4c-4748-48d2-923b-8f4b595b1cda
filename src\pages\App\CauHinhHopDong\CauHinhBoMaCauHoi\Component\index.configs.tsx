import {IFormInput} from "@src/@types";
import {colWidthByKey, defaultTableColumnsProps} from "@src/hooks";
import {TableProps} from "antd";

const ruleRequired = {
  required: true,
  message: "Thông tin bắt buộc",
};

export interface IFormChiTietDanhMucSanPhamFieldsConfig {
  ma_doi_tac_ql: IFormInput;
  ten: IFormInput;
  nv: IFormInput;
}
const FormChiTietDanhMucSanPham: IFormChiTietDanhMucSanPhamFieldsConfig = {
  ma_doi_tac_ql: {
    component: "select",
    name: "ma_doi_tac_ql",
    label: "Đối tác",
    placeholder: "Chọn đối tác",
    rules: [ruleRequired],
  },

  ten: {
    component: "input",
    name: "ten",
    label: "Tên sản phẩm",
    placeholder: "Tên sản phẩm",
    rules: [ruleRequired],
  },
  nv: {
    component: "select",
    name: "nv",
    label: "<PERSON><PERSON>ệ<PERSON> vụ",
    placeholder: "<PERSON>ọn nghiệp vụ",
    rules: [ruleRequired],
  },
};
export const TRANG_THAI_CHI_TIET_SAN_PHAM = [
  {ten: "Đang sử dụng", ma: "D"},
  {ten: "Ngừng sử dụng", ma: "K"},
];

export default FormChiTietDanhMucSanPham;
export interface Props {
  listDoiTac: Array<CommonExecute.Execute.IDoiTac>;
}

export interface IModalChiTietBoMaCauHoiRef {
  open: (data?: CommonExecute.Execute.IDanhMucSanPham) => void;
  close: () => void;
}
//Bảng câu hỏi áp dụng
export interface TableCauHoiApDungDataType {
  key: string;
  ngay_ad: string;
  bt: number;
  ma_doi_tac_ql: string;
  nv: string;
  ma_sp: string;
  ngay_cap_nhat: string;
  nguoi_cap_nhat: string;
  hanh_dong?: () => JSX.Element | null; // Định nghĩa hanh_dong là hàm trả về JSX.Element;
}
export const cauHoiApDungColumns: TableProps<TableCauHoiApDungDataType>["columns"] = [
  {
    ...defaultTableColumnsProps,
    title: "Ngày áp dụng",
    dataIndex: "ngay_ad",
    key: "ngay_ad",
  },
  {...defaultTableColumnsProps, title: "Ngày cập nhật", dataIndex: "ngay_cap_nhat", key: "ngay_cap_nhat", width: colWidthByKey.ngay_cap_nhat},
  {title: "Người cập nhật", dataIndex: "nguoi_cap_nhat", key: "nguoi_cap_nhat", width: colWidthByKey.nguoi_cap_nhat, ...defaultTableColumnsProps},
  {
    title: "Xóa",
    dataIndex: "hanh_dong",
    // key: "hanh_dong",
    width: 50,

    render: (_, record) => record.hanh_dong?.(),
    ...defaultTableColumnsProps,
  },
];
export type DataIndexCauHoiApDung = keyof TableCauHoiApDungDataType;
export interface IFormThemNgayApDungFieldsConfig {
  ngay_ad?: IFormInput;
}

export const FormThemNgayApDung: IFormThemNgayApDungFieldsConfig = {
  ngay_ad: {
    component: "date-picker",
    name: "ngay_ad",
    placeholder: "Ngày áp dụng",
    label: "Ngày áp dụng", // cho label vào thì sẽ thành input with label
    rules: [ruleRequired],
  },
};

//Bảng câu hỏi
export interface TableCauHoiDataType {
  key: string;
  bt_ap_dung: number;
  ma: string;
  ten: string;
  kieu_chon: string;
  bat_buoc: string;
  do_rong: string;
  ngay_tao?: string;
  nguoi_tao?: string;
  ngay_cap_nhat?: string;
  nguoi_cap_nhat?: string;
  trang_thai?: string;
  stt?: number;
  hanh_dong?: () => JSX.Element | null; // Định nghĩa hanh_dong là hàm trả về JSX.Element;
}
export const cauHoiColumns: TableProps<TableCauHoiDataType>["columns"] = [
  {title: "STT", dataIndex: "stt", key: "stt", width: colWidthByKey.sott, align: "center", ...defaultTableColumnsProps},
  {
    ...defaultTableColumnsProps,
    title: "Mã",
    width: 100,
    dataIndex: "ma",
    key: "ma",
  },
  {
    ...defaultTableColumnsProps,
    title: "Tên",
    align: "left",
    dataIndex: "ten",
    key: "ten",
  },
  {
    ...defaultTableColumnsProps,
    title: "Kiểu chọn",
    width: 90,

    dataIndex: "kieu_chon",
    key: "kieu_chon",
  },
  {
    ...defaultTableColumnsProps,
    title: "Bắt buộc",
    width: 90,

    dataIndex: "bat_buoc",
    key: "bat_buoc",
  },
  {
    ...defaultTableColumnsProps,
    title: "Độ rộng",
    width: 100,

    dataIndex: "do_rong",
    key: "do_rong",
  },

  // {title: "Ngày tạo", dataIndex: "ngay_tao", key: "ngay_tao", width: colWidthByKey.ngay_tao, align: "center", ...defaultTableColumnsProps},
  // {title: "Người tạo", dataIndex: "nguoi_tao", key: "nguoi_tao", width: colWidthByKey.nguoi_tao, align: "center", ...defaultTableColumnsProps},
  // {title: "Ngày cập nhật", dataIndex: "ngay_cap_nhat", key: "ngay_cap_nhat", width: colWidthByKey.ngay_cap_nhat, align: "center", ...defaultTableColumnsProps},
  // {title: "Người cập nhật", dataIndex: "nguoi_cap_nhat", key: "nguoi_cap_nhat", width: colWidthByKey.nguoi_cap_nhat, align: "center", ...defaultTableColumnsProps},
  // {title: "Trạng Thái", dataIndex: "trang_thai_ten", key: "trang_thai_ten", width: 130, align: "center", ...defaultTableColumnsProps},
  {
    title: "Xóa",
    dataIndex: "hanh_dong",
    // key: "hanh_dong",
    width: 50,

    render: (_, record) => record.hanh_dong?.(),
    ...defaultTableColumnsProps,
  },
];
export type DataIndexCauHoi = keyof TableCauHoiDataType;
//form thêm câu hỏi
export interface IModalThemCauHoiRef {
  open: (data?: CommonExecute.Execute.ICauHoi) => void;
  close: () => void;
}
export interface PropsThem {
  CauHoiApDung: number | null;
  onDataChange?: (data: TableCauHoiCTDataType[]) => void;
}
export interface IFormThemCauHoiFielsConfig {
  ma?: IFormInput;
  ten?: IFormInput;
  kieu_chon?: IFormInput;
  bat_buoc?: IFormInput;
  do_rong?: IFormInput;
  trang_thai?: IFormInput;
  stt?: IFormInput;
}
export const FormThemCauHoi: IFormThemCauHoiFielsConfig = {
  ma: {
    component: "input",
    name: "ma",
    placeholder: "Mã",
    label: "Mã câu hỏi", // cho label vào thì sẽ thành input with label
    // rules: [ruleRequired],
  },
  ten: {
    component: "input",
    name: "ten",
    placeholder: "Tên câu hỏi",
    label: "Tên câu hỏi", // cho label vào thì sẽ thành input with label
    rules: [ruleRequired],
  },
  kieu_chon: {
    component: "select",
    name: "kieu_chon",
    placeholder: "Kiểu chọn",
    label: "Kiểu chọn", // cho label vào thì sẽ thành input with label
    rules: [ruleRequired],
  },
  bat_buoc: {
    component: "select",
    name: "bat_buoc",
    placeholder: "Bắt buộc",
    label: "Bắt buộc", // cho label vào thì sẽ thành input with label
    rules: [ruleRequired],
  },
  do_rong: {
    component: "input",
    name: "do_rong",
    placeholder: "Độ rộng",
    label: "Độ rộng", // cho label vào thì sẽ thành input with label
    rules: [ruleRequired],
  },
  trang_thai: {
    component: "select",
    name: "trang_thai",
    placeholder: " Trạng thái",
    label: "Trạng thái", // cho label vào thì sẽ thành input with label
  },
};
export const KIEU_CHON = [
  {ten: "RADIO", ma: "RADIO"},
  {ten: "TEXTAREA", ma: "TEXTAREA"},
  {ten: "CHECKBOX", ma: "CHECKBOX"},
  {ten: "NUMBER", ma: "NUMBER"},
];
export const BAT_BUOC = [
  {ten: "Bắt buộc", ma: "C"},
  {ten: "Không bắt buộc", ma: "K"},
];
export const TRANG_THAI = [
  {ten: "Đang sử dụng", ma: "D"},
  {ten: "Ngưng sử dụng", ma: "K"},
];
//Bảng câu hỏi
export interface TableCauHoiCTDataType {
  key: string;
  bt_ap_dung: number;
  ma_cau_hoi: string;
  gia_tri: string;
  ten_gia_tri: string;
  mac_dinh: string;
  ngay_tao?: string;
  nguoi_tao?: string;
  ngay_cap_nhat?: string;
  nguoi_cap_nhat?: string;
  trang_thai?: string;
  stt?: number;
}
export const cauHoiCTColumns: TableProps<TableCauHoiDataType>["columns"] = [
  {
    ...defaultTableColumnsProps,
    title: "Giá trị",
    width: 200,
    dataIndex: "gia_tri",
    key: "gia_tri",
  },
  {
    ...defaultTableColumnsProps,
    title: "Tên giá trị",
    // align: "left",
    dataIndex: "ten_gia_tri",
    key: "ten_gia_tri",
  },
  {
    ...defaultTableColumnsProps,
    title: "Mặc định",
    // align: "left",
    width: 100,
    dataIndex: "mac_dinh",
    key: "mac_dinh",
  },
];
export type DataIndexCauHoiCT = keyof TableCauHoiCTDataType;
//Bảng câu hỏi CT
export interface IFormThemCauHoiCTFieldsConfig {
  gia_tri?: IFormInput;
  ten_gia_tri?: IFormInput;
  bt_ap_dung?: IFormInput;
  mac_dinh?: IFormInput;
  trang_thai?: IFormInput;
}

export const FormThemCauHoiCT: IFormThemCauHoiCTFieldsConfig = {
  gia_tri: {
    component: "input",
    name: "gia_tri",
    placeholder: "Giá trị",
    label: "Giá trị", // cho label vào thì sẽ thành input with label
  },
  ten_gia_tri: {
    component: "input",
    name: "ten_gia_tri",
    placeholder: "Tên giá trị",
    label: "Tên giá trị", // cho label vào thì sẽ thành input with label
  },
};

import {ArrowLeftOutlined, CheckOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {Button, FormInput, HeaderModal, Popcomfirm} from "@src/components";
import {useChiNhanh, useDoiTac} from "@src/hooks";
import {Col, Flex, Form, Modal, Row} from "antd";
import {forwardRef, useEffect, useImperativeHandle, useMemo, useState} from "react";
import {useQuanLyPhongBanContext} from "../index.context";
import {FormInputConfigs, initFormFields, TRANG_THAI} from "./index.configs";

interface Props {
  danhSachPhongBan: Array<CommonExecute.Execute.IDanhSachPhongBanPhanTrang>;
}

export interface IModalChiTietPhongBanRef {
  open: (data?: CommonExecute.Execute.IPhongBan) => void;
  close: () => void;
}
const {dthoa<PERSON>, ten, ma, ma_doi_tac, ma_chi_nhanh, trang_thai} = FormInputConfigs;
const ModalChiTietPhongBan = forwardRef<IModalChiTietPhongBanRef, Props>(({danhSachPhongBan}: Props, ref) => {
  useImperativeHandle(ref, () => ({
    open: (dataPhongBan?: CommonExecute.Execute.IPhongBan) => {
      setIsOpen(true);

      formChiTietPhongBan.resetFields();
      if (dataPhongBan) setChiTietPhongBan(dataPhongBan);
    },
    close: () => setIsOpen(false),
  }));

  const {listDoiTac} = useDoiTac();
  const {listChiNhanh} = useChiNhanh();
  const {onUpdate, loading} = useQuanLyPhongBanContext();
  const [formChiTietPhongBan] = Form.useForm();
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const formValues = Form.useWatch([], formChiTietPhongBan);
  const [chiTietPhongBan, setChiTietPhongBan] = useState<CommonExecute.Execute.IPhongBan | null>(null);

  const watchMaDoiTac = Form.useWatch("ma_doi_tac", formChiTietPhongBan);

  // init form data gọi vào index.configs
  useEffect(() => {
    initFormFields(formChiTietPhongBan, chiTietPhongBan);
  }, [chiTietPhongBan]);

  //xử lý validate form
  useEffect(() => {
    formChiTietPhongBan
      .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
      .then(() => {
        setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
      })
      .catch(() => {
        setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
      });
  }, [formChiTietPhongBan, formValues]);

  //Bấm Update
  const onPressUpdatePhongBan = async () => {
    try {
      const values: ReactQuery.IUpdatePhongBanParams & ReactQuery.IChiTietPhongBanParams = formChiTietPhongBan.getFieldsValue(); //lấy ra values của form
      if (chiTietPhongBan && chiTietPhongBan.stt) values.stt = chiTietPhongBan.stt;
      else values.stt = 0;
      const response = await onUpdate(values);
      if (response) {
        setIsOpen(false);
        return;
      }
      setIsOpen(false);
      // const dataChiTiet = await layChiTietPhongBan(values);
      // setChiTietPhongBan(dataChiTiet);
    } catch (error) {
      console.log("onConfirm", error);
    }
  };

  //bấm huỷ / để sau xoá dữ liệu của modal khi nào mở thì gọi lại
  const onPressDeSau = () => {
    setIsOpen(false);
    setChiTietPhongBan(null);
  };

  const listChiNhanhFilteredTheoDoiTac = useMemo(() => {
    return listChiNhanh.filter(item => item.ma_doi_tac === watchMaDoiTac);
  }, [listChiNhanh, watchMaDoiTac]);

  const renderFormInputColum = (props?: any, span = 8) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  //renderFooter Modal
  const renderFooter = () => {
    return (
      <Form.Item className="mb-0">
        <Button type="default" htmlType="reset" form="formUpdatePhongBan" onClick={onPressDeSau} className="mr-2" icon={<ArrowLeftOutlined />}>
          Quay lại
        </Button>
        <Popcomfirm
          title="Thông báo"
          onConfirm={onPressUpdatePhongBan}
          htmlType="submit"
          okText="Lưu"
          description="Bạn có chắc chắn muốn lưu thông tin phòng ban không?"
          buttonTitle={"   Lưu"}
          buttonDisable={disableSubmit}
          buttonIcon={<CheckOutlined />}
          iconPosition="end"
          loading={loading}
        />
      </Form.Item>
    );
  };
  //Render
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        title={
          <HeaderModal
            title={chiTietPhongBan ? `Chi tiết phòng ban ${chiTietPhongBan.ten}` : "Tạo mới phòng ban"}
            trang_thai_ten={chiTietPhongBan?.trang_thai_ten}
            trang_thai={chiTietPhongBan?.trang_thai}
          />
        }
        // centered
        open={isOpen}
        onOk={() => setIsOpen(false)}
        onCancel={() => {
          formChiTietPhongBan.resetFields(); // Reset form fields
          setIsOpen(false); // Close the modal
          setChiTietPhongBan(null);
        }}
        width={"60vw"}
        styles={{
          body: {
            height: "22vh",
          },
        }}
        footer={renderFooter}
        className="[&_.ant-space]:w-full">
        <Form id="formUpdatePhongBan" onFinish={onPressUpdatePhongBan} form={formChiTietPhongBan} layout="vertical">
          <Row gutter={16} className="mt-4">
            {renderFormInputColum({...ma_doi_tac, options: listDoiTac, onChange: () => formChiTietPhongBan.setFieldValue("ma_chi_nhanh", undefined), disabled: !!chiTietPhongBan})}
            {renderFormInputColum({...ma_chi_nhanh, options: listChiNhanhFilteredTheoDoiTac, disabled: !!chiTietPhongBan})}
            {renderFormInputColum({...ma, disabled: !!chiTietPhongBan})}
          </Row>
          {/* Tên + mã phòng ban + điện thoại*/}
          <Row gutter={16}>
            {renderFormInputColum({...ten})}
            {renderFormInputColum({...dthoai})}
            {renderFormInputColum({...trang_thai, options: TRANG_THAI})}
          </Row>
        </Form>
      </Modal>
    </Flex>
  );
});

ModalChiTietPhongBan.displayName = "ModalChiTietPhongBan";
export default ModalChiTietPhongBan;

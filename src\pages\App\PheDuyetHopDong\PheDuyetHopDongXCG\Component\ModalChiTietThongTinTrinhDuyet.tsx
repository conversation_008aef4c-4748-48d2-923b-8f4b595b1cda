import {CheckOutlined, CloseOutlined, UndoOutlined} from "@ant-design/icons";
import {IFormInput} from "@src/@types";
import {Button, FormInput, HeaderModal, Popcomfirm} from "@src/components";
import {Col, Flex, Form, Modal, Tag} from "antd";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useState} from "react";
import {defaultValueFormTimKiemPhanTrang} from "../index.configs";
import {useHopDongTrinhDuyetContext} from "../index.context";
import {FormPheDuyetHopDongField} from "./Constant";
import {COLOR_PALETTE} from "@src/constants";

const {nd} = FormPheDuyetHopDongField;
const ModalChiTietThongTinTrinhDuyetComponent = forwardRef(({}, ref) => {
  useImperativeHandle(ref, () => ({
    open: (chiTietHopDong?: CommonExecute.Execute.IHopDongTrinhDuyetXCG) => {
      setIsOpen(true);
      if (chiTietHopDong) setChiTietHopDongTrinhDuyet(chiTietHopDong); // nếu có dữ liệu -> set chi tiết đối tác -> là sửa
    },
    close: () => setIsOpen(false),
  }));

  const {timKiemPhanTrangHopDongTrinhDuyetXCG, loading, handlePheDuyetHopDong, handleGoDuyetHopDong, handleTuChoiDuyetHopDong} = useHopDongTrinhDuyetContext();
  const [chiTietHopDongTrinhDuyet, setChiTietHopDongTrinhDuyet] = useState<CommonExecute.Execute.IHopDongTrinhDuyetXCG | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  const [formPheDuyetHopDong] = Form.useForm();
  const formValues = Form.useWatch([], formPheDuyetHopDong);
  const [disableSubmit, setDisableSubmit] = useState<boolean>(true);
  const trangThai = chiTietHopDongTrinhDuyet?.trang_thai;

  // init form data - load dữ liệu vào form khi sửa
  useEffect(() => {
    if (chiTietHopDongTrinhDuyet) {
      const arrFormData = [];

      // Xử lý riêng cho field 'nd' - ưu tiên lấy nd_duyet hoặc nd_tchoi
      let ndValue = "";
      if (chiTietHopDongTrinhDuyet.nd_duyet) {
        ndValue = chiTietHopDongTrinhDuyet.nd_duyet;
      } else if (chiTietHopDongTrinhDuyet.nd_tchoi) {
        ndValue = chiTietHopDongTrinhDuyet.nd_tchoi;
      }
      //  else if (chiTietHopDongTrinhDuyet.nd_trinh) {
      //   ndValue = chiTietHopDongTrinhDuyet.nd_trinh;
      // }

      arrFormData.push({
        name: "nd",
        value: ndValue,
      });

      formPheDuyetHopDong.setFields(arrFormData);

      //validate sau khi setFields
      formPheDuyetHopDong
        .validateFields({validateOnly: true})
        .then(() => setDisableSubmit(false))
        .catch(() => setDisableSubmit(true));
    }
  }, [chiTietHopDongTrinhDuyet]);

  //xử lý validate form
  useEffect(() => {
    formPheDuyetHopDong
      .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
      .then(() => {
        setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
      })
      .catch(() => {
        setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
      });
  }, [formPheDuyetHopDong, formValues]);

  //đóng modal
  const closeModal = useCallback(() => {
    setIsOpen(false);
    setChiTietHopDongTrinhDuyet(null);
    formPheDuyetHopDong.resetFields();
  }, []);

  // Hanlde duyệt
  const onApprove = async () => {
    const formValues = formPheDuyetHopDong.getFieldsValue();
    try {
      const params = {
        bt: chiTietHopDongTrinhDuyet?.bt,
        nd: formValues.nd || "",
      };
      const responeApprove = await handlePheDuyetHopDong(params);
      if (responeApprove) {
        await timKiemPhanTrangHopDongTrinhDuyetXCG(defaultValueFormTimKiemPhanTrang);
        closeModal();
      }
    } catch (error) {
      console.log("onApprove", error);
    }
  };
  //hanlde gỡ duyệt
  const onGoDuyet = async () => {
    try {
      const responeUnApprove = await handleGoDuyetHopDong({bt: chiTietHopDongTrinhDuyet?.bt});
      if (responeUnApprove) {
        await timKiemPhanTrangHopDongTrinhDuyetXCG(defaultValueFormTimKiemPhanTrang);
        closeModal();
      }
    } catch (error) {
      console.log("onUnapprove", error);
    }
  };
  //handle Từ chối duyệt
  const onTuChoiDuyet = async () => {
    const formValues = formPheDuyetHopDong.getFieldsValue();
    try {
      const params = {
        bt: chiTietHopDongTrinhDuyet?.bt,
        nd: formValues.nd || "",
      };
      const responeUnApprove = await handleTuChoiDuyetHopDong(params);
      if (responeUnApprove) {
        await timKiemPhanTrangHopDongTrinhDuyetXCG(defaultValueFormTimKiemPhanTrang);
        closeModal();
      }
    } catch (error) {
      console.log("onReject", error);
    }
  };

  // RENDER
  //FOOTER
  const renderFooter = () => {
    return (
      <Form.Item className="!mb-0">
        <Button type="default" onClick={closeModal} className="mr-2" icon={<CloseOutlined />}>
          Đóng
        </Button>
        {trangThai === "C" && (
          <>
            <Popcomfirm
              title={"Duyệt hợp đồng"}
              onConfirm={onApprove}
              okText="Đồng ý"
              description="Bạn có chắc muốn duyệt hợp đồng này không?"
              buttonTitle={"Duyệt"}
              buttonIcon={<CheckOutlined />}
              loading={loading}
              className="mr-2"
              buttonDisable={disableSubmit}
            />
            <Popcomfirm
              title={"Từ chối duyệt"}
              onConfirm={onTuChoiDuyet}
              okText="Đồng ý"
              description="Bạn có chắc muốn từ chối duyệt hợp đồng này không?"
              buttonTitle={"Từ chối duyệt"}
              buttonIcon={<CloseOutlined />}
              danger
              loading={loading}
              type="default"
              buttonDisable={disableSubmit}
            />
          </>
        )}
        {trangThai === "D" && (
          <Popcomfirm
            title={"Gỡ duyệt hợp đồng"}
            onConfirm={onGoDuyet}
            okText="Đồng ý"
            description="Bạn có chắc muốn gỡ duyệt hợp đồng này không?"
            buttonTitle={"Gỡ duyệt"}
            buttonIcon={<UndoOutlined />}
            loading={loading}
            type="default"
          />
        )}
      </Form.Item>
    );
  };
  const renderFormColum = (props: IFormInput) => (
    <Col span={8}>
      <FormInput {...props} />
    </Col>
  );

  //Render
  //header modal
  const renderHeaderModal = (title: string, trang_thai: any, trang_thai_ten: any) => {
    const getStatusColor = () => {
      let color = COLOR_PALETTE.gray[70];
      if (trang_thai === "D") color = COLOR_PALETTE.green[100];
      else if (trang_thai === "T") color = COLOR_PALETTE.red[50];
      return color;
    };
    return (
      <div className="ant-modal-header flex flex-row">
        <div className="ant-modal-title !mr-2">{title}</div>
        {trang_thai_ten && (
          <Tag color={getStatusColor()} className="mb-1.5">
            {trang_thai_ten}
          </Tag>
        )}
      </div>
    );
  };
  //render modal
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        // centered
        title={renderHeaderModal("Phê duyệt hợp đồng", chiTietHopDongTrinhDuyet?.trang_thai, chiTietHopDongTrinhDuyet?.trang_thai_ten)}
        maskClosable={false}
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width={"40vw"}
        styles={{
          body: {
            height: "30vh",
            // overflowY: "auto",
            // overflowX: "hidden",
          },
        }}
        footer={renderFooter}>
        <Form form={formPheDuyetHopDong} layout="vertical" className="mt-2">
          <FormInput {...nd} disabled={trangThai === "T"} className="fix-textarea-height" />
        </Form>
      </Modal>
    </Flex>
  );
});

ModalChiTietThongTinTrinhDuyetComponent.displayName = "ModalChiTietThongTinTrinhDuyetComponent";
export const ModalChiTietThongTinTrinhDuyet = memo(ModalChiTietThongTinTrinhDuyetComponent, isEqual);

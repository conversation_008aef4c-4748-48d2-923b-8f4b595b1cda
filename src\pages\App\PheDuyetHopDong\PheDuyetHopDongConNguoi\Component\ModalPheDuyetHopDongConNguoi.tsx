import {ArrowLeftOutlined, ArrowRightOutlined, CheckOutlined, CloseOutlined, DownloadOutlined, EditOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {Button, HeaderModal, Popcomfirm} from "@src/components";
import {useHopDongConNguoiContext} from "@src/pages/App/BaoHiemConNguoi/HopDongConNguoi/index.context";
import {IModalTrinhDuyetHopDongRef, useTrinhDuyetHopDongContext} from "@src/pages/App/TrinhDuyetHopDong";
import {useNotiContext} from "@src/providers";
import {Flex, Form, message, Modal, Space, Steps} from "antd";
import dayjs from "dayjs";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useRef, useState} from "react";
import {
  FormTaoMoiHopDong,
  IModalPheDuyetHopDongRef,
  IPheDuyetHopDongStep1Ref,
  IPheDuyetHopDongStep2Ref,
  IPheDuyetHopDongStep3Ref,
  IPheDuyetHopDongStep4Ref,
  messageAlertByStep,
  ModalPheDuyetHopDongProps,
  STEP_PHE_DUYET_HOP_DONG,
} from "./Constant";
import {PheDuyetHopDongStep1} from "./PheDuyetHopDongStep1";
import {PheDuyetHopDongStep2} from "./PheDuyetHopDongStep2";
import {PheDuyetHopDongStep3} from "./PheDuyetHopDongStep3";
import {PheDuyetHopDongStep4} from "./PheDuyetHopDongStep4";
import {PheDuyetHopDongStep5} from "./PheDuyetHopDongStep5";
import {ModalChiTietThongTinTrinhDuyet, IModalChiTietThongTinTrinhDuyetRef} from "./ModalPheDuyet";
import {useHopDongTrinhDuyetContext} from "../index.context";

//định nghĩa các hàm, biến mà modal sẽ expose để component bên ngoài sử dụng thông qua ref
const ModalPheDuyetHopDongConNguoiComponent = forwardRef<IModalPheDuyetHopDongRef, ModalPheDuyetHopDongProps>(({}: ModalPheDuyetHopDongProps, ref) => {
  useImperativeHandle(ref, () => ({
    open: (dataHopDong?: CommonExecute.Execute.IHopDongConNguoi) => {
      setIsOpen(true);
      setDisableSubmit(true);
      if (dataHopDong) {
        setChiTietHopDong(dataHopDong);
      }
    },
    close: () => setIsOpen(false),
  }));

  const {
    timKiemPhanTrangHopDongBaoHiem,
    getChiTietHopDong,
    updateHopDong,
    luuThongTinNguoiDuocBaoHiem,
    timKiemPhanTrangNguoiDuocBaoHiem,
    setChiTietNguoiDuocBaoHiem,
    luuDieuKhoanNguoiDuocBaoHiem,
    luuDieuKhoanBoSungNguoiDuocBaoHiem,
    luuDanhGiaSucKhoe,
    loading,
    chiTietNguoiDuocBaoHiem,
  } = useHopDongConNguoiContext();

  const {noti} = useNotiContext();
  const {xemChiTietHopDongTrinhDuyet} = useHopDongTrinhDuyetContext();

  const refPheDuyetHopDongStep1 = useRef<IPheDuyetHopDongStep1Ref>(null);
  const refPheDuyetHopDongStep2 = useRef<IPheDuyetHopDongStep2Ref>(null);
  const refPheDuyetHopDongStep3 = useRef<IPheDuyetHopDongStep3Ref>(null);
  const refPheDuyetHopDongStep4 = useRef<IPheDuyetHopDongStep4Ref>(null);
  const refModalTrinhDuyetHopDong = useRef<IModalTrinhDuyetHopDongRef>(null);
  const refModalChiTietThongTinTrinhDuyet = useRef<IModalChiTietThongTinTrinhDuyetRef>(null);

  // Chỉ sử dụng local state để lưu dữ liệu từ props
  const [chiTietHopDong, setChiTietHopDong] = useState<CommonExecute.Execute.IHopDongConNguoi | null>(null);

  const [step, setStep] = useState<number>(0);
  const [isOpen, setIsOpen] = useState<boolean>(false);

  const [formThongTinHopDong] = Form.useForm();
  const [formThongTinNguoiBaoHiem] = Form.useForm();

  const [disableSubmit, setDisableSubmit] = useState<boolean>(true);
  const [isChangeData, setIsChangeData] = useState<boolean>(false); //CHECK XEM DATA CÓ ĐANG THAY ĐỔI MÀ CHƯA LƯU HAY KHÔNG

  // State cho download PDF function
  const [downloadPDFFunction, setDownloadPDFFunction] = useState<(() => void) | null>(null);
  const [hasSelectedFile, setHasSelectedFile] = useState<boolean>(false);

  // Callback để nhận download function từ PheDuyetHopDongStep5
  const handleDownloadPDFCallback = useCallback((downloadFn: () => void, hasFile: boolean) => {
    setDownloadPDFFunction(() => downloadFn);
    setHasSelectedFile(hasFile);
  }, []);

  // Function để mở Modal cấu hình bệnh viện
  const handleOpenModalCauHinhBenhVien = useCallback(() => {
    if (!chiTietHopDong?.so_id || !chiTietNguoiDuocBaoHiem?.so_id_dt) {
      console.warn("Chưa chọn hợp đồng hoặc người được bảo hiểm");
      return;
    }

    refPheDuyetHopDongStep2.current?.openModalCauHinhBenhVien(Number(chiTietHopDong.so_id), Number(chiTietNguoiDuocBaoHiem.so_id_dt));
  }, [chiTietHopDong, chiTietNguoiDuocBaoHiem]);

  // Function để mở Modal chi tiết thông tin trình duyệt
  const handleOpenModalChiTietThongTinTrinhDuyet = useCallback(async () => {
    if (!chiTietHopDong?.so_id) {
      console.warn("Chưa có thông tin hợp đồng để xem chi tiết trình duyệt");
      return;
    }

    try {
      // Sử dụng so_id làm bt parameter
      const response = await xemChiTietHopDongTrinhDuyet({bt: chiTietHopDong.so_id});
      console.log("🚀 ~ response:", response);
      if (response) {
        refModalChiTietThongTinTrinhDuyet.current?.open(response);
      }
    } catch (error) {
      console.error("Lỗi khi lấy chi tiết thông tin trình duyệt:", error);
    }
  }, [chiTietHopDong, xemChiTietHopDongTrinhDuyet]);

  const allFormValuesHopDong = Form.useWatch([], formThongTinHopDong);
  const allFormValuesNguoiBaoHiem = Form.useWatch([], formThongTinNguoiBaoHiem);

  // init form data
  useEffect(() => {
    if (chiTietHopDong) {
      fillValueVaoForm();
    }
  }, [chiTietHopDong]);

  const fillValueVaoForm = useCallback(async () => {
    const arrFormData = [];
    const arrInputFormThongTinHopDong = Object.keys(FormTaoMoiHopDong); //lấy ra key của form
    //chỉ điền những thuộc tính cần hiển thị lên input
    for (const key in chiTietHopDong) {
      if (arrInputFormThongTinHopDong.includes(key)) {
        let value: any = chiTietHopDong[key as keyof CommonExecute.Execute.IHopDongConNguoi];
        //xử lý các key đặc biệt
        if (key === "ngay_cap") value = dayjs(value + "", "YYYYMMDD").isValid() ? dayjs(value + "", "YYYYMMDD") : "";
        else if (key === "ngay_hl") value = dayjs(value + "", "YYYYMMDD").isValid() ? dayjs(value + "", "YYYYMMDD") : "";
        else if (key === "gio_hl") value = dayjs(value + "", "HH:mm").isValid() ? dayjs(value + "", "HH:mm") : "";
        else if (key === "ngay_kt") value = dayjs(value + "", "YYYYMMDD").isValid() ? dayjs(value + "", "YYYYMMDD") : "";
        else if (key === "gio_kt") value = dayjs(value + "", "HH:mm").isValid() ? dayjs(value + "", "HH:mm") : "";
        else if (key === "vip") value = value == "VIP" ? value : "K";

        arrFormData.push({
          name: key as keyof CommonExecute.Execute.IHopDongConNguoi,
          value: value,
        });
      }
    }
    //đổ data vào form
    formThongTinHopDong.setFields(arrFormData);
    //xử lý riêng input khách hàng
    formThongTinHopDong.setFieldValue("ma_kh", {
      ten: chiTietHopDong?.ten_kh,
      ma: chiTietHopDong?.ma_kh,
      key: chiTietHopDong?.ma_kh,
    });
    //xử lý riêng input cán bộ
    formThongTinHopDong.setFieldValue("ma_cb_ql", {
      ten: chiTietHopDong?.ten_cb_ql,
      ma: chiTietHopDong?.ma_cb_ql,
      key: chiTietHopDong?.ma_cb_ql,
    });

    //xử lý riêng input cán bộ
    formThongTinHopDong.setFieldValue("daily_kt", {
      ten: chiTietHopDong?.ten_daily_kt,
      ma: chiTietHopDong?.daily_kt,
      key: chiTietHopDong?.daily_kt,
    });
  }, [formThongTinHopDong, chiTietHopDong]);

  //xử lý disable nút Lưu khi form chưa valid
  useEffect(() => {
    if (step === 0) {
      formThongTinHopDong
        .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
        .then(response => {
          setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
        })
        .catch(error => {
          //xử lý trường hợp khi back từ step 2 về step 1 -> dính lỗi so_hd_g
          if (allFormValuesHopDong.kieu_hd === "G" && error?.errorFields[0]?.name[0] === "so_hd_g") {
            setDisableSubmit(false); // nếu có lỗi -> cho disable nút Lưu
          } else setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
        });
    } else if (step === 1) {
      formThongTinNguoiBaoHiem
        .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
        .then(response => {
          setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
        })
        .catch(error => {
          if (error.errorFields.length > 0) setDisableSubmit(true);
          else setDisableSubmit(false); // nếu có lỗi -> cho disable nút Lưu
        });
    } else if (step === 2) {
      setDisableSubmit(false);
    }
  }, [
    // form thông tin hợp đồng
    formThongTinHopDong,
    allFormValuesHopDong,
    //form đối tượng bảo hiểm
    formThongTinNguoiBaoHiem,
    allFormValuesNguoiBaoHiem,
    step,
    refPheDuyetHopDongStep2,
  ]);

  const closeModal = useCallback(() => {
    setIsOpen(false);
    setChiTietHopDong(null);
    setChiTietNguoiDuocBaoHiem(undefined);
    formThongTinHopDong.resetFields();
    formThongTinNguoiBaoHiem.resetFields();
    setStep(0);
    setIsChangeData(false);
  }, [formThongTinHopDong, formThongTinNguoiBaoHiem]);

  //Bấm nút  ĐÓNG / QUAY LẠI / TIẾP THEO / LƯU / LƯU VÀ ĐÓNG
  const onConfirm = async (action: "Lưu" | "Lưu và đóng" | "Lưu và chuyển tiếp" | "Lưu và quay lại" | "", newStep?: number) => {
    try {
      // STEP === 0 -> LƯU THÔNG TIN HỢP ĐỒNG
      if (step === 0) luuThongTinHopDongStep1(action, newStep);
      // STEP === 1 -> LƯU THÔNG TIN NGƯỜI ĐƯỢC BẢO HIỂM
      else if (step === 1) {
        const currentTab = refPheDuyetHopDongStep2.current?.getCurrentTab();
        if (currentTab === "1") luuThongTinNguoiDuocBaoHiemStep2(action, newStep);
        else if (currentTab === "2") luuQuyenLoiChinhStep2(action, newStep);
        else if (currentTab === "3") luuQuyenLoiBoSungStep2(action, newStep);
        else if (currentTab === "5") luuDanhGiaSucKhoeStep2(action, newStep);
      }
      setIsChangeData(false);
    } catch (error) {
      console.log("onConfirm error", error);
    }
  };

  // LƯU THÔNG TIN HỢP ĐỒNG
  const luuThongTinHopDongStep1 = async (action: "Lưu" | "Lưu và đóng" | "Lưu và chuyển tiếp" | "Lưu và quay lại" | "", newStep?: number) => {
    const values: ReactQuery.IUpdateHopDongParams & {
      ma_kh: {
        ma: string;
        ten: string;
        key: string;
      };
      ma_cb_ql: {
        ma: string;
        ten: string;
        key: string;
      };
      daily_kt: {
        ma: string;
        ten: string;
        key: string;
      }; //bổ sung type này vào do ma_kh đang bị định dạng theo string
    } = formThongTinHopDong.getFieldsValue(); //lấy ra values của form
    console.log("values", values);

    const params: ReactQuery.IUpdateHopDongParams = {
      ...values,
      so_id: chiTietHopDong ? chiTietHopDong.so_id : undefined,
      nv: "NG",
      ma_kh: values.ma_kh.ma || "",
      ma_cb_ql: values.ma_cb_ql.ma || "",
      daily_kt: values.daily_kt.ma || "",
      ngay_cap: dayjs(values.ngay_cap).format("YYYYMMDD"),
      gio_hl: dayjs(values.gio_hl).format("HH:mm"),
      ngay_hl: dayjs(values.ngay_hl).format("YYYYMMDD"),
      gio_kt: dayjs(values.gio_kt).format("HH:mm"),
      ngay_kt: dayjs(values.ngay_kt).format("YYYYMMDD"),
      vip: values.vip === "K" ? "" : values.vip,
    };
    console.log("params", params);
    const response = await updateHopDong(params); //cập nhật lại đơn vị chi nhánh
    if (response.data === -1) {
      await timKiemPhanTrangHopDongBaoHiem(); //lấy lại danh sách đơn vị chi nhánh
      message.success((!chiTietHopDong ? "Tạo mới" : "Cập nhật") + " thành công ");
      if (action === "Lưu và đóng") setIsOpen(false);
      if (action === "Lưu và chuyển tiếp") {
        if (newStep !== undefined) setStep(newStep);
        else setStep(1);
      }
    }
  };

  //LƯU THÔNG TIN NGƯỜI ĐƯỢC BẢO HIỂM STEP 2
  const luuThongTinNguoiDuocBaoHiemStep2 = async (action: "Lưu" | "Lưu và đóng" | "Lưu và chuyển tiếp" | "Lưu và quay lại" | "", newStep?: number) => {
    const values: ReactQuery.ICapNhatThongTinNguoiDuocBaoHiemHopDongConNguoi = formThongTinNguoiBaoHiem.getFieldsValue(); //bổ sung type này vào do ma_kh đang bị định dạng theo string //lấy ra values của form
    console.log("values", values);
    // ĐANG LÀM DỞ Ở ĐÂY

    const params: ReactQuery.ICapNhatThongTinNguoiDuocBaoHiemHopDongConNguoi = {
      so_id: chiTietHopDong?.so_id,
      so_id_dt: chiTietNguoiDuocBaoHiem?.so_id_dt,
      ...values,
      ngay_cap: dayjs(values.ngay_cap).format("YYYYMMDD"),
      gio_hl: dayjs(values.gio_hl).format("HH:mm"),
      ngay_hl: dayjs(values.ngay_hl).format("YYYYMMDD"),
      gio_kt: dayjs(values.gio_kt).format("HH:mm"),
      ngay_kt: dayjs(values.ngay_kt).format("YYYYMMDD"),
      ngay_sinh: dayjs(values.ngay_sinh).format("YYYYMMDD"),
    };

    const response = await luuThongTinNguoiDuocBaoHiem(params); //cập nhật lại đơn vị chi nhánh
    if (response.data === -1) {
      await timKiemPhanTrangNguoiDuocBaoHiem();
      message.success((!chiTietNguoiDuocBaoHiem ? "Tạo mới" : "Cập nhật") + " thành công ");
      if (action === "Lưu và đóng") setIsOpen(false);
      else if (action === "Lưu và chuyển tiếp") {
        if (newStep !== undefined) setStep(newStep);
        else setStep(2);
      } else if (action === "Lưu và quay lại") setStep(0);
      // else setStep(2);
    }
    console.log("response", response);
  };

  //LƯU QUYỀN LỢI CHÍNH STEP 2
  const luuQuyenLoiChinhStep2 = async (action: "Lưu" | "Lưu và đóng" | "Lưu và chuyển tiếp" | "Lưu và quay lại" | "", newStep?: number) => {
    const listQuyenLoi = refPheDuyetHopDongStep2.current?.getListQuyenLoiNguoiDuocBaoHiem() || [];
    const checboxApDungGoiBaoHiem = refPheDuyetHopDongStep2.current?.getCheckboxApDungGoiBaohiemQuyenLoi() || false;
    const params: ReactQuery.ILuuDieuKhoanNguoiDuocBaoHiemParams = {
      so_id: chiTietHopDong ? chiTietHopDong.so_id : 0,
      so_id_dt: chiTietNguoiDuocBaoHiem ? +chiTietNguoiDuocBaoHiem.so_id_dt : 0,
      ad_goi_bh: checboxApDungGoiBaoHiem ? "C" : "K",
      dk: [], //QUYỀN LỢI
    };
    //QUYỀN LỢI BẢO HIỂM
    for (let i = 0; i < listQuyenLoi.length; i++) {
      const itemQuyenLoi = listQuyenLoi[i];
      params.dk?.push({...itemQuyenLoi});
    }
    console.log("paramsCapNhat", params);
    const response = await luuDieuKhoanNguoiDuocBaoHiem(params); //cập nhật lại đơn vị chi nhánh
    console.log("response", response);
    if (response === -1) {
      message.success("Cập nhật thành công");

      // Gọi lại danh sách đối tượng để làm mới dữ liệu
      refPheDuyetHopDongStep2.current?.refreshData();

      if (action === "Lưu và đóng") setIsOpen(false);
      else if (action === "Lưu và chuyển tiếp") {
        if (newStep !== undefined) setStep(newStep);
        else setStep(2);
      } else if (action === "Lưu và quay lại") setStep(0);
    }
  };

  //LƯU QUYỀN LỢI BỔ SUNG STEP 2
  const luuQuyenLoiBoSungStep2 = async (action: "Lưu" | "Lưu và đóng" | "Lưu và chuyển tiếp" | "Lưu và quay lại" | "", newStep?: number) => {
    const listQuyenLoi = refPheDuyetHopDongStep2.current?.getListQuyenLoiNguoiDuocBaoHiem() || [];
    const checboxApDungGoiBaoHiem = refPheDuyetHopDongStep2.current?.getCheckboxApDungGoiBaohiemQuyenLoiBoSung() || false;
    const params: ReactQuery.ILuuDieuKhoanBoSungNguoiDuocBaoHiemParams = {
      so_id: chiTietHopDong ? chiTietHopDong.so_id : 0,
      so_id_dt: chiTietNguoiDuocBaoHiem ? +chiTietNguoiDuocBaoHiem.so_id_dt : 0,
      ad_goi_bh: checboxApDungGoiBaoHiem ? "C" : "K",
      dk: [], //QUYỀN LỢI
    };
    //QUYỀN LỢI BẢO HIỂM
    for (let i = 0; i < listQuyenLoi.length; i++) {
      const itemQuyenLoi = listQuyenLoi[i];
      params.dk?.push({...itemQuyenLoi});
    }
    console.log("paramsCapNhat", params);
    const response = await luuDieuKhoanBoSungNguoiDuocBaoHiem(params); //cập nhật lại đơn vị chi nhánh
    if (response === -1) {
      message.success("Cập nhật thành công");
      if (action === "Lưu và đóng") setIsOpen(false);
      else if (action === "Lưu và chuyển tiếp") {
        if (newStep !== undefined) setStep(newStep);
        else setStep(2);
      } else if (action === "Lưu và quay lại") setStep(0);
    }
  };

  //LƯU ĐÁNH GIÁ SỨC KHOẺ STEP 2
  const luuDanhGiaSucKhoeStep2 = async (action: "Lưu" | "Lưu và đóng" | "Lưu và chuyển tiếp" | "Lưu và quay lại" | "", newStep?: number) => {
    // Validate dữ liệu trước khi lưu
    const validation = refPheDuyetHopDongStep2.current?.validateDanhGiaSucKhoe();
    if (!validation?.isValid) {
      message.error("Vui lòng trả lời đầy đủ các câu hỏi bắt buộc");
      return;
    }

    // Lấy dữ liệu để lưu
    const saveData = refPheDuyetHopDongStep2.current?.getDataForSaveDanhGiaSucKhoe();
    if (!saveData || saveData.dgsk.length === 0) {
      message.warning("Không có dữ liệu đánh giá sức khoẻ để lưu!");
      return;
    }

    const params: ReactQuery.ILuuDanhGiaSucKhoeConNguoiParams = {
      so_id: chiTietHopDong ? Number(chiTietHopDong.so_id) : 0,
      so_id_dt: chiTietNguoiDuocBaoHiem ? Number(chiTietNguoiDuocBaoHiem.so_id_dt) : 0,
      dgsk: saveData.dgsk,
    };

    const response = await luuDanhGiaSucKhoe(params);
    if (response) {
      // Refresh data sau khi lưu thành công
      refPheDuyetHopDongStep2.current?.refreshDanhGiaSucKhoe();

      if (action === "Lưu và đóng") setIsOpen(false);
      else if (action === "Lưu và chuyển tiếp") {
        if (newStep !== undefined) setStep(newStep);
      } else if (action === "Lưu và quay lại") {
        if (newStep !== undefined) setStep(newStep);
      }
    }
  };

  //LẤY TITLE CỦA BUTTON BACK THEO STEP
  const getTitleButtonBackByStep = useCallback((current: number) => {
    if (current === 0) return "Đóng";
    return "Quay lại";
  }, []);

  const getOkTextBtnBackByStep = useCallback(
    (current: number) => {
      if (current === 0) return "Lưu và đóng";
      return "Lưu và quay lại";
    },
    [refPheDuyetHopDongStep2],
  );

  const getCancelTextBtnBackByStep = useCallback(
    (current: number) => {
      if (current === 0) return "Đóng";
      return "Quay lại";
    },
    [refPheDuyetHopDongStep2],
  );

  const getDescriptionDongVaTiepTheoByStep = (current: number) => {
    if (current === 0) return "Bạn chưa lưu Thông tin hợp đồng!";
    else if (current === 1) {
      if (refPheDuyetHopDongStep2.current?.getCurrentTab() === "1") return "Bạn chưa lưu Thông tin đối tượng bảo hiểm!";
      else if (refPheDuyetHopDongStep2.current?.getCurrentTab() === "2") return "Bạn chưa lưu Quyền lợi chính!";
    }
  };

  //LẤY TITLE CỦA BUTTON BACK THEO STEP
  const getIconButtonBackByCurrentStep = useCallback((current: number) => {
    if (current == 0) return <CloseOutlined />;
    return <ArrowLeftOutlined />;
  }, []);

  const onBack = () => {
    if (step === 0) return closeModal();
    setStep(step - 1);
  };

  const onClickBtnTiepTheo = () => {
    if (step === 4) return;
    setStep(step + 1);
  };

  const onChangeStep = (newStep: number) => {
    if (isChangeData) {
      const btn = (
        <Space>
          <Button
            type="link"
            onClick={() => {
              setStep(newStep);
              noti.destroy();
            }}>
            Chuyển bước {newStep + 1}
          </Button>
          <Button
            type="primary"
            onClick={async () => {
              await onConfirm(newStep > step ? "Lưu và chuyển tiếp" : "Lưu và quay lại", newStep);
              noti.destroy();
            }}>
            Lưu và chuyển bước {newStep + 1}
          </Button>
        </Space>
      );
      noti.warning({
        message: "Thông báo",
        description: getDescriptionDongVaTiepTheoByStep(step),
        btn,
        placement: "topRight",
      });
    } else setStep(newStep);
  };

  // RENDER
  //FOOTER
  const renderFooter = () => {
    const showButtonHuy = Number(chiTietHopDong?.ngay_huy_num) >= 30000101;
    const showButtonGoHuy = Number(chiTietHopDong?.ngay_huy_num) < 30000101;
    const showButtonHuyTrinh = Number(chiTietHopDong?.ngay_trinh_num) < 30000101;
    return (
      <div className="flex w-full items-center justify-between">
        <div>
          {/* BUTTON HUỶ TRÌNH */}
          {/* {step === 4 && showButtonHuyTrinh && (
            <Popcomfirm
              danger
              title="Thông báo"
              onConfirm={() => {
                // huyTrinhPheDuyetHopDong();
              }}
              okText="Đồng ý"
              description="Bạn có chắc chắn muốn huỷ trình hợp đồng này hay không?"
              buttonTitle="Huỷ trình"
              buttonClassName="mr-2"
              buttonIcon={<CloseOutlined />}
              type="default"
            />
          )} */}

          {/* BUTTON TẢI XUỐNG PDF CHO STEP 4 */}
          {step === 4 && downloadPDFFunction && hasSelectedFile && (
            <Button type="primary" onClick={downloadPDFFunction} className="mr-2" icon={<DownloadOutlined />}>
              Tải xuống PDF
            </Button>
          )}

          {/* CÁC BUTTON CHƯA LÀM ACTION */}
        </div>
        {/* Bên phải: các nút còn lại */}
        <div>
          {/* BUTTON PHÊ DUYỆT */}
          {chiTietHopDong && (
            <Button type="primary" onClick={handleOpenModalChiTietThongTinTrinhDuyet} className="mr-2" icon={<CheckOutlined />}>
              Phê Duyệt
            </Button>
          )}

          {/* {step === 2 && (
            <Button
              type="primary"
              onClick={() => refModalDanhGiaTonThat?.current?.open({so_id: chiTietHopDongBaoHiemXe?.so_id, so_id_dt: chiTietDoiTuongBaoHiemXe?.gcn?.so_id_dt || 0})}
              className="mr-2"
              icon={<FileAddOutlined />}
              loading={loading}
              disabled={disableSubmit}>
              Đánh giá rủi do
            </Button>
          )} */}
          {isChangeData ? (
            <Popcomfirm
              title="Thông báo"
              onConfirm={() => onConfirm(step === 0 ? "Lưu và đóng" : "Lưu và quay lại")}
              onCancel={onBack}
              okText={getOkTextBtnBackByStep(step)}
              cancelText={getCancelTextBtnBackByStep(step)}
              description={getDescriptionDongVaTiepTheoByStep(step)}
              buttonTitle={getTitleButtonBackByStep(step)}
              buttonDisable={disableSubmit}
              buttonClassName="ml-2"
              buttonIcon={getIconButtonBackByCurrentStep(step)}
              loading={loading}
              buttonType={"default"}
            />
          ) : (
            <Button type="default" onClick={onBack} icon={getIconButtonBackByCurrentStep(step)}>
              {getTitleButtonBackByStep(step)}
            </Button>
          )}

          {step < 4 && (
            <>
              {isChangeData ? (
                <Popcomfirm
                  title="Thông báo"
                  onConfirm={() => onConfirm("Lưu và chuyển tiếp")}
                  onCancel={onClickBtnTiepTheo}
                  okText="Lưu và chuyển tiếp"
                  cancelText="Bỏ qua không lưu"
                  description={getDescriptionDongVaTiepTheoByStep(step)}
                  buttonTitle="Tiếp theo"
                  buttonDisable={disableSubmit}
                  buttonClassName="ml-2"
                  buttonIcon={<ArrowRightOutlined />}
                  loading={loading}
                  buttonIconPosition="end"
                />
              ) : (
                <Button disabled={!chiTietHopDong} type="primary" onClick={onClickBtnTiepTheo} className="ml-2" iconPosition="end" icon={<ArrowRightOutlined />}>
                  Tiếp theo
                </Button>
              )}
            </>
          )}

          {/* Button Trình duyệt - chỉ hiển thị khi step 4 và chưa trình */}
          {step === 4 && !showButtonHuyTrinh && (
            <Button
              disabled={!chiTietHopDong}
              type="primary"
              onClick={() => refModalTrinhDuyetHopDong?.current?.open()}
              className="ml-2"
              icon={<EditOutlined />}
              title={!chiTietHopDong ? "Cần có chi tiết hợp đồng để trình duyệt" : undefined}>
              Trình duyệt
            </Button>
          )}

          {/* Button Huỷ trình - chỉ hiển thị khi step 4 và đã trình */}
          {/* {step === 4 && showButtonHuyTrinh && <HuyTrinhDuyetButtonConNguoi chiTietHopDong={chiTietHopDong} />} */}
          {/* <Button
            disabled={!chiTietHopDong}
            type="primary"
            onClick={() => {
              onClickBtnTiepTheo();
            }}
            className="ml-2"
            iconPosition="end"
            icon={<ArrowRightOutlined />}>
            Tiếp theo
          </Button> */}
          {/* {(step === 0 || step === 1) && (
            <>
              <Button type="primary" onClick={() => onConfirm("Lưu")} className="ml-2" icon={<CheckOutlined />} loading={loading} disabled={disableSubmit}>
                Lưu
              </Button>
              <Popcomfirm
                title="Thông báo"
                onConfirm={() => onConfirm("Lưu và đóng")}
                okText="Lưu"
                description={messageAlertByStep[step]}
                buttonTitle="Lưu và đóng"
                buttonDisable={disableSubmit}
                buttonClassName="ml-2"
                buttonIcon={<CheckOutlined />}
                loading={loading}
              />
            </>
          )} */}
        </div>
      </div>
    );
  };

  //Render
  return (
    <Flex vertical gap="middle" align="center">
      <Modal
        title={
          <HeaderModal
            title={chiTietHopDong ? `Chi tiết hợp đồng ${chiTietHopDong.so_hd}` : "Tạo mới hợp đồng"}
            trang_thai_ten={chiTietHopDong?.trang_thai_ten}
            trang_thai={chiTietHopDong?.trang_thai}
          />
        }
        destroyOnClose={true}
        className="modal-them-hop-dong-con-nguoi"
        maskClosable={false}
        centered
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width="100vw"
        styles={{
          body: {
            height: "80vh",
          },
        }}
        footer={renderFooter}>
        <Steps current={step} items={STEP_PHE_DUYET_HOP_DONG} percent={(step + 1) * 20} onChange={onChangeStep} size="small" />
        {step === 0 && <PheDuyetHopDongStep1 ref={refPheDuyetHopDongStep1} formThongTinHopDong={formThongTinHopDong} setIsChangeData={setIsChangeData} chiTietHopDong={chiTietHopDong} />}
        {step === 1 && <PheDuyetHopDongStep2 ref={refPheDuyetHopDongStep2} formThongTinNguoiBaoHiem={formThongTinNguoiBaoHiem} setIsChangeData={setIsChangeData} />}
        {step === 2 && <PheDuyetHopDongStep3 ref={refPheDuyetHopDongStep3} />}
        {step === 3 && <PheDuyetHopDongStep4 ref={refPheDuyetHopDongStep4} />}
        {step === 4 && <PheDuyetHopDongStep5 ref={refPheDuyetHopDongStep4} onDownloadPDF={handleDownloadPDFCallback} />}
        {/* MODAL TRÌNH DUYỆT HỢP ĐỒNG */}
      </Modal>

      {/* MODAL CHI TIẾT THÔNG TIN TRÌNH DUYỆT */}
      <ModalChiTietThongTinTrinhDuyet ref={refModalChiTietThongTinTrinhDuyet} />
    </Flex>
  );
});

// Component con để xử lý hủy trình duyệt sử dụng TrinhDuyetHopDong context
const HuyTrinhDuyetButtonConNguoi: React.FC<{chiTietHopDong: any}> = ({chiTietHopDong}) => {
  const {huyTrinhPheDuyetHopDong} = useTrinhDuyetHopDongContext();

  const handleHuyTrinh = async () => {
    await huyTrinhPheDuyetHopDong({
      so_id: chiTietHopDong.so_id,
      nv: chiTietHopDong.nv,
    });
  };

  return (
    <Popcomfirm
      danger
      title="Thông báo"
      onConfirm={handleHuyTrinh}
      okText="Đồng ý"
      description="Bạn có chắc chắn muốn huỷ trình hợp đồng này hay không?"
      buttonTitle="Huỷ trình"
      buttonClassName="ml-2"
      buttonIcon={<CloseOutlined />}
      type="default"
    />
  );
};

ModalPheDuyetHopDongConNguoiComponent.displayName = "ModalPheDuyetHopDongConNguoiComponent";
export const ModalPheDuyetHopDongConNguoi = memo(ModalPheDuyetHopDongConNguoiComponent, isEqual);

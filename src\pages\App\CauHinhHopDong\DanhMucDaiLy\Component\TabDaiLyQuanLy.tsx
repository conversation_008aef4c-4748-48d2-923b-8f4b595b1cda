import {Checkbox, FormInput, Highlighter, Select, TableFilterDropdown} from "@src/components";
import {defaultTableProps, fillRowTableEmpty} from "@src/hooks";
import {Row, Table, Input, InputRef} from "antd";
import {FilterDropdownProps} from "antd/es/table/interface";
import {useCallback, useEffect, useMemo, useRef, useState} from "react";
import {SearchOutlined} from "@ant-design/icons";
import {DataIndexDLQL, daiLyQuanLyColumns, TableDaiLyQuanLyDataType} from "./index.configs";
import {useDanhMucDaiLyContext} from "../index.context";
import {Col} from "antd/lib";
// import Highlighter from "react-highlight-words";

interface TabDaiLyQuanLyProps {
  chiTietDaiLy: CommonExecute.Execute.IChiTietDanhMucDaiLy["dl"] | null;
  onDataChange?: (data: TableDaiLyQuanLyDataType[]) => void;
}

const TabDaiLyQuanLy: React.FC<TabDaiLyQuanLyProps> = ({onDataChange}) => {
  const {loading, daiLyQuanLy, setDaiLyQuanLySelected, listDoiTac} = useDanhMucDaiLyContext();
  const [searchedColumn, setSearchedColumn] = useState("");
  const [searchText, setSearchText] = useState("");
  const [dataSource, setDataSource] = useState<TableDaiLyQuanLyDataType[]>([]);
  const [tableSearchText, setTableSearchText] = useState(""); // Ô tìm kiếm cho bảng Đơn Vị Quản Lý
  const [isAllChecked, setIsAllChecked] = useState(false); // State để theo dõi checkbox chọn tất cả
  const refSearchInputTable = useRef<InputRef>(null);
  const [selectedDoiTac, setSelectedDoiTac] = useState<string | undefined>();
  const filteredDonViQuanLy = dataSource.filter(item => {
    const textMatch = Object.values(item).some(value => typeof value === "string" && value.toLowerCase().includes(tableSearchText.toLowerCase()));
    const doiTacMatch = selectedDoiTac ? item.ma_doi_tac_ql === selectedDoiTac : true;
    return textMatch && doiTacMatch;
  });

  // Trong component TabDaiLyQuanLy
  useEffect(() => {
    // Kiểm tra trạng thái checkbox trong filteredDonViQuanLy
    const allChecked = filteredDonViQuanLy.every(item => item.key.includes("empty") || item.is_checked === "1");
    setIsAllChecked(allChecked);
  }, [filteredDonViQuanLy]);

  const dataTableListDonViQuanLy = useMemo<Array<TableDaiLyQuanLyDataType>>(() => {
    try {
      const tableData = Array.isArray(daiLyQuanLy) ? daiLyQuanLy : [];
      const mappedData = tableData.map((item: any, index: number) => ({
        stt: item.stt ?? index + 1,
        ma_doi_tac_ql: item.ma_doi_tac_ql || "",
        ten_chi_nhanh_ql: item.ten_chi_nhanh_ql || "",
        ten_doi_tac_ql: item.ten_doi_tac_ql || "",
        ma_chi_nhanh_ql: item.ma_chi_nhanh_ql || "",
        is_checked: item.is_checked ? "1" : "0",
        key: `${item.ma_doi_tac_ql}_${item.ma_chi_nhanh_ql}`,
      }));

      // Lọc dữ liệu dựa trên tableSearchText
      // const filteredData = tableSearchText
      //   ? mappedData.filter(item => Object.values(item).some(value => typeof value === "string" && value.toLowerCase().includes(tableSearchText.toLowerCase())))
      //   : mappedData;

      const arrEmptyRow = fillRowTableEmpty(mappedData.length, 10);
      const finalData = [...mappedData, ...arrEmptyRow];
      setDataSource(finalData); // Cập nhật dataSource
      const filtered = finalData.filter(i => i.is_checked === "1" && !i.key.includes("empty"));
      onDataChange?.(filtered);
      // console.log("đơn vị quản lý đã được chọn", filtered);
      setDaiLyQuanLySelected(
        filtered.map(item => ({
          ma_doi_tac_ql: item.ma_doi_tac_ql,
          ma_chi_nhanh_ql: item.ma_chi_nhanh_ql,
          is_checked: item.is_check,
        })),
      );
      return finalData;
    } catch (error) {
      console.error("Lỗi khi xử lý dữ liệu bảng Đơn Vị Quản Lý:", error);
      return [];
    }
  }, [daiLyQuanLy]);

  // Xử lý click vào hàng
  const handleRowClick = (record: TableDaiLyQuanLyDataType) => {
    if (!record.ma_chi_nhanh_ql || !record.ma_doi_tac_ql || !record.key) {
      // console.warn("Invalid record, skipping row click:", record);
      return;
    }
    const newChonValue = record.is_checked === "1" ? "0" : "1";
    handleInputChange(record.key, "is_checked", newChonValue);
  };

  // Xử lý tìm kiếm
  const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: DataIndexDLQL) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);

  const handleReset = useCallback(
    (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: DataIndexDLQL) => {
      clearFilters();
      setSearchedColumn("");
      handleSearch([""], confirm, dataIndex);
    },
    [handleSearch],
  );

  // Hàm render cột tìm kiếm
  const getColumnSearchProps = (dataIndex: DataIndexDLQL, title: string) => ({
    filterDropdown: (filterDropdownParams: FilterDropdownProps) => (
      <TableFilterDropdown
        ref={refSearchInputTable}
        title={title}
        dataIndex={dataIndex}
        handleSearch={(selectedKeys, confirm, dataIndex) => handleSearch(selectedKeys, confirm, dataIndex)}
        handleReset={(clearFilters, confirm, dataIndex) => clearFilters && handleReset(clearFilters, confirm, dataIndex)}
        {...filterDropdownParams}
      />
    ),
    // filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} />,
    onFilter: (value: string, record: any) => (record[dataIndex] ? record[dataIndex].toString().toLowerCase().includes(value.toLowerCase()) : false),
    render: (text: string) => (searchedColumn === dataIndex ? <Highlighter searchWords={[searchText]} textToHighlight={text ? text.toString() : ""} /> : text || ""),
  });

  // Xử lý thay đổi checkbox
  const handleInputChange = (key: string, field: keyof TableDaiLyQuanLyDataType, value: any) => {
    if (!key) {
      // console.warn("Key is undefined, skipping update");
      return;
    }
    setDataSource(prevDataSource => {
      const updated = prevDataSource.map(item => (item.key === key ? {...item, [field]: value} : item));
      const filtered = updated.filter(i => i.is_checked === "1");
      onDataChange?.(filtered);
      setDaiLyQuanLySelected(
        filtered.map(item => ({
          is_checked: item.is_checked ?? "",
          ma_doi_tac_ql: item.ma_doi_tac_ql,
          ma_chi_nhanh_ql: item.ma_chi_nhanh_ql,
        })),
      );
      // Kiểm tra xem tất cả checkbox có được chọn hay không
      const allChecked = updated.every(item => item.key.includes("empty") || item.is_checked === "1");
      setIsAllChecked(allChecked);
      return updated;
    });
  };
  // Xử lý tìm kiếm toàn bảng
  const handleTableSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTableSearchText(e.target.value);
  };

  const handleSelectAll = (checked: boolean) => {
    setIsAllChecked(checked);
    setDataSource(prevDataSource => {
      const updated = prevDataSource.map(item => {
        if (item.key.includes("empty")) return item;
        return {...item, is_checked: checked ? "1" : "0"};
      });
      const filtered = updated.filter(i => i.is_checked === "1" && !i.key.includes("empty"));
      onDataChange?.(filtered);
      setDaiLyQuanLySelected(
        filtered.map(item => ({
          ma_doi_tac_ql: item.ma_doi_tac_ql,
          ma_chi_nhanh_ql: item.ma_chi_nhanh_ql,
          is_checked: item.is_checked ?? "",
        })),
      );
      return updated;
    });
  };

  // Render cột cho bảng đơn vị quản lý
  const renderColumn = (column: any) => {
    if (column.dataIndex === "is_checked") {
      return {
        ...column,
        title: (
          <div style={{height: 19, alignItems: "center", verticalAlign: "middle"}}>
            <Checkbox checked={isAllChecked} onChange={e => handleSelectAll(e.target.checked)}></Checkbox>
          </div>
        ),
        render: (_: any, record: TableDaiLyQuanLyDataType) => (
          <div className="custom-checkbox-cell" style={{height: 19, alignItems: "center", verticalAlign: "midle"}}>
            <FormInput
              className="!mb-0"
              component="checkbox"
              checked={record.is_checked === "1"}
              onChange={e => {
                if (!record.key) {
                  // console.warn("Record missing key:", record);
                  return;
                }
                handleInputChange(record.key, "is_checked", e.target.checked ? "1" : "0");
              }}
            />
          </div>
        ),
      };
    }
    return {
      ...column,
      ...(column.key && typeof column.title === "string" && column.key !== "stt" ? getColumnSearchProps(column.key, column.title) : {}),
      render: (text: string) => text || "", // Đảm bảo hiển thị chuỗi rỗng nếu text undefined
    };
  };

  return (
    <Row>
      <Row className="w-full">
        <Col span={9} style={{marginRight: 16}}>
          <Input placeholder="Tìm kiếm đại lý quản lý" value={tableSearchText} onChange={handleTableSearch} style={{marginBottom: 16}} prefix={<SearchOutlined />} />
        </Col>
        <Col span={8}>
          <Select
            allowClear
            style={{width: "100%"}}
            placeholder="Lọc theo đối tác"
            value={selectedDoiTac}
            onChange={value => setSelectedDoiTac(value)}
            options={listDoiTac.map(dt => ({label: dt.ten, value: dt.ma}))}
          />
        </Col>
      </Row>

      <Table<TableDaiLyQuanLyDataType>
        className="no-header-border-radius pr-2"
        {...defaultTableProps}
        style={{borderBottom: "1px solid #f0f0f0", marginBottom: "1rem"}}
        loading={loading}
        onRow={record => ({
          style: {cursor: loading ? "progress" : "pointer"},
          onClick: () => handleRowClick(record),
        })}
        columns={(daiLyQuanLyColumns || []).map(renderColumn)}
        // dataSource={dataSource.filter(item => Object.values(item).some(value => typeof value === "string" && value.toLowerCase().includes(tableSearchText.toLowerCase())))}
        dataSource={filteredDonViQuanLy}
        pagination={false}
        scroll={{y: 220}}
      />
    </Row>
  );
};

export default TabDaiLyQuanLy;

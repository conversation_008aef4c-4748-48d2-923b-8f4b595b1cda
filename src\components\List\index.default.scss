.custom-list {
  .ant-spin-container {
    div {
      &:first-child {
        min-height: 0 !important;
      }
    }
  }
}

.custom-virtual-list {
  .rc-virtual-list-holder-inner {
    /* width */
    ::-webkit-scrollbar {
      @apply w-2 h-2 dark:bg-black-40;
    }

    /* Track */
    ::-webkit-scrollbar-track {
      @apply rounded-md;
    }

    /* Handle */
    ::-webkit-scrollbar-thumb {
      @apply dark:bg-pink-30 bg-purple-40 rounded-md;
    }

    // ::-webkit-scrollbar-corner {
    //   @apply hidden;
    // }
  }
}

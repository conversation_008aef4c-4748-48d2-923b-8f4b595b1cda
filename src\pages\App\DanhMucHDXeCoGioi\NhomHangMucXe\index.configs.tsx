import {IFormInput} from "@src/@types";
import {REGUlAR_EXPRESSION} from "@src/constants";
import {colWidthByKey, defaultTableColumnsProps, ruleInputMessage, validationRules} from "@src/hooks";
import {TableProps} from "antd";

/**
 * Interface định nghĩa structure của dữ liệu trong từng row của table
 */
export interface TableNhomHangMucXeColumnDataType {
  key: string; 
  sott?: number; 
  ma?: string; 
  ten?: string; 
  nv?: string; 
  ten_nv?: string; 
  stt?: number; 
  trang_thai?: string; 
  ngay_tao?: string; 
  nguoi_tao?: string; 
  ngay_cap_nhat?: string; 
  nguoi_cap_nhat?: string; 
  trang_thai_ten?: string; 
}

/**
 * C<PERSON>u hình các cột hiển thị trong table danh sách nhóm hạng mục xe
 */
export const tableNhomHangMucXeColumn: TableProps<TableNhomHangMucXeColumnDataType>["columns"] = [
  {
    title: "STT", 
    dataIndex: "sott",
    key: "sott",
    align: "center", 
    width: colWidthByKey.sott, 
    ...defaultTableColumnsProps,
  },
  {
    title: "Mã",
    dataIndex: "ma",
    key: "ma",
    align: "center",
    width: 120, 
    ...defaultTableColumnsProps,
  },
  {
    title: "Tên",
    dataIndex: "ten",
    key: "ten",
    width: 250, 
    ...defaultTableColumnsProps,
  },
  // {
  //   title: "Nghiệp vụ",
  //   dataIndex: "nv",
  //   key: "nv",
  //   align: "center",
  //   width: 80,
  //   ...defaultTableColumnsProps,
  // },
  {
    title: "Tên nghiệp vụ", 
    dataIndex: "ten_nv",
    key: "ten_nv",
    width: 150, 
    ...defaultTableColumnsProps,
  },
  // {
  //   title: "Số thứ tự",
  //   dataIndex: "stt",
  //   key: "stt",
  //   align: "center",
  //   width: 80,
  //   ...defaultTableColumnsProps,
  // },
  {
    title: "Ngày tạo",
    dataIndex: "ngay_tao",
    key: "ngay_tao",
    width: colWidthByKey.ngay_tao, 
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Người tạo",
    dataIndex: "nguoi_tao",
    key: "nguoi_tao",
    width: colWidthByKey.nguoi_tao, 
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Ngày cập nhật",
    dataIndex: "ngay_cap_nhat",
    key: "ngay_cap_nhat",
    width: colWidthByKey.ngay_cap_nhat, 
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Người cập nhật",
    dataIndex: "nguoi_cap_nhat",
    key: "nguoi_cap_nhat",
    width: colWidthByKey.nguoi_cap_nhat,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Trạng thái",
    dataIndex: "trang_thai_ten", 
    key: "trang_thai_ten",
    width: 130,
    align: "center",
    ...defaultTableColumnsProps,
  },
];

// keyof: return ra key của interface TableNhomHangMucXeColumnDataType;
export type TableNhomHangMucXeColumnDataIndex = keyof TableNhomHangMucXeColumnDataType;

// Radio trong table (dùng cho filter dropdown)
export const radioItemTrangThaiNhomHangMucXeTable = [
  {value: "Đang sử dụng", text: "Đang sử dụng"},
  {value: "Ngưng sử dụng", text: "Ngưng sử dụng"},
];

// Radio trong search 
export const radioItemTrangThaiNhomHangMucXeSelect: Array<{ma: string; ten: string}> = [
  {ma: "", ten: "Tất cả"}, 
  {ma: "D", ten: "Đang sử dụng"},
  {ma: "K", ten: "Ngưng sử dụng"},
];

// Danh sách nghiệp vụ cơ bản
export const DANH_SACH_NGHIEP_VU_XE = [
//  {ten: "Bảo hiểm con người", ma: "NG"},
{ten: "Bảo hiểm ô tô", ma: "XE"}, 
{ten: "Bảo hiểm xe máy", ma: "XE_MAY"}, 
];

// Danh sách nghiệp vụ cho tìm kiếm 
export const DANH_SACH_NGHIEP_VU_TIM_KIEM = [
  {ma: "", ten: "Tất cả"}, 
  ...DANH_SACH_NGHIEP_VU_XE,
];

// ===== FORM SEARCH INTERFACE =====
export interface IFormTimKiemNhomHangMucXeFieldsConfig {
  ma: IFormInput;
  ten: IFormInput;
  nv: IFormInput;
  trang_thai: IFormInput;
}

/**
 * Định nghĩa các field trong form search ở đầu table
 */
export const FormTimKiemNhomHangMucXe: IFormTimKiemNhomHangMucXeFieldsConfig = {
  ma: {
    component: "input", 
    name: "ma", 
    label: "Mã nhóm", 
    placeholder: "Nhập mã nhóm", 
    className: "!mb-0", 
  },
  ten: {
    component: "input",
    name: "ten",
    label: "Tên nhóm",
    placeholder: "Nhập tên nhóm",
    className: "!mb-0",
  },
  nv: {
    component: "select", 
    name: "nv",
    label: "Nghiệp vụ",
    placeholder: "Chọn nghiệp vụ",
    className: "!mb-0",
  },
  trang_thai: {
    component: "select",
    name: "trang_thai",
    label: "Trạng thái",
    placeholder: "Chọn trạng thái",
    className: "!mb-0",
  },
};

// Form update / create
export interface IFormTaoMoiNhomHangMucXeFieldsConfig {
  ma: IFormInput;
  ten: IFormInput;
  nv: IFormInput;
  stt: IFormInput;
  trang_thai: IFormInput;
}

/**
 * Định nghĩa các field trong modal create/edit
 */
export const FormTaoMoiNhomHangMucXe: IFormTaoMoiNhomHangMucXeFieldsConfig = {
  ma: {
    component: "input",
    label: "Mã",
    name: "ma",
    placeholder: "Nhập mã",
    rules: [ruleInputMessage.required], 
  },
  ten: {
    component: "input",
    label: "Tên",
    name: "ten",
    placeholder: "Nhập tên nhóm hạng mục xe",
    rules: [ruleInputMessage.required],
  },
  nv: {
    component: "select",
    label: "Nghiệp vụ",
    name: "nv",
    placeholder: "Chọn nghiệp vụ",
    rules: [ruleInputMessage.required],
  },
  stt: {
    component: "input",
    label: "Số thứ tự",
    name: "stt",
    placeholder: "Nhập số thứ tự",
    rules: validationRules.stt,
  },
  trang_thai: {
    component: "select",
    label: "Trạng thái",
    name: "trang_thai",
    placeholder: "Chọn trạng thái",
    rules: [ruleInputMessage.required],
  },
};

// ===== TRẠNG THÁI OPTIONS CHO MODAL =====
export const TRANG_THAI_TAO_MOI_NHOM_HANG_MUC_XE = [
  {ten: "Đang sử dụng", ma: "D"},
  {ten: "Ngừng sử dụng", ma: "K"},
];

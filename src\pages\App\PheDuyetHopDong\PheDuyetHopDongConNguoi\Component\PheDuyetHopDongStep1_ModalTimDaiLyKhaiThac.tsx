import {CheckCircleOutlined, SearchOutlined} from "@ant-design/icons";
import {IFormInput, ReactQuery} from "@src/@types";
import {Button, FormInput, Highlighter, TableFilterDropdown} from "@src/components";
import {COLOR_PALETTE} from "@src/constants";
import {defaultPaginationTableProps, defaultTableProps, fillRowTableEmpty} from "@src/hooks";
import {Col, Flex, Form, InputRef, Modal, Row, Table, TableColumnType, Tag, Tooltip} from "antd";
import {FilterDropdownProps} from "antd/es/table/interface";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState} from "react";
import {useHopDongConNguoiContext} from "@src/pages/App/BaoHiemConNguoi/HopDongConNguoi/index.context";
import "../index.default.scss";
import {
  defaultFilterDaiLyKhaiThacParams,
  FormTimKiemDaiLyKhaiThac,
  IModalTimDaiLyKhaiThacRef,
  listLoaiDaiLySelect,
  listTrangThaiSelect,
  ModalTimDaiLyKhaiThacProps,
  radioItemTrangThaiDaiLyKhaiThacTable,
  tableDaiLyKhaiThacColumn,
  TableDaiLyKhaiThacColumnDataIndex,
  TableDaiLyKhaiThacColumnDataType,
} from "./Constant";

const {ma_doi_tac_ql, ma, ten, loai, trang_thai} = FormTimKiemDaiLyKhaiThac;

const ModalTimDaiLyKhaiThacHopDongConNguoiComponent = forwardRef<IModalTimDaiLyKhaiThacRef, ModalTimDaiLyKhaiThacProps>(
  ({onSelectDaiLyKhaiThac, maDoiTacSelected}: ModalTimDaiLyKhaiThacProps, ref) => {
    useImperativeHandle(ref, () => ({
      open: daiLyKhaiThacSelected => {
        setIsOpen(true);
        initData();
        if (daiLyKhaiThacSelected) setChiTietDaiLyKhaiThacSelected(daiLyKhaiThacSelected);
      },
      close: () => setIsOpen(false),
    }));

    const {listDoiTac, loading, searchDaiLy} = useHopDongConNguoiContext();

    // DATA TABLE KHÁCH HÀNG
    const [listDaiLyKhaiThac, setListDaiLyKhaiThac] = useState<Array<CommonExecute.Execute.IDanhMucDaiLy>>([]);
    const [tongSoDongDaiLyKhaiThac, setTongSoDongDaiLyKhaiThac] = useState<number>(0);
    const [filterDaiLyKhaiThacParams, setFilterDaiLyKhaiThacParams] = useState<ReactQuery.ITimKiemPhanTrangDanhSachDaiLyParams>(defaultFilterDaiLyKhaiThacParams);

    const [chiTietDaiLyKhaiThacSelected, setChiTietDaiLyKhaiThacSelected] = useState<CommonExecute.Execute.IDanhMucDaiLy | null>(null);
    const [isOpen, setIsOpen] = useState<boolean>(false);

    const refSearchInputTable = useRef<InputRef>(null);
    const [searchTextTable, setSearchTextTable] = useState<string>(""); //lưu textsearch để hiển thị vào cell
    const [searchedColumn, setSearchedColumn] = useState<TableDaiLyKhaiThacColumnDataIndex | "">(""); //key column đang được search

    const [formTimDaiLyKhaiThac] = Form.useForm();

    const closeModal = useCallback(() => {
      setIsOpen(false);
      setChiTietDaiLyKhaiThacSelected(null);
      formTimDaiLyKhaiThac.resetFields();
    }, [formTimDaiLyKhaiThac]);

    //Bấm tiếp theo
    const onPressXacNhan = async (chiTietDaiLyKhaiThacSelected: CommonExecute.Execute.IDanhMucDaiLy | null) => {
      try {
        onSelectDaiLyKhaiThac(chiTietDaiLyKhaiThacSelected);
        closeModal();
      } catch (error) {
        console.log("onPressXacNhan error", error);
      }
    };

    /* ĐẠI LÝ */
    // TÌM KIẾM PHÂN TRANG ĐẠI LÝ
    const onPressSearchDaiLyKhaiThac = useCallback(async () => {
      try {
        const response = await searchDaiLy({
          ...filterDaiLyKhaiThacParams,
        });
        if (response.data) {
          setListDaiLyKhaiThac(response.data);
          setTongSoDongDaiLyKhaiThac(response.tong_so_dong);
        }
      } catch (error) {
        console.log("onPressSearchDaiLyKhaiThac error ", error);
      }
    }, [filterDaiLyKhaiThacParams, searchDaiLy]);

    useEffect(() => {
      onPressSearchDaiLyKhaiThac();
    }, [filterDaiLyKhaiThacParams]);

    const initData = async () => {
      try {
        setFilterDaiLyKhaiThacParams({...filterDaiLyKhaiThacParams, ma_doi_tac_ql: maDoiTacSelected});
        formTimDaiLyKhaiThac.setFields([
          {
            name: "ma_doi_tac_ql",
            value: maDoiTacSelected,
          },
        ]);
        await onPressSearchDaiLyKhaiThac();
      } catch (error) {
        console.log("error", error);
      }
    };

    //MAP VALUE CỦA LIST VÀO TABLE
    const dataTableListDaiLyKhaiThac = useMemo<Array<TableDaiLyKhaiThacColumnDataType>>(() => {
      try {
        const tableData = listDaiLyKhaiThac.map((itemDaiLyKhaiThac, index) => {
          return {
            key: itemDaiLyKhaiThac.ma, // bắt buộc phải có key
            stt: itemDaiLyKhaiThac.stt ?? index + 1,
            ...itemDaiLyKhaiThac,
          };
        });
        const arrEmptyRow: Array<TableDaiLyKhaiThacColumnDataType> = fillRowTableEmpty(tableData.length, 10);
        return [...tableData, ...arrEmptyRow];
      } catch (error) {
        console.log("dataTableListDaiLyKhaiThac error", error);
        return [];
      }
    }, [listDaiLyKhaiThac]);

    const onSearchDaiLyKhaiThac = (values: ReactQuery.ITimKiemPhanTrangDanhSachDaiLyParams) => {
      setFilterDaiLyKhaiThacParams({...values, trang: 1, so_dong: 10});
    };

    const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: TableDaiLyKhaiThacColumnDataIndex) => {
      confirm();
      setSearchTextTable(selectedKeys[0]);
      setSearchedColumn(dataIndex);
    }, []);

    const handleReset = useCallback(
      (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: TableDaiLyKhaiThacColumnDataIndex) => {
        clearFilters();
        setSearchTextTable("");
        handleSearch([""], confirm, dataIndex);
      },
      [handleSearch],
    );

    // RENDER
    //FOOTER
    const renderFooter = () => {
      return (
        <Tooltip title={chiTietDaiLyKhaiThacSelected ? "" : "Vui lòng chọn đối tác"}>
          <Button type={"primary"} onClick={() => onPressXacNhan(chiTietDaiLyKhaiThacSelected)} className="w-40" icon={<CheckCircleOutlined />} disabled={chiTietDaiLyKhaiThacSelected ? false : true}>
            Chọn
          </Button>
        </Tooltip>
      );
    };
    const renderFormInputColum = (props: IFormInput) => (
      <Col span={3}>
        <FormInput {...props} />
      </Col>
    );

    // FORM TÌM KIẾM TABLE ĐẠI LÝ
    const renderHeaderTableQuanLyDaiLyKhaiThac = () => (
      <Form
        form={formTimDaiLyKhaiThac}
        initialValues={{}}
        layout="vertical" //Tất cả các input nằm trên 1 dòng
        onFinish={onSearchDaiLyKhaiThac}
        className="[&_.ant-form-item]:mb-0">
        <Row gutter={16} align="bottom">
          {renderFormInputColum({...ma_doi_tac_ql, options: listDoiTac})}
          {renderFormInputColum({...ma})}
          {renderFormInputColum(ten)}
          {renderFormInputColum({...loai, options: listLoaiDaiLySelect})}
          {renderFormInputColum({...trang_thai, options: listTrangThaiSelect})}
          <Col span={3}>
            <Button type="primary" htmlType="submit" loading={loading} icon={<SearchOutlined />} block>
              Tìm kiếm
            </Button>
          </Col>
        </Row>
      </Form>
    );

    //lấy ra loại filter ở CELL HEADER tương ứng
    const getFilterTableDaiLyKhaiThac = (dataIndex: string) => {
      if (dataIndex === "trang_thai_ten") return radioItemTrangThaiDaiLyKhaiThacTable;
      return undefined;
    };

    // dataIndex : là các key của column, title : tiêu đề của column
    const getColumnSearchProps = (dataIndex: TableDaiLyKhaiThacColumnDataIndex, title: string): TableColumnType<TableDaiLyKhaiThacColumnDataType> => ({
      /**
       *  filterDropdown: UI tuỳ chỉnh của ô filter (component render ra trong dropdown filter)
       * @param param0
       * @returns
       * dataIndex !== "trang_thai_ten"
       */
      filterDropdown:
        dataIndex !== "trang_thai_ten"
          ? filterDropdownParams => (
              <TableFilterDropdown
                ref={refSearchInputTable}
                title={title}
                dataIndex={dataIndex}
                handleSearch={(selectedKeys, confirm, dataIndex) => handleSearch(selectedKeys, confirm, dataIndex)}
                handleReset={(clearFilters, confirm, dataIndex) => clearFilters && handleReset(clearFilters, confirm, dataIndex)}
                {...filterDropdownParams}
              />
            )
          : undefined,
      /**
       * filterIcon : icon hiển thị trên header column khi filter
       * @param filtered :  // "filtered". là boolean, true nếu đang áp dụng filter,
       *  biến này thay đổi khi hàm confirm được gọi, hoặc hàm clearFilters được gọi
       * @returns
       */
      filterIcon: (filtered: boolean) => {
        return <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} size={40} />;
      },
      /**
       * onFilter: hàm filter dữ liệu tại client-side (bắt buộc phải khai báo để filter hoạt động)
       * do hàm onFilter luôn phải trả về boolean, nên thêm || false vào để k bị lỗi
       * @param value : giá trị filter người dùng nhập vào
       * @param record : từng bản ghi trong dataSource
       * @returns
       */
      onFilter: (value, record) => {
        return (
          record[dataIndex]
            ?.toString()
            .toLowerCase()
            .includes((value as string).toLowerCase()) || false
        );
      },
      //filterDropdownProps: thuộc tính tuì chỉnh hành vi dropdown
      filterDropdownProps: {
        // onOpenChange: callback khi dropdown mở/ tắt thì sẽ focus vào input
        onOpenChange(open) {
          if (open) {
            setTimeout(() => refSearchInputTable.current?.select(), 100); // Focus/ select input khi mở filter
          }
        },
      },
      filterSearch: true,
      filters: getFilterTableDaiLyKhaiThac(dataIndex),
      render: (
        text,
        record,
        //  index
      ) => {
        if (dataIndex === "trang_thai_ten") {
          const color = text === "Đang sử dụng" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];
          if (record.key.toString().includes("empty")) return "";
          return (
            <Tag color={color} className="text-[11px]">
              {text}
            </Tag>
          );
        }
        return searchedColumn === dataIndex ? <Highlighter searchWords={[searchTextTable]} textToHighlight={text ? text.toString() : ""} /> : text !== undefined ? text : "\u00A0"; // xử lý chuyển text thành 1 dòng khi text quá dài
      },
    });

    const renderTable = () => (
      <Table<TableDaiLyKhaiThacColumnDataType>
        {...defaultTableProps}
        dataSource={dataTableListDaiLyKhaiThac} //mảng dữ liệu record được hiển thị
        columns={
          tableDaiLyKhaiThacColumn?.map(item => {
            //Nếu là cột stt thì không hiển thị search
            return {...item, ...(item.key === "sott" ? {} : getColumnSearchProps(item.key as keyof TableDaiLyKhaiThacColumnDataType, item.title as string))};
          }) || []
        } //định nghĩa cột của table
        rowClassName={record => (record.key === chiTietDaiLyKhaiThacSelected?.ma ? "custom-row-selected" : "")} // xử lý việc 1 row được selected -> row đấy sẽ được highlight lên
        loading={loading} //hiển thị loading khi đang gọi API để loading data
        pagination={{
          ...defaultPaginationTableProps,
          defaultPageSize: 10,
          total: tongSoDongDaiLyKhaiThac,
          onChange: (page, pageSize) => setFilterDaiLyKhaiThacParams({...filterDaiLyKhaiThacParams, trang: page, so_dong: pageSize}),
        }}
        title={renderHeaderTableQuanLyDaiLyKhaiThac}
        onRow={record => {
          return {
            style: {cursor: loading ? "progress" : "pointer"}, //  thể hiện vị trí con trỏ là button(👈)
            onClick: () => setChiTietDaiLyKhaiThacSelected(record as CommonExecute.Execute.IDanhMucDaiLy),
            onDoubleClick: () => onPressXacNhan(record as CommonExecute.Execute.IDanhMucDaiLy),
          };
        }}
      />
    );

    return (
      <Flex vertical gap="middle" align="flex-start">
        <Modal
          className="modal-tim-dai-ly-khai-thac-hop-dong-con-nguoi"
          title="Chọn đại lý"
          centered
          open={isOpen}
          onOk={() => closeModal()}
          onCancel={() => closeModal()}
          width={"95%"}
          styles={{
            body: {
              height: "70vh",
            },
          }}
          footer={renderFooter}>
          {renderTable()}
        </Modal>
      </Flex>
    );
  },
);

ModalTimDaiLyKhaiThacHopDongConNguoiComponent.displayName = "ModalTimDaiLyKhaiThacHopDongConNguoiComponent";
export const ModalTimDaiLyKhaiThacHopDongConNguoi = memo(ModalTimDaiLyKhaiThacHopDongConNguoiComponent, isEqual);

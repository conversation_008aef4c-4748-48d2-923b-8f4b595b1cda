import {ReactQuery} from "@src/@types";

// khai báo interface props Context của Login
export interface QuanLyPhongBanContextProps {
  danhSachPhongBan: Array<CommonExecute.Execute.IDanhSachPhongBanPhanTrang>;
  loading: boolean;
  onUpdate: (item: ReactQuery.IUpdatePhongBanParams) => Promise<boolean>;
  layDanhSachPhongBanPhanTrang: (params: ReactQuery.ILayDanhSachPhongBanPhanTrangParams) => void;
  tongSoDong: number;
  layChiTietPhongBan: (params: ReactQuery.IChiTietPhongBanParams) => Promise<CommonExecute.Execute.IPhongBan | null>;
  defaultFormValue: object;
}

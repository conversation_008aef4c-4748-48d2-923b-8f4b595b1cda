import React, {createContext, useContext} from "react";
import {IDanhMucBenhVienProvider} from "./index.model";

export const DanhMucBenhVienContext = createContext<IDanhMucBenhVienProvider>({} as IDanhMucBenhVienProvider);

export const useDanhMucBenhVienContext = (): IDanhMucBenhVienProvider => {
  const context = useContext(DanhMucBenhVienContext);
  if (!context) {
    throw new Error("useDanhMucBenhVienContext must be used within a DanhMucBenhVienProvider");
  }
  return context;
};

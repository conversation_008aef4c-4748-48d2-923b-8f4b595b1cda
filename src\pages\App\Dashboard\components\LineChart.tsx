import React from "react";
import ReactApex<PERSON><PERSON> from "react-apexcharts";
import {ApexOptions} from "apexcharts";

interface ILineChartProps {
  data: {
    labels: string[];
    currentYear: number[];
    previousYear: number[];
    target: number[];
  };
  height?: number;
  title?: string;
}

const LineChart: React.FC<ILineChartProps> = ({data, height = 300, title}) => {
  const options: ApexOptions = {
    chart: {
      type: "bar",
      height: height,
      stacked: false,
      toolbar: {
        show: false,
      },
      zoom: {
        enabled: false,
      },
    },
    title: {
      text: title || undefined,
      align: "left",
      style: {
        fontSize: "16px",
        fontWeight: "600",
        color: "#262626",
      },
    },
    dataLabels: {
      enabled: false,
    },
    stroke: {
      width: [0, 0, 3], // No stroke for bars, stroke for line
      curve: "smooth",
    },
    colors: ["#52c41a", "#d9f7be", "#faad14"], // Dark green, light green, yellow
    xaxis: {
      categories: data.labels,
      labels: {
        style: {
          colors: "#8c8c8c",
          fontSize: "12px",
        },
      },
    },
    yaxis: {
      labels: {
        style: {
          colors: "#8c8c8c",
          fontSize: "12px",
        },
        formatter: function (value) {
          return (value / 1000000).toFixed(0) + "M";
        },
      },
    },
    grid: {
      borderColor: "#f0f0f0",
      strokeDashArray: 4,
    },
    legend: {
      position: "top",
      horizontalAlign: "right",
      fontSize: "12px",
      markers: {
        width: 8,
        height: 8,
        radius: 4,
      },
    },
    tooltip: {
      custom: function ({series, seriesIndex, dataPointIndex, w}) {
        return (
          '<div class="apexcharts-tooltip">' +
          `<div>${w.globals.labels[dataPointIndex]}</div>` +
          `<div>2025: ${new Intl.NumberFormat("vi-VN", {
            style: "currency",
            currency: "VND",
          }).format(series[0][dataPointIndex])}</div>` +
          `<div>2024: ${new Intl.NumberFormat("vi-VN", {
            style: "currency",
            currency: "VND",
          }).format(series[1][dataPointIndex])}</div>` +
          `<div>Target: ${new Intl.NumberFormat("vi-VN", {
            style: "currency",
            currency: "VND",
          }).format(series[2][dataPointIndex])}</div>` +
          "</div>"
        );
      },
    },
    responsive: [
      {
        breakpoint: 768,
        options: {
          chart: {
            height: 250,
          },
          legend: {
            position: "bottom",
          },
        },
      },
    ],
  };

  const series = [
    {
      name: "2025",
      type: "column",
      data: data.currentYear,
    },
    {
      name: "2024",
      type: "column",
      data: data.previousYear,
    },
    {
      name: "Mục tiêu",
      type: "line",
      data: data.target,
    },
  ];

  return (
    <div className="line-chart-container">
      <ReactApexChart options={options} series={series} type="bar" height={height} />
    </div>
  );
};

export default LineChart;

import {
  ArrowLeftOutlined,
  ArrowRightOutlined,
  CheckOutlined,
  CloseOutlined,
  CopyOutlined,
  DownloadOutlined,
  EditOutlined,
  EyeOutlined,
  FileAddOutlined,
  PrinterFilled,
  ReloadOutlined,
  StopOutlined,
} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {Button, HeaderModal, Popcomfirm} from "@src/components";
import {Flex, Form, Modal, Steps, message} from "antd";
import {Dayjs} from "dayjs";
import {forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState} from "react";
import {useBaoHiemXeCoGioiContext} from "../index.context";
import {initFormFields} from "./Constant";
import {ModalDanhGiaTonThat, ThongTinCauHinhBaoHiemXeStep, ThongTinDoiTuongBaoHiemXeStep, ThongTinHopDongXeStep, ThongTinPheDuyetHopDongXeStep} from "./StepComponent";

import {ACTION_CODE} from "@src/constants";
import {IModalTrinhDuyetHopDongRef, ModalTrinhDuyetHopDong, TrinhDuyetHopDongProvider, useTrinhDuyetHopDongContext} from "@src/pages/App/TrinhDuyetHopDong";
import {formatDateTimeToNumber} from "@src/utils";
import {NHOM_XCG} from "../index.configs";
import {IModalDanhGiaTonThatRef} from "./StepComponent/Constant";
import ThongTin_HinhAnhHoSoTaiLieu_HopDongXe_Step from "./StepComponent/ThongTin_HinhAnhHoSoTaiLieu_HopDongXe_Step";

interface Props {
  danhSachKhachHang: Array<CommonExecute.Execute.IKhachHang>;
}

export interface IModalThemHopDongBaoHiemXeRef {
  open: (data?: CommonExecute.Execute.IHopDongXe) => void;
  close: () => void;
}

const ModalThemHopDongBaoHiemXe = forwardRef<IModalThemHopDongBaoHiemXeRef, Props>(({}: Props, ref) => {
  const {
    getListDaiLyKhaiThac,
    updateHopDongXe,
    updateDoiTuongBaoHiemXe,
    loading,
    huyHopDongXe,
    goHuyHopDongXe,
    chiTietHopDongBaoHiemXe,
    chiTietDoiTuongBaoHiemXe,
    taoHopDongSuaDoiBoSung,
    layChiTietHopDongBaoHiemXe,
    layDanhSachHopDongBaoHiemXePhanTrang,
    defaultFormValue,
    resetChiTietHopDongBaoHiemXe,
  } = useBaoHiemXeCoGioiContext();

  // Helper function để kiểm tra điều kiện disable khi không có chi tiết hợp đồng
  const isDisabledWhenNoContract = !chiTietHopDongBaoHiemXe?.so_id || Object.keys(chiTietHopDongBaoHiemXe).length === 0;

  const [formNhapHopDongXe] = Form.useForm();
  const [formNhapDoiTuongBaoHiemXe] = Form.useForm();
  const formNhapHopDongXeValues = Form.useWatch([], formNhapHopDongXe);
  const formNhapDoiTuongBaoHiemXeValues = Form.useWatch([], formNhapDoiTuongBaoHiemXe);
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [daiLyKhaiThacSelected, setDaiLyKhaiThacSelected] = useState<CommonExecute.Execute.IChiTietDanhMucDaiLy | null>(null);
  const [khachHangSelected, setKhachHangSelected] = useState<CommonExecute.Execute.IKhachHang | null>(null);
  const [canBoSelected, setCanBoSelected] = useState<CommonExecute.Execute.IDoiTacNhanVien | null>(null);
  let refModalTrinhDuyetHopDong = useRef<IModalTrinhDuyetHopDongRef>(null);
  let refModalDanhGiaTonThat = useRef<IModalDanhGiaTonThatRef>(null);

  // State cho download PDF
  const [downloadPDFFunction, setDownloadPDFFunction] = useState<(() => void) | null>(null);
  const [hasSelectedFile, setHasSelectedFile] = useState(false);

  // Callback để nhận download function từ child component
  const handleDownloadPDFCallback = useCallback((downloadFn: () => void, hasFile: boolean) => {
    setDownloadPDFFunction(() => downloadFn);
    setHasSelectedFile(hasFile);
  }, []);

  //watch
  const watchDoiTacCapDon = Form.useWatch("ma_doi_tac_ql", formNhapHopDongXe);

  useImperativeHandle(ref, () => ({
    open: (dataChiTietHopDongBaoHiemXe?: CommonExecute.Execute.IHopDongXe) => {
      setIsOpen(true);
      if (dataChiTietHopDongBaoHiemXe) {
        initFormFields(formNhapHopDongXe, dataChiTietHopDongBaoHiemXe);
      } else {
        formNhapHopDongXe.resetFields();
        formNhapDoiTuongBaoHiemXe.resetFields();
      }
    },
    close: () => {
      // Reset local state
      setCurrentStep(0);
      setIsOpen(false);
      setKhachHangSelected(null);
      setCanBoSelected(null);
      setDaiLyKhaiThacSelected(null);
      setDownloadPDFFunction(null);
      setHasSelectedFile(false);
      // Reset context data - quan trọng để tránh hiển thị dữ liệu cũ
      resetChiTietHopDongBaoHiemXe();
    },
  }));

  useEffect(() => {
    if (watchDoiTacCapDon) {
      initListDaiLyKhaiThac();
    } else {
      // setListDaiLyKhaiThac([]);
    }
  }, [watchDoiTacCapDon]);

  useEffect(() => {
    if (chiTietHopDongBaoHiemXe) {
      initFormFields(formNhapHopDongXe, chiTietHopDongBaoHiemXe);
      //khai báo khách hàng được chọn
      const objectKhachHang: CommonExecute.Execute.IKhachHang = {
        ten: chiTietHopDongBaoHiemXe?.ten_kh || "",
        ma: chiTietHopDongBaoHiemXe?.ma_kh || "",
      };
      setKhachHangSelected(objectKhachHang);
      // khai báo cán bộ được chọn
      const objectCanBo: CommonExecute.Execute.IDoiTacNhanVien = {
        ten: chiTietHopDongBaoHiemXe?.ten_cb_ql || "",
        ma: chiTietHopDongBaoHiemXe?.ma_cb_ql || "",
      };
      setCanBoSelected(objectCanBo);
    }
  }, [chiTietHopDongBaoHiemXe]);

  //đại lý
  const initListDaiLyKhaiThac = useCallback(async () => {
    try {
      const response = await getListDaiLyKhaiThac({ma_doi_tac_ql: watchDoiTacCapDon || "ESCS"});
      if (response?.data) {
        const daiLyMatch = response.data.find(item => item.ma === chiTietHopDongBaoHiemXe?.daily_kt);
        if (daiLyMatch) {
          setDaiLyKhaiThacSelected(daiLyMatch);
        }
      }
    } catch (error) {
      console.log("initListDaiLyKhaiThac error", error);
    }
  }, [watchDoiTacCapDon, getListDaiLyKhaiThac]);

  //Bấm Update
  const onConfirm = async (action: string) => {
    const formatTime = (value: Dayjs | string | undefined) => {
      if (!value) return "";
      return typeof value === "string" ? value : value.format("HH:mm");
    };

    if (currentStep === 0) {
      //lưu thông tin hợp đồng
      try {
        await formNhapHopDongXe.validateFields();
        const formNhapHopDongValues: ReactQuery.IUpdateHopDongParams = formNhapHopDongXe.getFieldsValue();
        const params: ReactQuery.IUpdateHopDongParams = {
          ...formNhapHopDongValues,
          nv: formNhapHopDongValues.nv || NHOM_XCG, // Sử dụng nghiệp vụ từ form, fallback về NHOM_XCG nếu không có
          ngay_cap: formNhapHopDongValues.ngay_cap ? formatDateTimeToNumber(formNhapHopDongValues.ngay_cap) : 0,
          ngay_hl: formNhapHopDongValues.ngay_hl ? formatDateTimeToNumber(formNhapHopDongValues.ngay_hl) : 0,
          ngay_kt: formNhapHopDongValues.ngay_kt ? formatDateTimeToNumber(formNhapHopDongValues.ngay_kt) : 0,
          gio_hl: formNhapHopDongValues.gio_hl ? formatTime(formNhapHopDongValues.gio_hl) : "",
          gio_kt: formNhapHopDongValues.gio_kt ? formatTime(formNhapHopDongValues.gio_kt) : "",
          so_hd_g: formNhapHopDongValues.so_hd_g ? formNhapHopDongValues.so_hd_g : "",
          so_id: chiTietHopDongBaoHiemXe ? chiTietHopDongBaoHiemXe?.so_id : 0,
        };
        const response = await updateHopDongXe(params);

        if (response.success) {
          if (response.isNewContract && response.contractInfo) {
            // Trường hợp tạo hợp đồng mới (so_id = 0 hoặc không có)
            console.log("✅ Tạo hợp đồng mới thành công:", response.contractInfo);

            // Tự động load chi tiết hợp đồng vừa tạo
            await layChiTietHopDongBaoHiemXe({
              so_id: response.contractInfo.so_id,
              ma_doi_tac_ql: params.ma_doi_tac_ql || "",
            });

            // Message đã được hiển thị trong provider: "Tạo hợp đồng thành công!"
            // Hiển thị thêm thông tin số hợp đồng
            message.success(`Số hợp đồng: ${response.contractInfo.so_hd}`);
          } else {
            // Trường hợp cập nhật hợp đồng có sẵn (so_id > 0)
            console.log("✅ Cập nhật hợp đồng có sẵn thành công");
            // Message "Cập nhật thông tin hợp đồng thành công!" đã được hiển thị trong provider
            // Dữ liệu đã được reload trong provider thông qua layDanhSachHopDongBaoHiemXePhanTrang
          }

          // Đóng modal nếu action là "close"
          if (action === "close") {
            closeModal();
          }
        } else {
          console.error("❌ Lỗi khi lưu hợp đồng:", response);
        }
      } catch (error) {
        console.log("onConfirm error", error);
      }
    }

    //lưu thông tin đối tượng
    if (currentStep === 1) {
      try {
        await formNhapDoiTuongBaoHiemXe.validateFields();
        const formNhapDoiTuongValues: ReactQuery.IFormCapNhatDoiTuongBaoHiemXeCoGioiParams = formNhapDoiTuongBaoHiemXe.getFieldsValue();
        const params: ReactQuery.IFormCapNhatDoiTuongBaoHiemXeCoGioiParams = {
          ...formNhapDoiTuongValues,
          ngay_cap: formatDateTimeToNumber(formNhapDoiTuongValues.ngay_cap),
          ngay_hl: formatDateTimeToNumber(formNhapDoiTuongValues.ngay_hl),
          ngay_kt: formatDateTimeToNumber(formNhapDoiTuongValues.ngay_kt),
          gio_hl: formatTime(formNhapDoiTuongValues.gio_hl),
          gio_kt: formatTime(formNhapDoiTuongValues.gio_kt),
          so_id_dt: formNhapDoiTuongValues.so_id_dt ?? 0,
          so_id: chiTietHopDongBaoHiemXe ? chiTietHopDongBaoHiemXe?.so_id : 0,
        };
        const response = await updateDoiTuongBaoHiemXe(params); //cập nhật lại đơn vị chi nhánh
        if (response && action === "close") {
          closeModal();
          formNhapDoiTuongBaoHiemXe.resetFields();
        }
      } catch (error) {
        console.log("onConfirm error", error);
      }
    }
  };

  const closeModal = useCallback(() => {
    // Reset form fields
    // formNhapHopDongXe.resetFields();
    // formNhapDoiTuongBaoHiemXe.resetFields();

    // Reset local state
    setIsOpen(false);
    setCurrentStep(0);
    setKhachHangSelected(null);
    setCanBoSelected(null);
    setDaiLyKhaiThacSelected(null);
    setDownloadPDFFunction(null);
    setHasSelectedFile(false);

    // Reset filtered lists
    // Tất cả filtered lists đã được chuyển sang useMemo, không cần reset

    // Reset context data - quan trọng để tránh hiển thị dữ liệu cũ
    resetChiTietHopDongBaoHiemXe();
  }, [formNhapHopDongXe, formNhapDoiTuongBaoHiemXe, resetChiTietHopDongBaoHiemXe]);

  const onBack = () => {
    if (currentStep === 0) {
      closeModal();
      return;
    }
    if (currentStep === 1) formNhapDoiTuongBaoHiemXe.resetFields();
    setCurrentStep(currentStep - 1);
  };

  const getMessAlertByCurrentStep = (current: number) => {
    switch (current) {
      case 0:
        return "Bạn có chắc chắn muốn lưu thông tin hợp đồng?";
      case 1:
        return "Bạn có chắc chắn muốn lưu thông tin đối tượng bảo hiểm?";
      case 2:
        return "Bạn có chắc chắn muốn lưu thông tin thanh toán?";
      case 3:
        return "Bạn có chắc chắn muốn lưu thông tin cấu hình?";
      case 4:
        return "Bạn có chắc chắn muốn lưu thông tin phê duyệt?";
      default:
        return "";
    }
  };

  const getTitleButtonBackByCurrentStep = (current: number) => {
    switch (current) {
      case 0:
        return "Đóng";
      default:
        return "Quay lại";
    }
  };

  const getIconButtonBackByCurrentStep = (current: number) => {
    switch (current) {
      case 0:
        return <CloseOutlined />;
      default:
        return <ArrowLeftOutlined />;
    }
  };

  useEffect(() => {
    if (currentStep === 0) {
      formNhapHopDongXe
        .validateFields({validateOnly: true})
        .then(response => {
          setDisableSubmit(false);
        })
        .catch(error => {
          if (formNhapHopDongXeValues.kieu_hd === "G" && error?.errorFields[0]?.name[0] === "so_hd_g") {
            setDisableSubmit(false);
          } else setDisableSubmit(true);
        });
    } else if (currentStep === 1) {
      formNhapDoiTuongBaoHiemXe
        .validateFields({validateOnly: true})
        .then(response => {
          setDisableSubmit(false);
        })
        .catch(error => {
          setDisableSubmit(true);
        });
    }
  }, [currentStep, formNhapHopDongXe, formNhapDoiTuongBaoHiemXe, formNhapHopDongXeValues, formNhapDoiTuongBaoHiemXeValues]);

  // Thêm hàm xử lý khi click vào nút "Tạo SĐBS"
  const handleTaoHopDongSDBS = async () => {
    if (!chiTietHopDongBaoHiemXe) return;

    try {
      const params: ReactQuery.ITaoHopDongSuaDoiBoSungParams = {
        ma_doi_tac_ql: chiTietHopDongBaoHiemXe.ma_doi_tac_ql || "",
        so_id_g: chiTietHopDongBaoHiemXe.so_id || 0,
      };

      const result = await taoHopDongSuaDoiBoSung(params);
      if (result) {
        // Nếu tạo thành công, có thể thực hiện thêm các hành động khác ở đây
        // Ví dụ: Đóng modal hiện tại và mở modal mới với hợp đồng SĐBS
        layChiTietHopDongBaoHiemXe({
          so_id: result.so_id,
          ma_doi_tac_ql: chiTietHopDongBaoHiemXe.ma_doi_tac_ql || "",
        });
      }
    } catch (error) {
      console.error("Lỗi khi tạo hợp đồng SĐBS:", error);
    }
  };

  //renderFooter Modal
  const renderFooter = () => {
    const showButtonHuy = Number(chiTietHopDongBaoHiemXe?.ngay_huy_num) >= 30000101;
    const showButtonGoHuy = Number(chiTietHopDongBaoHiemXe?.ngay_huy_num) < 30000101;
    const showButtonHuyTrinh = Number(chiTietHopDongBaoHiemXe?.ngay_trinh_num) < 30000101;
    const disableButtonSDBS = chiTietHopDongBaoHiemXe?.ngay_duyet_num && Number(chiTietHopDongBaoHiemXe?.ngay_duyet_num) >= 30000101;
    return (
      <Form.Item className="mb-0" style={{width: "100%"}}>
        <div className="flex w-full items-center justify-between">
          <div>
            {currentStep === 0 && (
              <>
                <Popcomfirm
                  title="Thông báo"
                  onConfirm={handleTaoHopDongSDBS}
                  okText="Đồng ý"
                  description={`Bạn có chắc chắn muốn tạo sửa đổi bổ sung cho hợp đồng ${chiTietHopDongBaoHiemXe?.so_hd || ""} này không?`}
                  buttonTitle="Tạo SĐBS"
                  buttonClassName="mr-2"
                  buttonIcon={<FileAddOutlined />}
                  type="primary"
                  buttonDisable={!!disableButtonSDBS || isDisabledWhenNoContract}
                />
                <Button type="primary" className="mr-2" icon={<PrinterFilled />}>
                  Xem hợp đồng
                </Button>

                {/* BUTTON HUỶ HỢP ĐỒNG XE */}
                {showButtonHuy && (
                  <Popcomfirm
                    danger
                    variant="outlined"
                    title="Thông báo"
                    onConfirm={huyHopDongXe}
                    okText="Đồng ý"
                    description="Bạn có chắc chắn muốn huỷ hợp đồng này hay không?"
                    buttonTitle="Huỷ hợp đồng"
                    buttonClassName="mr-2"
                    buttonIcon={<StopOutlined />}
                    type="default"
                  />
                )}
                {/* BUTTON GỠ HUỶ HĐ */}
                {showButtonGoHuy && (
                  <Popcomfirm
                    title="Thông báo"
                    onConfirm={goHuyHopDongXe}
                    okText="Đồng ý"
                    description="Bạn có chắc chắn muốn gỡ huỷ hợp đồng này hay không?"
                    buttonTitle="Gỡ huỷ hợp đồng"
                    buttonClassName="mr-2"
                    buttonIcon={<ReloadOutlined />}
                    type="default"
                  />
                )}
                {/* BUTTON COPY HĐ */}
                <Button type="primary" className="mr-2" icon={<CopyOutlined />}>
                  Copy hợp đồng
                </Button>
              </>
            )}

            {/* CÁC BUTTON CHƯA LÀM ACTION */}
            {currentStep === 1 && (
              <>
                <Button type="primary" className="mr-2" icon={<CopyOutlined />}>
                  Copy đối tượng
                </Button>
                <Button type="primary" className="mr-2" icon={<EyeOutlined />}>
                  Xem GCN
                </Button>
                <Button danger type="default" className="mr-2" icon={<CloseOutlined />}>
                  Xoá đối tượng
                </Button>
              </>
            )}

            {/* Button Download PDF - hiển thị ở bên trái khi step 4 */}
            {currentStep === 4 && downloadPDFFunction && hasSelectedFile && (
              <Button type="primary" onClick={downloadPDFFunction} className="mr-2" icon={<DownloadOutlined />}>
                Tải xuống PDF
              </Button>
            )}
          </div>
          {/* Bên phải: các nút còn lại */}
          <div>
            {currentStep === 2 && (
              <Button
                type="primary"
                onClick={() => refModalDanhGiaTonThat?.current?.open({so_id: chiTietHopDongBaoHiemXe?.so_id, so_id_dt: chiTietDoiTuongBaoHiemXe?.gcn?.so_id_dt || 0})}
                className="mr-2"
                icon={<FileAddOutlined />}
                loading={loading}
                disabled={disableSubmit || isDisabledWhenNoContract}
                title={isDisabledWhenNoContract ? "Cần có chi tiết hợp đồng để đánh giá tổn thất" : undefined}>
                Đánh giá tổn thất
              </Button>
            )}
            <Button type="default" onClick={() => onBack()} icon={getIconButtonBackByCurrentStep(currentStep)}>
              {getTitleButtonBackByCurrentStep(currentStep)}
            </Button>
            {/* Button Tiếp theo - chỉ hiển thị khi không phải step 4 */}
            {currentStep !== 4 && (
              <Button
                disabled={isDisabledWhenNoContract}
                type="primary"
                onClick={() => {
                  setCurrentStep(currentStep + 1);
                }}
                className="ml-2"
                iconPosition="end"
                icon={<ArrowRightOutlined />}
                title={isDisabledWhenNoContract ? "Cần có chi tiết hợp đồng để tiếp tục" : undefined}>
                Tiếp theo
              </Button>
            )}

            {/* Các button phê duyệt - chỉ hiển thị khi step 4 */}
            {currentStep === 4 && !showButtonHuyTrinh && (
              <Button
                disabled={isDisabledWhenNoContract}
                type="primary"
                onClick={() => refModalTrinhDuyetHopDong?.current?.open()}
                className="ml-2"
                icon={<EditOutlined />}
                title={isDisabledWhenNoContract ? "Cần có chi tiết hợp đồng để trình duyệt" : undefined}>
                Trình duyệt
              </Button>
            )}

            {currentStep === 4 && showButtonHuyTrinh && <HuyTrinhDuyetButton chiTietHopDong={chiTietHopDongBaoHiemXe} />}
            {(currentStep === 0 || currentStep === 1) && (
              <>
                <Button
                  type="primary"
                  onClick={() => onConfirm("save")}
                  className="ml-2"
                  icon={<CheckOutlined />}
                  loading={loading}
                  disabled={disableSubmit || (currentStep > 0 && isDisabledWhenNoContract)}
                  title={currentStep > 0 && isDisabledWhenNoContract ? "Cần có chi tiết hợp đồng để lưu" : undefined}>
                  Lưu
                </Button>
                <Button
                  type="primary"
                  onClick={() => onConfirm("close")}
                  className="ml-2"
                  icon={<CheckOutlined />}
                  loading={loading}
                  disabled={disableSubmit || (currentStep > 0 && isDisabledWhenNoContract)}
                  title={currentStep > 0 && isDisabledWhenNoContract ? "Cần có chi tiết hợp đồng để lưu" : undefined}>
                  Lưu và đóng
                </Button>
              </>
            )}
          </div>
        </div>
      </Form.Item>
    );
  };
  //Render
  const onChangeStep = (value: number) => {
    // Disable step navigation khi không có chi tiết hợp đồng
    if (isDisabledWhenNoContract) return;
    setCurrentStep(value);
  };

  const StepComponent = () => {
    return (
      <Steps
        size="small"
        // status='error'
        current={currentStep}
        percent={60}
        onChange={isDisabledWhenNoContract ? undefined : onChangeStep}
        style={{
          cursor: isDisabledWhenNoContract ? "not-allowed" : "pointer",
          opacity: isDisabledWhenNoContract ? 0.6 : 1,
        }}
        items={[
          {
            title: "Thông tin hợp đồng",
            disabled: isDisabledWhenNoContract,
            // status: 'error'
            // description: 'Thông tin đơn vị quản lý, khách hàng...',
          },
          {
            title: "Thông tin đối tượng bảo hiểm",
            disabled: isDisabledWhenNoContract,
            // subTitle: 'Left 00:00:08',
            // description: 'Thông tin xe ô tô, xe máy',
          },
          {
            title: "Hình ảnh / Hồ sơ / Tài liệu",
            disabled: isDisabledWhenNoContract,
            // description: 'Thông tin đồng tái, email, ...',
          },
          {
            title: "Thông tin cấu hình",
            disabled: isDisabledWhenNoContract,
            // description: 'Thông tin đồng tái, email, ...',
          },
          {
            title: "Phê duyệt hợp đồng",
            disabled: isDisabledWhenNoContract,
            // description: 'Thông tin đồng tái, email, ...',
          },
        ]}
      />
    );
  };

  // render modal
  return (
    <TrinhDuyetHopDongProvider
      onTrinhDuyetSuccess={() => {
        layChiTietHopDongBaoHiemXe({so_id: chiTietHopDongBaoHiemXe.so_id, ma_doi_tac_ql: chiTietHopDongBaoHiemXe.ma_doi_tac_ql});
        layDanhSachHopDongBaoHiemXePhanTrang(defaultFormValue as any);
      }}
      onHuyTrinhDuyetSuccess={() => {
        layChiTietHopDongBaoHiemXe({so_id: chiTietHopDongBaoHiemXe.so_id, ma_doi_tac_ql: chiTietHopDongBaoHiemXe.ma_doi_tac_ql});
        layDanhSachHopDongBaoHiemXePhanTrang(defaultFormValue as any);
      }}>
      <Flex vertical gap="middle" align="flex-start">
        <Modal
          centered
          title={
            <HeaderModal
              title={chiTietHopDongBaoHiemXe && chiTietHopDongBaoHiemXe?.so_hd ? `Chi tiết hợp đồng ${chiTietHopDongBaoHiemXe?.so_hd}` : "Tạo mới hợp đồng"}
              trang_thai_ten={chiTietHopDongBaoHiemXe?.trang_thai_ten}
              trang_thai={chiTietHopDongBaoHiemXe?.trang_thai}
            />
          }
          open={isOpen}
          onOk={() => setIsOpen(false)}
          onCancel={() => {
            // Sử dụng closeModal để đảm bảo reset đầy đủ
            closeModal();
          }}
          footer={renderFooter}
          // loading={loading}
          closable
          maskClosable={false}
          width="100vw"
          style={{
            top: 0,
            left: 0,
            padding: 0,
          }}
          styles={{
            body: {
              height: "80vh",
            },
          }}
          className="custom-full-modal">
          {StepComponent()}
          {currentStep === 0 && (
            <ThongTinHopDongXeStep
              listDaiLyKhaiThac={[]}
              formNhapHopDongXe={formNhapHopDongXe}
              initListDaiLyKhaiTac={initListDaiLyKhaiThac}
              khachHangSelected={khachHangSelected || {ten: "", ma: ""}}
              setKhachHangSelected={setKhachHangSelected}
              canBoSeleceted={canBoSelected || {ten: "", ma: ""}}
              setCanBoSelected={setCanBoSelected}
              daiLyKhaiThacSelected={daiLyKhaiThacSelected || {ten: "", ma: ""}}
              setDaiLyKhaiThacSelected={setDaiLyKhaiThacSelected}
            />
          )}
          {currentStep === 1 && <ThongTinDoiTuongBaoHiemXeStep formNhapDoiTuongBaoHiemXe={formNhapDoiTuongBaoHiemXe} />}
          {currentStep === 2 && <ThongTin_HinhAnhHoSoTaiLieu_HopDongXe_Step />}
          {currentStep === 3 && <ThongTinCauHinhBaoHiemXeStep pageSize={13} />}
          {currentStep === 4 && <ThongTinPheDuyetHopDongXeStep onDownloadPDF={handleDownloadPDFCallback} />}
          <ModalTrinhDuyetHopDong ref={refModalTrinhDuyetHopDong} chiTietHopDong={chiTietHopDongBaoHiemXe} actionCode={ACTION_CODE.TRINH_PHE_DUYET_HD_XCG} />
        </Modal>

        <ModalDanhGiaTonThat ref={refModalDanhGiaTonThat} />
      </Flex>
    </TrinhDuyetHopDongProvider>
  );
});

// Component con để xử lý hủy trình duyệt sử dụng TrinhDuyetHopDong context
const HuyTrinhDuyetButton: React.FC<{chiTietHopDong: any}> = ({chiTietHopDong}) => {
  const {huyTrinhPheDuyetHopDong} = useTrinhDuyetHopDongContext();

  const handleHuyTrinh = async () => {
    await huyTrinhPheDuyetHopDong({
      so_id: chiTietHopDong.so_id,
      nv: chiTietHopDong.nv,
    });
  };

  return (
    <Popcomfirm
      danger
      title="Thông báo"
      onConfirm={handleHuyTrinh}
      okText="Đồng ý"
      description="Bạn có chắc chắn muốn huỷ trình hợp đồng này hay không?"
      buttonTitle="Huỷ trình"
      buttonClassName="ml-2"
      buttonIcon={<CloseOutlined />}
      type="default"
    />
  );
};

ModalThemHopDongBaoHiemXe.displayName = "ModalThemHopDongBaoHiemXe";
export default ModalThemHopDongBaoHiemXe;

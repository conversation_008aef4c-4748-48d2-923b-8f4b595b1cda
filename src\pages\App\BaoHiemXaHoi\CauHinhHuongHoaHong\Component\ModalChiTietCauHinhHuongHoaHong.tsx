import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState} from "react";
import {
  ngayApDungColumns,
  IModalChiTietCauHinhHuongHoaHongRef,
  Props,
  TableNgayApDungDataType,
  FormThemNgayApDung,
  DataIndexNgayApDung,
  TableCauHinhHuongHoaHongDataType,
  cauHinhHuongHoaHongColumns,
  DataIndexCauHinhHuongHoaHong,
  IModalThemCauHinhHoaHongRef,
} from "./index.configs";
import {Col, Dropdown, Flex, Form, InputRef, Modal, Row, Space, Table, TableColumnType} from "antd";
import {useCauHinhHuongHoaHongContext} from "../index.context";
import {isEqual} from "lodash";
import {Button, FormInput, HeaderModal, Highlighter, Popcomfirm, TableFilterDropdown} from "@src/components";
import {CloseOutlined, PlusCircleOutlined} from "@ant-design/icons";
import {defaultTableProps, fillRowTableEmpty} from "@src/hooks";
import {FilterDropdownProps} from "antd/es/table/interface";
import dayjs from "dayjs";
import isSameOrBefore from "dayjs/plugin/isSameOrBefore";
import {ModalThemCauHinhHoaHong} from "./ModalThemCauHinhHoaHong";
import "../index.default.scss";
// import {ModalThemCauHoi} from "./ModalThemCauHoi";

dayjs.extend(isSameOrBefore);
// const {ma_doi_tac_ql, ten, nv} = FormchiTietTaiKhoanDonViThuHo;

const ModalChiTietCauHinhHuongHoaHongComponent = forwardRef<IModalChiTietCauHinhHuongHoaHongRef, Props>(({}: Props, ref) => {
  useImperativeHandle(ref, () => ({
    open: (dataDanhMucTinhThanh?: CommonExecute.Execute.IDanhMucTinhThanh) => {
      setIsOpen(true);
      //   if (dataDanhMucTinhThanh) setchiTietTaiKhoanDonViThuHo(dataDanhMucTinhThanh);
    },
    close: () => {
      setIsOpen(false);
      //   setchiTietTaiKhoanDonViThuHo(null);
      setDanhSachCauHinhHuongHoaHong([]);
      console.log("danhSachCauHinhHuongHoaHong", danhSachCauHinhHuongHoaHong);
    },
  }));
  const refModalThemCauHinhHoaHong = useRef<IModalThemCauHinhHoaHongRef>(null);
  const {ngay_ad} = FormThemNgayApDung;
  const [isOpen, setIsOpen] = useState(false);
  const [pageSize, setPageSize] = useState(8);
  const {
    loading,
    filterParams,
    danhSachCauHinhHuongHoaHongNgayApDung,
    chiTietTaiKhoanDonViThuHo,
    setDanhSachCauHinhHuongHoaHong,
    layDanhSachCauHinhHuongHoaHongNgayApDung,
    layDanhSachCauHinhHuongHoaHong,
    danhSachCauHinhHuongHoaHong,
    xoaNgayApDungCauHinhHuongHoaHong,
    xoaCauHinhHuongHoaHong,
    updateCauHinhHuongHoaHongNgayApDung,
    layChiTietCauHinhHuongHoaHong,
  } = useCauHinhHuongHoaHongContext();
  const [searchedColumn, setSearchedColumn] = useState("");
  const [searchText, setSearchText] = useState("");
  const searchInput = useRef<InputRef>(null);
  const [formBHXH] = Form.useForm();
  const [formBHYT] = Form.useForm();
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [formThemNgayApDung] = Form.useForm();
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const [ngayAdMoiTao, setNgayAdMoiTao] = useState<string | null>(null);
  const [selectedNgayApDung, setSelectedNgayApDung] = useState<number | null>(null);

  useEffect(() => {
    if (chiTietTaiKhoanDonViThuHo?.ma) {
      layDanhSachCauHinhHuongHoaHongNgayApDung({ma_tvv: chiTietTaiKhoanDonViThuHo.ma, ma_dvi: chiTietTaiKhoanDonViThuHo.ma_dvi});
    }
  }, [chiTietTaiKhoanDonViThuHo]);

  useEffect(() => {
    if (selectedNgayApDung !== null) {
      setDisableSubmit(false);
    } else {
      setDisableSubmit(true);
    }
  }, [selectedNgayApDung]);
  // Tự động chọn dòng cuối cùng khi danh sách thay đổi
  useEffect(() => {
    if (danhSachCauHinhHuongHoaHongNgayApDung.length > 0) {
      let selected;
      if (ngayAdMoiTao) {
        // Tìm ngày vừa tạo
        selected = danhSachCauHinhHuongHoaHongNgayApDung.find(item => dayjs(item.ngay_ad, "DD/MM/YYYY").isSame(dayjs(ngayAdMoiTao, "DD/MM/YYYY")));
        setNgayAdMoiTao(null); // Reset lại sau khi đã select
      } else {
        // Tìm ngày gần hôm nay nhất (không vượt quá hôm nay)
        const today = dayjs();
        const validDates = danhSachCauHinhHuongHoaHongNgayApDung.filter(item => dayjs(item.ngay_ad, "DD/MM/YYYY").isBefore(today) || dayjs(item.ngay_ad, "DD/MM/YYYY").isSame(today));

        if (validDates.length > 0) {
          selected = validDates.reduce((prev, curr) => {
            const prevDiff = today.diff(dayjs(prev.ngay_ad, "DD/MM/YYYY"), "day");
            const currDiff = today.diff(dayjs(curr.ngay_ad, "DD/MM/YYYY"), "day");
            return currDiff < prevDiff ? curr : prev;
          });
        } else {
          // Fallback: nếu không có ngày nào <= hôm nay, chọn ngày gần nhất
          selected = danhSachCauHinhHuongHoaHongNgayApDung.reduce((prev, curr) => {
            const prevDiff = Math.abs(today.diff(dayjs(prev.ngay_ad, "DD/MM/YYYY"), "day"));
            const currDiff = Math.abs(today.diff(dayjs(curr.ngay_ad, "DD/MM/YYYY"), "day"));
            return currDiff < prevDiff ? curr : prev;
          });
        }
      }
      if (selected && selected.ngay_ad !== undefined && selected.ngay_ad !== null) {
        setSelectedNgayApDung(Number(selected.bt));
        layDanhSachCauHinhHuongHoaHong({bt_ad: selected.bt || 0});
        console.log("selected.ngay_ad", selected.ngay_ad);
      }
    } else {
      setSelectedNgayApDung(null);
      setDanhSachCauHinhHuongHoaHong([]);
    }
  }, [danhSachCauHinhHuongHoaHongNgayApDung, chiTietTaiKhoanDonViThuHo]);
  const closeModal = () => {
    setIsOpen(false);
    // setchiTietTaiKhoanDonViThuHo(null);
    // setDanhSachCauHoi([]);
    setDanhSachCauHinhHuongHoaHong([]);
    // form.resetFields();
    formBHXH.resetFields();
    formBHYT.resetFields();
    // setFilterParams(filterParams);
  };
  // Dữ liệu bảng câu hỏi áp dụng(bên trái)
  const dataTableListNgayApDung = useMemo<Array<TableNgayApDungDataType>>(() => {
    try {
      const mappedData = danhSachCauHinhHuongHoaHongNgayApDung.map((item: any, index: number) => ({
        stt: item.sott ?? index + 1,
        ngay_ad: item.ngay_ad || "",
        ma_tvv: item.ma_tvv,
        ma_dvi: item.ma_dvi,
        key: index.toString(),
        bt: item.bt,
        hanh_dong: () => renderDeleteButton(item.bt),
      }));
      const arrEmptyRow = fillRowTableEmpty(mappedData.length, pageSize);
      return [...mappedData, ...arrEmptyRow];
    } catch (error) {
      console.error("Lỗi khi xử lý dữ liệu bảng ngày áp dụng:", error);
      return [];
    }
  }, [danhSachCauHinhHuongHoaHongNgayApDung, pageSize]);
  const dataTableListCauHinhHuongHoaHong = useMemo<Array<TableCauHinhHuongHoaHongDataType>>(() => {
    try {
      const mappedData = danhSachCauHinhHuongHoaHong.map((item: any, index: number) => ({
        stt: item.sott ?? index + 1,
        bt: item.bt,
        bt_ad: item.bt_ad,
        ma_sp: item.ma_sp,
        ma_dvi: item.ma_dvi,
        loai_hd: item.loai_hd,
        loai_ho_gia_dinh: item.loai_ho_gia_dinh,
        tlhh: item.tlhh,
        so_thang_tu: item.so_thang_tu,
        so_thang_toi: item.so_thang_toi,
        loai_hd_ten: item.loai_hd_ten,
        loai_ho_gia_dinh_ten: item.loai_ho_gia_dinh_ten,
        key: index.toString(),
        hanh_dong: () => renderDeleteButtonCT(item.bt),
      }));
      const arrEmptyRow = fillRowTableEmpty(mappedData.length, pageSize);
      return [...mappedData, ...arrEmptyRow];
    } catch (error) {
      console.error("Lỗi khi xử lý dữ liệu bảng ngày áp dụng:", error);
      return [];
    }
  }, [danhSachCauHinhHuongHoaHong, pageSize, selectedNgayApDung]);
  const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: DataIndexNgayApDung) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);

  const handleReset = useCallback(
    (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: DataIndexNgayApDung) => {
      clearFilters();
      setSearchedColumn("");
      handleSearch([""], confirm, dataIndex);
    },
    [handleSearch],
  );
  const handleNgayApDungRowClick = async (record: TableNgayApDungDataType) => {
    // Nếu là hàng trống (key chứa "empty" hoặc không có bt), set null
    if (record.key?.toString().includes("empty") || record.bt === undefined || record.bt === null) {
      setSelectedNgayApDung(null);
      setDanhSachCauHinhHuongHoaHong([]);
      return;
    }

    console.log("record", record);
    setSelectedNgayApDung(record.bt);

    layDanhSachCauHinhHuongHoaHong({bt_ad: record.bt});
    // updateCauHinhHuongHoaHongNgayApDung({ma_tvv: chiTietTaiKhoanDonViThuHo.ma, ngay_ad: Number(dayjs(record.ngay_ad, "DD/MM/YYYY").format("YYYYMMDD"))});
  };
  const handleSubmit = async () => {
    try {
      const values = await formThemNgayApDung.validateFields();

      if (!values.ngay_ad) {
        console.log("Vui lòng chọn ngày áp dụng");
        return;
      }
      console.log("chiTietTaiKhoanDonViThuHo", chiTietTaiKhoanDonViThuHo);
      await updateCauHinhHuongHoaHongNgayApDung({
        ma_tvv: chiTietTaiKhoanDonViThuHo?.ma,
        ma_dvi: chiTietTaiKhoanDonViThuHo?.ma_dvi,
        ngay_ad: Number(dayjs(values.ngay_ad).format("YYYYMMDD")),
      });
      setDropdownOpen(false); // Đóng dropdown sau khi lưu thành công
      formThemNgayApDung.resetFields();
      setNgayAdMoiTao(values.ngay_ad); // Lưu lại ngày vừa tạo

      if (chiTietTaiKhoanDonViThuHo?.ma) {
        layDanhSachCauHinhHuongHoaHongNgayApDung({ma_tvv: chiTietTaiKhoanDonViThuHo.ma, ma_dvi: chiTietTaiKhoanDonViThuHo.ma_dvi});
      }
    } catch (error) {
      console.log("Lỗi khi submit:", error);
      // Không đóng dropdown nếu có lỗi để người dùng có thể sửa
    }
  };
  const handleDelete = async (bt: number) => {
    try {
      // TODO: Implement delete function - onDeleteCauHoiApDung is not available
      await xoaNgayApDungCauHinhHuongHoaHong({
        bt: Number(bt),
      });

      // Reset selection sau khi xóa
      setSelectedNgayApDung(null);
      //  setDanhSachCauHinhPhanCapPheDuyetCT([]);

      // Refresh danh sách
      if (chiTietTaiKhoanDonViThuHo?.ma) {
        layDanhSachCauHinhHuongHoaHongNgayApDung({ma_tvv: chiTietTaiKhoanDonViThuHo.ma, ma_dvi: chiTietTaiKhoanDonViThuHo.ma_dvi});
      }
    } catch (error) {
      console.log("Lỗi khi xóa ngày áp dụng:", error);
    }
  };
  const handleDeleteCT = async (bt: number) => {
    try {
      await xoaCauHinhHuongHoaHong({
        bt: Number(bt),
      });

      // Refresh danh sách
      if (chiTietTaiKhoanDonViThuHo?.ma) {
        layDanhSachCauHinhHuongHoaHongNgayApDung({ma_tvv: chiTietTaiKhoanDonViThuHo.ma, ma_dvi: chiTietTaiKhoanDonViThuHo.ma_dvi});
      }
    } catch (error) {
      console.log("Lỗi khi xóa ngày áp dụng:", error);
    }
  };
  const getColumnSearchCauHoiApDungProps = (dataIndex: DataIndexNgayApDung, title: string): TableColumnType<DataIndexNgayApDung> => ({
    filterDropdown: ({setSelectedKeys, selectedKeys, confirm, clearFilters}) => (
      <TableFilterDropdown
        ref={searchInput}
        title={title}
        selectedKeys={selectedKeys}
        dataIndex={dataIndex}
        setSelectedKeys={setSelectedKeys}
        handleSearch={handleSearch}
        confirm={confirm}
        clearFilters={clearFilters}
        handleReset={handleReset}
      />
    ),
    // filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} />,
    onFilter: (value, record) =>
      record[dataIndex as keyof DataIndexNgayApDung]
        ? record[dataIndex as keyof DataIndexNgayApDung]
            .toString()
            .toLowerCase()
            .includes((value as string).toLowerCase())
        : false,
    filterDropdownProps: {
      onOpenChange(open) {
        if (open) {
          setTimeout(() => searchInput.current?.select(), 100);
        }
      },
    },
    // filters: dataIndex === "trang_thai_ten" ? radioItemTrangThaiNhomTable : undefined,
    render: (text, record, index) => {
      //   if (dataIndex === "trang_thai_ten") {
      //     // Xác định màu dựa vào text (trạng thái hiển thị)
      //     const color = text === "Đang sử dụng" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];
      //     if (record.key.toString().includes("empty")) return "";
      //     return <Tag color={color}>{text}</Tag>;
      //   }
      // if (record.key.toString().includes("empty")) return <div style={{height: 20}} />;

      return searchedColumn === dataIndex ? (
        <Highlighter searchWords={[searchText]} textToHighlight={text ? text.toString() : ""} />
      ) : text !== undefined ? (
        text
      ) : (
        <Button type="link" className="h-auto p-0" style={{color: "transparent"}} icon={<CloseOutlined style={{visibility: "hidden"}} />}></Button>
      );
    },
  });
  const getColumnSearchCauHinhHuongHoaHongProps = (dataIndex: DataIndexCauHinhHuongHoaHong, title: string): TableColumnType<DataIndexCauHinhHuongHoaHong> => ({
    filterDropdown: ({setSelectedKeys, selectedKeys, confirm, clearFilters}) => (
      <TableFilterDropdown
        ref={searchInput}
        title={title}
        selectedKeys={selectedKeys}
        dataIndex={dataIndex}
        setSelectedKeys={setSelectedKeys}
        handleSearch={handleSearch}
        confirm={confirm}
        clearFilters={clearFilters}
        handleReset={handleReset}
      />
    ),
    // filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} />,
    onFilter: (value, record) =>
      record[dataIndex as keyof DataIndexCauHinhHuongHoaHong]
        ? record[dataIndex as keyof DataIndexCauHinhHuongHoaHong]
            .toString()
            .toLowerCase()
            .includes((value as string).toLowerCase())
        : false,
    filterDropdownProps: {
      onOpenChange(open) {
        if (open) {
          setTimeout(() => searchInput.current?.select(), 100);
        }
      },
    },
    render: (text, record, index) => {
      return searchedColumn === dataIndex ? (
        <Highlighter searchWords={[searchText]} textToHighlight={text ? text.toString() : ""} />
      ) : text !== undefined ? (
        text
      ) : (
        <Button type="link" className="h-auto p-0" style={{color: "transparent"}} icon={<CloseOutlined style={{visibility: "hidden"}} />}></Button>
      );
    },
  });
  const renderDeleteButton = (bt?: number) => {
    if (!bt) return null;
    return (
      <div>
        <Popcomfirm
          title="Thông báo"
          onConfirm={() => handleDelete(bt)}
          htmlType="button"
          okText="Xóa"
          description="Bạn có chắc muốn xóa ngày áp dụng?"
          buttonTitle={""}
          buttonColor="red"
          okButtonProps={{
            style: {
              backgroundColor: "white",
              borderColor: "red",
              color: "red",
            },
          }}
          style={{width: "fit-content"}}
          variant="text"
          className="h-auto"
          icon={<CloseOutlined />}
          buttonIcon
        />
      </div>
    );
  };
  const renderDeleteButtonCT = (bt?: number) => {
    if (!bt) return null;
    return (
      <span onClick={e => e.stopPropagation()}>
        <Popcomfirm
          title="Thông báo"
          onConfirm={() => handleDeleteCT(bt)}
          htmlType="button"
          okText="Xóa"
          description="Bạn có chắc muốn xóa cấu hình?"
          buttonTitle={""}
          buttonColor="red"
          okButtonProps={{
            style: {
              backgroundColor: "white",
              borderColor: "red",
              color: "red",
            },
          }}
          style={{width: "fit-content"}}
          variant="text"
          className="h-auto"
          icon={<CloseOutlined />}
          buttonIcon
        />
      </span>
    );
  };

  const renderTableCauHoiApDungFooter = () => {
    return (
      <div className="">
        <Form.Item className="" style={{marginTop: 16, marginRight: 8, marginBottom: 0}}>
          <Space className="">
            <Dropdown
              className=""
              open={dropdownOpen}
              onOpenChange={setDropdownOpen}
              trigger={["click"]}
              getPopupContainer={triggerNode => triggerNode.parentNode as HTMLElement}
              dropdownRender={() => (
                <div style={{padding: 8, display: "flex"}}>
                  <Form id="formThemNgayApDung" form={formThemNgayApDung} layout="vertical">
                    <div style={{display: "flex", justifyContent: "space-between", alignItems: "flex-end"}}>
                      {renderFormInputColum({...ngay_ad}, 24)}

                      <Button type="primary" onClick={handleSubmit} style={{marginLeft: 8, marginBottom: 8}}>
                        Áp dụng
                      </Button>
                    </div>
                  </Form>
                </div>
              )}
              placement="topRight">
              <Button className="w-full" type="primary" icon={<PlusCircleOutlined />} onClick={() => setDropdownOpen(true)}>
                Thêm ngày áp dụng
              </Button>
            </Dropdown>
          </Space>
        </Form.Item>
      </div>
    );
  };

  const renderTableCauHoiApDung = () => {
    return (
      <Table<TableNgayApDungDataType>
        className="table-ngay-ap-dung no-header-border-radius"
        {...defaultTableProps}
        style={{cursor: "pointer", borderBottom: "1px solid #f0f0f0"}}
        onRow={(record, rowIndex) => {
          return {
            style: {
              cursor: "pointer",
              background: record.bt === selectedNgayApDung ? "#96BF49" : undefined, // Làm nổi bật hàng được chọn
            },
            onClick: () => handleNgayApDungRowClick(record), // Xử lý nhấp vào hàng
          };
        }}
        title={null}
        pagination={false}
        columns={(ngayApDungColumns || []).map(item => {
          // Đảm bảo item.key được định nghĩa, item.title là chuỗi và item.key không phải là "stt" trước khi thêm ô tìm kiếm
          return {
            ...item,
            ...(item.key && typeof item.title === "string" && item.key !== "stt" ? getColumnSearchCauHoiApDungProps(item.key as keyof TableNgayApDungDataType, item.title) : {}),
          };
        })}
        dataSource={dataTableListNgayApDung}
        bordered
        scroll={dataTableListNgayApDung.length > pageSize ? {y: 215} : undefined}
      />
    );
  };
  const renderTableCauHinhHoaHong = () => {
    return (
      <Table<TableCauHinhHuongHoaHongDataType>
        className="table-cau-hinh-hoa-hong no-header-border-radius"
        {...defaultTableProps}
        style={{cursor: "pointer", borderBottom: "1px solid #f0f0f0"}}
        onRow={(record, rowIndex) => {
          return {
            style: {
              cursor: "pointer",
              // background: record.ngay_ad && record.ngay_ad === selectedCauHinhHuongHoaHong ? "#96BF49" : undefined,
            },
            onClick: async () => {
              if (record.key.toString().includes("empty")) {
                return;
              }
              const response = await layChiTietCauHinhHuongHoaHong({bt: record.bt});

              if (response?.bt) {
                refModalThemCauHinhHoaHong.current?.open(response);
              } else {
                console.log("[TaiKhoanDonViThuHo] Không có response.ma, không mở modal");
              }
            },
          };
        }}
        title={null}
        pagination={false}
        columns={(cauHinhHuongHoaHongColumns || []).map(item => {
          // Đảm bảo item.key được định nghĩa, item.title là chuỗi và item.key không phải là "stt" trước khi thêm ô tìm kiếm
          return {
            ...item,
            ...(item.key && typeof item.title === "string" && item.key !== "stt" ? getColumnSearchCauHinhHuongHoaHongProps(item.key as keyof TableCauHinhHuongHoaHongDataType, item.title) : {}),
          };
        })}
        dataSource={dataTableListCauHinhHuongHoaHong}
        scroll={dataTableListCauHinhHuongHoaHong.length > pageSize ? {y: 225} : undefined}
      />
    );
  };
  const renderFormInputColum = (props?: any, span = 4) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );

  const renderFooter = () => {
    return (
      <Form.Item className="mb-0" style={{display: "flex", justifyContent: "end"}}>
        <Button
          type="primary"
          block
          icon={<PlusCircleOutlined />}
          onClick={() => {
            refModalThemCauHinhHoaHong.current?.open();
          }}>
          Tạo mới
        </Button>
      </Form.Item>
    );
  };
  const renderTable = () => {
    return (
      <Row>
        <Col span={5} style={{paddingRight: 16}}>
          {renderTableCauHoiApDung()}
          {renderTableCauHoiApDungFooter()}
        </Col>
        <Col span={19} style={{justifyContent: "space-between", display: "flex", flexDirection: "column"}}>
          {renderTableCauHinhHoaHong()}
          {renderFooter()}
        </Col>
      </Row>
    );
  };
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        title={<HeaderModal title={chiTietTaiKhoanDonViThuHo ? `Chi tiết cấu hình hưởng hoa hồng  ${chiTietTaiKhoanDonViThuHo.ten}` : ""} trang_thai={chiTietTaiKhoanDonViThuHo?.trang_thai} />}
        maskClosable={false}
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width="80vw"
        // styles={{
        //   body: {
        //     height: "60vh",
        //   },
        // }}
        footer={null}
        className="modal-ct-cau-hinh-huong-hoa-hong [&_.ant-space]:w-full">
        {renderTable()}
      </Modal>
      <ModalThemCauHinhHoaHong ref={refModalThemCauHinhHoaHong} selectedNgayApDung={selectedNgayApDung} />
    </Flex>
  );
});
ModalChiTietCauHinhHuongHoaHongComponent.displayName = "ModalChiTietCauHinhHuongHoaHongComponent";
export const ModalChiTietCauHinhHuongHoaHong = memo(ModalChiTietCauHinhHuongHoaHongComponent, isEqual);

import {CheckCircleOutlined, SearchOutlined} from "@ant-design/icons";
import {IFormInput, ReactQuery} from "@src/@types";
import {Button, FormInput, Highlighter, TableFilterDropdown} from "@src/components";
import {COLOR_PALETTE} from "@src/constants";
import {defaultPaginationTableProps, defaultTableProps, fillRowTableEmpty} from "@src/hooks";
import {Col, Flex, Form, InputRef, Modal, Row, Table, TableColumnType, Tag, Tooltip} from "antd";
import {FilterDropdownProps} from "antd/es/table/interface";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState} from "react";
import {useHopDongConNguoiContext} from "@src/pages/App/BaoHiemConNguoi/HopDongConNguoi/index.context";
import "../index.default.scss";
import {
  defaultFilterCanBoParams,
  FormTimKiemCanBo,
  IModalTimCanBoRef,
  ModalTimCanBoProps,
  radioItemTrangThaiCanBoSelect,
  radioItemTrangThaiCanBoTable,
  tableCanBoColumn,
  TableCanBoColumnDataIndex,
  TableCanBoColumnDataType,
} from "./Constant";

const {ma_doi_tac_ql, ma_chi_nhanh_ql, ma, ten, nd_tim, phong_ql, cmt, dthoai, email, trang_thai} = FormTimKiemCanBo;

const ModalTimCanBoHopDongConNguoiComponent = forwardRef<IModalTimCanBoRef, ModalTimCanBoProps>(
  ({onSelectCanBo, maDoiTacSelected, listChiNhanhSelect, listPhongBanSelect, maPhongBanSelected}: ModalTimCanBoProps, ref) => {
    useImperativeHandle(ref, () => ({
      open: canBoSelected => {
        setIsOpen(true);
        initData();
        if (canBoSelected) setChiTietCanBoSelected(canBoSelected);
      },
      close: () => setIsOpen(false),
    }));

    const {listDoiTac, loading, getListCanBoQuanLy} = useHopDongConNguoiContext();

    // DATA TABLE CÁN BỘ
    const [listCanBo, setListCanBo] = useState<Array<CommonExecute.Execute.IDoiTacNhanVien>>([]);
    const [tongSoDongCanBo, setTongSoDongCanBo] = useState<number>(0);
    const [filterCanBoParams, setFilterCanBoParams] = useState<ReactQuery.ITimKiemPhanTrangCanBoQuanLyParams>(defaultFilterCanBoParams);

    const [chiTietCanBoSelected, setChiTietCanBoSelected] = useState<CommonExecute.Execute.IDoiTacNhanVien | null>(null);

    const [isOpen, setIsOpen] = useState<boolean>(false);

    const refSearchInputTable = useRef<InputRef>(null);
    const [searchTextTable, setSearchTextTable] = useState<string>(""); //lưu textsearch để hiển thị vào cell
    const [searchedColumn, setSearchedColumn] = useState<TableCanBoColumnDataIndex | "">(""); //key column đang được search

    const [formTimCanBo] = Form.useForm();

    const closeModal = useCallback(() => {
      setIsOpen(false);
      // setChiTietCanBoSelected(null);// cho thằng này vào thì object chiTietCanBo ở ô select sẽ bị null ????
      formTimCanBo.resetFields();
    }, [formTimCanBo]);

    //Bấm tiếp theo
    const onPressXacNhan = useCallback(
      async (chiTietCanBoSelected: CommonExecute.Execute.IDoiTacNhanVien | null) => {
        try {
          onSelectCanBo(chiTietCanBoSelected);
          closeModal();
        } catch (error) {
          console.log("onPressXacNhan error", error);
        }
      },
      [onSelectCanBo, closeModal],
    );

    /* CÁN BỘ */
    // TÌM KIẾM PHÂN TRANG CÁN BỘ
    const onPressSearchCanBo = useCallback(async () => {
      try {
        const response = await getListCanBoQuanLy({
          ...filterCanBoParams,
        });
        if (response.data) {
          setListCanBo(response.data);
          setTongSoDongCanBo(response.tong_so_dong);
        }
      } catch (error) {
        console.log("onPressSearchCanBo error ", error);
      }
    }, [filterCanBoParams, getListCanBoQuanLy]);

    useEffect(() => {
      onPressSearchCanBo();
    }, [filterCanBoParams]);

    const initData = async () => {
      try {
        setFilterCanBoParams({
          ...filterCanBoParams,
          ma_doi_tac_ql: maDoiTacSelected,
          // phong_ql: maPhongBanSelected
        });
        formTimCanBo.setFields([
          {
            name: "ma_doi_tac_ql",
            value: maDoiTacSelected,
          },
          // {
          //   name: "phong_ql",
          //   value: maPhongBanSelected,
          // },
        ]);
      } catch (error) {
        console.log("error", error);
      }
    };

    //MAP VALUE CỦA LIST VÀO TABLE
    const dataTableListCanBo = useMemo<Array<TableCanBoColumnDataType>>(() => {
      try {
        const tableData = listCanBo.map(itemCanBo => {
          return {
            key: itemCanBo.ma, // bắt buộc phải có key
            sott: itemCanBo.sott,
            ten: itemCanBo.ten,
            ma: itemCanBo.ma,
            dchi: itemCanBo.dchi,
            dthoai: itemCanBo.dthoai,
            trang_thai_ten: itemCanBo.trang_thai_ten,
          };
        });
        const arrEmptyRow: Array<TableCanBoColumnDataType> = fillRowTableEmpty(tableData.length, 10);
        return [...tableData, ...arrEmptyRow];
      } catch (error) {
        console.log("dataTableListCanBo error", error);
        return [];
      }
    }, [listCanBo]);

    const onSearchCanBo = (values: ReactQuery.ITimKiemPhanTrangCanBoQuanLyParams) => {
      setFilterCanBoParams({...values, trang: 1, so_dong: 10});
    };

    const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: TableCanBoColumnDataIndex) => {
      confirm();
      setSearchTextTable(selectedKeys[0]);
      setSearchedColumn(dataIndex);
    }, []);

    const handleReset = useCallback(
      (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: TableCanBoColumnDataIndex) => {
        clearFilters();
        setSearchTextTable("");
        handleSearch([""], confirm, dataIndex);
      },
      [handleSearch],
    );

    // RENDER
    //FOOTER
    const renderFooter = () => {
      return (
        <Tooltip title={chiTietCanBoSelected ? "" : "Vui lòng chọn cán bộ"}>
          <Button type={"primary"} onClick={() => onPressXacNhan(chiTietCanBoSelected)} className="w-40" icon={<CheckCircleOutlined />} disabled={chiTietCanBoSelected ? false : true}>
            Chọn
          </Button>
        </Tooltip>
      );
    };
    const renderFormInputColum = (props: IFormInput) => (
      <Col span={4}>
        <FormInput {...props} />
      </Col>
    );

    // FORM TÌM KIẾM TABLE CÁN BỘ
    const renderHeaderTableQuanLyCanBo = () => (
      <Form
        form={formTimCanBo}
        initialValues={{}}
        layout="vertical" //Tất cả các input nằm trên 1 dòng
        onFinish={onSearchCanBo}
        className="[&_.ant-form-item]:mb-0">
        <Row gutter={16}>
          {renderFormInputColum({...ma_doi_tac_ql, options: listDoiTac})}
          {renderFormInputColum({...ma_chi_nhanh_ql, options: listChiNhanhSelect})}
          {renderFormInputColum({...phong_ql, options: listPhongBanSelect})}
          {renderFormInputColum(ma)}
          {renderFormInputColum(ten)}
          {renderFormInputColum(cmt)}
        </Row>

        <Row gutter={16}>
          {renderFormInputColum(dthoai)}
          {renderFormInputColum(email)}
          {renderFormInputColum(nd_tim)}
          {renderFormInputColum({...trang_thai, options: radioItemTrangThaiCanBoSelect})}
          <Col span={4}>
            <Button type="primary" htmlType="submit" loading={loading} icon={<SearchOutlined />} block>
              Tìm kiếm
            </Button>
          </Col>
        </Row>
      </Form>
    );

    //lấy ra loại filter ở CELL HEADER tương ứng
    const getFilterTableCanBo = (dataIndex: string) => {
      if (dataIndex === "trang_thai_ten") return radioItemTrangThaiCanBoTable;
      return undefined;
    };

    // dataIndex : là các key của column, title : tiêu đề của column
    const getColumnSearchProps = (dataIndex: TableCanBoColumnDataIndex, title: string): TableColumnType<TableCanBoColumnDataType> => ({
      /**
       *  filterDropdown: UI tuỳ chỉnh của ô filter (component render ra trong dropdown filter)
       * @param param0
       * @returns
       * dataIndex !== "trang_thai_ten"
       */
      filterDropdown:
        dataIndex !== "trang_thai_ten"
          ? filterDropdownParams => (
              <TableFilterDropdown
                ref={refSearchInputTable}
                title={title}
                dataIndex={dataIndex}
                handleSearch={(selectedKeys, confirm, dataIndex) => handleSearch(selectedKeys, confirm, dataIndex)}
                handleReset={(clearFilters, confirm, dataIndex) => clearFilters && handleReset(clearFilters, confirm, dataIndex)}
                {...filterDropdownParams}
              />
            )
          : undefined,
      /**
       * filterIcon : icon hiển thị trên header column khi filter
       * @param filtered :  // "filtered". là boolean, true nếu đang áp dụng filter,
       *  biến này thay đổi khi hàm confirm được gọi, hoặc hàm clearFilters được gọi
       * @returns
       */
      filterIcon: (filtered: boolean) => {
        return <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} size={40} />;
      },
      /**
       * onFilter: hàm filter dữ liệu tại client-side (bắt buộc phải khai báo để filter hoạt động)
       * do hàm onFilter luôn phải trả về boolean, nên thêm || false vào để k bị lỗi
       * @param value : giá trị filter người dùng nhập vào
       * @param record : từng bản ghi trong dataSource
       * @returns
       */
      onFilter: (value, record) => {
        return (
          record[dataIndex]
            ?.toString()
            .toLowerCase()
            .includes((value as string).toLowerCase()) || false
        );
      },
      //filterDropdownProps: thuộc tính tuì chỉnh hành vi dropdown
      filterDropdownProps: {
        // onOpenChange: callback khi dropdown mở/ tắt thì sẽ focus vào input
        onOpenChange(open) {
          if (open) {
            setTimeout(() => refSearchInputTable.current?.select(), 100); // Focus/ select input khi mở filter
          }
        },
      },
      filterSearch: true,
      filters: getFilterTableCanBo(dataIndex),
      render: (
        text,
        record,
        //  index
      ) => {
        if (dataIndex === "trang_thai_ten") {
          const color = text === "Đang sử dụng" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];
          if (record.key.toString().includes("empty")) return "";
          return (
            <Tag color={color} className="text-[11px]">
              {text}
            </Tag>
          );
        }
        return searchedColumn === dataIndex ? <Highlighter searchWords={[searchTextTable]} textToHighlight={text ? text.toString() : ""} /> : text !== undefined ? text : "\u00A0"; // xử lý chuyển text thành 1 dòng khi text quá dài
      },
    });

    const renderTable = () => (
      <Table<TableCanBoColumnDataType>
        {...defaultTableProps}
        dataSource={dataTableListCanBo} //mảng dữ liệu record được hiển thị
        columns={
          tableCanBoColumn?.map(item => {
            //Nếu là cột stt thì không hiển thị search
            return {...item, ...(item.key === "sott" ? {} : getColumnSearchProps(item.key as keyof TableCanBoColumnDataType, item.title as string))};
          }) || []
        } //định nghĩa cột của table
        rowClassName={record => (record.key === chiTietCanBoSelected?.ma ? "custom-row-selected" : "")} // xử lý việc 1 row được selected -> row đấy sẽ được highlight lên
        loading={loading} //hiển thị loading khi đang gọi API để loading data
        pagination={{
          ...defaultPaginationTableProps,
          defaultPageSize: 10,
          total: tongSoDongCanBo,
          onChange: (page, pageSize) => setFilterCanBoParams({...filterCanBoParams, trang: page, so_dong: pageSize}),
        }}
        title={renderHeaderTableQuanLyCanBo}
        onRow={record => {
          return {
            style: {cursor: loading ? "progress" : "pointer"}, //  thể hiện vị trí con trỏ là button(👈)
            onClick: () => setChiTietCanBoSelected(record as CommonExecute.Execute.IDoiTacNhanVien),
            onDoubleClick: () => onPressXacNhan(record as CommonExecute.Execute.IDoiTacNhanVien),
          };
        }}
      />
    );

    return (
      <Flex vertical gap="middle" align="flex-start">
        <Modal
          className="modal-tim-can-bo-hop-dong-con-nguoi"
          title="Chọn cán bộ"
          centered
          open={isOpen}
          onOk={() => closeModal()}
          onCancel={() => closeModal()}
          width={"95%"}
          styles={{
            body: {
              height: "70vh",
            },
          }}
          maskClosable={false}
          footer={renderFooter}>
          {renderTable()}
        </Modal>
      </Flex>
    );
  },
);

ModalTimCanBoHopDongConNguoiComponent.displayName = "ModalTimCanBoHopDongConNguoiComponent";
export const ModalTimCanBoHopDongConNguoi = memo(ModalTimCanBoHopDongConNguoiComponent, isEqual);

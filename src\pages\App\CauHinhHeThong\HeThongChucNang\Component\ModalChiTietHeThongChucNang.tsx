import {ArrowLeftOutlined, CheckOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {Button, FormInput, HeaderModal, Popcomfirm} from "@src/components";
import {Col, Form, Modal, Row} from "antd";
import {forwardRef, useEffect, useImperativeHandle, useState} from "react";
import {optionLoaiChucNangData, optionLoaiKieuApDungData, optionTrangThaiHeThongChucNangSelect} from "../index.configs";
import {useHeThongChucNangContext} from "../index.context";
import {FormInputConfigs, initFormFields} from "./index.configs";
interface Props {
  danhSachHeThongChucNang: Array<CommonExecute.Execute.IDanhSachHeThongChucNangPhanTrang>;
}

export interface IModalChiTietHeThongChucNangRef {
  open: (data?: CommonExecute.Execute.IChiTietChucNang) => void;
  close: () => void;
}

const ModalChiTietHeThongChucNang = forwardRef<IModalChiTietHeThongChucNangRef, Props>(({danhSachHeThongChucNang}: Props, ref) => {
  const {onUpdateHeThongChucNang, loading, layChiTietChucNang} = useHeThongChucNangContext();
  const {ten, ma, loai, kieu_ad, trang_thai} = FormInputConfigs;
  const [form] = Form.useForm();
  const formValues = Form.useWatch([], form);
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const [chiTietChucNang, setChiTietChucNang] = useState<CommonExecute.Execute.IChiTietChucNang | null>(null);

  useImperativeHandle(ref, () => ({
    open: (dataChiTietChucNang?: CommonExecute.Execute.IChiTietChucNang) => {
      setIsOpen(true);
      form.resetFields();
      if (dataChiTietChucNang) setChiTietChucNang(dataChiTietChucNang);
    },
    close: () => setIsOpen(false),
  }));

  // init form data gọi vào index.configs
  useEffect(() => {
    initFormFields(form, chiTietChucNang);
  }, [chiTietChucNang]);

  //xử lý validate form
  useEffect(() => {
    form
      .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
      .then(() => {
        setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
      })
      .catch(() => {
        setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
      });
  }, [form, formValues]);

  //Bấm Update
  const onPressUpdateHeThongChucNang = async () => {
    try {
      const values: ReactQuery.IUpdateChucNangParams = form.getFieldsValue(); //lấy ra values của form
      await onUpdateHeThongChucNang(values);
      const dataChiTiet = await layChiTietChucNang(values);
      setChiTietChucNang(dataChiTiet);
    } catch (error: any) {
      console.log("onConfirm", error);
    }
  };

  //bấm huỷ / để sau xoá dữ liệu của modal khi nào mở thì gọi lại
  const onPressDeSau = () => {
    setIsOpen(false);
    setChiTietChucNang(null);
  };

  //renderFooter Modal
  const renderFooter = () => {
    return (
      <Form.Item className="mb-0">
        <Button type="default" htmlType="reset" form="formUpdatePhongBan" onClick={onPressDeSau} className="mr-2" icon={<ArrowLeftOutlined />}>
          Quay lại
        </Button>
        <Popcomfirm
          title={disableSubmit ? "Vui lòng nhập đầy đủ thông tin" : ""}
          onConfirm={onPressUpdateHeThongChucNang}
          htmlType="submit"
          okText="Lưu"
          description="Bạn có chắc chắn muốn lưu thông tin?"
          buttonTitle={"   Lưu"}
          buttonDisable={disableSubmit}
          buttonIcon={<CheckOutlined />}
          iconPosition="end"
          loading={loading}
        />
      </Form.Item>
    );
  };
  //Render

  // form input

  const renderFormInputColum = (props?: any, span = 8) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );

  // render modal
  return (
    <Modal
      title={
        <HeaderModal
          title={chiTietChucNang ? ` ${chiTietChucNang.ten}` : "Tạo mới chức năng"}
          trang_thai_ten={chiTietChucNang?.trang_thai_ten || chiTietChucNang?.trang_thai === "D" ? "Đang sử dụng" : chiTietChucNang?.trang_thai === "K" ? "Ngưng sử dụng" : ""}
          trang_thai={chiTietChucNang?.trang_thai}
        />
      }
      open={isOpen}
      onOk={() => setIsOpen(false)}
      onCancel={() => {
        form.resetFields();
        setIsOpen(false);
        setChiTietChucNang(null);
      }}
      footer={renderFooter}
      closable
      maskClosable={false}
      width={{
        xs: "60%",
        sm: "60%",
        md: "60%",
        lg: "60%",
        xl: "60%",
        xxl: "60%",
      }}
      className="[&_.ant-space]:w-full">
      <Form id="formUpdateHeThongChucNang" onFinish={onPressUpdateHeThongChucNang} form={form} layout="vertical" autoComplete="on" className="mt-5">
        <Row gutter={16}>
          {renderFormInputColum({...ma, disabled: chiTietChucNang ? true : false})}
          {renderFormInputColum({...ten, disabled: chiTietChucNang ? true : false}, 16)}
        </Row>
        <Row gutter={16}>
          {renderFormInputColum({...loai, options: optionLoaiChucNangData})}
          {renderFormInputColum({...kieu_ad, options: optionLoaiKieuApDungData})}
          {renderFormInputColum({...trang_thai, options: optionTrangThaiHeThongChucNangSelect})}
        </Row>
      </Form>
    </Modal>
  );
});
ModalChiTietHeThongChucNang.displayName = "ModalChiTietHeThongChucNang";
export default ModalChiTietHeThongChucNang;

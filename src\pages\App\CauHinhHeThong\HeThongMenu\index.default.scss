#HE_THONG_MENU {
  .ant-table-row:hover td {
    background-color: #e8f5e9 !important; //test
  }

  .header-cell-custom {
    background-color: #96bf49 !important;
    // background: linear-gradient(to right, #96bf49, #009a55) !important;
    color: #fff !important;
    font-weight: bold !important;
    text-align: center !important;
    border-radius: 0 !important;
  }

  /* 🔒 Ẩn cả thanh cuộn dọc và ngang */
  .antd-table-hide-scroll .ant-table-body {
    scrollbar-width: none;
    /* Firefox */
    -ms-overflow-style: none;
    /* IE 10+ */
  }

  .antd-table-hide-scroll .ant-table-body::-webkit-scrollbar {
    display: none;
    /* Chrome, Safari */
  }

  //style row đại lý khi được chọn -> highlight background
}

.modal-chon-menu-cha .header-cell-custom {
  background-color: #96bf49 !important;
  color: #fff !important;
  font-weight: bold !important;
  text-align: center !important;
}

.modal-chon-menu-cha .custom-row-selected {
  background-color: #96bf49 !important;
}

.modal-chon-menu-cha .custom-row-selected.ant-table-row:hover {
  background-color: #96bf49 !important;
}
.modal-chon-menu-cha .ant-table-row:hover td {
  background-color: #e8f5e9 !important;
}
.modal-chon-menu-cha .ant-table-content {
  border-bottom: 1px solid #f0f0f0;
  border-radius: unset !important;
}

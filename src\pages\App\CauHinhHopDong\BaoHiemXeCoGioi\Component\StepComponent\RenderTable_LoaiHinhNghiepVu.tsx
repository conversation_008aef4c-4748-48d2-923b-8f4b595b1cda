import {FormInput} from "@src/components";
import {formatCurrencyUS} from "@src/utils/number";
import {Table, TableColumnType} from "antd";
import {isEqual} from "lodash";
import {memo, useMemo, useState} from "react";
import {useBaoHiemXeCoGioiContext} from "../../index.context";
import {TableLoaiHinhNghiepVuDataType, parseNumber, tableLoaiHinhNghiepVuColumns} from "./Constant";

interface RenderLoaiHinhNghiepVuTableProps {
  chiTietHopDongBaoHiem: CommonExecute.Execute.IHopDongXe;
  chiTietDoiTuongBaoHiemXe: CommonExecute.Execute.IDoiTuongBaoHiemXeCoGioi;
  onDataChange?: (data: TableLoaiHinhNghiepVuDataType[]) => void;
}

const RenderLoaiHinhNghiepVuTableComponent = (props: RenderLoaiHinhNghiepVuTableProps) => {
  const {listLoaiHinhNghiepVuXeCoGioi} = useBaoHiemXeCoGioiContext();
  const {chiTietHopDongBaoHiem, chiTietDoiTuongBaoHiemXe, onDataChange} = props;
  const [loaiHinhNghiepVuData, setLoaiHinhNghiepVuData] = useState<TableLoaiHinhNghiepVuDataType[]>([]);
  const INPUT_FIELD_ARR = ["tien", "mien_thuong", "phi", "thue", "tong_phi", "phi_giam", "thue_giam"];

  //DATA BẢNG LOẠI HÌNH NV
  const listSanPhamBaoHiemXeCoGioi = useMemo<TableLoaiHinhNghiepVuDataType[]>(() => {
    const data = listLoaiHinhNghiepVuXeCoGioi
      .filter(item => item.nv === chiTietHopDongBaoHiem.nv && item.ma_doi_tac_ql === chiTietHopDongBaoHiem?.ma_doi_tac_ql && chiTietHopDongBaoHiem.ma_sp === item.ma_sp)
      .map((item, index) => {
        const loaiHinhNghiepVuTheoGCNData = chiTietDoiTuongBaoHiemXe?.gcn_dk?.find((lhnv: any) => lhnv.lh_nv === item.ma);
        const tien = parseNumber(loaiHinhNghiepVuTheoGCNData?.tien);
        const phi = parseNumber(loaiHinhNghiepVuTheoGCNData?.phi);
        const thue = parseNumber(loaiHinhNghiepVuTheoGCNData?.thue);
        const mien_thuong = parseNumber(loaiHinhNghiepVuTheoGCNData?.mien_thuong);
        const phi_giam = parseNumber(loaiHinhNghiepVuTheoGCNData?.phi_giam);
        const thue_giam = parseNumber(loaiHinhNghiepVuTheoGCNData?.thue_giam);
        return {
          ...item,
          key: item.key || index.toString(),
          stt: index + 1,
          lh_nv: item.ma || "",
          ten: item.ten || "",
          doi_tuong: item.doi_tuong || "",
          loai: item.loai || "",
          ma: item.ma || "",
          ma_doi_tac: item.ma_doi_tac || "",
          ma_doi_tac_ql: item.ma_doi_tac_ql || "",
          nhom: item.nhom || "",
          nv: item.nv || "",
          ma_sp: item.ma_sp || "",
          tien: tien,
          phi: phi,
          thue: thue,
          mien_thuong: mien_thuong,
          ktru: loaiHinhNghiepVuTheoGCNData?.ktru ?? "K",
          tong_phi: phi + thue - phi_giam - thue_giam,
          phi_giam: phi_giam,
          thue_giam: thue_giam,
        };
      });
    setLoaiHinhNghiepVuData(data);
    return data;
  }, [listLoaiHinhNghiepVuXeCoGioi, chiTietHopDongBaoHiem, chiTietDoiTuongBaoHiemXe]);

  //Hàm xử lý khi nhập tay
  const handleInputChange = (key: string, field: keyof TableLoaiHinhNghiepVuDataType, value: any) => {
    const updatedData = loaiHinhNghiepVuData.map(item => {
      if (item.key === key) {
        const updatedItem = {...item, [field]: value};

        // Calculate tong_phi
        if (INPUT_FIELD_ARR.includes(field)) {
          const phi = field === "phi" ? parseNumber(value) : parseNumber(item.phi);
          const thue = field === "thue" ? parseNumber(value) : parseNumber(item.thue);
          const phi_giam = field === "phi_giam" ? parseNumber(value) : parseNumber(item.phi_giam);
          const thue_giam = field === "thue_giam" ? parseNumber(value) : parseNumber(item.thue_giam);
          updatedItem.tong_phi = phi + thue - phi_giam - thue_giam;
        }

        return updatedItem;
      }
      return item;
    });
    setLoaiHinhNghiepVuData(updatedData);
    //khi bấm check box cũng cập nhật luôn mảng
    if (field === "ktru") {
      handleInputBlur(updatedData);
    }
  };

  //cập nhật mảng mới
  const handleInputBlur = (baseData: TableLoaiHinhNghiepVuDataType[]) => {
    const filteredData = baseData.filter(item => (typeof item.tien === "number" && item.tien > 0) || (typeof item.tien === "string" && item.tien !== ""));
    onDataChange?.(filteredData);
  };

  // Calculate totals
  const totals = loaiHinhNghiepVuData.reduce(
    (acc, curr) => ({
      tien: acc.tien + parseNumber(curr.tien),
      mien_thuong: acc.mien_thuong + parseNumber(curr.mien_thuong),
      phi: acc.phi + parseNumber(curr.phi),
      thue: acc.thue + parseNumber(curr.thue),
      tong_phi: acc.tong_phi + parseNumber(curr.tong_phi),
      phi_giam: (acc.phi_giam || 0) + parseNumber(curr.phi_giam),
      thue_giam: (acc.thue_giam || 0) + parseNumber(curr.thue_giam),
    }),
    {tien: 0, mien_thuong: 0, phi: 0, thue: 0, tong_phi: 0, phi_giam: 0, thue_giam: 0},
  );

  const renderColumn = (column: TableColumnType<TableLoaiHinhNghiepVuDataType>) => {
    if (!column.dataIndex) return column;

    if (INPUT_FIELD_ARR.includes(column.dataIndex as string)) {
      return {
        ...column,
        render: (text: any, record: TableLoaiHinhNghiepVuDataType) => (
          //input
          <FormInput
            className="border-b-1 !mb-0 !h-[24px] !border-t-0 border-b-gray-50"
            variant="underlined"
            component="input-price"
            onChange={(value: any) => handleInputChange(record.key, column.dataIndex as keyof TableLoaiHinhNghiepVuDataType, value?.target?.value ?? value)}
            onBlur={() => handleInputBlur(loaiHinhNghiepVuData)}
            placeholder="0"
            disabled={column.dataIndex === "tong_phi"}
            value={text}
          />
        ),
      };
    }

    //checkbox
    if (column.dataIndex === "ktru") {
      return {
        ...column,
        render: (_: any, record: TableLoaiHinhNghiepVuDataType) => (
          <FormInput
            className="!mb-0"
            component="checkbox"
            checked={record.ktru === "C"}
            onChange={(e: any) => {
              handleInputChange(record.key, "ktru", e.target.checked ? "C" : "K");
            }}
          />
        ),
      };
    }

    return column;
  };

  //render tổng cộng của cột
  const renderSummaryCell = (index: number, value: string) => {
    return (
      <Table.Summary.Cell index={index} className="!p-[8px] !pr-[24px]">
        <div className="text-right font-medium">{value}</div>
      </Table.Summary.Cell>
    );
  };
  //render
  return (
    <div style={{overflowX: "auto"}}>
      <Table<TableLoaiHinhNghiepVuDataType>
        className="no-header-border-radius"
        columns={(tableLoaiHinhNghiepVuColumns || []).map(renderColumn)}
        dataSource={loaiHinhNghiepVuData}
        pagination={false}
        scroll={{x: "max-content", y: 140}}
        bordered
        summary={() => (
          <Table.Summary fixed>
            <Table.Summary.Row>
              <Table.Summary.Cell className="!p-[8px]" index={0} colSpan={(tableLoaiHinhNghiepVuColumns || []).findIndex(col => "dataIndex" in col && col.dataIndex === "tien")}>
                <div className="text-center font-medium">Tổng cộng</div>
              </Table.Summary.Cell>
              {renderSummaryCell(1, formatCurrencyUS(totals.tien))}
              {renderSummaryCell(2, "")}
              {renderSummaryCell(3, formatCurrencyUS(totals.mien_thuong))}
              {renderSummaryCell(4, formatCurrencyUS(totals.phi))}
              {renderSummaryCell(5, formatCurrencyUS(totals.thue))}
              {renderSummaryCell(6, formatCurrencyUS(totals.phi_giam))}
              {renderSummaryCell(7, formatCurrencyUS(totals.thue_giam))}
              {renderSummaryCell(8, formatCurrencyUS(totals.tong_phi))}
            </Table.Summary.Row>
          </Table.Summary>
        )}
      />
    </div>
  );
};

RenderLoaiHinhNghiepVuTableComponent.displayName = "RenderLoaiHinhNghiepVuTableComponent";
export const RenderLoaiHinhNghiepVuTable = memo(RenderLoaiHinhNghiepVuTableComponent, isEqual);

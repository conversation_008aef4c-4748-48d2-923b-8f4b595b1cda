import {ReactQuery} from "./ReactQuery";

declare namespace User {
  namespace GetUserList {
    interface Request extends ReactQuery.Queries {}
    interface Response {
      results: Array<{
        email: string;
        gender: string;
        name: {
          first: string;
          last: string;
          title: string;
        };
        nat: string;
        picture: {
          large: string;
          medium: string;
          thumbnail: string;
        };
      }>;
    }
  }
}

/**
 * T<PERSON><PERSON> dụng: Quản lý state và logic nghiệp vụ cho module danh mục khu vực
 */
import React, { PropsWithChildren, useCallback, useEffect, useMemo, useState } from "react";
import { message } from "antd";
import { isEqual } from "lodash";

import { ReactQuery } from "@src/@types";
import { ACTION_CODE } from "@src/constants";
import { useCommonExecute } from "@src/services/react-queries";
import { defaultPaginationTableProps } from "@src/hooks";

import DanhMucKhuVucContext from "./index.context";
import { IDanhMucKhuVucProvider } from "./index.model";

const DanhMucKhuVucProvider: React.FC<PropsWithChildren> = props => {
    const { children } = props;

    const mutateUseCommonExecute = useCommonExecute();
    const [filterParams, setFilterParams] = useState<ReactQuery.ITimKiemPhanTrangDanhMucKhuVucParams & ReactQuery.IPhanTrang>({
        ma: "",
        ten: "",
        ma_chau_luc: "",
        trang_thai: "",
        trang: 1,
        so_dong: defaultPaginationTableProps.defaultPageSize,
    });
    const [tongSoDong, setTongSoDong] = useState<number>(0);
    const [listKhuVuc, setListKhuVuc] = useState<Array<CommonExecute.Execute.IDanhMucKhuVuc>>([]);
    const [listChauLuc, setListChauLuc] = useState<Array<CommonExecute.Execute.IDanhMucChauLuc>>([]);

    useEffect(() => {
        initData();
    }, []);

    //Tác dụng: Lấy danh sách tỉnh thành để hiển thị trong dropdown filter
    const getListChauLuc = useCallback(async () => {
        try {
            const response = await mutateUseCommonExecute.mutateAsync({
                ma: "",
                ten: "",
                trang_thai: "",
                trang: 1,
                so_dong: 100,
                actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_DANH_MUC_CHAU_LUC,
            } as any);

            if (response.data) {
                //Xử lý response structure để đảm bảo tương thích
                if (Array.isArray(response.data)) {
                    setListChauLuc(response.data);
                } else if (response.data && typeof response.data === "object") {
                    const responseData = response.data as any;
                    if (responseData.data && Array.isArray(responseData.data)) {
                        setListChauLuc(responseData.data);
                    } else {
                        setListChauLuc([]);
                    }
                }
            }
        } catch (error) {
            setListChauLuc([]);
        }
    }, [mutateUseCommonExecute]);

    //Tác dụng: Lấy thông tin chi tiết của một khu vực khi click vào row
    const getChiTietKhuVuc = useCallback(
        async (data: {ma: string;}) => {
            try {
                const response = await mutateUseCommonExecute.mutateAsync({
                    ma: data.ma,
                    actionCode: ACTION_CODE.CHI_TIET_DANH_MUC_KHU_VUC,
                } as any);

                //Response structure confirmed: data.lke[0]
                const responseData = response.data as any;

                if (responseData?.lke && Array.isArray(responseData.lke) && responseData.lke.length > 0) {
                    return responseData.lke[0] as CommonExecute.Execute.IDanhMucKhuVuc;
                }

                return {} as CommonExecute.Execute.IDanhMucKhuVuc;
            } catch (error) {
                message.error("Không thể tải chi tiết khu vực");
                return {} as CommonExecute.Execute.IDanhMucKhuVuc;
            }
        },
        [mutateUseCommonExecute],
    );

    //Tác dụng: Tìm kiếm và load danh sách khu vực theo filter parameters
    const searchKhuVuc = useCallback(async () => {
        try {
            const response = await mutateUseCommonExecute.mutateAsync({
                ...filterParams,
                actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_DANH_MUC_KHU_VUC,
            } as any);

            //Response structure confirmed: data.data[] + data.tong_so_dong
            const responseData = response.data;
            if (responseData?.data && Array.isArray(responseData.data)) {
                setListKhuVuc(responseData.data);
                setTongSoDong(responseData.tong_so_dong || responseData.data.length);
            }
        } catch (error) {
            message.error("Không thể tải danh sách khu vực");
        }
    }, [mutateUseCommonExecute, filterParams]);

    useEffect(() => {
        searchKhuVuc();
    }, [filterParams]);

    //Tác dụng: Tạo mới hoặc cập nhật thông tin khu vực
    const capNhatChiTietKhuVuc = useCallback(
        async (data: ReactQuery.ICapNhatDanhMucKhuVucParams) => {
            try {
                const response = await mutateUseCommonExecute.mutateAsync({
                    ...data,
                    actionCode: ACTION_CODE.UPDATE_DANH_MUC_KHU_VUC,
                } as any);
                message.success(data.ma ? "Cập nhật khu vực thành công" : "Thêm mới khu vực thành công");
                return response.data;
            } catch (error) {
                message.error(data.ma ? "Có lỗi xảy ra khi cập nhật khu vực" : "Có lỗi xảy ra khi thêm mới khu vực");
                return {} as CommonExecute.Execute.IDanhMucKhuVuc;
            }
        },
        [mutateUseCommonExecute],
    );

    //Tác dụng: Khởi tạo dữ liệu ban đầu khi component mount
    const initData = () => {
        getListChauLuc();
    };

    const value = useMemo<IDanhMucKhuVucProvider>(
        () => ({
            listChauLuc,
            listKhuVuc,
            tongSoDong,
            loading: mutateUseCommonExecute.isLoading,
            searchKhuVuc,
            getChiTietKhuVuc,
            capNhatChiTietKhuVuc,
            filterParams,
            setFilterParams,
        }),
        [listKhuVuc, listChauLuc, tongSoDong, mutateUseCommonExecute, searchKhuVuc, getChiTietKhuVuc, capNhatChiTietKhuVuc, filterParams],
    );

    return <DanhMucKhuVucContext.Provider value={value}>{children}</DanhMucKhuVucContext.Provider>;
};

export default DanhMucKhuVucProvider;

import {DownloadOutlined, PlusCircleOutlined, UploadOutlined} from "@ant-design/icons";
import {IFormInput, ReactQuery} from "@src/@types";
import {Button, FormInput} from "@src/components";
import {defaultPaginationTableProps, defaultTableProps, fillRowTableEmpty} from "@src/hooks";
import {Col, Form, Row, Table, Tabs} from "antd";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState} from "react";
import {NumericFormat} from "react-number-format";
import {useHopDongConNguoiContext} from "../index.context";
import {ModalCauHinhBenhVien, IModalCauHinhBenhVienRef} from "./ModalCauHinhBenhVien";
import {
  FormTimKiemNguoiDuocBaoHiem,
  IThemHopDongStep2Ref,
  tableNguoiDuocBaoHiemColumn,
  TableNguoiDuocBaoHiemColumnDataType,
  ThemHopDongStep2_TabNguoiPhuThuocRef,
  ThemHopDongStep2_TabQuyenLoiBoSungRef,
  ThemHopDongStep2_TabQuyenLoiRef,
  ThemHopDongStep2_TabThongTinNguoiDuocBaoHiemRef,
  ThemHopDongStep2_TabDanhGiaSucKhoeRef,
  ThemHopDongStep2Props,
} from "./Constant";
import {ThemHopDongStep2_TabThongTinNguoiDuocBaoHiem} from "./ThemHopDongStep2_TabThongTinNguoiDuocBaoHiem";
import {ThemHopDongStep2_TabQuyenLoi} from "./ThemHopDongStep2_TabQuyenLoi";
import {ThemHopDongStep2_TabQuyenLoiBoSung} from "./ThemHopDongStep2_TabQuyenLoiBoSung";
import {ThemHopDongStep2_TabNguoiPhuThuoc} from "./ThemHopDongStep2_TabNguoiPhuThuoc";
import {ThemHopDongStep2_TabDanhGiaSucKhoe} from "./ThemHopDongStep2_TabDanhGiaSucKhoe";

const {nd_tim} = FormTimKiemNguoiDuocBaoHiem;

const ThemHopDongStep2Component = forwardRef<IThemHopDongStep2Ref, ThemHopDongStep2Props>(({formThongTinNguoiBaoHiem, setIsChangeData}: ThemHopDongStep2Props, ref) => {
  useImperativeHandle(ref, () => ({
    getCurrentTab: () => tabActive,
    //GET DATA TAB QUYỀN LỢI
    getListQuyenLoiNguoiDuocBaoHiem: () => {
      return refTabQuyenLoi.current?.getListQuyenLoiNguoiDuocBaoHiem() || [];
    },
    getCheckboxApDungGoiBaohiemQuyenLoi: () => {
      return refTabQuyenLoi.current?.getCheckboxApDungGoiBaohiem() || false;
    },
    //GET DATA TAB QUYỀN LỢI BỔ SUNG
    getListQuyenLoiBoSungNguoiDuocBaoHiem: () => {
      return refTabQuyenLoiBoSung.current?.getListQuyenLoiNguoiDuocBaoHiem() || [];
    },
    getCheckboxApDungGoiBaohiemQuyenLoiBoSung: () => {
      return refTabQuyenLoi.current?.getCheckboxApDungGoiBaohiem() || false;
    },
    //GET DATA TAB ĐÁNH GIÁ SỨC KHOẺ
    getDanhSachCauHoiDanhGiaSucKhoe: () => {
      return refTabDanhGiaSucKhoe.current?.getDanhSachCauHoiDanhGiaSucKhoe() || [];
    },
    getDanhSachCauTraLoiDanhGiaSucKhoe: () => {
      return refTabDanhGiaSucKhoe.current?.getDanhSachCauTraLoiDanhGiaSucKhoe() || [];
    },
    getDataForSaveDanhGiaSucKhoe: () => {
      return refTabDanhGiaSucKhoe.current?.getDataForSave() || {dgsk: []};
    },
    validateDanhGiaSucKhoe: () => {
      return refTabDanhGiaSucKhoe.current?.validateData() || {isValid: true, errors: []};
    },
    refreshDanhGiaSucKhoe: () => {
      refTabDanhGiaSucKhoe.current?.refreshData();
    },
    // Thêm method để làm mới dữ liệu
    refreshData: () => {
      timKiemPhanTrangNguoiDuocBaoHiem();
    },
    // Method để mở modal cấu hình bệnh viện
    openModalCauHinhBenhVien: (soId: number, soIdDt: number) => {
      refModalCauHinhBenhVien.current?.open(soId, soIdDt);
    },
  }));

  const {
    loading,
    tongSoDongNguoiDuocBaoHiem,
    listNguoiDuocBaoHiem,
    tongPhiBaoHiemFromAPI,
    timKiemPhanTrangNguoiDuocBaoHiemParams,
    chiTietNguoiDuocBaoHiem,
    getChiTietNguoiDuocBaoHiem,
    setTimKiemPhanTrangNguoiDuocBaoHiemParams,
    timKiemPhanTrangGoiBaoHiem,
    setChiTietNguoiDuocBaoHiem,
    chiTietHopDong,
  } = useHopDongConNguoiContext();

  const refTabThongTinNguoiDuocBaoHiem = useRef<ThemHopDongStep2_TabThongTinNguoiDuocBaoHiemRef>(null);
  const refTabQuyenLoi = useRef<ThemHopDongStep2_TabQuyenLoiRef>(null);
  const refTabQuyenLoiBoSung = useRef<ThemHopDongStep2_TabQuyenLoiBoSungRef>(null);
  const refTabNguoiPhuThuoc = useRef<ThemHopDongStep2_TabNguoiPhuThuocRef>(null);
  const refTabDanhGiaSucKhoe = useRef<ThemHopDongStep2_TabDanhGiaSucKhoeRef>(null);
  const refModalCauHinhBenhVien = useRef<IModalCauHinhBenhVienRef>(null);

  const [formTimKiemNguoiDuocBaoHiem] = Form.useForm();
  const [tabActive, setTabActive] = useState<string>("1");

  // State cho phân trang
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);

  //khởi tạo dữ liệu();
  useEffect(() => {
    initData();
  }, []);

  // Sync page state với API params khi page hoặc pageSize thay đổi
  useEffect(() => {
    if (page !== timKiemPhanTrangNguoiDuocBaoHiemParams.trang || pageSize !== timKiemPhanTrangNguoiDuocBaoHiemParams.so_dong) {
      timKiemPhanTrangNguoiDuocBaoHiem();
    }
  }, [page, pageSize]);

  const initData = useCallback(() => {
    setTimKiemPhanTrangNguoiDuocBaoHiemParams({...timKiemPhanTrangNguoiDuocBaoHiemParams, so_id: chiTietHopDong?.so_id, so_hd: chiTietHopDong?.so_hd, trang: 1, so_dong: 10});
    timKiemPhanTrangGoiBaoHiem({
      ma_sp: chiTietHopDong?.ma_sp,
    });
  }, [timKiemPhanTrangNguoiDuocBaoHiemParams, chiTietHopDong]);

  const dataTableNguoiDuocBaoHiem = useMemo<Array<TableNguoiDuocBaoHiemColumnDataType>>(() => {
    try {
      const tableData = listNguoiDuocBaoHiem.map(itemNguoiDuocBaoHiem => {
        return {
          key: itemNguoiDuocBaoHiem.so_id_dt + "",
          ten: itemNguoiDuocBaoHiem.ten + " / " + itemNguoiDuocBaoHiem.so_cmt,
          ngay_hl: itemNguoiDuocBaoHiem.ngay_hl,
          so_id: itemNguoiDuocBaoHiem.so_id + "",
          so_id_dt: itemNguoiDuocBaoHiem.so_id_dt,
          tong_phi: +(itemNguoiDuocBaoHiem.tong_phi || 0),
        };
      });
      const arrEmptyRow: Array<TableNguoiDuocBaoHiemColumnDataType> = fillRowTableEmpty(tableData.length, 10);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      console.log("dataTableNguoiDuocBaoHiem error", error);
      return [];
    }
  }, [listNguoiDuocBaoHiem]);

  const timKiemPhanTrangNguoiDuocBaoHiem = useCallback(() => {
    try {
      const values: {nd_tim: string} = formTimKiemNguoiDuocBaoHiem.getFieldsValue(); //bổ sung type này vào do ma_kh đang bị định dạng theo string //lấy ra values của form
      setTimKiemPhanTrangNguoiDuocBaoHiemParams({...timKiemPhanTrangNguoiDuocBaoHiemParams, so_id: chiTietHopDong?.so_id, nd_tim: values.nd_tim});
    } catch (error) {
      console.log("timKiemPhanTrangNguoiDuocBaoHiem error", error);
    }
  }, [timKiemPhanTrangNguoiDuocBaoHiemParams]);

  // RENDER
  const renderFormInputColum = (props: IFormInput, span = 6) => {
    return (
      <Col span={span}>
        <FormInput {...props} />
      </Col>
    );
  };

  //render header table đối tượng
  const renderHeaderTableNguoiDuocBaoHiem = () => {
    return (
      <Form form={formTimKiemNguoiDuocBaoHiem} layout="vertical" className="[&_.ant-form-item]:mb-0" onFinish={timKiemPhanTrangNguoiDuocBaoHiem}>
        {renderFormInputColum({...nd_tim}, 24)}
      </Form>
    );
  };

  // RENDER TABLE NGƯỜI ĐƯỢC BẢO HIỂM BÊN TAY TRÁI MODAL
  const renderDanhSachNguoiDuocBaoHiem = () => {
    return (
      <>
        <Table<TableNguoiDuocBaoHiemColumnDataType>
          className="table-danh-sach-nguoi-duoc-bao-hiem-hop-dong-con-nguoi mt-5"
          {...defaultTableProps}
          dataSource={dataTableNguoiDuocBaoHiem} //mảng dữ liệu record được hiển thị
          rowClassName={record => (record.so_id_dt && record.so_id_dt === chiTietNguoiDuocBaoHiem?.so_id_dt ? "table-row-active" : "")} // ✅ Set class cho dòng được chọn
          //định nghĩa cột của table
          columns={tableNguoiDuocBaoHiemColumn || []}
          loading={loading} //hiển thị loading khi đang gọi API để loading data
          pagination={{
            ...defaultPaginationTableProps,
            total: tongSoDongNguoiDuocBaoHiem,
            pageSize: pageSize,
            current: page,
            size: "small",
            // showSizeChanger: true,
            // showQuickJumper: true,
            // showTotal: (total, range) => `${range[0]}-${range[1]} của ${total} bản ghi`,
            onChange: (newPage, newPageSize) => {
              setPage(newPage);
              if (newPageSize && newPageSize !== pageSize) {
                setPageSize(newPageSize);
                setPage(1); // Reset về trang 1 khi thay đổi pageSize
              }
              setTimKiemPhanTrangNguoiDuocBaoHiemParams({
                ...timKiemPhanTrangNguoiDuocBaoHiemParams,
                so_id: chiTietHopDong?.so_id,
                trang: newPageSize && newPageSize !== pageSize ? 1 : newPage,
                so_dong: newPageSize || pageSize,
              });
            },
          }}
          sticky
          title={renderHeaderTableNguoiDuocBaoHiem}
          onRow={record => {
            return {
              style: {cursor: loading ? "progress" : "pointer"}, //  thể hiện vị trí con trỏ là button
              onClick: () => {
                if (record.key.toString().includes("empty")) return;
                getChiTietNguoiDuocBaoHiem(record as ReactQuery.IChiTietNguoiDuocBaoHiemHopDongConNguoi);
              },
            };
          }}
          summary={() => (
            <Table.Summary fixed>
              <Table.Summary.Row>
                <Table.Summary.Cell index={0} colSpan={1} className="!p-[8px]">
                  <div className="text-center font-medium">Tổng phí bảo hiểm</div>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={1} align="right" className="!p-[8px]">
                  <div className="text-right font-medium">
                    <NumericFormat value={tongPhiBaoHiemFromAPI} displayType="text" thousandSeparator="," decimalSeparator="." decimalScale={2} style={{textAlign: "right"}} />
                  </div>
                </Table.Summary.Cell>
              </Table.Summary.Row>
            </Table.Summary>
          )}
        />
      </>
    );
  };

  const renderImportExportThemBtn = () => {
    return (
      <>
        <Row gutter={16} className="mt-5">
          <Col>
            <Button type="dashed" htmlType="submit" loading={loading} icon={<UploadOutlined />} className="w-full">
              Import
            </Button>
          </Col>
          <Col>
            <Button type="dashed" htmlType="submit" loading={loading} icon={<DownloadOutlined />} className="w-full">
              Export
            </Button>
          </Col>
          <Col>
            <Button
              className="w-full"
              type="primary"
              icon={<PlusCircleOutlined />}
              onClick={() => {
                formThongTinNguoiBaoHiem.resetFields();
                setChiTietNguoiDuocBaoHiem(undefined);
              }}>
              Thêm mới
            </Button>
          </Col>
        </Row>
      </>
    );
  };

  const renderTabDoiTuongBaoHiem = () => {
    return (
      <Tabs
        defaultActiveKey="1"
        className="ml-3"
        activeKey={tabActive}
        animated={false}
        tabBarExtraContent={{
          right:
            tabActive === "4" ? (
              <Button size="middle" icon={<PlusCircleOutlined />} onClick={() => refTabNguoiPhuThuoc.current?.openModalChiTietNguoiPhuThuoc()}>
                Thêm người phụ thuộc
              </Button>
            ) : (
              <></>
            ),
        }}
        items={[
          {
            key: "1",
            label: "Thông tin người được bảo hiểm",
            children: <ThemHopDongStep2_TabThongTinNguoiDuocBaoHiem ref={refTabThongTinNguoiDuocBaoHiem} formThongTinNguoiBaoHiem={formThongTinNguoiBaoHiem} setIsChangeData={setIsChangeData} />,
          },
          {
            key: "2",
            label: "Quyền lợi chính",
            children: <ThemHopDongStep2_TabQuyenLoi ref={refTabQuyenLoi} setIsChangeData={setIsChangeData} tabActive={tabActive} />,
          },
          {
            key: "3",
            label: "Quyền lợi bổ sung",
            children: <ThemHopDongStep2_TabQuyenLoiBoSung ref={refTabQuyenLoiBoSung} setIsChangeData={setIsChangeData} tabActive={tabActive} />,
          },
          {
            key: "4",
            label: "Người phụ thuộc",
            children: <ThemHopDongStep2_TabNguoiPhuThuoc ref={refTabNguoiPhuThuoc} tabActive={tabActive} chiTietHopDong={chiTietHopDong} />,
          },
          {
            key: "5",
            label: "Đánh giá sức khoẻ",
            children: <ThemHopDongStep2_TabDanhGiaSucKhoe ref={refTabDanhGiaSucKhoe} setIsChangeData={setIsChangeData} tabActive={tabActive} />,
          },
        ]}
        onChange={setTabActive}
      />
    );
  };
  return (
    <>
      <Row>
        <Col span={6}>
          {renderDanhSachNguoiDuocBaoHiem()}
          {renderImportExportThemBtn()}
        </Col>
        <Col span={18}>{renderTabDoiTuongBaoHiem()}</Col>
      </Row>

      {/* Modal Cấu hình bệnh viện */}
      <ModalCauHinhBenhVien ref={refModalCauHinhBenhVien} />
    </>
  );
});

ThemHopDongStep2Component.displayName = "ThemHopDongStep2Component";
export const ThemHopDongStep2 = memo(ThemHopDongStep2Component, isEqual);

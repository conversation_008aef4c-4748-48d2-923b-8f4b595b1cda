import React, { memo, NamedExoticComponent } from "react";
import { Skeleton as AntSkeleton, SkeletonProps } from "antd";
import { twMerge } from "tailwind-merge";

import AvatarSkeleton from "./Avatar";
import ButtonSkeleton from "./Button";
import ImageSkeleton from "./Image";
import InputSkeleton from "./Input";
import ParagraphSkeleton from "./Paragraph";
import { isEqual } from "lodash";

interface CompoundedComponent {
  Avatar: typeof AvatarSkeleton;
  Button: typeof ButtonSkeleton;
  Input: typeof InputSkeleton;
  Image: typeof ImageSkeleton;
  Paragraph: typeof ParagraphSkeleton;
}

const SkeletonComponent: React.FC<SkeletonProps> = (props: SkeletonProps) => {
  const { className = "", ...etc } = props;
  return (
    <AntSkeleton className={twMerge("custom-skeleton", className)} {...etc} />
  );
};

const Skeleton = memo(
  SkeletonComponent,
  isEqual,
) as NamedExoticComponent<SkeletonProps> & CompoundedComponent;

Skeleton.Avatar = AvatarSkeleton;
Skeleton.Button = ButtonSkeleton;
Skeleton.Input = InputSkeleton;
Skeleton.Image = ImageSkeleton;
Skeleton.Paragraph = ParagraphSkeleton;

export default Skeleton;

import R from "@R";
import {Authen} from "@src/@types/Authentication";
import {<PERSON><PERSON>, Footer, FormInput, Switch} from "@src/components";
import {AUTHEN_KEYS, ID_PAGE} from "@src/constants";
import {useSetting} from "@src/hooks";
import {Card, Form, Layout} from "antd";
import Cookies from "js-cookie";
import {isEqual} from "lodash";
import React, {memo, useCallback, useEffect} from "react";
import {LoginFormConfigs} from "./index.configs"; // file này lưu biến cấu hình
import {useLoginContext} from "./index.context"; // file này lưu biến về state
import "./index.dark.scss";
import "./index.default.scss";

//cấu hình của form ĐĂNG NHẬP
const {tai_khoan, mat_khau} = LoginFormConfigs;

const {Content} = Layout;

/*
file LoginContent : xử lý phần UI của màn hình
React.Fc : khai báo rằng type LoginContent là 1 React Function
*/
const DangNhapContent: React.FC = () => {
  //lấy thuộc tính onSubmit trong LoginContext bằng cách sử dụng useLoginContext
  const {onSubmit, error, isLoading} = useLoginContext();
  const {setLuuDangNhap, luuDangNhap} = useSetting();

  useEffect(() => {
    initData();
  }, []);

  //xử lý khi có lỗi từ server -> set error message vào field tai_khoan
  const [form] = Form.useForm();
  useEffect(() => {
    //nếu có lỗi trả ra từ server -> hiển thị lên form
    if (error) {
      if (error.status !== 200) {
        form.setFields([
          {
            name: "tai_khoan",
            errors: [error.response?.data?.error_message],
          },
        ]);
      }
    }
  }, [error]);

  // INIT DATA
  const initData = () => {
    initLastUserLogin();
  };

  const initLastUserLogin = useCallback(() => {
    if (luuDangNhap) {
      const lasUserLogin = Cookies.get(AUTHEN_KEYS.USER_NAME);
      if (lasUserLogin) {
        form.setFields([
          {
            name: "tai_khoan",
            value: lasUserLogin,
          },
        ]);
      }
    }
  }, [luuDangNhap]);

  const onFinish = (values: Authen.Login.IFormFieldsValue) => {
    onSubmit(values);
  };

  // const onFinishFailed = (errorInfo: any) => {};

  const onChangeLuuDangNhap = useCallback(
    (value: boolean) => {
      setLuuDangNhap(value);
    },
    [setLuuDangNhap],
  );

  // RENDER
  return (
    <div id={ID_PAGE.DANG_NHAP} className="layout-default layout-sign-up layout-sign-up-custom">
      <Content className="p-0">
        <div className="sign-up-header">
          <div className="content">{/* <h1 className="mb-3 font-bold mbt-5">Đăng nhập</h1> */}</div>
        </div>
        <Card
          className="card-signup header-solid ant-card pt-0"
          // title={<h5 className="font-semibold text-center">Đăng nhập</h5>}
          variant="borderless"
          styles={{
            body: {paddingTop: "0px"},
          }}>
          <Form
            form={form}
            name="dang_nhap" //tên của form
            //giá trị khởi tạo
            initialValues={
              {
                // tai_khoan: "admin",
                // mat_khau: "admin",
              }
            }
            variant="outlined" //các biến thể input của form : outlined | borderless | filled | underlined
            onFinish={onFinish} // hàm callback khi submit, Kích hoạt sau khi gửi biểu mẫu và xác minh dữ liệu thành công
            // onFinishFailed={onFinishFailed} // hàm callback khi có lỗi, Kích hoạt sau khi gửi biểu mẫu và xác minh dữ liệu không thành công
            className="row-col"
            // size="small" //Đặt size component field (chỉ dành cho thành phần antd) "small | middle | large"
            // onValuesChange={(changedValues, allValues) => {}} //hàm callback khi các input change
            // validateTrigger="onBlur" // kích hoạt validate input : onBlur, onChange(defalut)
          >
            <div className="div_img_logo_login">
              <img src={R.images.img_logo_sanbh} className="img_logo_login" />
            </div>
            <FormInput {...tai_khoan} allowClear={false} />
            <FormInput {...mat_khau} allowClear={false} />
            <Form.Item className="aligin-center">
              <Switch onChange={onChangeLuuDangNhap} value={luuDangNhap} className="mr-2" />
              Lưu đăng nhập
            </Form.Item>
            <Button type="primary" htmlType="submit" block loading={isLoading}>
              Đăng nhập
            </Button>
          </Form>
        </Card>
        <Footer />
      </Content>
    </div>
  );
};

DangNhapContent.displayName = "DangNhapContent"; //Được sử dụng trong các thông báo gỡ lỗi

export default memo(DangNhapContent, isEqual);

import {ReactQuery} from "@src/@types";

export interface BoMaCauHoiContextProps {
  loading: boolean;
  tongSoDong: number;
  // defaultFormValue: ReactQuery.ITimKiemPhanTrangDanhSachSanPhamParams;
  filterParams: ReactQuery.ITimKiemPhanTrangDanhSachSanPhamParams & ReactQuery.IPhanTrang;
  danhSachSanPhamPhanTrang: Array<CommonExecute.Execute.IDanhMucSanPham>;
  danhSachCauHoiApDung: Array<CommonExecute.Execute.ICauHoiApDung>;
  danhSachCauHoi: Array<CommonExecute.Execute.ICauHoi>;
  danhSachCauHoiCT: Array<CommonExecute.Execute.ICauHoiCT>;
  selecteCauHoiApDung: number | null;
  listNghiepVu: Array<CommonExecute.Execute.IDanhMucNghiepVu>;
  getListNghiepVu: () => Promise<void>;
  setSelectedCauHoiApDung: (data: number | null) => void;
  // layDanhSachCauHoiCT: (params: ReactQuery.ILietKeCauHoiCTParams) => Promise<Array<CommonExecute.Execute.ICauHoiCT>>;
  setDanhSachCauHoiCT: (data: Array<CommonExecute.Execute.ICauHoiCT>) => void;
  setDanhSachCauHoi: (data: Array<CommonExecute.Execute.ICauHoi>) => void;
  setDanhSachCauHoiApDung: (data: Array<CommonExecute.Execute.ICauHoiApDung>) => void;
  onDeleteCauHoi: (item: ReactQuery.IDeleteCauHoiParams) => void;
  onUpdateCauHoi: (item: ReactQuery.IUpdateCauHoiParams) => Promise<number | null | undefined>;
  layDanhSachCauHoi: (params: ReactQuery.ILietKeCauHoiParams) => Promise<Array<CommonExecute.Execute.ICauHoi>>;
  onDeleteCauHoiApDung: (item: ReactQuery.IDeleteCauHoiApDungParams) => void;
  onUpdateCauHoiApDung: (item: ReactQuery.IUpdateCauHoiApDungParams) => Promise<number | null | undefined>;
  layDanhSachCauHoiApDung: (params: ReactQuery.ILietKeCauHoiApDungParams) => void;
  setFilterParams: React.Dispatch<React.SetStateAction<ReactQuery.ITimKiemPhanTrangDanhSachSanPhamParams & ReactQuery.IPhanTrang>>;
  layDanhSachSanPhamPhanTrang: (params: ReactQuery.ITimKiemPhanTrangDanhSachSanPhamParams) => void;
  layChiTietSanPham: (params: ReactQuery.IChiTietDanhMucSanPhamParams) => Promise<CommonExecute.Execute.IDanhMucSanPham | null>;
  layChiTietCauHoi: (params: ReactQuery.IChiTietCauHoiParams) => Promise<CommonExecute.Execute.ICauHoi | null>;
  listDoiTac: Array<CommonExecute.Execute.IDoiTac>;
  getListDoiTac: () => Promise<void>;
}

import {ReactQuery} from "@src/@types";
import {ROUTER_PATHS, URL_API} from "@src/constants";
import {useProfile} from "@src/hooks";
import {useMenuNguoiDung} from "@src/hooks/menuNguoiDungStore";
import {navigateTo} from "@src/utils";
import {message} from "antd";
import {AuthEndpoint, axiosInstance} from "..";
import {AxiosProgressEvent} from "axios";
import {IUploadExcelResponse} from "@src/components/ModalImportExcel/constants";

type CommonExecuteParams = {actionCode: string} & ReactQuery.IUploadFileParams &
  ReactQuery.ILayChiTietFileParams &
  ReactQuery.ICapNhatFileParams &
  ReactQuery.IXoaFileParams &
  ReactQuery.ITaoFolderParams &
  ReactQuery.ITimKiemFolderParams &
  ReactQuery.IExportPDFParams &
  ReactQuery.IExportPDFHopDongParams &
  ReactQuery.IExportExcelParams &
  ReactQuery.IXoaFolderParams;

type CommonExecuteResponse = {
  output?: {bt?: number; url_file?: string; url_file_thumnail?: string; id?: number};
  data: number & File.GetFolder.IGetFolder[];
};

// UPLOAD FILE
export const uploadFile = async (
  params: CommonExecuteParams,
  callbacks?: {
    onProgress?: (event: {percent: number}) => void;
    onSuccess?: (body: any) => void;
    onError?: (error: Error) => void;
  },
): Promise<CommonExecuteResponse> => {
  try {
    const formDataUpload = new FormData();
    formDataUpload.append("file", params.file);
    formDataUpload.append("ma_doi_tac_ql", params?.ma_doi_tac_ql || "");
    formDataUpload.append("bt", params.bt + "");
    formDataUpload.append("ten", params.ten || "");
    formDataUpload.append("stt", params.stt + "");
    formDataUpload.append("file_public", params.file_public + "");
    formDataUpload.append("id_folder", params.id_folder + "");
    formDataUpload.append("thumbnail", params.thumbnail + "");
    formDataUpload.append("nhom", params.nhom + "");
    formDataUpload.append("actionCode", params.actionCode);
    const response = await axiosInstance.post(URL_API.UPLOAD_FILE, formDataUpload, {
      onUploadProgress: (event: AxiosProgressEvent) => {
        if (callbacks?.onProgress && event.total) {
          const percent = Math.round((event.loaded * 100) / event.total);
          callbacks.onProgress({percent});
        }
      },
    });
    callbacks?.onSuccess?.(response);
    return response;
  } catch (error) {
    console.log("uploadFile error", error);
    callbacks?.onError?.(error as Error);
    return handleError(error, async () => {
      const response = await uploadFile(params, callbacks);
      console.log("uploadFile handleError ", response);
      return response;
    });
  }
};

// UPLOAD EXCEL FILE VÀ XỬ LÝ DỮ LIỆU
export const uploadExcelFile = async (
  params: ReactQuery.IUploadExcelFileParams ,
  callbacks?: {
    onProgress?: (event: {percent: number}) => void;
    onSuccess?: (body: any) => void;
    onError?: (error: Error) => void;
  },
): Promise<IUploadExcelResponse> => {
  try {
    const formDataUpload = new FormData();
    formDataUpload.append("file", params.file);
    formDataUpload.append("ma_doi_tac_ql", params?.ma_doi_tac_ql || "");
    formDataUpload.append("actionCode", params.actionCode || "");

    console.log("📤 Excel Upload Request:", {
      url: URL_API.UPLOAD_EXCEL_FILE,
      fileName: params.file?.name,
      fileSize: params.file?.size,
      ma_doi_tac_ql: params?.ma_doi_tac_ql,
      actionCode: params.actionCode
    });

    const response = await axiosInstance.post(URL_API.UPLOAD_EXCEL_FILE, formDataUpload, {
      onUploadProgress: (event: AxiosProgressEvent) => {
        if (callbacks?.onProgress && event.total) {
          const percent = Math.round((event.loaded * 100) / event.total);
          callbacks.onProgress({percent});
        }
      },
    });

    callbacks?.onSuccess?.(response);
    return response;
  } catch (error) {
    console.log("uploadExcelFile error", error);
    callbacks?.onError?.(error as Error);
    return handleError(error, async () => {
      const response = await uploadExcelFile(params, callbacks);
      console.log("uploadExcelFile handleError ", response);
      return response;
    });
  }
};

// CHI TIẾT FILE
export const getChiTietFile = async (params: CommonExecuteParams): Promise<CommonExecuteResponse> => {
  try {
    const response = await axiosInstance.post(URL_API.GET_FILE, params);
    return response;
  } catch (error) {
    return handleError(error, async () => {
      const response = await getChiTietFile(params);
      console.log("getChiTietFile handleError ", response);
      return response;
    });
  }
};

// CẬP NHẬT FILE
export const capNhatFile = async (params: CommonExecuteParams): Promise<CommonExecuteResponse> => {
  try {
    const response = await axiosInstance.post(URL_API.RENAME_FILE, params);
    return response;
  } catch (error) {
    return handleError(error, async () => {
      const response = await capNhatFile(params);
      console.log("capNhatFile handleError ", response);
      return response;
    });
  }
};

// XOÁ FILE
export const xoaFile = async (params: CommonExecuteParams): Promise<CommonExecuteResponse> => {
  try {
    const response = await axiosInstance.post(URL_API.REMOVE_FILE, params);
    return response;
  } catch (error) {
    return handleError(error, async () => {
      const response = await xoaFile(params);
      console.log("xoaFile handleError ", response);
      return response;
    });
  }
};

// TẠO / CẬP NHẬT FOLDER
export const updateFolder = async (params: CommonExecuteParams): Promise<CommonExecuteResponse> => {
  try {
    const response = await axiosInstance.post(URL_API.CREATE_DIRECTORY, params);
    return response;
  } catch (error) {
    return handleError(error, async () => {
      const response = await updateFolder(params);
      console.log("updateFolder handleError ", response);
      return response;
    });
  }
};

// TẠO / CẬP NHẬT FOLDER
export const xoaFolder = async (params: CommonExecuteParams): Promise<CommonExecuteResponse> => {
  try {
    const response = await axiosInstance.post(URL_API.REMOVE_FOLDER, params);
    return response;
  } catch (error) {
    return handleError(error, async () => {
      const response = await xoaFolder(params);
      console.log("xoaFolder handleError ", response);
      return response;
    });
  }
};

// TÌM KIẾM TRONG FOLDER
export const timKiemTrongFolder = async (params: CommonExecuteParams): Promise<CommonExecuteResponse> => {
  try {
    const response = await axiosInstance.post(URL_API.GET_FOLDER, params);
    return response;
  } catch (error) {
    return handleError(error, async () => {
      const response = await timKiemTrongFolder(params);
      console.log("timKiemTrongFolder handleError ", response);
      return response;
    });
  }
};

// EXPORT PDF
export const exportPdf = async (params: CommonExecuteParams): Promise<CommonExecuteResponse> => {
  try {
    const response = await axiosInstance.post(URL_API.EXPORT_PDF, params);
    return response;
  } catch (error) {
    return handleError(error, async () => {
      const response = await exportPdf(params);
      console.log("exportPdf handleError ", response);
      return response;
    });
  }
};

// EXPORT PDF HỢP ĐỒNG
export const exportPdfHopDong = async (params: CommonExecuteParams): Promise<Blob> => {
  try {
 
    const response = await axiosInstance.post(URL_API.EXPORT_PDF, params, {
      responseType: 'blob', // Quan trọng: để nhận file PDF dưới dạng blob
    });


    let blob: Blob;

    // With the fixed response interceptor, blob responses should come as response.data
    if (response.data instanceof Blob) {

      blob = response.data;
    }
    // Nếu response.data là ArrayBuffer, chuyển thành blob
    else if (response.data instanceof ArrayBuffer) {
      blob = new Blob([response.data], { type: 'application/pdf' });
    }
    // Kiểm tra response có phải là blob không (fallback)
    else if (response instanceof Blob) {
      blob = response;
    }
    // Fallback: tạo blob từ data
    else {
      blob = new Blob([response.data], { type: 'application/pdf' });
    }
    return blob;
  } catch (error) {
    return handleError(error, async () => {
      const response = await exportPdfHopDong(params);
      console.log("exportPdfHopDong handleError ", response);
      return response;
    });
  }
};

// EXPORT EXCEL
export const exportExcel = async (params: CommonExecuteParams): Promise<Blob> => {
  try {
    const response = await axiosInstance.post(URL_API.EXPORT_EXCEL, params, {
      responseType: 'blob', // Quan trọng: để nhận file Excel dưới dạng blob
    });

    let blob: Blob;

    // With the fixed response interceptor, blob responses should come as response.data
    if (response.data instanceof Blob) {
      blob = response.data;
    }
    // Nếu response.data là ArrayBuffer, chuyển thành blob
    else if (response.data instanceof ArrayBuffer) {
      blob = new Blob([response.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    }
    // Kiểm tra response có phải là blob không (fallback)
    else if (response instanceof Blob) {
      blob = response;
    }
    // Fallback: tạo blob từ data
    else {
      blob = new Blob([response.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    }
    return blob;
  } catch (error) {
    return handleError(error, async () => {
      const response = await exportExcel(params);
      console.log("exportExcel handleError ", response);
      return response;
    });
  }
};

const handleError = async (error: any, funcGoiLaiAPI: () => void) => {
  console.log("handleError", error);
  if (error.status === 400) {
    if (error.response?.data?.error_message === "Thông tin token không hợp lệ hoặc đã bị hết hạn.") {
      //nếu token hết hạn -> gọi api refresh lại access_token
      const {profile, setToken} = useProfile.getState();
      const responseRefreshAccessToken = await AuthEndpoint.rereshAccessToken({token: profile.token, refresh_token: profile.refresh_token});
      console.log("responseRefreshAccessToken");
      console.log(responseRefreshAccessToken);
      //cập nhật lại token
      if (typeof responseRefreshAccessToken === "string") {
        setToken(responseRefreshAccessToken);
        return await funcGoiLaiAPI();
      } else if (responseRefreshAccessToken.status === 400) {
        const profileTmp = profile;
        profileTmp.token = "";
        useProfile.getState().setProfile(profileTmp);
        useMenuNguoiDung.getState().setMenuNguoiDung([]);
        navigateTo(ROUTER_PATHS.DANG_NHAP);
        message.info("Phiên đăng nhập hết hạn. Vui lòng đăng nhập lại");
      }
    } else if (error.response?.data?.error_message) {
      message.warning(error.response?.data?.error_message);
    }
  } else if (error.status === 404) {
    if (error.code === "ERR_BAD_REQUEST") message.warning(error.message);
  }
  return error;
};

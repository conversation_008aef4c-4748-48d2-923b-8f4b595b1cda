import React, {memo} from "react";
import {Checkbox as AntCheckbox, CheckboxProps} from "antd";
import {twMerge} from "tailwind-merge";

import {isEqual} from "lodash";

const CheckboxComponent: React.FC<CheckboxProps> = props => {
  const {className = "", value, onChange, ...etc} = props;
  return <AntCheckbox className={twMerge("custom-checkbox", className)} value={value} onChange={onChange} {...etc} />;
};

const Checkbox = memo(CheckboxComponent, isEqual);

export default memo(Checkbox);

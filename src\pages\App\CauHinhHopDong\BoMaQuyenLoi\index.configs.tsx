import {IFormInput, ReactQuery} from "@src/@types";
import {colWidthByKey, defaultTableColumnsProps} from "@src/hooks";
import {TableProps} from "antd";

export interface IFormTimKiemPhanTrangBoMaQuyenLoiFieldsConfig {
  ma_doi_tac_ql: IFormInput;
  nd_tim: IFormInput;
  trang_thai: IFormInput;
  nv: IFormInput;
  ma_sp: IFormInput;
}
export const FormTimKiemPhanTrangBoMaQuyenLoi: IFormTimKiemPhanTrangBoMaQuyenLoiFieldsConfig = {
  ma_doi_tac_ql: {
    component: "select",
    name: "ma_doi_tac_ql",
    label: "Đối tác",
    placeholder: "Chọn đối tác",
  },
  nd_tim: {
    component: "input",
    name: "nd_tim",
    label: "Mã/tên quyền lợi",
    placeholder: "Mã/tên quyền lợi",
  },

  trang_thai: {
    component: "select",
    name: "trang_thai",
    label: "Trạng thái",
    placeholder: "Chọn trạng thái",
  },
  nv: {
    component: "select",
    name: "nv",
    label: "Nghiệp vụ",
    placeholder: "Chọn nghiệp vụ",
  },
  ma_sp: {
    component: "select",
    name: "ma_sp",
    label: "Sản phẩm",
    placeholder: "Chọn sản phẩm",
  },
};
export const radioItemTrangThaiBoMaQuyenLoiTable = [
  {value: "Đang sử dụng", text: "Đang sử dụng"},
  {value: "Ngừng sử dụng", text: "Ngừng sử dụng"},
];
export const TRANG_THAI_BO_MA_QUYEN_LOI = [
  {ten: "Đang sử dụng", ma: "D"},
  {ten: "Ngừng sử dụng", ma: "K"},
  // {label: "Tất cả", ma: ""},
];
export const LOAI_QUYEN_LOI = [
  {ten: "Quyền lợi chính", ma: "CHINH"},
  {ten: "Quyền lợi bổ sung", ma: "DKBS"},
];

export const defaultFormValue: ReactQuery.ITimKiemPhanTrangBoMaQuyenLoiParams = {
  ma_doi_tac_ql: "",
  nv: "",
  ma_sp: "",
  nd_tim: "",
  trang_thai: "",
  // trang: 1,
  // so_dong: 15,
  // actionCode: "",
};
export interface TableBoMaQuyenLoiDataType {
  key: string;
  stt: number;
  ma: string;
  ten: string;
  // doi_tac_ql_ten_tat: string;
  ma_ct: string;
  trang_thai_ten: string;
  nguoi_tao: string;
  ngay_tao: string;
  nguoi_cap_nhat: string;
  ngay_cap_nhat: string;
  ten_nv: string;
  ma_sp: string;
  nv: string;
  ma_doi_tac_ql: string;
  loai: string;
  qloi_gh: string;
  bl_nt: string;
  bl_gt: string;
  bl_ra: string;
  ma_dtac: string;
  trang_thai: string;
  ten_sp: string;
}

// const onHeaderCell = () => ({
//   className: "header-cell-custom",
// });
export const BoMaQuyenLoiColumns: TableProps<TableBoMaQuyenLoiDataType>["columns"] = [
  {title: "STT", dataIndex: "stt", key: "stt", width: colWidthByKey.sott, align: "center", ...defaultTableColumnsProps},
  {title: "Mã ", dataIndex: "ma", key: "ma", width: 100, align: "center", ...defaultTableColumnsProps},
  {...defaultTableColumnsProps, title: "Tên quyền lợi", dataIndex: "ten", key: "ten", width: 300, align: "left"},
  {title: "Mã cấp trên", dataIndex: "ma_ct", key: "ma_ct", width: 100, align: "center", ...defaultTableColumnsProps},
  {title: "Mã sản phẩm", dataIndex: "ma_sp", key: "ma_sp", width: 120, align: "center", ...defaultTableColumnsProps},
  {title: "Nghiệp vụ", dataIndex: "ten_nv", key: "ten_nv", width: 150, align: "center", ...defaultTableColumnsProps},
  {title: "Đối tác", dataIndex: "doi_tac_ql_ten_tat", key: "doi_tac_ql_ten_tat", width: 150, align: "center", ...defaultTableColumnsProps},
  {title: "Loại quyền lợi", dataIndex: "loai", key: "loai", width: 130, align: "center", ...defaultTableColumnsProps},
  {title: "BL nội trú", dataIndex: "bl_nt", key: "bl_nt", width: 100, align: "center", ...defaultTableColumnsProps},
  {title: "BL ngoại trú", dataIndex: "bl_gt", key: "bl_gt", width: 100, align: "center", ...defaultTableColumnsProps},
  {title: "BL răng", dataIndex: "bl_ra", key: "bl_ra", width: 100, align: "center", ...defaultTableColumnsProps},
  {title: "Q.lợi giới hạn", dataIndex: "qloi_gh", key: "qloi_gh", width: 110, align: "center", ...defaultTableColumnsProps},
  {title: "Tên sản phẩm", dataIndex: "ten_sp", key: "ten_sp", width: 200, align: "center", ...defaultTableColumnsProps},
  // {title: "Mã ql đối tác", dataIndex: "ma_dtac", key: "ma_dtac", width: 200, align: "center", ...defaultTableColumnsProps},
  {title: "Ngày tạo", dataIndex: "ngay_tao", key: "ngay_tao", width: colWidthByKey.ngay_tao, align: "center", ...defaultTableColumnsProps},
  {title: "Người tạo", dataIndex: "nguoi_tao", key: "nguoi_tao", width: colWidthByKey.nguoi_tao, align: "center", ...defaultTableColumnsProps},
  {title: "Ngày cập nhật", dataIndex: "ngay_cap_nhat", key: "ngay_cap_nhat", width: colWidthByKey.ngay_cap_nhat, align: "center", ...defaultTableColumnsProps},
  {title: "Người cập nhật", dataIndex: "nguoi_cap_nhat", key: "nguoi_cap_nhat", width: colWidthByKey.nguoi_cap_nhat, align: "center", ...defaultTableColumnsProps},
  {title: "Trạng Thái", dataIndex: "trang_thai_ten", key: "trang_thai_ten", width: 130, align: "center", ...defaultTableColumnsProps},
];

import {ReactQuery} from "@src/@types";

export interface DanhMucSanPhamContextProps {
  loading: boolean;
  tongSoDong: number;
  // defaultFormValue: ReactQuery.ITimKiemPhanTrangDanhSachSanPhamParams;
  filterParams: ReactQuery.ITimKiemPhanTrangDanhSachSanPhamParams & ReactQuery.IPhanTrang;
  listNghiepVu: Array<CommonExecute.Execute.IDanhMucNghiepVu>;
  danhSachSanPhamPhanTrang: Array<CommonExecute.Execute.IDanhMucSanPham>;
  listDoiTac: Array<CommonExecute.Execute.IDoiTac>;
  setFilterParams: React.Dispatch<React.SetStateAction<ReactQuery.ITimKiemPhanTrangDanhSachSanPhamParams & ReactQuery.IPhanTrang>>;
  onUpdateDanhMucSanPham: (item: ReactQuery.IUpdateDanhMucSanPhamParams) => Promise<number | null | undefined>;
  layDanhSachSanPhamPhanTrang: (params: ReactQuery.ITimKiemPhanTrangDanhSachSanPhamParams) => void;
  layChiTietSanPham: (params: ReactQuery.IChiTietDanhMucSanPhamParams) => Promise<CommonExecute.Execute.IDanhMucSanPham | null>;
  getListDoiTac: () => Promise<void>;
  getListNghiepVu: () => Promise<void>;
}

const fs = require("fs");
const path = require("path");
const readline = require("readline");

const generateContextModelFileFormat = component => {
  return `export interface I${component}ContextProps {}`;
};

const generateContextFileFormat = component => {
  return `import {createContext, useContext} from "react";

import {I${component}ContextProps} from "./index.model";

export const ${component}Context = createContext<I${component}ContextProps>({} as any);

export const use${component}Context = () => useContext(${component}Context);
`;
};

const generateProviderFileFormat = component => {
  return `import React, {PropsWithChildren, useMemo} from "react";

import {${component}Context} from "./index.context";
import {I${component}ContextProps} from "./index.model";

const ${component}Provider: React.FC<PropsWithChildren> = (props) => {
  const {children} = props;

  const value = useMemo<I${component}ContextProps>(() => ({}), []);

  return <${component}Context.Provider value={value}>{children}</${component}Context.Provider>;
};

export default ${component}Provider;
`;
};

const generateContentFileFormat = component => {
  return `import React, {memo} from "react";

import {use${component}Context} from "./index.context";
import {isEqual} from "lodash";

const ${component}Content: React.FC = memo(() => {
  const {} = use${component}Context();

  return <div></div>;
}, isEqual);

${component}Content.displayName = "${component}Content";

export default ${component}Content;
`;
};

const generateIndexFileFormat = component => {
  return `import React from "react";

import ${component}Content from "./index.content";
import ${component}Provider from "./index.provider";

const ${component}: React.FC = () => {
  return (
    <${component}Provider>
      <${component}Content />
    </${component}Provider>
  );
};

export default ${component};
`;
};

const createFiles = (pageDest, pageName) => {
  // create new page folder
  fs.mkdirSync(pageDest, {recursive: true});

  // create context configs file
  fs.writeFileSync(`${pageDest}/index.configs.tsx`, ``);
  console.log("index.configs.tsx được tạo thành công.");

  // create context model file
  fs.writeFileSync(`${pageDest}/index.model.tsx`, generateContextModelFileFormat(pageName));
  console.log("index.model.tsx được tạo thành công.");

  // create context file
  fs.writeFileSync(`${pageDest}/index.context.tsx`, generateContextFileFormat(pageName));
  console.log("index.context.tsx được tạo thành công.");

  // create provider file
  fs.writeFileSync(`${pageDest}/index.provider.tsx`, generateProviderFileFormat(pageName));
  console.log("index.provider.tsx được tạo thành công.");

  // create content file
  fs.writeFileSync(`${pageDest}/index.content.tsx`, generateContentFileFormat(pageName));
  console.log("index.content.tsx được tạo thành công.");

  // create index file
  fs.writeFileSync(`${pageDest}/index.tsx`, generateIndexFileFormat(pageName));
  console.log("index.tsx được tạo thành công.");
};

const generatePage = () => {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  rl.question("Nhập đường dẫn của page tính từ src/pages/... : ", pagePath => {
    rl.question("Nhâp tên page: ", pageName => {
      console.log("Tên page: ", pageName);
      // page path for new page folder
      const pageDest = `${path.resolve(__dirname, "..")}/${pagePath}/${pageName}`;
      console.log("Đường dẫn page: ", pageDest);
      // check if folder exists
      const existed = fs.existsSync(pageDest);
      if (existed) {
        rl.question("Thư mục đã tồn tại. Bạn có muốn ghi đè? (Y/N)", answer => {
          if (answer === "Y" || answer === "y") {
            createFiles(pageDest, pageName);
          } else {
            console.log("No files were created.");
          }
        });
      } else {
        createFiles(pageDest, pageName);
      }
      rl.close();
    });
  });
};

generatePage();

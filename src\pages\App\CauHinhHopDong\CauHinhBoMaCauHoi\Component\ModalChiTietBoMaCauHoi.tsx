import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState} from "react";
import FormChiTietDanhMucSanPham, {
  cauHoiApDungColumns,
  cauHoiColumns,
  DataIndexCauHoi,
  DataIndexCauHoiApDung,
  FormThemNgayApDung,
  IModalChiTietBoMaCauHoiRef,
  IModalThemCauHoiRef,
  Props,
  TableCauHoiApDungDataType,
  TableCauHoiDataType,
  TRANG_THAI_CHI_TIET_SAN_PHAM,
} from "./index.configs";
import {Col, Dropdown, Flex, Form, InputRef, Modal, Row, Space, Table, TableColumnType} from "antd";
import {useBoMaCauHoiContext} from "../index.context";
import {isEqual} from "lodash";
import {Button, FormInput, HeaderModal, <PERSON>lighter, Popcomfirm, TableFilterDropdown} from "@src/components";
import {CloseOutlined, PlusCircleOutlined} from "@ant-design/icons";
import {defaultTableProps, fillRowTableEmpty} from "@src/hooks";
import {FilterDropdownProps} from "antd/es/table/interface";
import dayjs from "dayjs";
import isSameOrBefore from "dayjs/plugin/isSameOrBefore";
import {ModalThemCauHoi} from "./ModalThemCauHoi";

dayjs.extend(isSameOrBefore);
const {ma_doi_tac_ql, ten, nv} = FormChiTietDanhMucSanPham;

const ModalChiTietBoMaCauHoiComponent = forwardRef<IModalChiTietBoMaCauHoiRef, Props>(({listDoiTac}: Props, ref) => {
  useImperativeHandle(ref, () => ({
    open: (dataDanhMucSanPham?: CommonExecute.Execute.IDanhMucSanPham) => {
      setIsOpen(true);
      form.resetFields();
      if (dataDanhMucSanPham) setChiTietDanhMucSanPham(dataDanhMucSanPham);
    },
    close: () => {
      setIsOpen(false);
      setChiTietDanhMucSanPham(null);
      setDanhSachCauHoi([]);
    },
  }));
  const refModalThemCauHoi = useRef<IModalThemCauHoiRef>(null);
  const {ngay_ad} = FormThemNgayApDung;
  const [chiTietDanhMucSanPham, setChiTietDanhMucSanPham] = useState<CommonExecute.Execute.IDanhMucSanPham | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  const [pageSize, setPageSize] = useState(10);
  const {
    loading,
    filterParams,
    danhSachCauHoiApDung,
    danhSachCauHoi,
    selecteCauHoiApDung,
    setFilterParams,
    setSelectedCauHoiApDung,
    onUpdateCauHoiApDung,
    setDanhSachCauHoi,
    layDanhSachCauHoiApDung,
    onDeleteCauHoiApDung,
    onDeleteCauHoi,
    layDanhSachCauHoi,
    layChiTietCauHoi,
    listNghiepVu,
  } = useBoMaCauHoiContext();
  const [searchedColumn, setSearchedColumn] = useState("");
  const [searchText, setSearchText] = useState("");
  const searchInput = useRef<InputRef>(null);
  const [searchedColumnCH, setSearchedColumnCH] = useState("");
  const [searchTextCH, setSearchTextCH] = useState("");
  const searchInputCH = useRef<InputRef>(null);
  const [form] = Form.useForm();
  const formValues = Form.useWatch([], form);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [formThemNgayApDung] = Form.useForm();
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const [ngayAdMoiTao, setNgayAdMoiTao] = useState<string | null>(null);
  useEffect(() => {
    if (chiTietDanhMucSanPham) {
      const arrFormData = [];
      for (const key in chiTietDanhMucSanPham) {
        arrFormData.push({
          name: key as keyof CommonExecute.Execute.IDanhMucSanPham,
          value: chiTietDanhMucSanPham[key as keyof CommonExecute.Execute.IDanhMucSanPham],
        });
      }
      form.setFields(arrFormData);
    }
  }, [chiTietDanhMucSanPham, form]);
  useEffect(() => {
    if (chiTietDanhMucSanPham?.ma) {
      layDanhSachCauHoiApDung({ma_sp: chiTietDanhMucSanPham.ma, ma_doi_tac_ql: chiTietDanhMucSanPham.ma_doi_tac_ql, nv: chiTietDanhMucSanPham.nv});
    }
  }, [chiTietDanhMucSanPham]);

  useEffect(() => {
    if (selecteCauHoiApDung !== null) {
      setDisableSubmit(false);
    } else {
      setDisableSubmit(true);
    }
  }, [selecteCauHoiApDung]);
  // Tự động chọn dòng cuối cùng khi danh sách thay đổi
  useEffect(() => {
    if (danhSachCauHoiApDung.length > 0) {
      let selected;
      if (ngayAdMoiTao) {
        // Tìm ngày vừa tạo
        selected = danhSachCauHoiApDung.find(item => dayjs(item.ngay_ad, "DD/MM/YYYY").isSame(dayjs(ngayAdMoiTao, "DD/MM/YYYY")));
        setNgayAdMoiTao(null); // Reset lại sau khi đã select
      } else {
        // Tìm ngày gần hôm nay nhất (không vượt quá hôm nay)
        const today = dayjs();
        const validDates = danhSachCauHoiApDung.filter(item => dayjs(item.ngay_ad, "DD/MM/YYYY").isBefore(today) || dayjs(item.ngay_ad, "DD/MM/YYYY").isSame(today));

        if (validDates.length > 0) {
          selected = validDates.reduce((prev, curr) => {
            const prevDiff = today.diff(dayjs(prev.ngay_ad, "DD/MM/YYYY"), "day");
            const currDiff = today.diff(dayjs(curr.ngay_ad, "DD/MM/YYYY"), "day");
            return currDiff < prevDiff ? curr : prev;
          });
        } else {
          // Fallback: nếu không có ngày nào <= hôm nay, chọn ngày gần nhất
          selected = danhSachCauHoiApDung.reduce((prev, curr) => {
            const prevDiff = Math.abs(today.diff(dayjs(prev.ngay_ad, "DD/MM/YYYY"), "day"));
            const currDiff = Math.abs(today.diff(dayjs(curr.ngay_ad, "DD/MM/YYYY"), "day"));
            return currDiff < prevDiff ? curr : prev;
          });
        }
      }
      if (selected && selected.bt !== undefined && selected.bt !== null) {
        setSelectedCauHoiApDung(Number(selected.bt));
        layDanhSachCauHoi({bt_ap_dung: Number(selected.bt)});
      }
    } else {
      setSelectedCauHoiApDung(null);
      // setDanhSachCauHoi([]);
    }
  }, [danhSachCauHoiApDung]);
  const closeModal = () => {
    setIsOpen(false);
    setChiTietDanhMucSanPham(null);
    setDanhSachCauHoi([]);

    form.resetFields();
    setFilterParams(filterParams);
  };
  // Dữ liệu bảng câu hỏi áp dụng(bên trái)
  const dataTableListNgayApDung = useMemo<Array<TableCauHoiApDungDataType>>(() => {
    try {
      const mappedData = danhSachCauHoiApDung.map((item: any, index: number) => ({
        stt: item.sott ?? index + 1,
        ngay_ad: item.ngay_ad || "",
        bt: item.bt,
        ma_doi_tac_ql: chiTietDanhMucSanPham?.ma_doi_tac_ql,
        nv: chiTietDanhMucSanPham?.nv,
        ma_sp: chiTietDanhMucSanPham?.ma,
        ngay_cap_nhat: item.ngay_cap_nhat,
        nguoi_cap_nhat: item.nguoi_cap_nhat,
        key: index.toString(),
        hanh_dong: () => renderDeleteButton(item.bt),
      }));
      const arrEmptyRow = fillRowTableEmpty(mappedData.length, pageSize);
      return [...mappedData, ...arrEmptyRow];
    } catch (error) {
      console.error("Lỗi khi xử lý dữ liệu bảng ngày áp dụng:", error);
      return [];
    }
  }, [danhSachCauHoiApDung, pageSize]);
  const dataTableListCauHoi = useMemo<Array<TableCauHoiDataType>>(() => {
    try {
      const mappedData = danhSachCauHoi.map((item: any, index: number) => ({
        stt: item.sott ?? index + 1,
        ngay_ad: item.ngay_ad || "",
        bt_ap_dung: item.bt_ap_dung,
        ma: item.ma,
        ten: item.ten,
        kieu_chon: item.kieu_chon,
        bat_buoc: item.bat_buoc,
        do_rong: item.do_rong,
        ngay_tao: item.ngay_tao,
        nguoi_tao: item.nguoi_tao,
        ngay_cap_nhat: item.ngay_cap_nhat,
        nguoi_cap_nhat: item.nguoi_cap_nhat,
        key: index.toString(),
        hanh_dong: () => renderDeleteButtonCH(item.ma),
      }));

      const arrEmptyRow = fillRowTableEmpty(mappedData.length, pageSize);
      return [...mappedData, ...arrEmptyRow];
    } catch (error) {
      console.error("Lỗi khi xử lý dữ liệu bảng ngày áp dụng:", error);
      return [];
    }
  }, [danhSachCauHoi, pageSize]);
  const renderFormInputColum = (props?: any, span = 8) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: DataIndexCauHoiApDung) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);
  const handleSearchCH = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: DataIndexCauHoi) => {
    confirm();
    setSearchTextCH(selectedKeys[0]);
    setSearchedColumnCH(dataIndex);
  }, []);
  const handleReset = useCallback(
    (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: DataIndexCauHoiApDung) => {
      clearFilters();
      setSearchedColumn("");
      handleSearch([""], confirm, dataIndex);
    },
    [handleSearch],
  );
  const handleResetCH = useCallback(
    (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: DataIndexCauHoi) => {
      clearFilters();
      setSearchedColumnCH("");
      handleSearchCH([""], confirm, dataIndex);
    },
    [handleSearchCH],
  );
  const handleSubmit = async () => {
    try {
      const values = await formThemNgayApDung.validateFields();

      if (!values.ngay_ad) {
        console.log("Vui lòng chọn ngày áp dụng");
        return;
      }

      await onUpdateCauHoiApDung({
        ma_sp: chiTietDanhMucSanPham?.ma,
        nv: chiTietDanhMucSanPham?.nv,
        ma_doi_tac_ql: chiTietDanhMucSanPham?.ma_doi_tac_ql,
        ngay_ad: Number(dayjs(values.ngay_ad).format("YYYYMMDD")),
      });
      setDropdownOpen(false); // Đóng dropdown sau khi lưu thành công
      formThemNgayApDung.resetFields();
      setNgayAdMoiTao(values.ngay_ad); // Lưu lại ngày vừa tạo

      if (chiTietDanhMucSanPham?.ma && chiTietDanhMucSanPham.nv && chiTietDanhMucSanPham.ma_doi_tac_ql) {
        layDanhSachCauHoiApDung({ma_sp: chiTietDanhMucSanPham.ma, nv: chiTietDanhMucSanPham.nv, ma_doi_tac_ql: chiTietDanhMucSanPham.ma_doi_tac_ql});
      }
    } catch (error) {
      console.log("Lỗi khi submit:", error);
      // Không đóng dropdown nếu có lỗi để người dùng có thể sửa
    }
  };
  const handleDelete = async (bt: number) => {
    try {
      await onDeleteCauHoiApDung({
        bt: Number(bt),
      });

      // Reset selection sau khi xóa
      setSelectedCauHoiApDung(null);
      //  setDanhSachCauHinhPhanCapPheDuyetCT([]);

      // Refresh danh sách
      if (chiTietDanhMucSanPham?.ma && chiTietDanhMucSanPham.nv && chiTietDanhMucSanPham.ma_doi_tac_ql) {
        layDanhSachCauHoiApDung({ma_sp: chiTietDanhMucSanPham.ma, nv: chiTietDanhMucSanPham.nv, ma_doi_tac_ql: chiTietDanhMucSanPham.ma_doi_tac_ql});
      }
    } catch (error) {
      console.log("Lỗi khi xóa ngày áp dụng:", error);
    }
  };
  const handleDeleteCH = async (ma: string) => {
    try {
      await onDeleteCauHoi({
        ma: ma,
      });

      console.log("Xóa ngày áp dụng thành công");

      // Reset selection sau khi xóa
      // setSelectedCauHoiApDung(null);
      //  setDanhSachCauHinhPhanCapPheDuyetCT([]);
      setDanhSachCauHoi([]);
      // Refresh danh sách
      if (chiTietDanhMucSanPham?.ma && chiTietDanhMucSanPham.nv && chiTietDanhMucSanPham.ma_doi_tac_ql) {
        layDanhSachCauHoi({bt_ap_dung: Number(selecteCauHoiApDung)});
      }
    } catch (error) {
      console.log("Lỗi khi xóa ngày áp dụng:", error);
    }
  };
  const handleCauHoiApDungRowClick = async (record: TableCauHoiApDungDataType) => {
    // Nếu là hàng trống (key chứa "empty" hoặc không có bt), set null
    if (record.key?.toString().includes("empty") || record.bt === undefined || record.bt === null) {
      setSelectedCauHoiApDung(null);
      setDanhSachCauHoi([]);
      return;
    }
    setSelectedCauHoiApDung(record.bt);
    try {
      const cauHoiData = await layDanhSachCauHoi({bt_ap_dung: record.bt});
      setDanhSachCauHoi(cauHoiData);
    } catch (error) {
      setDanhSachCauHoi([]);
    }
  };
  const getColumnSearchCauHoiApDungProps = (dataIndex: DataIndexCauHoiApDung, title: string): TableColumnType<DataIndexCauHoiApDung> => ({
    filterDropdown: ({setSelectedKeys, selectedKeys, confirm, clearFilters}) => (
      <TableFilterDropdown
        ref={searchInput}
        title={title}
        selectedKeys={selectedKeys}
        dataIndex={dataIndex}
        setSelectedKeys={setSelectedKeys}
        handleSearch={handleSearch}
        confirm={confirm}
        clearFilters={clearFilters}
        handleReset={handleReset}
      />
    ),
    // filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} />,
    onFilter: (value, record) =>
      record[dataIndex as keyof DataIndexCauHoiApDung]
        ? record[dataIndex as keyof DataIndexCauHoiApDung]
            .toString()
            .toLowerCase()
            .includes((value as string).toLowerCase())
        : false,
    filterDropdownProps: {
      onOpenChange(open) {
        if (open) {
          setTimeout(() => searchInput.current?.select(), 100);
        }
      },
    },
    // filters: dataIndex === "trang_thai_ten" ? radioItemTrangThaiNhomTable : undefined,
    render: (text, record, index) => {
      //   if (dataIndex === "trang_thai_ten") {
      //     // Xác định màu dựa vào text (trạng thái hiển thị)
      //     const color = text === "Đang sử dụng" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];
      //     if (record.key.toString().includes("empty")) return "";
      //     return <Tag color={color}>{text}</Tag>;
      //   }
      // if (record.key.toString().includes("empty")) return <div style={{height: 20}} />;

      return searchedColumn === dataIndex ? (
        <Highlighter searchWords={[searchText]} textToHighlight={text ? text.toString() : ""} />
      ) : text !== undefined ? (
        text
      ) : (
        <Button type="link" className="h-auto p-0" style={{color: "transparent"}} icon={<CloseOutlined style={{visibility: "hidden"}} />}></Button>
      );
    },
  });
  const getColumnSearchCauHoiProps = (dataIndex: DataIndexCauHoi, title: string): TableColumnType<DataIndexCauHoi> => ({
    filterDropdown: ({setSelectedKeys, selectedKeys, confirm, clearFilters}) => (
      <TableFilterDropdown
        ref={searchInputCH}
        title={title}
        selectedKeys={selectedKeys}
        dataIndex={dataIndex}
        setSelectedKeys={setSelectedKeys}
        handleSearch={handleSearchCH}
        confirm={confirm}
        clearFilters={clearFilters}
        handleReset={handleResetCH}
      />
    ),
    // filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} />,
    onFilter: (value, record) =>
      record[dataIndex as keyof DataIndexCauHoi]
        ? record[dataIndex as keyof DataIndexCauHoi]
            .toString()
            .toLowerCase()
            .includes((value as string).toLowerCase())
        : false,
    filterDropdownProps: {
      onOpenChange(open) {
        if (open) {
          setTimeout(() => searchInputCH.current?.select(), 100);
        }
      },
    },
    // filters: dataIndex === "trang_thai_ten" ? radioItemTrangThaiNhomTable : undefined,
    render: (text, record, index) => {
      //   if (dataIndex === "trang_thai_ten") {
      //     // Xác định màu dựa vào text (trạng thái hiển thị)
      //     const color = text === "Đang sử dụng" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];
      //     if (record.key.toString().includes("empty")) return "";
      //     return <Tag color={color}>{text}</Tag>;
      //   }
      // if (record.key.toString().includes("empty")) return <div style={{height: 20}} />;

      return searchedColumnCH === dataIndex ? (
        <Highlighter searchWords={[searchTextCH]} textToHighlight={text ? text.toString() : ""} />
      ) : text !== undefined ? (
        text
      ) : (
        <Button type="link" className="h-auto p-0" style={{color: "transparent"}} icon={<CloseOutlined style={{visibility: "hidden"}} />}></Button>
      );
    },
  });
  const renderDeleteButton = (bt?: number) => {
    if (!bt) return null;
    return (
      <div>
        <Popcomfirm
          title="Thông báo"
          onConfirm={() => handleDelete(bt)}
          htmlType="button"
          okText="Xóa"
          description="Bạn có chắc muốn xóa ngày áp dụng?"
          buttonTitle={""}
          buttonColor="red"
          okButtonProps={{
            style: {
              backgroundColor: "white",
              borderColor: "red",
              color: "red",
            },
          }}
          style={{width: "fit-content"}}
          variant="text"
          className="h-auto"
          icon={<CloseOutlined />}
          buttonIcon
        />
      </div>
    );
  };
  const renderDeleteButtonCH = (ma?: string) => {
    if (!ma) return null;
    // if (!ma) {
    //   // Render placeholder giữ chiều cao
    //   return (
    //     <div style={{minHeight: 32, display: "flex", alignItems: "center", justifyContent: "center"}}>
    //       {/* {"\u00A0"} hoặc có thể dùng <Tag color="transparent" className="!text-white text-[12px]">{'\u00A0'}</Tag> */}
    //       {"\u00A0"}
    //     </div>
    //   );
    // }
    return (
      <div
        onClick={e => {
          e.stopPropagation();
        }}>
        <Popcomfirm
          title="Thông báo"
          onConfirm={() => handleDeleteCH(ma)}
          htmlType="button"
          okText="Xóa"
          description="Bạn có chắc muốn xóa câu hỏi?"
          buttonTitle={""}
          buttonColor="red"
          okButtonProps={{
            style: {
              backgroundColor: "white",
              borderColor: "red",
              color: "red",
            },
          }}
          style={{width: "fit-content"}}
          variant="text"
          className="h-auto"
          icon={<CloseOutlined />}
          buttonIcon
        />
      </div>
    );
  };
  //render footer
  //   const renderFooter = () => {
  //     return (
  //       <Form.Item>
  //         <Button type="default" onClick={() => setIsOpen(false)} className="mr-2" icon={<ArrowLeftOutlined />}>
  //           Quay lại
  //         </Button>
  //         <Popcomfirm
  //           title="Thông báo"
  //           //   onConfirm={onConfirm}
  //           okText="Lưu"
  //           description="Bạn có chắc muốn lưu thông tin?"
  //           buttonTitle="Lưu"
  //           buttonDisable={disableSubmit}
  //           buttonIcon={<CheckOutlined />}
  //           iconPosition="end"
  //           loading={loading}
  //         />
  //       </Form.Item>
  //     );
  //   };
  const renderTableCauHoiApDungFooter = () => {
    return (
      <div className="">
        <Form.Item className="" style={{marginTop: 16, marginRight: 8, marginBottom: 0}}>
          {/* <Space direction="vertical" > */}
          <Space className="">
            <Dropdown
              className=""
              open={dropdownOpen}
              onOpenChange={setDropdownOpen}
              trigger={["click"]}
              getPopupContainer={triggerNode => triggerNode.parentNode as HTMLElement}
              dropdownRender={() => (
                <div style={{padding: 8, display: "flex"}}>
                  <Form id="formThemNgayApDung" form={formThemNgayApDung} layout="vertical">
                    <div style={{display: "flex", justifyContent: "space-between", alignItems: "flex-end"}}>
                      {renderFormInputColum({...ngay_ad}, 24)}
                      {/* <Form.Item> */}
                      <Button type="primary" onClick={handleSubmit} style={{marginLeft: 8, marginBottom: 8}}>
                        Áp dụng
                      </Button>
                      {/* </Form.Item> */}
                    </div>
                  </Form>
                </div>
              )}
              placement="topRight">
              <Button className="w-full" type="primary" icon={<PlusCircleOutlined />} onClick={() => setDropdownOpen(true)}>
                Thêm ngày áp dụng
              </Button>
            </Dropdown>
          </Space>
          {/* </Space> */}
        </Form.Item>
      </div>
    );
  };

  const renderForm = () => (
    <Form form={form} layout="vertical" initialValues={{trang_thai: TRANG_THAI_CHI_TIET_SAN_PHAM[0].ma}}>
      <Row gutter={16}>
        {renderFormInputColum({...ma_doi_tac_ql, options: listDoiTac, disabled: chiTietDanhMucSanPham ? true : false})}
        {renderFormInputColum({...nv, options: listNghiepVu, disabled: chiTietDanhMucSanPham ? true : false})}
        {renderFormInputColum({...ten, disabled: chiTietDanhMucSanPham ? true : false})}
      </Row>
    </Form>
  );
  const renderTableCauHoiApDung = () => {
    return (
      <Table<TableCauHoiApDungDataType>
        className="table-vai-tro no-header-border-radius"
        {...defaultTableProps}
        style={{cursor: "pointer"}}
        onRow={(record, rowIndex) => {
          return {
            style: {
              cursor: "pointer",
              background: record.bt === selecteCauHoiApDung ? "#96BF49" : undefined, // Làm nổi bật hàng được chọn
            },
            onClick: () => handleCauHoiApDungRowClick(record), // Xử lý nhấp vào hàng
          };
        }}
        title={null}
        pagination={false}
        columns={(cauHoiApDungColumns || []).map(item => {
          // Đảm bảo item.key được định nghĩa, item.title là chuỗi và item.key không phải là "stt" trước khi thêm ô tìm kiếm
          return {
            ...item,
            ...(item.key && typeof item.title === "string" && item.key !== "stt" ? getColumnSearchCauHoiApDungProps(item.key as keyof TableCauHoiApDungDataType, item.title) : {}),
          };
        })}
        dataSource={dataTableListNgayApDung}
        bordered
        scroll={dataTableListNgayApDung.length > pageSize ? {y: 285} : undefined}
      />
    );
  };
  const renderTableCauHoiFooter = () => {
    return (
      <Form.Item style={{marginTop: 16, marginBottom: 0, textAlign: "end"}}>
        <Button className="" type="primary" icon={<PlusCircleOutlined />} onClick={() => refModalThemCauHoi.current?.open()} loading={loading} disabled={disableSubmit}>
          Thêm câu hỏi
        </Button>
      </Form.Item>
    );
  };
  const renderTableCauHoi = () => {
    return (
      <Table<TableCauHoiDataType>
        className="table-nhom no-header-border-radius"
        {...defaultTableProps}
        style={{cursor: "pointer"}}
        onRow={(record, rowIndex) => {
          return {
            style: {cursor: "pointer"},
            onClick: async event => {
              if (record.key.toString().includes("empty")) return;
              const response: CommonExecute.Execute.ICauHoi | null = await layChiTietCauHoi({ma: record.ma, bt_ap_dung: Number(selecteCauHoiApDung)});
              if (response) refModalThemCauHoi.current?.open(response);
            }, // click row
          };
        }}
        title={null}
        pagination={false}
        columns={(cauHoiColumns || []).map(item => {
          // Đảm bảo item.key được định nghĩa, item.title là chuỗi và item.key không phải là "stt" trước khi thêm ô tìm kiếm
          return {
            ...item,
            ...(item.key && typeof item.title === "string" && item.key !== "stt" ? getColumnSearchCauHoiProps(item.key as keyof TableCauHoiDataType, item.title) : {}),
          };
        })}
        dataSource={dataTableListCauHoi}
        bordered
        scroll={dataTableListCauHoi.length > pageSize ? {y: 285} : undefined}
      />
    );
  };
  const renderTable = () => {
    return (
      <Row>
        <Col span={7} style={{paddingRight: 16}}>
          {renderTableCauHoiApDung()}
          {renderTableCauHoiApDungFooter()}
        </Col>
        <Col span={17}>
          {renderTableCauHoi()}
          {renderTableCauHoiFooter()}
        </Col>
      </Row>
    );
  };
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        title={
          <HeaderModal
            title={chiTietDanhMucSanPham ? `Chi tiết bộ mã câu hỏi của ${chiTietDanhMucSanPham.ten}` : ""}
            trang_thai_ten={chiTietDanhMucSanPham?.trang_thai_ten}
            trang_thai={chiTietDanhMucSanPham?.trang_thai}
          />
        }
        maskClosable={false}
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width="100vw"
        // styles={{
        //   body: {
        //     height: "60vh",
        //   },
        // }}
        footer={null}
        className="modal-them-cau-hoi-ct [&_.ant-space]:w-full">
        {renderForm()}
        {renderTable()}
        <ModalThemCauHoi ref={refModalThemCauHoi} CauHoiApDung={selecteCauHoiApDung}></ModalThemCauHoi>
      </Modal>
    </Flex>
  );
});
ModalChiTietBoMaCauHoiComponent.displayName = "ModalChiTietBoMaCauHoiComponent";
export const ModalChiTietBoMaCauHoi = memo(ModalChiTietBoMaCauHoiComponent, isEqual);

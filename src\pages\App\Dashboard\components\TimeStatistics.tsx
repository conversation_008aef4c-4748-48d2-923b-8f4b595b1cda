import React, { useState } from 'react';
import { Button } from 'antd';

interface TimeStatisticsProps {
  selectedPeriod: 'week' | 'month' | 'year';
  onPeriodChange: (period: 'week' | 'month' | 'year') => void;
}

const TimeStatistics: React.FC<TimeStatisticsProps> = ({
  selectedPeriod,
  onPeriodChange,
}) => {
  // Mock data for different periods
  const periodData = {
    week: {
      label: 'Tuần này',
      date: 'Tuần 42/2024',
      currentValue: 45000000,
      yearValue: 42000000,
      lastYearValue: 38000000,
    },
    month: {
      label: 'Tháng này',
      date: 'Tháng 10/2024',
      currentValue: 195000000,
      yearValue: 170000000,
      lastYearValue: 190000000,
    },
    year: {
      label: 'Năm này',
      date: 'Năm 2024',
      currentValue: 2340000000,
      yearValue: 2340000000,
      lastYearValue: 2100000000,
    },
  };

  const currentData = periodData[selectedPeriod];

  const formatNumber = (value: number) => {
    return new Intl.NumberFormat('vi-VN').format(value);
  };

  return (
    <div className="time-statistics-container">
      {/* Period Tabs */}
      <div className="period-tabs">
        <Button
          type={selectedPeriod === 'week' ? 'primary' : 'default'}
          size="small"
          onClick={() => onPeriodChange('week')}
        >
          Tuần
        </Button>
        <Button
          type={selectedPeriod === 'month' ? 'primary' : 'default'}
          size="small"
          onClick={() => onPeriodChange('month')}
        >
          Tháng
        </Button>
        <Button
          type={selectedPeriod === 'year' ? 'primary' : 'default'}
          size="small"
          onClick={() => onPeriodChange('year')}
        >
          Năm
        </Button>
      </div>

      {/* Current Period Stats */}
      <div className="current-period">
        <div className="period-label">{currentData.label}</div>
        <div className="period-date">{currentData.date}</div>
        <div className="period-value">{formatNumber(currentData.currentValue)}đ</div>
      </div>

      {/* Period Comparison */}
      <div className="period-comparison">
        <div className="comparison-item">
          <Button className="period-btn current" size="small">
            {selectedPeriod === 'year' ? 'Năm nay' : 'Năm nay'}
          </Button>
          <div className="comparison-value">{formatNumber(currentData.yearValue)}đ</div>
        </div>
        <div className="comparison-item">
          <Button className="period-btn previous" size="small">
            {selectedPeriod === 'year' ? 'Năm trước' : 'Năm trước'}
          </Button>
          <div className="comparison-value">{formatNumber(currentData.lastYearValue)}đ</div>
        </div>
      </div>
    </div>
  );
};

export default TimeStatistics;

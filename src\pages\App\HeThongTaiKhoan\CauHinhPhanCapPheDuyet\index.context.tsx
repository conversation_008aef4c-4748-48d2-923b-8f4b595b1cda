import {createContext, useContext} from "react";
import {CauHinhPhanCapPheDuyetContextProps} from "./index.model";

//createContext : khai báo 1 context mà component có thể cung cấp hoặc đọc
export const CauHinhPhanCapPheDuyetContext = createContext<CauHinhPhanCapPheDuyetContextProps>({
  //khởi tạo các giá trị mặc định, trong context này có giá trị là onSubmit
  danhSachTaiKhoanNguoiDung: [],
  loading: false,
  listDoiTac: [],
  listChiNhanh: [],
  defaultFormValue: {},
  tongSoDong: 0,
  danhSachCauHinhPhanCapPheDuyet: [],
  danhSachCauHinhPhanCapPheDuyetCT: [],
  listSanPham: [],
  listPhanCapNhom: [],
  onUpdateCauHinhPhanCapPheDuyet: () => Promise.resolve(),
  onDeleteCauHinhPhanCapPheDuyet: () => Promise.resolve(),
  onDeleteCauHinhPhanCapPheDuyetCT: () => Promise.resolve(),
  onUpdateCauHinhPhanCapPheDuyetCT: () => Promise.resolve(),
  layDanhSachCauHinhPhanCapPheDuyetCT: () => Promise.resolve(null),
  setDanhSachCauHinhPhanCapPheDuyetCT: () => {},
  layDanhSachCauHinhPhanCapPheDuyet: () => Promise.resolve(null),
  setDanhSachCauHinhPhanCapPheDuyet: () => {},
  layChiTietTaiKhoanNguoiDung: () => Promise.resolve(null),
  getListChiNhanhTheoDoiTac: () => Promise.resolve(null),
  getListSanPhamTheoDoiTac: () => {},
  getListDoiTac: () => Promise.resolve(),
  layDanhSachTaiKhoanNguoiDungPhanTrang: () => Promise.resolve(),
  layChiTietCauHinhPhanCapPheDuyetCT: () => Promise.resolve(null),
});

//useContext(someContext): là React Hook cho phép bạn đọc và subscribe context từ component của mình.
// file .context muốn truy câp context thì sử dụng useLoginContext : const {onSubmit} = useLoginContext();
export const useCauHinhPhanCapPheDuyetContext = () => useContext(CauHinhPhanCapPheDuyetContext);

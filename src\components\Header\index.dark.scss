.custom-header {
  /*
  gi<PERSON>i thích câu lệnh &.ant-layout-header
  - & đại diện cho selector cha (ở cấp trên).
  - .ant-layout-header là class bạn muốn thêm ngay sau selector cha, không có kho<PERSON> trắng (nghĩa là cùng cấp, không phải con).
  */
  &.ant-layout-header {
    /*
    //apply : là 1 directive, là cú phấp của Tailwind, dùng để chèn các utility class trực tiếp vào trong CSS.
    //dark: hoạt động dựa trên class="dark" trên <html> hoặc <body>, không tự động bật theo OS trừ khi bạn cấu hình.
    //flex 
    Có nghĩa là "dùng display: flex bình thường, nhưng khi bật dark mode, thì background sẽ là màu black-60".
    */
    
    @apply dark:bg-black-60 flex; // các gi<PERSON> trị sau apply phải là class tiện ích của tailwindcss
  }
}

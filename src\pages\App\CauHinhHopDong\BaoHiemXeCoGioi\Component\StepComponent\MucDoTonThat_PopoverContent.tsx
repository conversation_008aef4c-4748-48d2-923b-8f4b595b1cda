import {Button, Input, List} from "antd";
import {useMemo, useState, useEffect} from "react";
import {FormInput} from "@src/components";
import {useBaoHiemXeCoGioiContext} from "../../index.context";

interface PhanLoaiPopoverContentProps {
  onSelect: (ma_hang_muc: string) => void;
  onClose: () => void;
  selectedMucDo?: string; // Thêm prop để hiển thị mức độ đã chọn
}

const MucDoTonThatPopoverContent = ({onSelect, onClose, selectedMucDo}: PhanLoaiPopoverContentProps) => {
  const {listMucDoTonThatXe} = useBaoHiemXeCoGioiContext();
  const [selectedMucDoTonThat, setSelectedMucDoTonThat] = useState(selectedMucDo || "");
  const [searchText, setSearchText] = useState("");

  // Cập nhật selectedMucDoTonThat khi prop selectedMucDo thay đổi
  useEffect(() => {
    if (selectedMucDo) {
      setSelectedMucDoTonThat(selectedMucDo);
    }
  }, [selectedMucDo]);

  const handleSelectMucDoTonThat = () => {
    if (selectedMucDoTonThat) {
      onSelect(selectedMucDoTonThat);
      onClose();
      setSelectedMucDoTonThat("");
    }
  };

  // Lọc danh sách hạng mục theo từ khóa tìm kiếm
  const filteredHangMuc = useMemo(() => {
    if (!searchText.trim()) return listMucDoTonThatXe;
    return listMucDoTonThatXe.filter(item => item.ten?.toLowerCase().includes(searchText.toLowerCase()) || item.ma?.toLowerCase().includes(searchText.toLowerCase()));
  }, [listMucDoTonThatXe, searchText]);

  return (
    <div className="flex h-[45vh] w-[250px] flex-col">
      {/* Input tìm kiếm */}
      <Input placeholder="Tìm kiếm mức độ tổn thất..." value={searchText} onChange={e => setSearchText(e.target.value)} style={{marginBottom: 8}} allowClear />

      {/* Danh sách hạng mục với List + FormInput checkbox, chỉ chọn được 1 */}
      <div className="flex flex-1 flex-col overflow-hidden">
        {filteredHangMuc && filteredHangMuc.length > 0 ? (
          <List
            dataSource={filteredHangMuc}
            style={{
              flex: 1,
              overflowY: "auto",
              overflowX: "hidden",
              paddingRight: 4, // Thêm padding để tránh thanh cuộn che nội dung
            }}
            renderItem={(item: any) => (
              <div onClick={() => setSelectedMucDoTonThat(selectedMucDoTonThat === item.ma ? "" : item.ma)} key={item.value} className="mb-0 flex flex-row items-center gap-1">
                <FormInput
                  className="!mb-0 mr-1"
                  component="checkbox"
                  checked={selectedMucDoTonThat === item.ma}
                  onChange={() => setSelectedMucDoTonThat(selectedMucDoTonThat === item.ma ? "" : item.ma)}
                  label={undefined}
                />
                <span>{item.ten}</span>
              </div>
            )}
          />
        ) : (
          <div className="flex flex-1 items-center justify-center">{searchText ? `Không tìm thấy hạng mục nào cho "${searchText}"` : "Không có hạng mục nào"}</div>
        )}
      </div>

      <div className="mt-auto flex justify-end gap-2 border-t border-[#f0f0f0] pt-3">
        <Button size="small" onClick={onClose}>
          Hủy
        </Button>
        <Button type="primary" size="small" disabled={!selectedMucDoTonThat} onClick={handleSelectMucDoTonThat}>
          Chọn
        </Button>
      </div>
    </div>
  );
};

export default MucDoTonThatPopoverContent;

// import React, {<PERSON>ps<PERSON><PERSON><PERSON>hildren, use<PERSON>allback, useMemo, useState} from "react";
// import {useNavigate} from "react-router-dom";
// import {Authen} from "@src/@types/Authentication";
// import {useProfile} from "@src/hooks";
// import {SignupContext} from "./index.context";
// import {ISignupContextProps} from "./index.model";

// const SignupProvider: React.FC<PropsWithChildren> = props => {
//   const {children} = props;
//   const {setProfile} = useProfile();
//   const navigate = useNavigate();
//   const [loading, setLoading] = useState<boolean>(false);

//   const onSubmit = useCallback(
//     async (values: Authen.Signup.IFormFieldsValue) => {
//       setLoading(true);
//       const profile = {
//         access_token: "access_token_test",
//         email: values.email,
//         user_name: "user_name_test",
//         avatar: "",
//       };
//       setProfile(profile);
//       navigate("/home");
//     },
//     [navigate, setProfile],
//   );

//   const value: ISignupContextProps = useMemo(
//     () => ({
//       loading,
//       onSubmit,
//     }),
//     [loading, onSubmit],
//   );

//   return <SignupContext.Provider value={value}>{children}</SignupContext.Provider>;
// };

// export default SignupProvider;

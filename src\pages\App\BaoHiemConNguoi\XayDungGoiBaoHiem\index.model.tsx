import {ReactQuery} from "@src/@types";

export interface IXayDungGoiBaoHiemContextProps {
  windowHeight: number;

  loading: boolean;
  filterGoiBaoHiemParams: ReactQuery.ITimKiemPhanTrangGoiBaoHiemParams;
  tongSoDongGoiBaoHiem: number;
  listGoiBaoHiem: Array<CommonExecute.Execute.IGoiBaoHiemConNguoi>;
  listQuyenLoiRoot: CommonExecute.Execute.IDanhSachBoMaQuyenLoi[];

  listDoiTac: Array<CommonExecute.Execute.IDoiTac>;
  listSanPham: Array<CommonExecute.Execute.ISanPham>;
  listNguyenTe: Array<CommonExecute.Execute.IChiTietBoMaNguyenTe>;

  timKiemPhanTrangGoiBaoHiem: () => Promise<any>;
  setFilterGoiBaoHiemParams: React.Dispatch<React.SetStateAction<ReactQuery.ITimKiemPhanTrangGoiBaoHiemParams>>;
  getChiTietGoiBaoHiem: (params: ReactQuery.IChiTietGoiBaoHiemParams) => Promise<CommonExecute.Execute.IChiTietGoiBaoHiemConNguoi>;
  timKiemPhanTrangBoMaQuyenLoi: (params: ReactQuery.ITimKiemPhanTrangBoMaQuyenLoiParams) => Promise<void>;
  updateGoiBaoHiem: (params: ReactQuery.ICapNhatGoiBaoHiemParams) => Promise<any>;

  // Bệnh viện API functions
  timKiemBenhVienPhanTrang: (params: ReactQuery.ITimKiemBenhVienParams) => Promise<any>;
  layDanhSachBenhVienDaLuu: (params: ReactQuery.ILayDanhSachBenhVienDaLuuParams) => Promise<any>;
  luuCauHinhBenhVien: (params: ReactQuery.ILuuCauHinhBenhVienParams) => Promise<boolean>;

  // Alias functions for backward compatibility with table components
  timKiemPhanTrangMaBenhGoiBH: (params: ReactQuery.ITimKiemMaBenhGoiBaoHiemParams) => Promise<any>;
  layDanhSachMaBenhDaLuuGoiBaoHiem: (params: ReactQuery.ILayDanhSachMaBenhDaLuuGoiBaoHiemParams) => Promise<any>;
  luuCauHinhMaBenhGoiBaoHiem: (params: ReactQuery.ILuuCauHinhMaBenhGoiBaoHiemParams) => Promise<boolean>;
}

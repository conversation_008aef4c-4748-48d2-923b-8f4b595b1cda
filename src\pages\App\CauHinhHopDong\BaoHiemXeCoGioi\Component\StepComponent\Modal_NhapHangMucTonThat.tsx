import {CheckOutlined, CloseOutlined, SearchOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {Button, FormInput, HeaderModal} from "@src/components";
import {defaultPaginationTableProps, fillRowTableEmpty} from "@src/hooks";
import {Col, Flex, Form, Modal, Row, Table, TableColumnType} from "antd";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState} from "react";
import {defaultFormValueTimKiemHangMucTonThat} from "../../index.configs";
import {useBaoHiemXeCoGioiContext} from "../../index.context";
import {FormTimKiemHangMucTonThat, tableHangMucTonThatColumn, TableHangMucTonThatColumnDataType} from "./Constant";

const PAGE_SIZE = 10; // khai báo khác default cho vừa màn hình
const {ma, ten} = FormTimKiemHangMucTonThat;
export interface IModalNhapHangMucTonThatRef {
  open: () => void;
  close: () => void;
}

const ModalNhapHangMucTonThatComponent = forwardRef<IModalNhapHangMucTonThatRef>((props, ref) => {
  useImperativeHandle(ref, () => ({
    open: () => {
      setIsOpen(true);
      // Gọi API tìm kiếm với dữ liệu mặc định
      const so_id = chiTietHopDongBaoHiemXe?.so_id;
      if (so_id) {
        const defaultParams = {
          ...defaultFormValueTimKiemHangMucTonThat,
          trang: 1,
          so_dong: PAGE_SIZE,
        };
        setSearchParams(defaultParams);
        timKiemPhanTrangHangMucTonThat(defaultParams);
      }
    },
    close: () => setIsOpen(false),
  }));

  const {
    loading,
    timKiemPhanTrangHangMucTonThat,
    danhSachHangMucTonThat,
    tongSoDongHangMucTonThat,
    chiTietHopDongBaoHiemXe,
    luuHangMucTonThat,
    chiTietDoiTuongBaoHiemXe,
    layDanhSachHangMucTonThatTheoDoiTuongXe,
  } = useBaoHiemXeCoGioiContext();

  const [formTimKiemPhanTrangHangMucTonThat] = Form.useForm();
  const [disableSubmit, setDisableSubmit] = useState<boolean>(true);
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [page, setPage] = useState(1);
  const [dataTableListDoiTuongBaoHiemXe, setDataTableListDoiTuongBaoHiemXe] = useState<Array<TableHangMucTonThatColumnDataType>>([]);
  const [selectedItems, setSelectedItems] = useState<Array<TableHangMucTonThatColumnDataType>>([]);
  const [searchParams, setSearchParams] = useState<ReactQuery.ITimKiemPhanTrangHangMucTonThatParams & Partial<ReactQuery.IPhanTrang>>({
    ...defaultFormValueTimKiemHangMucTonThat,
    trang: 1,
    so_dong: PAGE_SIZE,
  });

  const selectedItemsRef = useRef(selectedItems);

  useEffect(() => {
    selectedItemsRef.current = selectedItems;
  }, [selectedItems]);

  //Init data khi mở modal
  const initDataTable = useMemo(() => {
    try {
      // match mảng đối tượng đã được áp dụng với mảng data phân trang, dùng 'ma' làm key
      const tableData = danhSachHangMucTonThat.map((item: any, index: number) => {
        const key = item.ma?.toString() || index.toString();
        const selected = selectedItems.find(i => i.ma === item.ma);
        return {
          ...item,
          key,
          ap_dung: selected ? "C" : "K",
        };
      });
      const arrEmptyRow: Array<TableHangMucTonThatColumnDataType> = fillRowTableEmpty(tableData.length, PAGE_SIZE);
      setDataTableListDoiTuongBaoHiemXe([...tableData, ...arrEmptyRow]);
    } catch (error) {
      setDataTableListDoiTuongBaoHiemXe([]);
      setSelectedItems([]);
    }
  }, [danhSachHangMucTonThat, selectedItems]);

  //xử lý validate form
  useEffect(() => {
    if (selectedItems.length <= 0) {
      setDisableSubmit(true);
      return;
    } else setDisableSubmit(false);
  }, [selectedItems]);

  // Handle change checkbox Áp dụng
  const handleCheckboxChange = (key: string, checked: boolean) => {
    const item = dataTableListDoiTuongBaoHiemXe.find(i => i.key === key);
    if (!item) return;
    const updatedItem = {...item, ap_dung: checked ? "C" : "K"};

    // Cập nhật selectedItems như cũ
    setSelectedItems(prev => {
      const index = prev.findIndex(i => i.key === updatedItem.key);
      if (checked) {
        if (index !== -1) {
          const newArr = [...prev];
          newArr[index] = {...newArr[index], ap_dung: "C"};
          return newArr;
        } else {
          return [...prev, updatedItem];
        }
      } else {
        if (index !== -1) {
          const newArr = [...prev];
          newArr.splice(index, 1);
          return newArr;
        }
        return prev;
      }
    });

    // Cập nhật dataTableListDoiTuongBaoHiemXe để đồng bộ trạng thái checkbox
    setDataTableListDoiTuongBaoHiemXe(prev => {
      return prev.map(i => (i.key === key ? {...i, ap_dung: checked ? "C" : "K"} : i));
    });
  };

  //Bấm Lưu
  const onPressLuuHangMucTonThat = async () => {
    const hm = selectedItems.filter(item => typeof item.ma === "string" && item.ma).map(item => ({ma_hang_muc: item.ma as string}));
    const success = await luuHangMucTonThat({
      so_id: chiTietHopDongBaoHiemXe.so_id || 0,
      so_id_dt: chiTietDoiTuongBaoHiemXe.gcn?.so_id_dt || 0,
      hm,
    });
    if (success) {
      const searchParams = {
        so_id: chiTietHopDongBaoHiemXe?.so_id || 0,
        so_id_dt: chiTietDoiTuongBaoHiemXe.gcn?.so_id_dt || 0,
      } as ReactQuery.IChiTietDoiTuongBaoHiemXeParams;
      layDanhSachHangMucTonThatTheoDoiTuongXe(searchParams);
      onCloseModal();
    }
  };

  // Gộp hàm xử lý tìm kiếm và chuyển trang
  const handleSearchAndPagination = useCallback(
    (values?: ReactQuery.ITimKiemPhanTrangHangMucTonThatParams & Partial<ReactQuery.IPhanTrang>, pageArg?: number) => {
      if (values) {
        const cleanedValues = {
          ...values,
          nv: values.nv ?? "",
          ma: values.ma ?? "",
          ten: values.ten ?? "",
          loai: values.loai ?? "",
          nhom: values.nhom ?? "",
          vi_tri: values.vi_tri ?? "",
          trang_thai: values.trang_thai ?? "",
        };
        setPage(1);
        // Lưu lại giá trị tìm kiếm để sử dụng khi chuyển trang
        setSearchParams({...cleanedValues, trang: 1, so_dong: PAGE_SIZE});
        timKiemPhanTrangHangMucTonThat({...cleanedValues, trang: 1, so_dong: PAGE_SIZE});
      } else {
        const page = pageArg || 1;
        setPage(page);
        // Sử dụng params đã lưu trước đó khi chuyển trang
        const params = {
          ...searchParams,
          trang: page,
          so_dong: PAGE_SIZE,
        };
        timKiemPhanTrangHangMucTonThat(params);
      }
    },
    [timKiemPhanTrangHangMucTonThat, searchParams],
  );

  //bấm huỷ / để sau xoá dữ liệu của modal khi nào mở thì gọi lại
  const onCloseModal = () => {
    setIsOpen(false);
    // Reset form tìm kiếm
    formTimKiemPhanTrangHangMucTonThat.resetFields();
    // Reset các state
    setSelectedItems([]);
    // Reset search params về mặc định
    setSearchParams({
      ...defaultFormValueTimKiemHangMucTonThat,
      trang: 1,
      so_dong: PAGE_SIZE,
    });
    setPage(1);
    setDataTableListDoiTuongBaoHiemXe([]);
  };

  //RENDER
  const renderFormInputColum = (props?: any, span = 10) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );

  //render cột
  const renderColumn = (column: TableColumnType<TableHangMucTonThatColumnDataType>) => {
    if (!("dataIndex" in column)) return column;

    //check box
    if (column.dataIndex === "ap_dung") {
      return {
        ...column,
        render: (_: any, record: TableHangMucTonThatColumnDataType) =>
          record.key && record.key.toString().startsWith("empty") ? null : (
            <FormInput className="!mb-0" component="checkbox" checked={record.ap_dung === "C"} onChange={e => handleCheckboxChange(record.key, e.target.checked)} />
          ),
      };
    }

    return column;
  };

  //render header table đối tượng
  const renderHeaderTableDoiTuongBaoHiemXe = () => {
    return (
      <Form form={formTimKiemPhanTrangHangMucTonThat} layout="vertical" className="[&_.ant-form-item]:mb-2" onFinish={handleSearchAndPagination}>
        <Row gutter={16}>
          {renderFormInputColum({...ma})}
          {renderFormInputColum({...ten})}

          <Col span={4}>
            <Form.Item>
              <Button type="primary" htmlType="submit" loading={loading} icon={<SearchOutlined />} className="w-full">
                Tìm kiếm
              </Button>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    );
  };

  //renderFooter Modal
  const renderFooter = () => {
    return (
      <Form.Item className="mb-0">
        <Button type="default" htmlType="reset" onClick={onCloseModal} className="mr-2" icon={<CloseOutlined />}>
          Đóng
        </Button>
        <Button htmlType="submit" onClick={onPressLuuHangMucTonThat} icon={<CheckOutlined />} disabled={disableSubmit}>
          Lưu
        </Button>
      </Form.Item>
    );
  };
  //Render
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        className="custom-full-modal"
        title={
          // "Kỳ thanh toán"
          <HeaderModal title={"Hạng mục tổn thất"} />
        }
        centered
        open={isOpen}
        onOk={() => setIsOpen(false)}
        onCancel={onCloseModal}
        width={"57vw"}
        maskClosable={false}
        footer={renderFooter}>
        {renderHeaderTableDoiTuongBaoHiemXe()}
        <Table<TableHangMucTonThatColumnDataType>
          className="hide-scrollbar no-header-border-radius"
          columns={(tableHangMucTonThatColumn || []).map(renderColumn)}
          dataSource={dataTableListDoiTuongBaoHiemXe}
          // scroll={{y: 300}}
          bordered
          pagination={{
            ...defaultPaginationTableProps,
            total: tongSoDongHangMucTonThat,
            pageSize: PAGE_SIZE,
            current: page, //set current page
            onChange: (page, pageSize) => {
              handleSearchAndPagination(undefined, page);
            },
            showLessItems: true,
            locale: {
              jump_to: "Tới trang",
              page: "",
            },
          }}
        />
      </Modal>
    </Flex>
  );
});

export const ModalNhapHangMucTonThat = memo(ModalNhapHangMucTonThatComponent, isEqual);

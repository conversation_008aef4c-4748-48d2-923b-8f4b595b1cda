import {forwardRef, memo, useCallback, useImperative<PERSON>andle, useRef, useState} from "react";
import {Modal, Table, Button, Progress, message, Flex, Typography} from "antd";
import {FileExcelOutlined, CheckOutlined, ReloadOutlined} from "@ant-design/icons";
import {isEqual} from "lodash";
import {HeaderModal} from "@src/components";
import {colWidthByKey, defaultTableColumnsProps, useProfile} from "@src/hooks";
import {ACTION_CODE} from "@src/constants";
import {FileEndpoint} from "@src/services/axios";
import {IModalImportExcelRef, IModalImportExcelProps, IExcelRowData, IUploadProgress, FILE_VALIDATION, ERROR_MESSAGES, MODAL_CONFIG} from "./constants";
import {createExcelImportColumns, validateAllData, addKeysToData, createEmptyRow} from "./tableConfig";
import {InputCellTable} from "@src/components";

const {Text} = Typography;

// Column types matching FormInput components
type ColumnType =
  | "input" // Regular text input
  | "input-number" // Number input
  | "input-price" // Price/currency input
  | "time-picker" // Time picker
  | "date-picker" // Date picker
  | "select" // Dropdown select
  | "textarea" // Multi-line text
  | "readonly"; // Non-editable display

// Helper function to create dynamic table columns from API headers
const createDynamicColumns = (headerObjects: any[], _fieldMapping: Record<string, string>, onChange: (index: number, dataIndex: string, value: string) => void) => {
  // Sort headers by index to maintain correct column order
  const sortedHeaders = [...headerObjects].sort((a, b) => a.index - b.index);

  return sortedHeaders.map((headerObj: any) => {
    const fieldName = headerObj.field_name; // Use API field name directly
    const displayName = headerObj.field_value; // Use API display name directly

    // Determine column type based on field name
    let columnType: ColumnType = "input";
    if (fieldName === "stt") {
      columnType = "readonly";
    } else if (fieldName.includes("tien") || fieldName.includes("thue") || fieldName.includes("phi") || fieldName.includes("gia_tri")) {
      columnType = "input-price";
    }

    return {
      ...defaultTableColumnsProps,
      title: displayName,
      dataIndex: fieldName, // Use API field name as dataIndex
      key: fieldName,
      width: fieldName === "dchi" ? 250 : fieldName === "stt" ? colWidthByKey.sott : 150,
      align: (columnType === "readonly" ? "center" : "left") as "center" | "left",
      render: (value: string, _record: any, index: number) => {
        if (columnType === "readonly") {
          return <div style={{textAlign: "center", padding: "4px 8px"}}>{value || ""}</div>;
        }
        return <InputCellTable component={columnType} value={value || ""} onChange={onChange} index={index} dataIndex={fieldName} placeholder="" />;
      },
    };
  });
};

const ModalImportExcelComponent = forwardRef<IModalImportExcelRef, IModalImportExcelProps>(({onDataConfirm, onCancel}, ref) => {
  // States
  const [isOpen, setIsOpen] = useState(false);
  const [tableData, setTableData] = useState<IExcelRowData[]>([]);
  const [dynamicColumns, setDynamicColumns] = useState<any[]>([]);
  const [uploadProgress, setUploadProgress] = useState<IUploadProgress>({
    percent: 0,
    status: "uploading",
  });
  const [isUploading, setIsUploading] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [validationSummary, setValidationSummary] = useState<{
    isValid: boolean;
    totalErrors: number;
    errors: Array<{row: number; errors: string[]}>;
  }>({isValid: true, totalErrors: 0, errors: []});

  // Hooks
  const {profile} = useProfile();
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handle file upload and processing
  const handleFileUpload = useCallback(
    async (file: File) => {
      // Validate file type
      const fileExtension = "." + file.name.split(".").pop()?.toLowerCase();
      if (!FILE_VALIDATION.ACCEPTED_TYPES.includes(fileExtension as any)) {
        message.error(ERROR_MESSAGES.INVALID_FILE_TYPE);
        return;
      }

      // Validate file size
      if (file.size > FILE_VALIDATION.MAX_SIZE_BYTES) {
        message.error(ERROR_MESSAGES.FILE_TOO_LARGE);
        return;
      }

      setIsUploading(true);
      setUploadProgress({percent: 0, status: "uploading"});

      try {
        const response = await FileEndpoint.uploadExcelFile(
          {
            file,
            ma_doi_tac_ql: profile.nsd?.ma_doi_tac || "",
            actionCode: ACTION_CODE.UPLOAD_EXCEL_FILE,
          },
          {
            onProgress: event => {
              setUploadProgress({
                percent: Math.min(event.percent, 90),
                status: "uploading",
              });
            },
            onSuccess: () => {
              setUploadProgress({percent: 90, status: "processing"});
            },
            onError: error => {
              console.error("Upload error:", error);
              console.error("Error response:", (error as any).response?.data);
              console.error("Error status:", (error as any).response?.status);
              console.error("Error headers:", (error as any).response?.headers);
              message.error(ERROR_MESSAGES.UPLOAD_FAILED);
              setIsUploading(false);
            },
          },
        );

        setIsProcessing(true);
        setUploadProgress({percent: 95, status: "processing"});

        if (response?.data) {
          let dataArray: any = null;
          if (Array.isArray(response.data)) {
            dataArray = response.data;
          } else {
            const responseObj = response.data as any;

            // Try common nested structures
            if (responseObj.data && responseObj.data.rows && Array.isArray(responseObj.data.rows)) {
              dataArray = responseObj.data.rows;

              if (responseObj.data.header && Array.isArray(responseObj.data.header)) {
                const dynamicCols = createDynamicColumns(responseObj.data.header, {}, handleCellChange);
                setDynamicColumns(dynamicCols);
                dataArray = dataArray.map((row: any) => ({...row}));
              }
            } else if (responseObj.header && Array.isArray(responseObj.header) && responseObj.rows && Array.isArray(responseObj.rows)) {
              dataArray = responseObj.rows;
              const dynamicCols = createDynamicColumns(responseObj.header, {}, handleCellChange);
              setDynamicColumns(dynamicCols);
              dataArray = dataArray.map((row: any) => ({...row}));
            } else if (responseObj.data && Array.isArray(responseObj.data)) {
              dataArray = responseObj.data;
            } else if (responseObj.output && Array.isArray(responseObj.output)) {
              dataArray = responseObj.output;
            } else if (responseObj.result && Array.isArray(responseObj.result)) {
              dataArray = responseObj.result;
            } else if (responseObj.items && Array.isArray(responseObj.items)) {
              dataArray = responseObj.items;
            } else {
              throw new Error("Invalid response data structure - no array found");
            }
          }

          if (!dataArray || !Array.isArray(dataArray)) {
            throw new Error("No valid data array found in response");
          }

          const processedData = addKeysToData(dataArray);
          const minRows = 15;
          const filledData = [...processedData];

          while (filledData.length < minRows) {
            const emptyRow = createEmptyRow(filledData.length);
            filledData.push(emptyRow);
          }

          setTableData(filledData);
          setValidationSummary({isValid: true, totalErrors: 0, errors: []});
          setUploadProgress({percent: 100, status: "done"});
          message.success(`Upload thành công! Đã tải ${processedData.length} dòng dữ liệu, tổng cộng ${filledData.length} dòng trong bảng.`);
          setIsOpen(true);
        } else {
          throw new Error("Invalid response data");
        }
      } catch (error) {
        message.error(ERROR_MESSAGES.PROCESSING_ERROR);
        setUploadProgress({percent: 0, status: "error"});
      } finally {
        setIsUploading(false);
        setIsProcessing(false);
      }
    },
    [profile.nsd?.ma_doi_tac],
  );

  // Ref methods
  useImperativeHandle(ref, () => ({
    openFilePicker: () => {
      fileInputRef.current?.click();
    },
    close: () => {
      setIsOpen(false);
      resetState();
    },
  }));

  // Reset state when modal opens/closes
  const resetState = useCallback(() => {
    setTableData([]);
    setDynamicColumns([]);
    setUploadProgress({percent: 0, status: "uploading"});
    setIsUploading(false);
    setIsProcessing(false);
    setValidationSummary({isValid: true, totalErrors: 0, errors: []});
  }, []);

  // Handle file input change
  const handleFileChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleFileUpload(file);
    }
    // Reset input value to allow selecting the same file again
    event.target.value = "";
  }, []);

  // Handle cell change in table
  const handleCellChange = useCallback((index: number, dataIndex: string, value: string) => {
    setTableData(prevData => {
      const newData = [...prevData];
      newData[index] = {
        ...newData[index],
        [dataIndex]: value,
      };

      // Re-validate data after change (only validate non-empty rows)
      const nonEmptyRows = newData.filter(row => {
        // Check if row has any non-empty values (excluding key)
        return Object.entries(row).some(([key, val]) => key !== "key" && val && val.toString().trim() !== "");
      });
      const validation = validateAllData(nonEmptyRows);
      setValidationSummary(validation);

      return newData;
    });
  }, []);

  const handleConfirm = useCallback(() => {
    const nonEmptyRows = tableData.filter(row => {
      return Object.entries(row).some(([key, val]) => key !== "key" && val && val.toString().trim() !== "");
    });

    onDataConfirm?.(nonEmptyRows);
    setIsOpen(false);
    resetState();
  }, [tableData, onDataConfirm, resetState]);

  // Handle cancel
  const handleCancel = useCallback(() => {
    onCancel?.();
    setIsOpen(false);
    resetState();
  }, [onCancel, resetState]);

  const handleReload = useCallback(() => {
    setIsOpen(false);
    resetState();
    fileInputRef.current?.click();
  }, [resetState]);

  const columns = dynamicColumns.length > 0 ? dynamicColumns : createExcelImportColumns(handleCellChange);

  // Render table
  const renderTable = () => (
    <div style={{height: "calc(90vh - 200px)", overflow: "auto"}}>
      {/* {renderValidationSummary()} */}
      <Table<IExcelRowData>
        dataSource={tableData}
        columns={columns}
        pagination={false}
        scroll={{x: "max-content", y: "calc(78vh - 200px)"}}
        size="small"
        bordered
        sticky
        className="no-header-border-radius"
      />
    </div>
  );

  // Render footer
  const renderFooter = () => {
    // Count non-empty rows for display
    const nonEmptyRowsCount = tableData.filter(row => {
      return Object.entries(row).some(([key, val]) => key !== "key" && val && val.toString().trim() !== "");
    }).length;

    return (
      <Flex justify="space-between" align="center">
        <Button icon={<ReloadOutlined />} onClick={handleReload} disabled={tableData.length === 0}>
          Tải lại
        </Button>
        <Flex gap="small">
          <Button onClick={handleCancel}>Hủy</Button>
          <Button type="primary" icon={<CheckOutlined />} onClick={handleConfirm} disabled={nonEmptyRowsCount === 0 || !validationSummary.isValid}>
            Xác nhận ({nonEmptyRowsCount} dòng có dữ liệu)
          </Button>
        </Flex>
      </Flex>
    );
  };

  return (
    <>
      {/* Hidden file input */}
      <input ref={fileInputRef} type="file" accept={FILE_VALIDATION.ACCEPTED_TYPES.join(",")} onChange={handleFileChange} style={{display: "none"}} />

      {/* Progress overlay */}
      {(isUploading || isProcessing) && (
        <div
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: "rgba(0, 0, 0, 0.5)",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            zIndex: 9999,
          }}>
          <div
            style={{
              backgroundColor: "white",
              padding: "40px",
              borderRadius: "8px",
              minWidth: "400px",
              textAlign: "center",
            }}>
            <FileExcelOutlined style={{fontSize: "48px", color: "#52c41a", marginBottom: "16px"}} />
            <Text strong style={{fontSize: "16px", display: "block", marginBottom: "16px"}}>
              {isUploading ? "Đang tải file lên server..." : "Đang xử lý dữ liệu..."}
            </Text>
            <Progress
              percent={uploadProgress.percent}
              status={uploadProgress.status === "error" ? "exception" : "active"}
              format={percent => `${percent}% ${uploadProgress.status === "uploading" ? "Tải lên" : uploadProgress.status === "processing" ? "Xử lý" : "Hoàn thành"}`}
            />
          </div>
        </div>
      )}

      {/* Modal only shows after successful upload */}
      {isOpen && tableData.length > 0 && (
        <Modal
          centered
          className="modal-import-excel"
          title={<HeaderModal title="Import dữ liệu từ Excel" />}
          open={isOpen}
          onCancel={handleCancel}
          width={MODAL_CONFIG.WIDTH}
          style={{
            maxWidth: "95vw",
          }}
          styles={{
            body: {
              height: MODAL_CONFIG.HEIGHT,
              overflow: "hidden",
            },
          }}
          footer={renderFooter()}
          maskClosable={false}
          destroyOnClose>
          {renderTable()}
        </Modal>
      )}
    </>
  );
});

ModalImportExcelComponent.displayName = "ModalImportExcelComponent";
export default memo(ModalImportExcelComponent, isEqual);

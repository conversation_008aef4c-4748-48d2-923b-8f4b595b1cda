import {ReactQuery} from "@src/@types";
import {ACTION_CODE} from "@src/constants";
import {CommonExecuteResponse, useCommonExecute} from "@src/services/react-queries";
import {message} from "antd";
import React, {PropsWithChildren, useCallback, useEffect, useMemo, useState} from "react";
import {QuanLyPhongBanContext} from "./index.context";
import {QuanLyPhongBanContextProps} from "./index.model";

/* 
  file Provider : Chứa logic quản lý state, gọi API, wrap context
*/
const QuanLyPhongBanProvider: React.FC<PropsWithChildren> = props => {
  const {children} = props;
  const mutateUseCommonExecute = useCommonExecute();
  const [danhSachPhongBan, setDanhSachPhongBan] = useState<Array<CommonExecute.Execute.IDanhSachPhongBanPhanTrang>>([]);
  const [tongSoDong, setTongSoDong] = useState<number>(0);
  const defaultFormValue = {
    ma_doi_tac: "",
    ma_chi_nhanh: "",
    ma: "",
    trang_thai: "",
    ten: "",
    // trang: 1,
    // so_dong: 10,
  };

  useEffect(() => {
    // layDanhSachPhongBan()
    initData();
  }, []);

  const initData = () => {
    layDanhSachPhongBanPhanTrang(defaultFormValue as ReactQuery.ILayDanhSachPhongBanPhanTrangParams);
  };

  //Lấy danh sách toàn bộ phòng ban -Ds này dùng việc khác
  const layDanhSachPhongBan = useCallback(async () => {
    try {
      const params = {
        actionCode: ACTION_CODE.LIET_KE_DANH_SACH_PHONG_BAN,
      };
      const response = await mutateUseCommonExecute.mutateAsync(params);
      const data = response.data.map(e => ({
        label: e.ten || "",
        value: e.ma || "",
        ...e,
      }));
      setDanhSachPhongBan(data);
    } catch (error: any) {
      console.log("layDanhSachPhongBan error ", error.message | error);
    }
  }, [mutateUseCommonExecute]);

  //DS phòng ban phân trang - Danh sách này để hiển thị table
  const layDanhSachPhongBanPhanTrang = useCallback(
    async (body: ReactQuery.ILayDanhSachPhongBanPhanTrangParams & ReactQuery.IPhanTrang) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.DANH_SACH_PHONG_BAN_PHAN_TRANG,
        };
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        const data = response.data.data.map(e => ({
          label: e.ten || "",
          value: e.ma || "",
          ...e,
        }));
        setDanhSachPhongBan(data);
        setTongSoDong(response.data.tong_so_dong);
      } catch (error: any) {
        console.log("layDanhSachPhongBan error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );

  //Lấy chi tiết 1 phòng ban
  const layChiTietPhongBan = useCallback(
    async (item: ReactQuery.IChiTietPhongBanParams): Promise<CommonExecute.Execute.IPhongBan | null> => {
      try {
        const params = {
          ma_doi_tac: item.ma_doi_tac,
          ma_chi_nhanh: item.ma_chi_nhanh,
          ma: item.ma,
          actionCode: ACTION_CODE.CHI_TIET_PHONG_BAN,
        };
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        return responseData.data as CommonExecute.Execute.IPhongBan;
      } catch (error: any) {
        console.log("layChiTietPhongBan error ", error.message | error);
      }
      return null;
    },
    [mutateUseCommonExecute],
  );

  //Cập nhật hoặc tạo mới 1 phòng ban
  const onUpdatePhongBan = useCallback(
    async (body: ReactQuery.IUpdatePhongBanParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.LUU_CAP_NHAT_PHONG_BAN,
        };
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        if (responseData && (responseData.data as unknown as number) === -1) {
          message.success("Cập nhật thông tin thành công!");
          initData();
          return true;
        } else return false;
      } catch (error: any) {
        console.log("onUpdatePhongBan error ", error.message | error);
        return false;
      }
    },
    [mutateUseCommonExecute],
  );

  //khai báo giá trị của context để truyền vào Component
  const value = useMemo<QuanLyPhongBanContextProps>(
    () => ({
      danhSachPhongBan: danhSachPhongBan,
      loading: mutateUseCommonExecute.isLoading,
      onUpdate: onUpdatePhongBan,
      tongSoDong,
      layDanhSachPhongBanPhanTrang,
      layChiTietPhongBan,
      defaultFormValue,
    }),
    [danhSachPhongBan, mutateUseCommonExecute, onUpdatePhongBan, tongSoDong, layDanhSachPhongBanPhanTrang, layChiTietPhongBan],
  );

  return <QuanLyPhongBanContext.Provider value={value}>{children}</QuanLyPhongBanContext.Provider>;
};

export default QuanLyPhongBanProvider;

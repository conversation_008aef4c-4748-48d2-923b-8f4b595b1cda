import {CommonExecuteResponse, useCommonExecute} from "@src/services/react-queries";
import React, {PropsWithChildren, useEffect, useMemo, useCallback} from "react";
import {useState} from "react";
import {ChuongTrinhBaoHiemContextProps} from "./index.model";
import {ChuongTrinhBaoHiemContext} from "./index.context";
import {ACTION_CODE} from "@src/constants";
import {ReactQuery} from "@src/@types";

import {message} from "antd";

const ChuongTrinhBaoHiemProvider: React.FC<PropsWithChildren> = props => {
  const {children} = props;
  console.log("Danh mục chương trình bảo hiểm PROVIDER", children);
  const mutateUseCommonExecute = useCommonExecute();
  const [listDoiTac, setListDoiTac] = useState<Array<CommonExecute.Execute.IDoiTac>>([]);
  const [tongSoDong, setTongSoDong] = useState<number>(0);
  // const [tongSoDongGoiBH, setTongSoDongGoiBH] = useState<number>(0);
  const [danhSachChuongTrinhBaoHiemPhanTrang, setDanhSachChuongTrinhBaoHiem] = useState<Array<CommonExecute.Execute.IChuongTrinhBaoHiem>>([]);
  const [danhSachGoiBaoHiemPhanTrang, setDanhSachGoiBaoHiem] = useState<Array<CommonExecute.Execute.IGoiBaoHiemConNguoi>>([]);
  const [listGoiBaoHiem, setListGoiBaoHiem] = useState<Array<CommonExecute.Execute.IGoiBaoHiem>>([]);
  const [listSanPham, setListSanPham] = useState<Array<CommonExecute.Execute.IDanhMucSanPham>>([]);
  const [listNghiepVu, setListNghiepVu] = useState<Array<CommonExecute.Execute.IDanhMucNghiepVu>>([]);
  const [filterParams, setFilterParams] = useState<ReactQuery.ITimKiemPhanTrangChuongTrinhBaoHiemParams & ReactQuery.IPhanTrang>({
    ma_doi_tac_ql: "",
    ma: "",
    ten: "",
    nv: "",
    trang_thai: "",
  });
  useEffect(() => {
    initData();
  }, []);
  const initData = () => {
    layDanhSachChuongTrinhBaoHiemPhanTrang(filterParams);
    getListDoiTac();
    // getListGoiBaoHiem();
    getListNghiepVu();
  };
  useEffect(() => {
    layDanhSachChuongTrinhBaoHiemPhanTrang(filterParams);
  }, [filterParams]);
  // Hàm chung để gọi API danh sách gói bảo hiểm
  const getListGoiBaoHiemTheoCTBH = useCallback(
    async (params: ReactQuery.ILietKeGoiBaoHiemParams) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...params,
          actionCode: ACTION_CODE.LIET_KE_GOI_BAO_HIEM,
        });

        const data = response.data;
        setListGoiBaoHiem(data);
        return response?.data || {data: []};
      } catch (error) {
        console.error("fetchGoiBaoHiem error:", error);
        return {data: []};
      }
    },
    [mutateUseCommonExecute],
  );
  /* ĐỐI TÁC */
  const getListDoiTac = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        actionCode: ACTION_CODE.LIET_KE_DANH_SACH_DOI_TAC,
      });
      setListDoiTac(response?.data?.map(item => ({...item, ten: item.ma + " - " + item.ten})));
    } catch (error) {
      console.log("getListDoiTac error ", error);
    }
  }, [mutateUseCommonExecute]);
  const getListNghiepVu = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        actionCode: ACTION_CODE.LIET_KE_DANH_SACH_NGHIEP_VU,
      });
      setListNghiepVu(response?.data as Array<CommonExecute.Execute.IDanhMucNghiepVu>);
    } catch (error) {
      console.log("getListNghiepVu error ", error);
    }
  }, [mutateUseCommonExecute]);

  const layDanhSachGoiBaoHiemPhanTrang = useCallback(
    async (body: ReactQuery.ITimKiemPhanTrangGoiBaoHiemParams & ReactQuery.IPhanTrang) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_GOI_BAO_HIEM,
        };
        console.log("danh sách gói bảo hiểm phân trang", params);
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);

        const data = response.data.data;

        setDanhSachGoiBaoHiem(data);
        // setTongSoDongGoiBH(response.data.tong_so_dong);
      } catch (error: any) {
        console.log("Lấy danh sách gói bảo hiểm error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );
  const layDanhSachChuongTrinhBaoHiemPhanTrang = useCallback(
    async (body: ReactQuery.ITimKiemPhanTrangChuongTrinhBaoHiemParams & ReactQuery.IPhanTrang) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_CHUONG_TRINH_BAO_HIEM,
        };

        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);

        const data = response.data.data;

        setDanhSachChuongTrinhBaoHiem(data);
        setTongSoDong(response.data.tong_so_dong);
      } catch (error: any) {
        console.log("Lấy danh sách chương trình bảo hiểm error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );
  //Lấy chi tiết 1 chương trình bảo hiểm
  const layChiTietChuongTrinhBaoHiem = useCallback(
    async (item: ReactQuery.IChiTietChuongTrinhBaoHiemParams): Promise<CommonExecute.Execute.IChuongTrinhBaoHiem | null> => {
      try {
        const params = {
          ...item,
          actionCode: ACTION_CODE.CHI_TIET_CHUONG_TRINH_BAO_HIEM,
        };

        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        return responseData.data as CommonExecute.Execute.IChuongTrinhBaoHiem;
      } catch (error: any) {
        console.log("layChiTietChuongTrinhBaoHiem error ", error.message || error);
        return null;
      }
    },
    [mutateUseCommonExecute],
  );
  const onUpdateChuongTrinhBaoHiem = useCallback(
    async (body: ReactQuery.ICapNhatChuongTrinhBaoHiemParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.CAP_NHAT_CHUONG_TRINH_BAO_HIEM,
          ngay_ad: body.ngay_ad ? Number(body.ngay_ad) : undefined,
        };
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        if (responseData && (responseData.data as unknown as number) === -1) {
          message.success("Cập nhật thông tin thành công!");
          initData();
          return responseData.data as unknown as number | undefined;
        }
      } catch (error: any) {
        console.log("onUpdateChuongTrinhBaoHiem error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );
  const onUpdateGoiTrongCTBH = useCallback(
    async (body: ReactQuery.ICapNhatGoiCTBHParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.CAP_NHAT_GOI_CTBH,
        };
        console.log("param update gói bh", params);
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        if (responseData && (responseData.data as unknown as number) === -1) {
          message.success("Cập nhật thông tin thành công!");
          initData();
          return responseData.data as unknown as number;
        }
      } catch (error: any) {
        console.log("onUpdateChuongTrinhBaoHiem error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );
  //Gọi API xóa gói bảo hiểm ra khỏi chương trnhf bảo hiểm
  const onXoaGoiBaoHiemKhoiCTBH = useCallback(
    async (body: ReactQuery.IXoaGoiBaoHiemKhoiCTBHParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.XOA_GOI_BAO_HIEM,
        };

        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);

        if (responseData && (responseData.data as unknown as number) === -1) {
          message.success("Cập nhật thông tin thành công!");
          // initData();
          return responseData.data as unknown as number;
        }
      } catch (error: any) {
        console.log("onXoaGoiBaoHiemKhoiCTBH error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );
  //Lấy sản phẩm theo đối tác và nghiệp vụ
  const getListSanPham = useCallback(
    async (params: ReactQuery.ILietKeSanPhamParams) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...params,
          trang: 1,
          so_dong: 1000,
          actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_DS_SAN_PHAM,
        });
        console.log("Danh sách sản phẩm ", response);
        return response?.data || {data: [], tong_so_dong: 0};
      } catch (error) {
        console.log("getListsanpham error ", error);
        return {data: [], tong_so_dong: 0};
      }
    },
    [mutateUseCommonExecute],
  );

  const getListSanPhamTheoDoiTacNV = useCallback(
    async (params: ReactQuery.ILietKeSanPhamParams) => {
      try {
        const response = await getListSanPham(params);
        const updatedData = [...response.data]; // Sao chép mảng để tránh mutate trực tiếp
        setListSanPham(updatedData); // Cập nhật state nếu cần
        return {data: updatedData, tong_so_dong: response.tong_so_dong};
      } catch (error) {
        console.log("error", error);
        return {data: [], tong_so_dong: 0}; // Trả về giá trị mặc định khi lỗi
      }
    },
    [getListSanPham],
  );
  //khai báo giá trị của context để truyền vào Component
  const value = useMemo<ChuongTrinhBaoHiemContextProps>(
    () => ({
      loading: mutateUseCommonExecute.isLoading,
      tongSoDong,
      listDoiTac,
      danhSachChuongTrinhBaoHiemPhanTrang,
      listGoiBaoHiem,
      danhSachGoiBaoHiemPhanTrang,
      listSanPham,
      filterParams,
      listNghiepVu,
      getListNghiepVu,
      setFilterParams,
      getListSanPhamTheoDoiTacNV,
      layDanhSachChuongTrinhBaoHiemPhanTrang,
      layChiTietChuongTrinhBaoHiem,
      onUpdateChuongTrinhBaoHiem,
      getListDoiTac,
      getListGoiBaoHiemTheoCTBH,
      onXoaGoiBaoHiemKhoiCTBH,
      layDanhSachGoiBaoHiemPhanTrang,
      onUpdateGoiTrongCTBH,
    }),
    [
      listSanPham,
      danhSachChuongTrinhBaoHiemPhanTrang,
      tongSoDong,
      mutateUseCommonExecute,
      listDoiTac,
      listGoiBaoHiem,
      danhSachGoiBaoHiemPhanTrang,
      filterParams,
      listNghiepVu,
      getListNghiepVu,
      setFilterParams,
      getListSanPhamTheoDoiTacNV,
      layDanhSachChuongTrinhBaoHiemPhanTrang,
      layChiTietChuongTrinhBaoHiem,
      onUpdateChuongTrinhBaoHiem,
      getListDoiTac,
      getListGoiBaoHiemTheoCTBH,
      onXoaGoiBaoHiemKhoiCTBH,
      layDanhSachGoiBaoHiemPhanTrang,
      onUpdateGoiTrongCTBH,
    ],
  );

  return <ChuongTrinhBaoHiemContext.Provider value={value}>{children}</ChuongTrinhBaoHiemContext.Provider>;
};
export default ChuongTrinhBaoHiemProvider;

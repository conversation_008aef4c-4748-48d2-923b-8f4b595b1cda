import { memo, useCallback } from "react";
import { List as AntList, ListProps } from "antd";
import { twMerge } from "tailwind-merge";
import "./index.default.scss";

import { Empty } from "..";
import { isEqual } from "lodash";

const ListComponent = <T,>(props: ListProps<T>) => {
  const { className = "", renderItem, ...etc } = props;

  const render = useCallback(
    (item: T, index: number) => {
      return <AntList.Item>{renderItem?.(item, index)}</AntList.Item>;
    },
    [renderItem],
  );

  return (
    <AntList
      className={twMerge("custom-list", className)}
      renderItem={render}
      locale={{
        emptyText: <Empty />,
      }}
      {...etc}
    />
  );
};

const List = memo(ListComponent, isEqual) as typeof ListComponent;

export default List;

import {LOCAL_STORAGE_KEY} from "@src/constants/localStorage";
import {create} from "zustand";
import {createJSONStorage, persist} from "zustand/middleware";

export interface IMenuNguoiDungStore {
  menuNguoiDung: Array<CommonExecute.Execute.IMenuNguoiDungTheoNhom>;
  setMenuNguoiDung: (menuNguoiDung: Array<CommonExecute.Execute.IMenuNguoiDungTheoNhom>) => void;
}
export const useMenuNguoiDung = create(
  //persist : middileware để lưu trạng thái vào localStorage
  persist<IMenuNguoiDungStore, [], [], Pick<IMenuNguoiDungStore, "menuNguoiDung">>(
    //set, get phải đúng thứ tự này, nếu không sẽ báo lỗi vì sử dụng nhầm cách get/set
    (set, get) => ({
      //khởi tạo state menuNguoiDung từ cookie + localStorage
      menuNguoiDung: get()?.menuNguoiDung || [],
      setMenuNguoiDung: (menuNguoiDung: Array<CommonExecute.Execute.IMenuNguoiDungTheoNhom>) => set(() => ({menuNguoiDung: [...menuNguoiDung]})),
    }),
    //cấu hình persist
    {
      name: LOCAL_STORAGE_KEY.MENU_NGUOI_DUNG, //key để lưu trong localStorate
      storage: createJSONStorage(() => localStorage), //nơi lưu trữ là localStorage
      //chỉ định những phần nào trong state sẽ được lưu, tránh lưu những thứ k cần thiết như các hàm set
      partialize: state => ({
        menuNguoiDung: state.menuNguoiDung,
      }),
    },
  ),
);

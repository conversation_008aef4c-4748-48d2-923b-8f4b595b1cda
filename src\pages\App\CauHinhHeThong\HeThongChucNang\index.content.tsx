import {ClearOutlined, SearchOutlined, SyncOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {Button, FormInput, Highlighter} from "@src/components";
import {COLOR_PALETTE, ID_PAGE} from "@src/constants";
import {Col, Flex, Form, Input, InputRef, Table, TableColumnType, Tag, Tooltip} from "antd";
import type {FilterDropdownProps} from "antd/es/table/interface";
import {isEqual} from "lodash";
import React, {memo, useCallback, useMemo, useRef, useState} from "react";

import ModalChiTietHeThongChucNang, {IModalChiTietHeThongChucNangRef} from "./Component/ModalChiTietHeThongChucNang";
import {
  FormTimKiemPhanTrangHeThongChucNang,
  heThongChucNangColumns,
  optionLoaiChucNangData,
  optionLoaiKieuApDungData,
  optionTrangThaiHeThongChucNangSelect,
  radioItemTrangThaiChucNangTable,
  TableHeThongChucNangDataType,
} from "./index.configs";
import {useHeThongChucNangContext} from "./index.context"; // file này lưu biến về state
import "./index.dark.scss";
import "./index.default.scss";
import {defaultPaginationTableProps, defaultTableProps, fillRowTableEmpty} from "@src/hooks";

type DataIndex = keyof TableHeThongChucNangDataType;
/*
file LoginContent : xử lý phần UI của màn hình
React.Fc : khai báo rằng type LoginContent là 1 React Function
*/

const {ten, loai, kieu_ad, trang_thai} = FormTimKiemPhanTrangHeThongChucNang;
const HeThongChucNangContent: React.FC = () => {
  const {danhSachHeThongChucNang, loading, layDanhSachChucNangPhanTrang, tongSoDong, layChiTietChucNang, defaultFormValue, onSyncChucNang} = useHeThongChucNangContext();
  const [searchText, setSearchText] = useState("");
  const [searchedColumn, setSearchedColumn] = useState("");
  const searchInput = useRef<InputRef>(null);
  const [searchParams, setSearchParams] = useState<ReactQuery.ILayDanhSachChucNangPhanTrangParams>(defaultFormValue);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(defaultPaginationTableProps.defaultPageSize);

  let refModalChiTietHeThongChucNang = useRef<IModalChiTietHeThongChucNangRef>(null);

  const dataTableListChucNang = useMemo<Array<TableHeThongChucNangDataType>>(() => {
    try {
      const tableData = danhSachHeThongChucNang.map((item: any, index: number) => {
        return {
          ...item,
          stt: item.stt ?? index + 1,
          key: index.toString(),
        };
      });
      const arrEmptyRow: Array<TableHeThongChucNangDataType> = fillRowTableEmpty(tableData.length, defaultPaginationTableProps.defaultPageSize);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      console.log("dataTableListPhongBan error", error);
      return [];
    }
  }, [danhSachHeThongChucNang]);

  // search client side
  const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: DataIndex) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);

  const handleReset = useCallback(
    (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: DataIndex) => {
      clearFilters();
      setSearchedColumn("");
      handleSearch([""], confirm, dataIndex);
    },
    [handleSearch],
  );

  //Bấm tìm kiếm //search API
  const onSearchApi = (values: ReactQuery.ILayDanhSachChucNangPhanTrangParams & ReactQuery.IPhanTrang) => {
    // đoạn này để sửa các giá trị undefined => '' trước khi request lên API
    // Ant Design Select không coi "" là một giá trị "chưa chọn", mà là một giá trị đã chọn hợp lệ (dù nó rỗng), nên nó sẽ không hiển thị placeholder nữa.
    const cleanedValues = {
      ...values,
      ten: values.ten ?? "",
      loai: values.loai ?? "",
      kieu_ad: values.kieu_ad ?? "",
      trang_thai: values.trang_thai ?? "",
      trang: values.trang ?? 1,
      so_dong: values.so_dong ?? 13,
    };
    setSearchParams(cleanedValues);
    setPage(1); // reset về trang 1 khi search mới
    layDanhSachChucNangPhanTrang({...cleanedValues, trang: 1, so_dong: pageSize});
  };

  //Gọi api khi chuyển trang
  const onChangePage = useCallback(
    (page: number, pageSize: number) => {
      setPage(page);
      setPageSize(pageSize);
      layDanhSachChucNangPhanTrang({...searchParams, trang: page, so_dong: pageSize});
    },
    [searchParams],
  );
  //Đồng bộ chức năng
  const handleSyncChucNang = useCallback(async () => {
    setPage(1);
    setPageSize(13);
    await onSyncChucNang();
    layDanhSachChucNangPhanTrang({...searchParams, trang: page, so_dong: pageSize});
  }, [onSyncChucNang]);

  // RENDER
  //tạo cột tìm kiếm
  const getColumnSearchProps = (dataIndex: DataIndex, title: string): TableColumnType<TableHeThongChucNangDataType> => ({
    filterDropdown:
      dataIndex !== "trang_thai_ten"
        ? ({setSelectedKeys, selectedKeys, confirm, clearFilters, close}) => (
            <div style={{padding: 8}} onKeyDown={e => e.stopPropagation()} className="flex flex-row items-center p-2">
              <Input
                ref={searchInput}
                placeholder={`Tìm theo ${title}`}
                value={selectedKeys[0]}
                onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                onPressEnter={() => handleSearch(selectedKeys as string[], confirm, dataIndex)}
                style={{display: "block", marginRight: 8}}
              />
              <Tooltip title="Tìm kiếm">
                <Button type="primary" shape="circle" icon={<SearchOutlined />} onClick={() => handleSearch(selectedKeys as string[], confirm, dataIndex)} className="mr-2" />
              </Tooltip>
              <Tooltip title="Xoá">
                <Button type="primary" shape="circle" icon={<ClearOutlined />} onClick={() => clearFilters && handleReset(clearFilters, confirm, dataIndex)} />
              </Tooltip>
            </div>
          )
        : null,
    filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} />,
    onFilter: (value, record) =>
      record[dataIndex]
        ? record[dataIndex]
            .toString()
            .toLowerCase()
            .includes((value as string).toLowerCase())
        : false,
    filterDropdownProps: {
      onOpenChange(open) {
        if (open) {
          setTimeout(() => searchInput.current?.select(), 100);
        }
      },
    },
    filters: dataIndex === "trang_thai_ten" ? radioItemTrangThaiChucNangTable : undefined,
    render: (text, record, index) => {
      if (dataIndex === "trang_thai_ten") {
        // Xác định màu dựa vào text (trạng thái hiển thị)
        const color = text === "Đang sử dụng" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];
        if (!record.key || record.key.toString().includes("empty")) return "";
        return <Tag color={color}>{text}</Tag>;
      }
      return searchedColumn === dataIndex ? (
        <Highlighter searchWords={[searchText]} textToHighlight={text ? text.toString() : ""} />
      ) : text !== undefined ? (
        text
      ) : (
        <Tag color={"transparent"} className="!text-white text-[11px]">
          {"\u00A0"}
        </Tag>
      );
    },
  });

  const renderFormInputColum = (props?: any, span = 4) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  //render header table
  const renderHeaderTableHeThongChucNang = () => {
    return (
      <div>
        <Form initialValues={{trang_thai: optionTrangThaiHeThongChucNangSelect[0].ma}} layout="vertical" className="[&_.ant-form-item]:!mb-0" onFinish={onSearchApi}>
          <div className="flex w-full flex-wrap items-end gap-4">
            {renderFormInputColum(ten)}
            {renderFormInputColum({...loai, options: optionLoaiChucNangData})}
            {renderFormInputColum({...kieu_ad, options: optionLoaiKieuApDungData})}
            {renderFormInputColum({...trang_thai, options: optionTrangThaiHeThongChucNangSelect})}

            <Form.Item>
              <Button type="primary" htmlType="submit" loading={loading} icon={<SearchOutlined />} className="mb-0 w-full">
                Tìm kiếm
              </Button>
            </Form.Item>

            <Form.Item>
              <Flex wrap="wrap" gap="small" className="w-full">
                <Button className="w-full" type="primary" icon={<SyncOutlined />} onClick={handleSyncChucNang} loading={loading}>
                  Đồng bộ chức năng
                </Button>
              </Flex>
            </Form.Item>
          </div>
        </Form>
      </div>
    );
  };

  //render table
  return (
    <div id={ID_PAGE.HE_THONG_CHUC_NANG} className="[&_.ant-space]:w-full">
      <Table<TableHeThongChucNangDataType>
        // sticky
        {...defaultTableProps}
        loading={loading}
        onRow={(record, rowIndex) => {
          return {
            style: {cursor: "pointer"}, //  thể hiện vị trí con trỏ là button(👈)
            onClick: async event => {
              // setOpenResponsive(true)
              // onClickXemChiTietPhongBan(record)
              if (!record.key || record.key.toString().includes("empty")) return;
              const response: CommonExecute.Execute.IChiTietChucNang | null = await layChiTietChucNang(record);
              if (response?.ma) refModalChiTietHeThongChucNang.current?.open(response);
            }, // click row
          };
        }}
        title={renderHeaderTableHeThongChucNang}
        pagination={{
          ...defaultPaginationTableProps,
          total: tongSoDong, //quan trọng - để hiển thị toàn bộ số lượng dòng dữ liệu phục vụ việc phân trang
          onChange: (page, pageSize) => {
            onChangePage(page, pageSize);
          },
        }}
        // pagination={false}
        columns={(heThongChucNangColumns || []).map(item => {
          // Đảm bảo item.key được định nghĩa, item.title là chuỗi và item.key không phải là "stt" trước khi thêm ô tìm kiếm
          return {
            ...item,
            ...(item.key && typeof item.title === "string" && item.key !== "stt" ? getColumnSearchProps(item.key as keyof TableHeThongChucNangDataType, item.title) : {}),
          };
        })} //định nghĩa cột của table
        dataSource={dataTableListChucNang}
        bordered
      />

      <ModalChiTietHeThongChucNang ref={refModalChiTietHeThongChucNang} danhSachHeThongChucNang={danhSachHeThongChucNang} />
    </div>
  );
};

HeThongChucNangContent.displayName = "HeThongChucNangContent"; //Được sử dụng trong các thông báo gỡ lỗi

export default memo(HeThongChucNangContent, isEqual);

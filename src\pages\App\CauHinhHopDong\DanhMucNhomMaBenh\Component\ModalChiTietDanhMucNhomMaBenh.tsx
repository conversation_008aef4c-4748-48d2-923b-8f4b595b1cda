import {ArrowLeftOutlined, CheckOutlined} from "@ant-design/icons";
import {IFormInput, ReactQuery} from "@src/@types";
import {Button, FormInput, HeaderModal, Popcomfirm} from "@src/components";
import {Col, Flex, Form, Modal, Row} from "antd";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useState} from "react";
import {FormTaoMoiDanhMucNhomMaBenh, TRANG_THAI_TAO_MOI_DANH_MUC_NHOM_MA_BENH} from "../index.configs";
import {useQuanLyDanhMucNhomMaBenhContext} from "../index.context";
import {ChiTietDanhMucNhomMaBenhProps, IModalChiTietDanhMucNhomMaBenhRef} from "./Constant";
const {ma, ten, stt, trang_thai} = FormTaoMoiDanhMucNhomMaBenh;

const ModalChiTietDanhMucNhomMaBenhComponent = forwardRef<IModalChiTietDanhMucNhomMaBenhRef, ChiTietDanhMucNhomMaBenhProps>(({listDanhMucNhomMaBenh}: ChiTietDanhMucNhomMaBenhProps, ref) => {
  useImperativeHandle(ref, () => ({
    open: (dataDanhMucNhomMaBenh?: CommonExecute.Execute.IDanhMucNhomMaBenh) => {
      setIsOpen(true);
      if (dataDanhMucNhomMaBenh) setChiTietDanhMucNhomMaBenh(dataDanhMucNhomMaBenh); // nếu có dữ liệu -> set chi tiết DanhMucNhomMaBenh -> là sửa
    },
    close: () => setIsOpen(false),
  }));
  const [chiTietDanhMucNhomMaBenh, setChiTietDanhMucNhomMaBenh] = useState<CommonExecute.Execute.IDanhMucNhomMaBenh | null>(null);
  const [isOpen, setIsOpen] = useState(false);

  const {capNhatChiTietDanhMucNhomMaBenh, getListDanhMucNhomMaBenh, loading} = useQuanLyDanhMucNhomMaBenhContext();
  const [form] = Form.useForm();
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const formValues = Form.useWatch([], form);
  // init form data - load dữ liệu vào form khi sửa
  useEffect(() => {
    if (chiTietDanhMucNhomMaBenh) {
      const arrFormData = [];
      for (const key in chiTietDanhMucNhomMaBenh) {
        arrFormData.push({
          name: key,
          value: chiTietDanhMucNhomMaBenh[key as keyof CommonExecute.Execute.IDanhMucNhomMaBenh],
        });
      }
      form.setFields(arrFormData);
    }
  }, [chiTietDanhMucNhomMaBenh]);

  //xử lý validate form
  useEffect(() => {
    form
      .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
      .then(() => {
        setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
      })
      .catch(() => {
        setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
      });
  }, [form, formValues]);

  const closeModal = useCallback(() => {
    setIsOpen(false);
    setChiTietDanhMucNhomMaBenh(null);
    form.resetFields();
  }, []);

  //Bấm Update
  const onConfirm = async () => {
    try {
      const values: ReactQuery.ICapNhatDanhMucNhomMaBenhParams = form.getFieldsValue(); //lấy ra values của form

      // Xác định đây là EDIT hay CREATE mode
      const isEditMode = !!chiTietDanhMucNhomMaBenh;

      await capNhatChiTietDanhMucNhomMaBenh(values, isEditMode); //cập nhật lại DanhMucNhomMaBenh
      await getListDanhMucNhomMaBenh(); //lấy lại danh sách DanhMucNhomMaBenh
      closeModal();
    } catch (error) {
      console.log("onConfirm", error);
    }
  };

  // RENDER
  //FOOTER
  const renderFooter = () => {
    return (
      <Form.Item>
        <Button type="default" onClick={() => closeModal()} className="mr-2" icon={<ArrowLeftOutlined />}>
          Quay lại
        </Button>
        <Popcomfirm
          title={disableSubmit ? "Vui lòng nhập đầy đủ thông tin" : ""}
          onConfirm={onConfirm}
          okText="Lưu"
          description="Bạn có chắc muốn lưu thông tin?"
          buttonTitle={"Lưu"}
          buttonDisable={disableSubmit}
          buttonClassName="w-40"
          buttonIcon={<CheckOutlined />}
          iconPosition="end"
          loading={loading}
        />
      </Form.Item>
    );
  };
  const renderFormColum = (props: IFormInput, span = 12) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  const renderForm = () => (
    <Form form={form} layout="vertical">
      <Row gutter={16}>
        {renderFormColum({...ma, disabled: chiTietDanhMucNhomMaBenh ? true : false}, 12)}
        {renderFormColum({...ten}, 12)}
      </Row>

      <Row gutter={16}>
        {renderFormColum({...stt}, 12)}
        {renderFormColum({...trang_thai, options: TRANG_THAI_TAO_MOI_DANH_MUC_NHOM_MA_BENH}, 12)}
      </Row>
    </Form>
  );
  //Render
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        title={
          <HeaderModal
            title={chiTietDanhMucNhomMaBenh ? `${chiTietDanhMucNhomMaBenh.ten}` : "Tạo mới danh mục Nhóm mã bệnh"}
            trang_thai_ten={chiTietDanhMucNhomMaBenh?.trang_thai_ten}
            trang_thai={chiTietDanhMucNhomMaBenh?.trang_thai}
          />
        }
        // centered
        maskClosable={false}
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width={600}
        styles={{
          body: {
            paddingTop: "8px",
            paddingBottom: "16px",
          },
        }}
        footer={renderFooter}>
        {renderForm()}
      </Modal>
    </Flex>
  );
});

ModalChiTietDanhMucNhomMaBenhComponent.displayName = "ModalChiTietDanhMucNhomMaBenhComponent";
export const ModalChiTietDanhMucNhomMaBenh = memo(ModalChiTietDanhMucNhomMaBenhComponent, isEqual);

import {ReactQuery} from "@src/@types";
import {TableDanhMucMaBenhICDColumnDataType} from "./index.configs";

export interface IQuanLyDanhMucMaBenhICDContextProps {
  listDanhMucMaBenhICD: Array<CommonExecute.Execute.IDanhMucMaBenhICD>;
  tongSoDong: number;
  loading: boolean;
  filterParams: ReactQuery.ITimKiemPhanTrangDanhMucMaBenhICDParams & ReactQuery.IPhanTrang;
  getListDanhMucMaBenhICD: (params?: ReactQuery.ITimKiemPhanTrangDanhMucMaBenhICDParams & ReactQuery.IPhanTrang) => Promise<void>;
  getChiTietDanhMucMaBenhICD: (params: TableDanhMucMaBenhICDColumnDataType) => Promise<CommonExecute.Execute.IDanhMucMaBenhICD>;
  capNhatChiTietDanhMucMaBenhICD: (params: ReactQuery.ICapNhatDanhMucMaBenhICDParams, isEditMode?: boolean) => Promise<any>;
  setFilterParams: React.Dispatch<React.SetStateAction<ReactQuery.ITimKiemPhanTrangDanhMucMaBenhICDParams & ReactQuery.IPhanTrang>>;
}

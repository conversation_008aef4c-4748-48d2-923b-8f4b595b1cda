import {IFormInput, ReactQuery} from "@src/@types";
import {colWidthByKey, defaultPaginationTableProps, defaultTableColumnsProps} from "@src/hooks";
import {TableProps} from "antd";

/* XÂY DỰNG GÓI BẢO HIỂM */
//ĐỊNH NGHĨA CẤU HÌNH TABLE ĐỐI TÁC GỒM NHỮNG CỘT NÀO, THỨ TỰ CỘT SẼ HIỂN THỊ THEO THỨ TỰ KHAI BÁO Ở DƯỚI
export interface TableGoiBaoHiemColumnDataType {
  key: number;
  sott?: number;
  id?: number;
  ma?: string;
  ten?: string;
  gioi_tinh?: string;
  tuoi_tu?: number;
  tuoi_toi?: number;
  trang_thai_ten?: string;
  ngay_ad?: string;
  ten_doi_tac_ql?: string;
}

export const tableGoiBaoHiemColumn: TableProps<TableGoiBaoHiemColumnDataType>["columns"] = [
  {title: "STT", dataIndex: "sott", key: "sott", width: colWidthByKey.sott, align: "center", ...defaultTableColumnsProps},
  {title: "Mã gói", dataIndex: "ma", key: "ma", width: 150, align: "center", ...defaultTableColumnsProps},
  {title: "Tên gói", dataIndex: "ten", key: "ten", width: 250, align: "center", ...defaultTableColumnsProps},
  {title: "Đối tác", dataIndex: "ten_doi_tac_ql", key: "ten_doi_tac_ql", width: 200, align: "center", ...defaultTableColumnsProps},
  {title: "Ngày áp dụng", dataIndex: "ngay_ad", key: "ngay_ad", width: colWidthByKey.ngay_tao, align: "center", ...defaultTableColumnsProps},
  {title: "Tên sản phẩm", dataIndex: "ten_sp", key: "ten_sp", width: 250, align: "center", ...defaultTableColumnsProps},
  {title: "Giới tính", dataIndex: "gioi_tinh", key: "gioi_tinh", width: 100, align: "center", ...defaultTableColumnsProps},
  {title: "Độ tuổi", dataIndex: "do_tuoi", key: "do_tuoi", width: 100, align: "center", ...defaultTableColumnsProps},
  {title: "Ngày tạo", dataIndex: "ngay_tao", key: "ngay_tao", width: colWidthByKey.ngay_tao, align: "center", ...defaultTableColumnsProps},
  {title: "Người tạo", dataIndex: "nguoi_tao", key: "nguoi_tao", width: colWidthByKey.nguoi_tao, align: "center", ...defaultTableColumnsProps},
  {title: "Ngày cập nhật", dataIndex: "ngay_cap_nhat", key: "ngay_cap_nhat", width: colWidthByKey.ngay_cap_nhat, align: "center", ...defaultTableColumnsProps},
  {title: "Người cập nhật", dataIndex: "nguoi_cap_nhat", key: "nguoi_cap_nhat", width: colWidthByKey.nguoi_cap_nhat, align: "center", ...defaultTableColumnsProps},
  {title: "Trạng thái", dataIndex: "trang_thai_ten", key: "trang_thai_ten", width: 150, align: "center", ...defaultTableColumnsProps},
];

//keyof: return ra key của inteface TableGoiBaoHiemColumnDataIndex;
export type TableGoiBaoHiemColumnDataIndex = keyof TableGoiBaoHiemColumnDataType;

export const radioItemTrangThaiGoiBaoHiemTable = [
  {value: "Đang sử dụng", text: "Đang sử dụng"},
  {value: "Ngưng sử dụng", text: "Ngưng sử dụng"},
];

//FORM TÌM KIẾM GOI BẢO HIỂM
export const defaultParamsTimKiemPhanTrangGoiBaoHiem: ReactQuery.ITimKiemPhanTrangGoiBaoHiemParams = {
  ma_doi_tac_ql: "",
  ma_sp: "",
  ma: "",
  ten: "",
  nd_tim: "",
  ngay_ad: undefined,
  trang_thai: "",
  trang: 1,
  so_dong: defaultPaginationTableProps.defaultPageSize,
};
//radio trong search
export const listGioiTinhSelect: Array<{ma: string; ten: string}> = [
  {ma: "NAM", ten: "Nam"},
  {ma: "NU", ten: "Nữ"},
];

export const listTrangThaiGoiBaoHiemSelect: Array<{ma: string; ten: string}> = [
  {ma: "C", ten: "Chưa sử dụng"},
  {ma: "D", ten: "Đã đang sử dụng"},
];
export interface IFormTimKiemGoiBaoHiemFieldsConfig {
  ma_doi_tac_ql: IFormInput;
  ma_sp: IFormInput;
  ma: IFormInput;
  ten: IFormInput;
  nd_tim: IFormInput;
  ngay_ad: IFormInput; //NGÀY ÁP DỤNG
  trang_thai: IFormInput;
}

export const FormTimKiemGoiBaoHiem: IFormTimKiemGoiBaoHiemFieldsConfig = {
  ma_doi_tac_ql: {component: "select", name: "ma_doi_tac_ql", label: "Đối tác", placeholder: "Chọn đối tác"},
  ma_sp: {component: "input", name: "ma_sp", label: "Mã sản phẩm", placeholder: "Nhập mã sản phẩm"},
  ma: {component: "input", name: "ma", label: "Mã gói", placeholder: "Nhập mã gói"},
  ten: {component: "input", name: "ten", label: "Tên", placeholder: "Nhập tên"},
  nd_tim: {component: "input", name: "nd_tim", placeholder: "Nhập nội dung tìm"},
  ngay_ad: {component: "date-picker", name: "ngay_ad", placeholder: "Chọn ngày áp dụng", allowClear: false},
  trang_thai: {component: "select", name: "trang_thai", placeholder: "Chọn trạng thái"},
};

import {COLOR_PALETTE} from "@src/constants";
import {Tag} from "antd";
import {isEqual} from "lodash";
import {memo} from "react";
import "./index.default.scss";

interface HeaderModalProps {
  title: string;
  trang_thai_ten?: string;
  trang_thai?: string;
}

const HeaderModalComponent = ({trang_thai_ten, trang_thai, title}: HeaderModalProps) => {
  const getStatusColor = () => {
    let color = COLOR_PALETTE.gray[70];
    if (trang_thai === "D") color = COLOR_PALETTE.green[100];
    else if (trang_thai === "K") color = COLOR_PALETTE.red[50];
    return color;
  };
  return (
    <div className="ant-modal-header flex flex-row">
      <div className="ant-modal-title !mr-2">{title}</div>
      {trang_thai_ten && (
        <Tag color={getStatusColor()} className="mb-1.5">
          {trang_thai_ten}
        </Tag>
      )}
    </div>
  );
};

export default memo(HeaderModalComponent, isEqual);

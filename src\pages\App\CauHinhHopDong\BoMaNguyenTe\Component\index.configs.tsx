import {IFormInput} from "@src/@types";
import {TableProps} from "antd";

export const initFormFields = (form: any, chiTietNhomChucNang: any) => {
  if (!chiTietNhomChucNang) return;
  const fields = Object.entries(chiTietNhomChucNang.nhom_cn).map(([name, value]) => ({
    name,
    value: value ?? "",
  }));
  form.setFields(fields);
};

const ruleRequired = {
  required: true,
  message: "Thông tin bắt buộc",
};
export const FormTimKiemPhanTrangKhachHangConfigs = {
  nd_tim: {
    name: "nd_tim",
    placeholder: "Thông tin khách hàng",
    label: "Tìm kiếm thông tin khách hàng", // cho label vào thì sẽ thành input with label
  },
  doi_tac_ql_ten_tat: {
    name: "doi_tac_ql_ten_tat",
    label: "<PERSON><PERSON><PERSON> tác cấp đơn",
    placeholder: "Chọn đối tác",
    rules: [ruleRequired],
  },
  ma_chi_nhanh_ql: {
    name: "ma_chi_nhanh_ql",
    label: "Chi nhánh cấp đơn",
    placeholder: "Chọn chi nhánh",
    rules: [ruleRequired],
  },
  loai_kh: {
    name: "loai_kh",
    label: "Loại khách hàng",
    placeholder: "Chọn loại khách hàng",
  },
};

export const radioItemTrangThaiChucNangTable = [
  {ma: "Đang sử dụng", ten: "Đang sử dụng"},
  {ma: "Ngưng sử dụng", ten: "Ngưng sử dụng"},
];
export const dataOptionLoaiKhachHang = [
  {ma: "C", ten: "Cá nhân"},
  {ma: "T", ten: "Tổ chức"},
];

export interface TableKhachHangDataType {
  stt?: number;
  ma?: string;
  ten?: string;
  ten_nhom?: string;
  sl_chuc_nang?: number;
  ngay_tao?: string;
  nguoi_tao?: string;
  ngay_cap_nhat?: string;
  nguoi_cap_nhat?: string;
  trang_thai_ten?: string;
  doi_tac_ql_ten_tat?: string;
}
const onHeaderCell = () => ({
  className: "header-cell-custom",
});

export const dataKhachHangColumns: TableProps<TableKhachHangDataType>["columns"] = [
  {title: "STT", dataIndex: "sott", key: "sott", width: 80, align: "center", onHeaderCell},
  {title: "Khách hàng", dataIndex: "ten_loai_kh", key: "ten_loai_kh", width: 200, align: "center", onHeaderCell},
  {title: "Mã khách hàng", dataIndex: "ma", key: "ma", width: 200, align: "center", onHeaderCell},
  {title: "Tên", dataIndex: "ten", key: "ten", width: 300, align: "left", onHeaderCell},
  {title: "Địa chỉ", dataIndex: "dchi", key: "dchi", width: 400, align: "center", onHeaderCell},
  {title: "Điện thoại", dataIndex: "dthoai", key: "dthoai", width: 120, align: "center", onHeaderCell},
  {title: "Email", dataIndex: "email", key: "email", width: 200, align: "center", onHeaderCell},
  {title: "CMT/CCCD", dataIndex: "cmt", key: "cmt", width: 150, align: "center", onHeaderCell},
  {title: "Mã số thuế", dataIndex: "mst", key: "mst", width: 150, align: "center", onHeaderCell},
  {title: "Đối tác", dataIndex: "doi_tac_ql_ten_tat", key: "doi_tac_ql_ten_tat", width: 200, align: "center", onHeaderCell},
  {title: "Đơn vị", dataIndex: "ma_chi_nhanh_ql", key: "ma_chi_nhanh_ql", width: 120, align: "center", onHeaderCell},
];
export interface IFormChiTietBoMaNguyenTeFieldsConfig {
  ma: IFormInput;
  ma_doi_tac_ql: IFormInput;
  stt: IFormInput;
  ten: IFormInput;
  trang_thai: IFormInput;
}

export const FormChiTietBoMaNguyenTe: IFormChiTietBoMaNguyenTeFieldsConfig = {
  ma: {
    component: "input",
    label: "Mã nguyên tệ",
    name: "ma",
    placeholder: "Nhập mã nguyên tệ",
    rules: [ruleRequired],
  },
  ten: {
    component: "input",
    label: "Nguyên tệ",
    name: "ten",
    placeholder: "Nhập nguyên tệ",
    rules: [ruleRequired],
  },
  ma_doi_tac_ql: {
    component: "select",
    label: "Đối tác",
    name: "ma_doi_tac_ql",
    placeholder: "Chọn đối tác",
    rules: [ruleRequired],
  },
  stt: {
    component: "input",
    label: "Thứ tự hiển thị",
    name: "stt",
    placeholder: "Nhập thứ tự hiển thị",
    rules: [ruleRequired],
  },
  trang_thai: {
    component: "select",
    label: "Trạng thái",
    name: "trang_thai",
    placeholder: "Chọn trạng thái",
    rules: [ruleRequired],
  },
};

export const TRANG_THAI_TAO_MOI_DOI_TAC = [
  {ten: "Đang sử dụng", ma: "D"},
  {ten: "Ngưng sử dụng", ma: "K"},
];

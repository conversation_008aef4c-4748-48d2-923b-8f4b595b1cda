import {ReactQuery} from "@src/@types";
import {createContext, useContext} from "react";
import {IQuanLyDonViChiNhanhContextProps} from "./index.model";

export const QuanLyDonViChiNhanhContext = createContext<IQuanLyDonViChiNhanhContextProps>({
  listDoiTac: [],
  listDonViChiNhanh: [],
  tongSoDong: 0,
  loading: false,
  filterParams: {},
  searchDonViChiNhanh: () => Promise.resolve(),
  getChiTietDonViChiNhanh: (params: ReactQuery.IChiTietDonViChiNhanhParams) => Promise.resolve({} as CommonExecute.Execute.IChiNhanh),
  capNhatChiTietDonViChiNhanh: (params: ReactQuery.IUpdateDoiTacParams) => Promise.resolve(),
  setFilterParams: () => {},
});

export const useQuanLyDonViChiNhanhContext = () => useContext(QuanLyDonViChiNhanhContext);

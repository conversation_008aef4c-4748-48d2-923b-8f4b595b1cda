import {ReactQuery} from "@src/@types";
import {ACTION_CODE} from "@src/constants";
import {CommonExecuteResponse, useCommonExecute} from "@src/services/react-queries";
import {message} from "antd";
import React, {PropsWithChildren, useCallback, useEffect, useMemo, useState} from "react";
import {DanhSachDonViBHXHContext} from "./index.context";
import {DanhSachDonViBHXHContextProps} from "./index.model";

/* 
  file Provider : Chứa logic quản lý state, gọi API, wrap context
*/

const DanhSachDonViBHXHProvider: React.FC<PropsWithChildren> = props => {
  const {children} = props;
  const mutateUseCommonExecute = useCommonExecute();
  const [danhSachDonViBHXH, setDanhSachDonViBHXH] = useState<Array<CommonExecute.Execute.IDanhSachDonViBHXH>>([]);
  const [tongSoDong, setTongSoDong] = useState<number>(0);
  const [listDoiTac, setListDoiTac] = useState<Array<CommonExecute.Execute.IDoiTac>>([]);
  const [filterParams, setFilterParams] = useState<ReactQuery.ITimKiemPhanTrangDanhSachDonViBHXHParams & ReactQuery.IPhanTrang>({
    ma: "",
    ten: "",

    trang_thai: "",
    trang: 1,
    // so_dong: ,
  });
  const [listNganHang, setListNganHang] = useState<Array<CommonExecute.Execute.IDanhMucNganHang>>([]);
  useEffect(() => {
    initData();
  }, []);

  const initData = () => {
    layDanhSachDonViBHXH(filterParams);
    getListDoiTac();
    getListNganHang();
  };
  useEffect(() => {
    layDanhSachDonViBHXH(filterParams);
  }, [filterParams]);
  /* ĐỐI TÁC */
  const getListDoiTac = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        actionCode: ACTION_CODE.LIET_KE_DANH_SACH_DOI_TAC,
      });
      setListDoiTac(response?.data?.map(item => ({...item, ten: item.ma + " - " + item.ten})));
    } catch (error) {
      console.log("getListDoiTac error ", error);
    }
  }, [mutateUseCommonExecute]);
  //NGÂN HÀNG
  const getListNganHang = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        actionCode: ACTION_CODE.LIET_KE_DANH_MUC_NGAN_HANG,
      });
      setListNganHang(response?.data as Array<CommonExecute.Execute.IDanhMucNganHang>);
    } catch (error) {
      console.log("getListNganHang error ", error);
    }
  }, [mutateUseCommonExecute]);
  //DS đại lý phân trang
  const layDanhSachDonViBHXH = useCallback(
    async (body: ReactQuery.ITimKiemPhanTrangDanhSachDonViBHXHParams & ReactQuery.IPhanTrang) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_DON_VI_BHXH,
        };

        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        const data = response.data.data || [];
        const tongSoDong = response.data.tong_so_dong || 0;

        setDanhSachDonViBHXH(data);
        setTongSoDong(response.data.tong_so_dong);
        return {
          data,
          tong_so_dong: tongSoDong,
        };
      } catch (error: any) {
        console.log("Lấy danh sách đại lý error:", error.message || error);
        return {
          data: [],
          tong_so_dong: 0,
        };
      }
    },
    [mutateUseCommonExecute],
  );
  //Lấy chi tiết 1 đơn vị BHXH
  const layChiTietDonViBHXH = useCallback(
    async (item: ReactQuery.IChiTietDanhSachDonViBHXHParams): Promise<CommonExecute.Execute.IDanhSachDonViBHXH | null> => {
      try {
        const params = {
          ma: item.ma,
          actionCode: ACTION_CODE.CHI_TIET_DON_VI_BHXH,
        };
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        console.log("chi tiết đơn vị BHXH", responseData);
        const data = (responseData.data as any).dl[0] as CommonExecute.Execute.IDanhSachDonViBHXH;
        return data;
      } catch (error: any) {
        console.log("layChiTietDonViBHXH error ", error.message || error);
        return null;
      }
    },
    [mutateUseCommonExecute],
  );

  //Cập nhật hoặc tạo mới 1 đại lý
  const onUpdateDanhSachDonViBHXH = useCallback(
    async (body: ReactQuery.IUpdateDonViBHXHParams): Promise<number | null | undefined> => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.CAP_NHAT_DON_VI_BHXH,
        };
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        if (responseData && (responseData.data as unknown as number) === -1) {
          message.success("Cập nhật thông tin thành công!");
          initData();
          // Chuyển đổi responseData.data thành number
          return responseData.data as unknown as number; // Chuyển đổi responseData.data thành number
        }
      } catch (error: any) {
        console.log("onUpdateDaiLy error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );

  //khai báo giá trị của context để truyền vào Component
  const value = useMemo<DanhSachDonViBHXHContextProps>(
    () => ({
      listDoiTac,
      tongSoDong,
      danhSachDonViBHXH,
      loading: mutateUseCommonExecute.isLoading,
      filterParams,
      listNganHang,
      setFilterParams,
      getListDoiTac,
      onUpdateDanhSachDonViBHXH,
      layDanhSachDonViBHXH,
      layChiTietDonViBHXH,
    }),
    [
      mutateUseCommonExecute,
      filterParams,
      tongSoDong,
      listDoiTac,
      danhSachDonViBHXH,
      listNganHang,
      setFilterParams,
      onUpdateDanhSachDonViBHXH,
      layDanhSachDonViBHXH,
      layChiTietDonViBHXH,
      getListDoiTac,
    ],
  );

  return <DanhSachDonViBHXHContext.Provider value={value}>{children}</DanhSachDonViBHXHContext.Provider>;
};

export default DanhSachDonViBHXHProvider;

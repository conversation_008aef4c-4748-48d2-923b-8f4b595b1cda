import {ReactQuery} from "@src/@types";

/**
 * Interface định nghĩa các props và methods cung cấp bởi CauHinhMauGCNProvider
 */
export interface ICauHinhMauGCNProvider {
  // ===== STATE VALUES =====
  danhSachCauHinhMauGCN: Array<CommonExecute.Execute.ICauHinhMauGCN>;
  listSanPham: Array<CommonExecute.Execute.ISanPham>;
  tongSoDong: number;
  loading: boolean;
  filterParams: ReactQuery.ITimKiemPhanTrangCauHinhMauGCNParams & ReactQuery.IPhanTrang;

  // ===== API FUNCTIONS =====
  searchCauHinhMauGCN: () => Promise<void>;
  getChiTietCauHinhMauGCN: (data: {bt: number}) => Promise<CommonExecute.Execute.ICauHinhMauGCN>;
  capNhatChiTietCauHinhMauGCN: (data: ReactQuery.ICapNhatCauHinhMauGCNParams) => Promise<any>;
  getListSanPham: () => Promise<void>;
  
  // ===== STATE SETTERS =====
  setFilterParams: React.Dispatch<React.SetStateAction<ReactQuery.ITimKiemPhanTrangCauHinhMauGCNParams & ReactQuery.IPhanTrang>>;
}

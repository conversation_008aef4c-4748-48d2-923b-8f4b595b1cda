import {IFormInput} from "@src/@types";
import {TableProps} from "antd";

export const initFormFields = (form: any, chiTietNhomChucNang: any) => {
  if (!chiTietNhomChucNang) return;
  const fields = Object.entries(chiTietNhomChucNang.nhom_cn).map(([name, value]) => ({
    name,
    value: value ?? "",
  }));
  form.setFields(fields);
};

const ruleRequired = {
  required: true,
  message: "Thông tin bắt buộc",
};
export const radioItemTrangThaiChucNangTable = [
  {value: "Đang sử dụng", text: "Đang sử dụng"},
  {value: "Ngừng sử dụng", text: "Ngừng sử dụng"},
];
export const dataOptionLoaiKhachHang = [
  {ma: "C", ten: "Cá nhân"},
  {ma: "T", ten: "Tổ chức"},
];

export interface TableKhachHangDataType {
  stt?: number;
  ma?: string;
  ten?: string;
  ten_nhom?: string;
  sl_chuc_nang?: number;
  ngay_tao?: string;
  nguoi_tao?: string;
  ngay_cap_nhat?: string;
  nguoi_cap_nhat?: string;
  trang_thai_ten?: string;
  doi_tac_ql_ten_tat?: string;
}
const onHeaderCell = () => ({
  className: "header-cell-custom",
});

export const dataKhachHangColumns: TableProps<TableKhachHangDataType>["columns"] = [
  {title: "STT", dataIndex: "sott", key: "sott", width: 80, align: "center", onHeaderCell},
  {title: "Khách hàng", dataIndex: "ten_loai_kh", key: "ten_loai_kh", width: 200, align: "center", onHeaderCell},
  {title: "Mã khách hàng", dataIndex: "ma", key: "ma", width: 200, align: "center", onHeaderCell},
  {title: "Tên", dataIndex: "ten", key: "ten", width: 300, align: "left", onHeaderCell},
  {title: "Địa chỉ", dataIndex: "dchi", key: "dchi", width: 400, align: "center", onHeaderCell},
  {title: "Điện thoại", dataIndex: "dthoai", key: "dthoai", width: 120, align: "center", onHeaderCell},
  {title: "Email", dataIndex: "email", key: "email", width: 200, align: "center", onHeaderCell},
  {title: "CMT/CCCD", dataIndex: "cmt", key: "cmt", width: 150, align: "center", onHeaderCell},
  {title: "Mã số thuế", dataIndex: "mst", key: "mst", width: 150, align: "center", onHeaderCell},
  {title: "Đối tác", dataIndex: "doi_tac_ql_ten_tat", key: "doi_tac_ql_ten_tat", width: 200, align: "center", onHeaderCell},
  {title: "Đơn vị", dataIndex: "ma_chi_nhanh_ql", key: "ma_chi_nhanh_ql", width: 120, align: "center", onHeaderCell},
];

export interface IFormChiTietPhuongThucKhaiThacFieldsConfig {
  ma: IFormInput;
  ma_doi_tac_ql: IFormInput;
  stt: IFormInput;
  ten: IFormInput;
  trang_thai: IFormInput;
}

export const FormChiTietPhuongThucKhaiThac: IFormChiTietPhuongThucKhaiThacFieldsConfig = {
  ma: {
    component: "input",
    label: "Mã phương thức khai thác",
    name: "ma",
    placeholder: "Nhập mã phương thức khai thác",
    rules: [ruleRequired],
  },
  ten: {
    component: "input",
    label: "Tên phương thức khai thác",
    name: "ten",
    placeholder: "Nhập tên phương Thức Khai Thác",
    rules: [ruleRequired],
  },
  ma_doi_tac_ql: {
    component: "select",
    label: "Đối tác",
    name: "ma_doi_tac_ql",
    placeholder: "Nhập đối tác",
    rules: [ruleRequired],
  },
  stt: {
    component: "input",
    label: "Thứ tự hiển thị",
    name: "stt",
    placeholder: "Nhập thứ tự hiển thị",
    rules: [ruleRequired],
  },
  trang_thai: {
    component: "input",
    label: "Trạng thái",
    name: "trang_thai",
    rules: [ruleRequired],
  },
};

export const TRANG_THAI_TAO_MOI_DOI_TAC = [
  {label: "Đang sử dụng", value: "D"},
  {label: "Ngưng sử dụng", value: "K"},
];
interface Props {
  listDoiTac: Array<CommonExecute.Execute.IDoiTac>;
}

//định nghĩa các hàm, biến mà modal sẽ expose để component bên ngoài sử dụng thông qua ref
export interface IModalChiTietPhuongThucKhaiThacRef {
  open: (data?: CommonExecute.Execute.IChiTietPhuongThucKhaiThac) => void;
}

import { useState, useCallback } from 'react';

/**
 * Custom hook để handle async actions với loading state
 * @param asyncFn - Function async cần execute
 * @returns [executeAction, isLoading]
 */

//Hàm này có thể dùng chung để tạo 1 biến isLoading riêng cho từng action
export const useAsyncAction = <T extends any[] = [], R = any>(
  asyncFn: (...args: T) => Promise<R>
) => {
  const [isLoading, setIsLoading] = useState(false);

  const executeAction = useCallback(
    async (...args: T): Promise<R | null> => {
      if (isLoading) {
        return null; // Prevent multiple executions
      }

      setIsLoading(true);
      try {
        const result = await asyncFn(...args);
        return result;
      } catch (error) {
        console.error('Async action error:', error);
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [asyncFn, isLoading]
  );

  return [executeAction, isLoading] as const;
};

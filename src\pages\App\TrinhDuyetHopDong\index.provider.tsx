import {message} from "antd";
import {ReactNode, useCallback, useMemo, useState} from "react";
import {CommonExecuteResponse, useCommonExecute} from "@src/services/react-queries";
import {ITrinhDuyetHopDongContextProps} from "./index.model.tsx";
import {TrinhDuyetHopDongContext} from "./index.context.tsx";
import {ReactQuery} from "@src/@types";
import {ACTION_CODE} from "@src/constants/axios.ts";

interface TrinhDuyetHopDongProviderProps {
  children: ReactNode;
  onTrinhDuyetSuccess?: () => void; // Callback để refresh data sau khi trình duyệt thành công
  onHuyTrinhDuyetSuccess?: () => void; // Callback để refresh data sau khi hủy trình duyệt thành công
}

export const TrinhDuyetHopDongProvider = ({children, onTrinhDuyetSuccess, onHuyTrinhDuyetSuccess}: TrinhDuyetHopDongProviderProps) => {
  // Hooks
  const mutateUseCommonExecute = useCommonExecute();

  // States
  const [danhSachNguoiDuyetHopDong, setDanhSachNguoiDuyetHopDong] = useState<Array<CommonExecute.Execute.INguoiPheDuyetHopDong>>([]);

  // Functions

  /**
   * Lấy danh sách người duyệt hợp đồng
   * @param body - Parameters để tìm kiếm người duyệt
   * @returns Promise<any>
   */
  const getDanhSachNguoiDuyetHopDong = useCallback(
    async (body: ReactQuery.ILietKeDanhSachNguoiDuyetParams): Promise<any> => {
      try {
        const params = {
          so_id: body.so_id || 0,
          nd_tim: body.nd_tim,
          nhom_duyet: "HOP_DONG",
          actionCode: ACTION_CODE.LIET_KE_DS_NGUOI_DUYET_HD,
        };
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        setDanhSachNguoiDuyetHopDong(responseData?.data || []);
        return responseData.data || [];
      } catch (error: any) {
        console.log("getDanhSachNguoiDuyetHopDong error ", error.message || error);
        return null;
      }
    },
    [mutateUseCommonExecute],
  );

  /**
   * Trình phê duyệt hợp đồng - tổng quát hóa để dùng cho nhiều loại hợp đồng
   * @param body - Parameters để trình duyệt
   * @param actionCode - Action code cụ thể cho từng loại hợp đồng
   * @returns Promise<boolean>
   */
  const trinhPheDuyetHopDong = useCallback(
    async (body: ReactQuery.ITrinhPheDuyetHopDongParams, actionCode: string): Promise<boolean> => {
      try {
        const params = {
          ...body,
          actionCode,
        };
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        if ((response.data as unknown as number) === -1) {
          message.success("Trình phê duyệt hợp đồng thành công!");

          // Gọi callback để refresh data nếu có
          if (onTrinhDuyetSuccess) {
            onTrinhDuyetSuccess();
          }

          return true;
        } else {
          return false;
        }
      } catch (error: any) {
        console.log("trinhPheDuyetHopDong error ", error.message || error);
        return false;
      }
    },
    [mutateUseCommonExecute, onTrinhDuyetSuccess],
  );

  /**
   * Hủy trình phê duyệt hợp đồng - tổng quát hóa để dùng cho nhiều loại hợp đồng
   * @param params - Parameters để hủy trình duyệt
   * @returns Promise<boolean>
   */
  const huyTrinhPheDuyetHopDong = useCallback(
    async (params: ReactQuery.IHuyTrinhPheDuyetHopDongParams): Promise<boolean> => {
      try {
        const requestParams = {
          nv: params.nv,
          so_id: params.so_id,
          actionCode: ACTION_CODE.HUY_TRINH_PHE_DUYET_HD_XCG,
        };
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(requestParams);
        if ((response.data as unknown as number) === -1) {
          message.success("Hủy trình phê duyệt hợp đồng thành công!");

          // Gọi callback để refresh data nếu có
          if (onHuyTrinhDuyetSuccess) {
            onHuyTrinhDuyetSuccess();
          }

          return true;
        } else {
          return false;
        }
      } catch (error: any) {
        console.log("huyTrinhPheDuyetHopDong error ", error.message || error);
        return false;
      }
    },
    [mutateUseCommonExecute, onHuyTrinhDuyetSuccess],
  );

  // Context value
  const value = useMemo<ITrinhDuyetHopDongContextProps>(
    () => ({
      loading: mutateUseCommonExecute.isLoading,
      danhSachNguoiDuyetHopDong,
      getDanhSachNguoiDuyetHopDong,
      trinhPheDuyetHopDong,
      huyTrinhPheDuyetHopDong,
      onTrinhDuyetSuccess,
      onHuyTrinhDuyetSuccess,
    }),
    [mutateUseCommonExecute.isLoading, danhSachNguoiDuyetHopDong, getDanhSachNguoiDuyetHopDong, trinhPheDuyetHopDong, huyTrinhPheDuyetHopDong, onTrinhDuyetSuccess, onHuyTrinhDuyetSuccess],
  );

  return <TrinhDuyetHopDongContext.Provider value={value}>{children}</TrinhDuyetHopDongContext.Provider>;
};

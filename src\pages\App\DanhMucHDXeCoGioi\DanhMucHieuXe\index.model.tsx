import {ReactQuery} from "@src/@types";

export interface DanhMucHieuXeProps {
  listHangXe: Array<CommonExecute.Execute.IDoiTac>;
  danhSachDanhMucHieuXe: Array<CommonExecute.Execute.IDanhMucHieuXe>;
  loading: boolean;
  layDanhSachDanhMucHieuXe: (Params: ReactQuery.ITimKiemPhanTrangDanhMucHieuXeParams) => void;
  tongSoDong: number;
  layChiTietDanhMucHieuXe: (Params: ReactQuery.ILayChiTietDanhMucHieuXeParams) => Promise<CommonExecute.Execute.IChiTietDanhMucHieuXe | null>;
  defaultFormValue: object;
  onUpdateDanhMucHieuXe: (Params: ReactQuery.IUpdateDanhMucHieuXeParams) => Promise<number | null | undefined>;
  getListHangXe: () => Promise<void>;
}

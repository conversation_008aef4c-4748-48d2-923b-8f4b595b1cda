import {ArrowLeftOutlined, CheckOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {Button, FormInput, HeaderModal, Popcomfirm} from "@src/components";
import {COLOR_PALETTE} from "@src/constants";
import {Col, Flex, Form, Modal, Row, Select} from "antd";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useState} from "react";
import {useChiTietPhuongThucKTContext} from "../index.context";
import {FormChiTietPhuongThucKhaiThac, IModalChiTietPhuongThucKhaiThacRef, TRANG_THAI_TAO_MOI_DOI_TAC} from "./index.configs";
import {Props} from "react-apexcharts";

const {ma_doi_tac_ql, ma, ten, trang_thai, stt} = FormChiTietPhuongThucKhaiThac;

const ModalChiTietPhuongThucKTComponent = forwardRef<IModalChiTietPhuongThucKhaiThacRef, Props>(({listDoiTac}: Props, ref) => {
  useImperativeHandle(ref, () => ({
    open: (dataDoiTac?: CommonExecute.Execute.IChiTietPhuongThucKhaiThac) => {
      setIsOpen(true);
      form.resetFields();
      if (dataDoiTac) setChiTietPhuongThucKhaiThac(dataDoiTac);
    },
    close: () => setIsOpen(false),
  }));
  const [chiTietPhuongThucKhaiThac, setChiTietPhuongThucKhaiThac] = useState<CommonExecute.Execute.IChiTietPhuongThucKhaiThac | null>(null);
  const [isOpen, setIsOpen] = useState(false);

  const {loading, updatePhuongThucKhaiThac} = useChiTietPhuongThucKTContext();

  const [form] = Form.useForm();
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const formValues = Form.useWatch([], form);
  //chuyển đổi listđối tác thành định dạng option

  const handleChangeDoitac = (ma_doi_tac_ql: string) => {
    console.log("Đã chọn đối tác", ma_doi_tac_ql);
  };
  // init form data// ĐỔ DỮ LIỆU VÀO FORM
  useEffect(() => {
    if (chiTietPhuongThucKhaiThac) {
      console.log("chiTietPhuongThucKhaiThac", chiTietPhuongThucKhaiThac);
      const arrFormData = [];
      for (const key in chiTietPhuongThucKhaiThac) {
        arrFormData.push({
          name: key,
          value: chiTietPhuongThucKhaiThac[key as keyof CommonExecute.Execute.IChiTietPhuongThucKhaiThac],
        });
      }
      form.setFields(arrFormData);
    }
  }, [chiTietPhuongThucKhaiThac]);

  //xử lý validate form
  useEffect(() => {
    form
      .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
      .then(() => {
        setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
      })
      .catch(() => {
        setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
      });
  }, [form, formValues]);

  const closeModal = useCallback(() => {
    setIsOpen(false);
    setChiTietPhuongThucKhaiThac(null);
    form.resetFields();
  }, []);

  //Bấm Update
  const onConfirm = async () => {
    try {
      const values: ReactQuery.IUpdatePhuongThucKhaiThacParams = form.getFieldsValue(); //lấy ra values của form
      //với trường hợp tạo mới -> check mã đối tác đã tồn tại
      console.log("values", values);

      // if (!chiTietPhuongThucKhaiThac) {
      //   for (let i = 0; i < listDoiTac.length; i++) {
      //     if (listDoiTac[i].ma === values.ma) {
      //       form.setFields([
      //         {
      //           name: "ma",
      //           errors: ["Mã đối tác đã tổn tại!"],
      //         },
      //       ]);
      //       return;
      //     }
      //   }
      // }

      await updatePhuongThucKhaiThac(values); //cập nhật lại đối tác
      // await getListDoiTac(); //lấy lại danh sách đối tác
      closeModal();
    } catch (error) {
      console.log("onConfirm", error);
    }
  };

  // RENDER
  //get màu trạng thái sử dụng
  const getStatusColor = (status?: string) => {
    let color = COLOR_PALETTE.gray[70];
    if (status === "D") color = COLOR_PALETTE.green[100];
    else if (status === "K") color = COLOR_PALETTE.red[50];
    return color;
  };

  //FOOTER
  const renderFooter = () => {
    return (
      <Form.Item>
        <Button type="default" onClick={() => closeModal()} className="mr-2" icon={<ArrowLeftOutlined />}>
          Quay lại
        </Button>
        <Popcomfirm
          title="Thông báo"
          onConfirm={onConfirm}
          okText="Lưu"
          description="Bạn có chắc muốn lưu thông tin?"
          buttonTitle={"Lưu"}
          buttonDisable={disableSubmit}
          buttonIcon={<CheckOutlined />}
          iconPosition="end"
          loading={loading}
        />
      </Form.Item>
    );
  };
  const renderFormColum = (props: any, span?: number) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  const renderForm = () => (
    <Form form={form} layout="vertical" initialValues={{trang_thai: TRANG_THAI_TAO_MOI_DOI_TAC[0].value}}>
      {/* MÃ */}
      <Row gutter={16}>
        {/* {renderFormSelect(ma_doi_tac_ql, listDoiTac, handleChangeDoitac, "ma_doi_tac_ql", 8)} */}
        {renderFormColum({...ma_doi_tac_ql, options: listDoiTac}, 16)}
        {/* <Col span={16}>
          <Form.Item {...ma_doi_tac_ql}>
            <Select options={listDoiTac} />
          </Form.Item>
        </Col> */}
        {renderFormColum({...ma, disabled: chiTietPhuongThucKhaiThac ? true : false}, 8)}
      </Row>

      {/* gutter : khoảng cách giữa các ô */}
      <Row gutter={16}>
        {/* 3 cột có width = nhau  */}
        {renderFormColum({...ten}, 8)}
        {renderFormColum({...stt}, 8)}
        <Col span={8}>
          <Form.Item {...trang_thai}>
            <Select options={TRANG_THAI_TAO_MOI_DOI_TAC} />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>{/* 3 cột có width = nhau  */}</Row>
    </Form>
  );
  //Render
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        styles={{
          body: {
            height: "20vh",
          },
        }}
        title={
          <HeaderModal
            title={chiTietPhuongThucKhaiThac ? `Chi tiết phương thức khai thác ${chiTietPhuongThucKhaiThac.ten}` : "Tạo mới phương thức khai thác"}
            trang_thai_ten={chiTietPhuongThucKhaiThac?.trang_thai_ten}
            trang_thai={chiTietPhuongThucKhaiThac?.trang_thai}
          />
        }
        // centered
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width={{
          xs: "50%",
          sm: "50%",
          md: "50%",
          lg: "50%",
          xl: "50%",
          xxl: "50%",
        }}
        footer={renderFooter}>
        {renderForm()}
      </Modal>
    </Flex>
  );
});

ModalChiTietPhuongThucKTComponent.displayName = "ModalChiTietPhuongThucKTComponent";
export const ModalChiTietPhuongThucKT = memo(ModalChiTietPhuongThucKTComponent, isEqual);

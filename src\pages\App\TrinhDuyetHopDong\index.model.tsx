import {ReactQuery} from "@src/@types";

// Interface cho TrinhDuyetHopDong Context - tổng quát hóa để dùng chung cho nhiều loại hợp đồng
export interface ITrinhDuyetHopDongContextProps {
  // Loading state
  loading: boolean;
  // Data states
  danhSachNguoiDuyetHopDong: Array<CommonExecute.Execute.INguoiPheDuyetHopDong>;
  // Functions
  getDanhSachNguoiDuyetHopDong: (params: ReactQuery.ILietKeDanhSachNguoiDuyetParams) => Promise<any>;
  trinhPheDuyetHopDong: (params: ReactQuery.ITrinhPheDuyetHopDongParams, actionCode: string) => Promise<boolean>;
  huyTrinhPheDuyetHopDong: (params: ReactQuery.IHuyTrinhPheDuyetHopDongParams) => Promise<boolean>;
  // Callback functions để refresh data sau khi trình duyệt thành công
  onTrinhDuyetSuccess?: () => void;
  onHuyTrinhDuyetSuccess?: () => void;
}

import {createContext, useContext} from "react";
import {ILoginContextProps} from "./index.model";

//createContext : khai báo 1 context mà component có thể cung cấp hoặc đọc
export const DangNhapContext = createContext<ILoginContextProps>({
  //khởi tạo các giá trị mặc định, trong context này có giá trị là onSubmit
  onSubmit: () => Promise.resolve(),
  isSuccess: false,
  isError: false,
  error: null,
  isLoading: false,
});

//useContext(someContext): là React Hook cho phép bạn đọc và subscribe context từ component của mình.
// file .context muốn truy câp context thì sử dụng useLoginContext : const {onSubmit} = useLoginContext();
export const useLoginContext = () => useContext(DangNhapContext);

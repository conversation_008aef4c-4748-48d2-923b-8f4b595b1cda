import {ReactQuery} from "@src/@types";

//Interface định nghĩa contract cho DanhMucBenhVienProvider
export interface IDanhMucBenhVienProvider {
  // Dữ liệu hiển thị
  listBenhVien: CommonExecute.Execute.IDanhMucBenhVien[]; // Danh sách bệnh viện hiển thị trong bảng
  listTinhThanh: any[]; // Danh sách tỉnh thành cho dropdown
  // listQuanHuyen: any[]; // Danh sách quận huyện cho dropdown
  listNganHang: any[]; // Danh sách ngân hàng cho dropdown
  listChiNhanhNganHang: any[]; // Danh sách chi nhánh ngân hàng cho dropdown
  tongSoDong: number; // Tổng số bản ghi để hiển thị pagination
  loading: boolean; // Trạng thái loading cho UI

  // Tham số filter và phân trang
  filterParams: ReactQuery.ITimKiemPhanTrangDanhMucBenhVienParams; // Tham số filter và phân trang

  // Actions
  getListBenhVien: (params?: ReactQuery.ITimKiemPhanTrangDanhMucBenhVienParams) => Promise<void>; // Tìm kiếm danh sách bệnh viện
  getListTinhThanh: () => Promise<void>; // Load danh sách tỉnh thành
  // getListQuanHuyen: (params?: {ma_tinh?: string}) => Promise<void>; // Load danh sách quận huyện theo tỉnh
  getListNganHang: () => Promise<void>; // Load danh sách ngân hàng
  getListChiNhanhNganHang: (params?: {ma_ngan_hang?: string}) => Promise<void>; // Load danh sách chi nhánh ngân hàng theo mã ngân hàng
  getChiTietBenhVien: (data: {ma: string;}) => Promise<CommonExecute.Execute.IDanhMucBenhVien>; // Lấy chi tiết bệnh viện
  capNhatChiTietBenhVien: (data: ReactQuery.ICapNhatDanhMucBenhVienParams, isUpdate?: boolean) => Promise<any>; // Tạo mới hoặc cập nhật bệnh viện
  setFilterParams: React.Dispatch<React.SetStateAction<ReactQuery.ITimKiemPhanTrangDanhMucBenhVienParams>>; // Cập nhật tham số filter
}

import React, {PropsWithChildren, useCallback, useEffect, useMemo, useState} from "react";

import {QuanLyDanhMucBangMaBenhContext} from "./index.context";
import {IQuanLyDanhMucBangMaBenhContextProps} from "./index.model";
import {ACTION_CODE} from "@src/constants";
import {ReactQuery} from "@src/@types";
import {TableDanhMucBangMaBenhColumnDataType} from "./index.configs";
import {useCommonExecute} from "@src/services/react-queries";
import {message} from "antd";

const QuanLyDanhMucBangMaBenhProvider: React.FC<PropsWithChildren> = props => {
  const {children} = props;
  const mutateUseCommonExecute = useCommonExecute();

  const [listDanhMucBangMaBenh, setListDanhMucBangMaBenh] = useState<Array<CommonExecute.Execute.IDanhMucBangMaBenh>>([]);
  const [tongSoDong, setTongSoDong] = useState<number>(0);
  const [filterParams, setFilterParams] = useState<ReactQuery.ITimKiemPhanTrangDanhMucBangMaBenhParams & ReactQuery.IPhanTrang>({ma: "", ten: "", trang_thai: "", trang: 1, so_dong: 20});
  
  //khởi tạo dữ liệu ban đàu
  useEffect(() => {//nè
    initData();
  }, []);

  const getChiTietDanhMucBangMaBenh = useCallback(
    async (data: TableDanhMucBangMaBenhColumnDataType) => {
      try {
        console.log("[Provider] getChiTietDanhMucBangMaBenh được gọi với data:", data);
        
        if (!data.ma) {
          console.log("[Provider] Không có mã để tìm kiếm, data.ma =", data.ma);
          return {} as CommonExecute.Execute.IDanhMucBangMaBenh;
        }
      
        console.log(" [Provider] Gọi API với params:", {
          ma: data.ma,
          actionCode: ACTION_CODE.CHI_TIET_DANH_MUC_BANG_MA_BENH
        });
        
        const response = await mutateUseCommonExecute.mutateAsync({
          ma: data.ma,
          actionCode: ACTION_CODE.CHI_TIET_DANH_MUC_BANG_MA_BENH,
        } as any);
        
        console.log("[Provider] Raw response từ API:", response);
        console.log("[Provider] Response.data:", response.data);
        
        // Xử lý structure response.data.lke
        const responseData = response.data as any; // Type casting để truy cập lke
        if (responseData && responseData.lke && Array.isArray(responseData.lke) && responseData.lke.length > 0) {
          const result = responseData.lke[0] as CommonExecute.Execute.IDanhMucBangMaBenh;
          console.log("[Provider] Trả về data từ lke[0]:", result);
          return result;
        } else {
          console.log("[Provider] Không tìm thấy data trong response.data.lke");
          return {} as CommonExecute.Execute.IDanhMucBangMaBenh;
        }
      } catch (error) {
        console.log("[Provider] getChiTietDanhMucBangMaBenh error:", error);
        return {} as CommonExecute.Execute.IDanhMucBangMaBenh;
      }
    },
    [mutateUseCommonExecute],
  );

  const getListDanhMucBangMaBenh = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        ...filterParams,
        actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_DANH_MUC_BANG_MA_BENH,
      } as any);
      if (response.data) {
        setListDanhMucBangMaBenh(response.data.data);
        setTongSoDong(response.data.tong_so_dong);
      }
    } catch (error) {
      console.log("getListDanhMucBangMaBenh error ", error);
    }
  }, [mutateUseCommonExecute]);

  useEffect(() => {
    getListDanhMucBangMaBenh();
  }, [filterParams]);

  const capNhatChiTietDanhMucBangMaBenh = useCallback(
    async (data: ReactQuery.ICapNhatDanhMucBangMaBenhParams, isEditMode: boolean = false) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...data,
          actionCode: ACTION_CODE.UPDATE_DANH_MUC_BANG_MA_BENH,
        } as any);
        message.success(isEditMode ? "Cập nhật bảng mã bệnh thành công" : "Thêm mới bảng mã bệnh thành công");
        return response.data;
      } catch (error) {
        message.error(isEditMode ? "Có lỗi xảy ra khi cập nhật bảng mã bệnh" : "Có lỗi xảy ra khi thêm mới bảng mã bệnh");
        console.log("capNhatChiTietDanhMucBangMaBenh err", error);
        // return {} as CommonExecute.Execute.IDanhMucBangMaBenh;
      }
    },
    [mutateUseCommonExecute],
  );

  //initData chỉ dùng trong useEffect 1 lần, không truyền đi đâu cả, nên không cần memo hoá
  const initData = () => {};

  const value = useMemo<IQuanLyDanhMucBangMaBenhContextProps>(
    () => ({
      listDanhMucBangMaBenh,
      tongSoDong,
      loading: mutateUseCommonExecute.isLoading,
      filterParams,
      getListDanhMucBangMaBenh,
      getChiTietDanhMucBangMaBenh,
      capNhatChiTietDanhMucBangMaBenh,
      setFilterParams,
    }),
    [listDanhMucBangMaBenh, tongSoDong, mutateUseCommonExecute, filterParams, getListDanhMucBangMaBenh, getChiTietDanhMucBangMaBenh, capNhatChiTietDanhMucBangMaBenh],
  );

  return <QuanLyDanhMucBangMaBenhContext.Provider value={value}>{children}</QuanLyDanhMucBangMaBenhContext.Provider>;
};

export default QuanLyDanhMucBangMaBenhProvider;

import {ReactQuery} from "@src/@types";

// khai báo interface props Context của Login
export interface DanhSachDonViBHXHContextProps {
  danhSachDonViBHXH: Array<CommonExecute.Execute.IDanhSachDonViBHXH>;
  loading: boolean;
  listDoiTac: Array<CommonExecute.Execute.IDoiTac>;
  filterParams: ReactQuery.ITimKiemPhanTrangDanhSachDonViBHXHParams & ReactQuery.IPhanTrang;
  listNganHang: Array<CommonExecute.Execute.IDanhMucNganHang>;
  setFilterParams: React.Dispatch<React.SetStateAction<ReactQuery.ITimKiemPhanTrangDanhSachDonViBHXHParams & ReactQuery.IPhanTrang>>;
  getListDoiTac: () => Promise<void>;
  onUpdateDanhSachDonViBHXH: (item: ReactQuery.IUpdateDaiLyParams) => Promise<number | null | undefined>;
  layDanhSachDonViBHXH: (params: ReactQuery.ITimKiemPhanTrangDanhSachDonViBHXHParams) => Promise<{data: Array<CommonExecute.Execute.IDanhSachDonViBHXH>; tong_so_dong: number} | null>;
  tongSoDong: number;
  layChiTietDonViBHXH: (params: ReactQuery.IChiTietDanhSachDonViBHXHParams) => Promise<CommonExecute.Execute.IDanhSachDonViBHXH | null>;
  // defaultFormValue: object;
}

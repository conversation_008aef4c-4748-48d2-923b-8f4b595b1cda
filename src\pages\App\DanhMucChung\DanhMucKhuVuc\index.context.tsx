/**
 * T<PERSON><PERSON> dụng: Tạo React Context để chia sẻ state và methods giữa các component
 */
import {createContext} from "react";

import {IDanhMucKhuVucProvider} from "./index.model";

//Tạo React Context để chia sẻ state và methods giữa các component
const DanhMucKhuVucContext = createContext<IDanhMucKhuVucProvider>({
  listKhuVuc: [],
  listChauLuc: [],
  loading: false,
  tongSoDong: 0,
  filterParams: {
    ma: "",
    ten: "",
    ma_chau_luc: "",
    trang_thai: "",
    trang: 1,
    so_dong: 13,
  },
  searchKhuVuc: async () => {},
  getChiTietKhuVuc: async () => ({} as CommonExecute.Execute.IDanhMucKhuVuc),
  capNhatChiTietKhuVuc: async () => {},
  setFilterParams: () => {},
});

export default DanhMucKhuVucContext;

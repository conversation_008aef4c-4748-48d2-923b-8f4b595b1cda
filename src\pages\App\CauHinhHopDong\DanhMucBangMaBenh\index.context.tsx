import {createContext, useContext} from "react";

import {IQuanLyDanhMucBangMaBenhContextProps} from "./index.model";

//khởi tạo giá trị mặc định
export const QuanLyDanhMucBangMaBenhContext = createContext<IQuanLyDanhMucBangMaBenhContextProps>({
  listDanhMucBangMaBenh: [],
  tongSoDong: 0,
  loading: false,
  filterParams: {},
  getListDanhMucBangMaBenh: async () => Promise.resolve(),
  getChiTietDanhMucBangMaBenh: async () => Promise.resolve({} as CommonExecute.Execute.IDanhMucBangMaBenh),
  capNhatChiTietDanhMucBangMaBenh: async () => Promise.resolve(),
  setFilterParams: () => {},
});

export const useQuanLyDanhMucBangMaBenhContext = () => useContext(QuanLyDanhMucBangMaBenhContext);
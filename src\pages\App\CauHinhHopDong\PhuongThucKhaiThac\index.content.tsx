import {ClearOutlined, PlusCircleOutlined, SearchOutlined} from "@ant-design/icons";
import {ReactQuery, IFormInput} from "@src/@types";
import {Button, FormInput, Highlighter} from "@src/components";
import {COLOR_PALETTE, ID_PAGE} from "@src/constants";
import {Col, Form, Input, InputRef, Row, Table, TableColumnType, Tag, Tooltip} from "antd";
import type {FilterDropdownProps} from "antd/es/table/interface";
import React, {useCallback, useMemo, useRef, useState} from "react";

import {FormTimKiemPhanTrangPhuongThucKhaiThac, optionNghiepVuSelect, PhuongThucKhaiThacColumns, radioItemTrangThaiHopDongTable, TablePhuongThucKhaiThacDataType} from "./index.configs";
import {useChiTietPhuongThucKTContext} from "./index.context"; // file này lưu biến về state
import "./index.dark.scss";
import "./index.default.scss";

import {defaultPaginationTableProps, defaultTableProps, fillRowTableEmpty} from "@src/hooks";
import {IModalChiTietPhuongThucKhaiThacRef} from "./Component/index.configs";
import {ModalChiTietPhuongThucKT} from "./Component/ModalChiThietPhuongThucKT";

const {ma_doi_tac_ql, ma, trang_thai, ten} = FormTimKiemPhanTrangPhuongThucKhaiThac;
// import { IModalThemHopDongBaoHiemXeRef } from "../BaoHiemXeCoGioi/Component/ModalThemHopDongBaoHiemXeCoGioi";

type DataIndex = keyof TablePhuongThucKhaiThacDataType;
/*
file LoginContent : xử lý phần UI của màn hình
React.Fc : khai báo rằng type LoginContent là 1 React Function
*/
const HighlighterTextSearch = Highlighter as unknown as React.FC<any>; //tạm thời vượt qua lỗi 'Highlighter' cannot be used as a JSX component...

const PhuongThucKhaiThacContent: React.FC = () => {
  const {listDoiTac} = useChiTietPhuongThucKTContext();
  // console.log("list đối tác", listDoiTac);
  const {danhSachPhuongThucKhaiThac, loading, LayDanhSachPhuongThuckhaiThac, tongSoDong, layChiTietPhuongThucKhaiThac, defaultFormValue} = useChiTietPhuongThucKTContext();
  console.log(danhSachPhuongThucKhaiThac);

  const [searchText, setSearchText] = useState("");
  const [searchTextTable, setSearchTextTable] = useState<string>(""); //lưu textsearch để hiển thị vào cell
  const [searchedColumn, setSearchedColumn] = useState("");
  const searchInput = useRef<InputRef>(null);
  const [searchParams, setSearchParams] = useState<ReactQuery.IlayChiTietPhuongThucKhaiThacParams>(defaultFormValue);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(defaultPaginationTableProps.defaultPageSize);
  // const [doiTacFiltered, setDoiTacFiltered] = listDoiTac;

  const [FormTimKiemQuanLyHopDong] = Form.useForm();

  const refModalChiTietPhuongThucKhaiThac = useRef<IModalChiTietPhuongThucKhaiThacRef>(null);

  // ĐỔ DỮ LIỆU CHO BẢNG
  const dataTableListPhuongThucKhaiThac = useMemo<Array<TablePhuongThucKhaiThacDataType>>(() => {
    try {
      const tableData = danhSachPhuongThucKhaiThac.map((item: any, index: number) => {
        return {
          stt: item.stt ?? index + 1,
          ma: item.ma,
          ma_doi_tac_ql: item.ma_doi_tac_ql,
          ten: item.ten,
          doi_tac: item.doi_tac,
          ngay_tao: item.ngay_tao,
          nguoi_tao: item.nguoi_tao,
          ngay_cap_nhat: item.ngay_cap_nhat,
          nguoi_cap_nhat: item.nguoi_cap_nhat,
          trang_thai: item.trang_thai,
          trang_thai_ten: item.trang_thai_ten,
          doi_tac_ql_ten_tat: item.doi_tac_ql_ten_tat,
          ma_doi_tac: item.ma_doi_tac,
          key: index.toString(),
        };
      });
      const arrEmptyRow: Array<TablePhuongThucKhaiThacDataType> = fillRowTableEmpty(tableData.length, defaultPaginationTableProps.defaultPageSize);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      console.log("dataTableListPhongBan error", error);
      return [];
    }
  }, [danhSachPhuongThucKhaiThac]);

  const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: DataIndex) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);

  const handleReset = useCallback(
    (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: DataIndex) => {
      clearFilters();
      setSearchedColumn("");
      handleSearch([""], confirm, dataIndex);
    },
    [handleSearch],
  );

  //Bấm tìm kiếm
  const onSearchApi = (values: ReactQuery.ITimKiemPhanTrangPhuongThucKhaiThacParams & ReactQuery.IPhanTrang) => {
    console.log("values onpress tìm ki", values);

    // đoạn này để sửa các giá trị undefined => '' trước khi request lên API
    // Ant Design Select không coi "" là một giá trị "chưa chọn", mà là một giá trị đã chọn hợp lệ (dù nó rỗng), nên nó sẽ không hiển thị placeholder nữa.
    const cleanedValues = {
      ...values,
      ma_doi_tac_ql: values.ma_doi_tac_ql ?? "",
      ma: values.ma ?? "",
      trang_thai: values.trang_thai ?? "",
      trang: values.trang ?? 1,
      so_dong: values.so_dong ?? 10,
    };
    setSearchParams(cleanedValues);
    setPage(1); // reset về trang 1 khi search mới
    LayDanhSachPhuongThuckhaiThac({...cleanedValues, trang: 1, so_dong: pageSize});
  };

  //Gọi api khi chuyển trang
  const onChangePage = useCallback(
    (page: number, pageSize: number) => {
      setPage(page);
      setPageSize(pageSize);
      LayDanhSachPhuongThuckhaiThac({...searchParams, trang: page, so_dong: pageSize});
    },
    [searchParams],
  );
  //Đồng bộ chức năng

  //Change đối tác

  // Lọc danh sách đối tác theo mã đối tác
  //   const filtered = listDoiTac.filter(pb => pb.ma_doi_tac === maDoiTac);
  //   setDoiTacFiltered(filtered);
  // };

  // RENDER
  //tạo cột tìm kiếm
  const getColumnSearchProps = (dataIndex: DataIndex, title: string): TableColumnType<TablePhuongThucKhaiThacDataType> => ({
    filterDropdown:
      dataIndex !== "trang_thai_ten"
        ? ({setSelectedKeys, selectedKeys, confirm, clearFilters, close}) => (
            <div style={{padding: 8}} onKeyDown={e => e.stopPropagation()} className="flex flex-row items-center p-2">
              <Input
                ref={searchInput}
                placeholder={`Tìm theo ${title}`}
                value={selectedKeys[0]}
                onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                onPressEnter={() => handleSearch(selectedKeys as string[], confirm, dataIndex)}
                style={{display: "block", marginRight: 8}}
              />
              <Tooltip title="Tìm kiếm">
                <Button type="primary" shape="circle" icon={<SearchOutlined />} onClick={() => handleSearch(selectedKeys as string[], confirm, dataIndex)} className="mr-2" />
              </Tooltip>
              <Tooltip title="Xoá">
                <Button type="primary" shape="circle" icon={<ClearOutlined />} onClick={() => clearFilters && handleReset(clearFilters, confirm, dataIndex)} />
              </Tooltip>
            </div>
          )
        : null,
    filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} />,
    onFilter: (value, record) =>
      record[dataIndex]
        ? record[dataIndex]
            .toString()
            .toLowerCase()
            .includes((value as string).toLowerCase())
        : false,
    filterDropdownProps: {
      onOpenChange(open) {
        if (open) {
          setTimeout(() => searchInput.current?.select(), 100);
        }
      },
    },
    filters: dataIndex === "trang_thai_ten" ? radioItemTrangThaiHopDongTable : undefined,
    render: (text, record, index) => {
      if (dataIndex === "trang_thai_ten") {
        // Xác định màu dựa vào text (trạng thái hiển thị)
        const color = text === "Đang sử dụng" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];
        if (record.key.toString().includes("empty")) return "";
        return (
          <Tag color={color} className="text-[11px]">
            {text}
          </Tag>
        );
      }
      return searchedColumn === dataIndex ? (
        <Highlighter searchWords={[searchText]} textToHighlight={text ? text.toString() : ""} />
      ) : text !== undefined ? (
        text
      ) : (
        <Tag color={"transparent"} className="!text-white text-[11px]">
          {"\u00A0"}
        </Tag>
      );
    },
  });

  // const renderFormColum = (props: any, span?: 6) => (
  //   <Col span={span}>
  //     <FormInput {...props} />
  //   </Col>
  // );
  const renderFormInputColum = (props: IFormInput, span = 4) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  //render header table
  const renderHeaderTablePhuongThucKhaiThac = () => {
    return (
      <>
        <Form layout="vertical" className="[&_.ant-form-item]:!mb-0" onFinish={onSearchApi}>
          <Row gutter={16} align={"bottom"}>
            {renderFormInputColum({...ma_doi_tac_ql, options: listDoiTac}, 4)}
            {renderFormInputColum({...ma}, 4)}
            {renderFormInputColum({...ten, options: listDoiTac}, 4)}
            {renderFormInputColum({...trang_thai, options: optionNghiepVuSelect}, 4)}
            <Col span={3}>
              <Button type="primary" htmlType="submit" loading={loading} icon={<SearchOutlined />} block>
                Tìm kiếm
              </Button>
            </Col>
            <Col span={3}>
              <Button type="primary" icon={<PlusCircleOutlined />} onClick={() => refModalChiTietPhuongThucKhaiThac.current?.open()} loading={loading} block>
                Tạo mới
              </Button>
            </Col>
          </Row>
        </Form>
      </>
    );
  };

  //render table
  return (
    <div id={ID_PAGE.PHUONG_THUC_KHAI_THAC} className="[&_.ant-space]:w-full">
      <Table<TablePhuongThucKhaiThacDataType>
        {...defaultTableProps}
        loading={loading}
        onRow={(record, rowIndex) => {
          return {
            onClick: async event => {
              if (record.key.toString().includes("empty")) return;
              const chiTietPhuongThucKhaiThac = await layChiTietPhuongThucKhaiThac({ma: record.ma, ma_doi_tac_ql: record.ma_doi_tac_ql});
              console.log("chiTietPhuongThucKhaiThac", chiTietPhuongThucKhaiThac);
              if (chiTietPhuongThucKhaiThac) {
                refModalChiTietPhuongThucKhaiThac.current?.open(chiTietPhuongThucKhaiThac);
              }
            },
          };
        }}
        columns={(PhuongThucKhaiThacColumns || []).map(item => {
          // Đảm bảo item.key được định nghĩa, item.title là chuỗi và item.key không phải là "stt" trước khi thêm ô tìm kiếm
          return {
            ...item,
            ...(item.key && typeof item.title === "string" && item.key !== "stt" ? getColumnSearchProps(item.key as keyof TablePhuongThucKhaiThacDataType, item.title) : {}),
          };
        })} //định nghĩa cột của table
        dataSource={dataTableListPhuongThucKhaiThac}
        title={renderHeaderTablePhuongThucKhaiThac}
        pagination={{
          ...defaultPaginationTableProps,
          total: tongSoDong,
          onChange: (page, pageSize) => {
            onChangePage(page, pageSize);
          },
        }}
      />
      <ModalChiTietPhuongThucKT ref={refModalChiTietPhuongThucKhaiThac} listDoiTac={listDoiTac} />
    </div>
  );
};

PhuongThucKhaiThacContent.displayName = "PhuongThucKhaiThacContent";

export default PhuongThucKhaiThacContent;

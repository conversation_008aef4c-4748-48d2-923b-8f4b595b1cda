import {create} from "zustand";

export interface IListChucDanhStore {
  listChucDanh: Array<CommonExecute.Execute.IDanhMucChucDanh>;
  setListChucDanh: (listChucDanh: Array<CommonExecute.Execute.IDanhMucChucDanh>) => void;
}

export const useChucDanh = create<IListChucDanhStore>(set => ({
  listChucDanh: [],
  setListChucDanh: listChucDanh => set({listChucDanh}),
}));

//create : hàm tạo store
// export const useChucDanh = create(
//   //persist : middileware để lưu trạng thái vào localStorage
//   persist<IListChucDanhStore, [], [], Pick<IListChucDanhStore, "listChucDanh">>(
//     //set, get phải đúng thứ tự này, nếu không sẽ báo lỗi vì sử dụng nhầm cách get/set
//     (set, get) => ({
//       //khởi tạo state menuNguoiDung từ cookie + localStorage
//       listChucDanh: get()?.listChucDanh || [],
//       setListChucDanh: (listChucDanh: Array<CommonExecute.Execute.IDanhMucChucDanh>) => set(() => ({listChucDanh: [...listChucDanh]})),
//     }),
//     //cấu hình persist
//     {
//       name: LOCAL_STORAGE_KEY.CHUC_DANH, //key để lưu trong localStorate
//       storage: createJSONStorage(() => localStorage), //nơi lưu trữ là localStorage
//       //chỉ định những phần nào trong state sẽ được lưu, tránh lưu những thứ k cần thiết như các hàm set
//       partialize: state => ({listChucDanh: state.listChucDanh}),
//     },
//   ),
// );

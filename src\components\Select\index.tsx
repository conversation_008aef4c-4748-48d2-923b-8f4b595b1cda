import {Select as AntSelect, SelectProps, Space} from "antd";
import React, {memo} from "react";
import {twMerge} from "tailwind-merge";

import {isEqual} from "lodash";
import {Typography} from "..";

const SelectComponent: React.FC<
  SelectProps & {
    labelDirection?: "horizontal" | "vertical";
    containerClassName?: string;
  }
> = props => {
  const {className = "", allowClear = true, options, value, onChange, title, popupClassName = "", labelDirection = "vertical", containerClassName, showSearch, defaultValue, ...etc} = props;
  return (
    <Space direction={labelDirection} className={containerClassName} style={{width: "100%"}}>
      {title && (
        <Typography className="font-bold" type="text">
          {title}
        </Typography>
      )}
      <AntSelect
        showSearch={showSearch}
        className={twMerge("custom-select", className)}
        allowClear={allowClear}
        options={options}
        value={value}
        onChange={onChange}
        // size={'small'}
        defaultValue={defaultValue}
        popupClassName={twMerge("custom-select-popup", popupClassName)}
        style={{
          width: "100%",
          // height: 32
        }} // cố định height cho bằng height của input component
        {...etc}
        placeholder={<span className="text-xs font-extralight">{props.placeholder}</span>}
      />
    </Space>
  );
};

const Select = memo(SelectComponent, isEqual);

export default memo(Select);

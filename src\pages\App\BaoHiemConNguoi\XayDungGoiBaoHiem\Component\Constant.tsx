import {IFormInput, ReactQuery} from "@src/@types";
import {defaultTableColumnsProps, ruleInputMessage} from "@src/hooks";
import {TableProps, TabsProps} from "antd";
import {Dispatch, SetStateAction} from "react";

/* MODAL THÊM GÓI BẢO HIỂM */
export interface IModalThemGoiBaoHiemRef {
  open: (data?: CommonExecute.Execute.IChiTietGoiBaoHiemConNguoi) => void;
  close: () => void;
}

export interface ModalThemGoiBaoHiemProps {}

export const tabsGoiBaoHiem: TabsProps["items"] = [
  {
    key: "1",
    label: "Cấu hình quyền lợi",
    // children: "Content of Tab Pane 1",
  },
  {
    key: "2",
    label: "Điều khoản bổ sung",
  },
  {
    key: "3",
    label: "Cấu hình đồng chi trả",
  },
];

//FORM THÔNG TIN GÓI BẢO HIỂM
export interface IFormTaoGoiBaoHiemFieldsConfig {
  ma: IFormInput; //mã gói bảo hiểm
  ten: IFormInput; //tên goi bảo hiểm
  ma_doi_tac_ql: IFormInput; //đối tác quản lý
  nv: IFormInput; //nghiệp vụ
  ma_sp: IFormInput; //sản phẩm - một gói bảo hiểm thuộc 1 sản phẩm

  ngay_ad: IFormInput; //ngày áp dụng
  gioi_tinh: IFormInput; //giới tính áp dụng
  tuoi_tu: IFormInput; //tuổi từ
  tuoi_toi: IFormInput; //tuổi tới
  ma_ct: IFormInput; //mã cấp trên - mã hoi bảo hiểm gốc
  trang_thai: IFormInput; // trạng thái
}

export const FormTaoGoiBaoHiem: IFormTaoGoiBaoHiemFieldsConfig = {
  ma: {component: "input", name: "ma", label: "Mã gói bảo hiểm", placeholder: "Nhập mã gói bảo hiểm", rules: [ruleInputMessage.required], allowClear: false},
  ten: {component: "input", name: "ten", label: "Tên gói bảo hiểm", placeholder: "Nhập tên gói bảo hiểm", rules: [ruleInputMessage.required], allowClear: false},
  ma_doi_tac_ql: {component: "select", name: "ma_doi_tac_ql", label: "Đối tác", placeholder: "Chọn đối tác", rules: [ruleInputMessage.required], allowClear: false},
  nv: {component: "select", name: "nv", label: "Nghiệp vụ", placeholder: "Chọn nghiệp vụ", rules: [ruleInputMessage.required], allowClear: false},
  ma_sp: {component: "select", name: "ma_sp", label: "Sản phẩm", placeholder: "Chọn sản phẩm", rules: [ruleInputMessage.required], allowClear: false},
  ngay_ad: {component: "date-picker", name: "ngay_ad", label: "Ngày áp dụng", placeholder: "DD/MM/YYYY", className: "block ", allowClear: false, rules: [ruleInputMessage.required]},
  gioi_tinh: {component: "select", name: "gioi_tinh", label: "Giới tính", placeholder: "Chọn giới tính", rules: [ruleInputMessage.required], className: "", allowClear: false},
  tuoi_tu: {
    component: "input",
    name: "tuoi_tu",
    label: "Tuổi từ",
    placeholder: "Nhập tuổi từ",
    rules: [
      ruleInputMessage.required,
      // {
      //   type: "number",
      //   min: 1,
      //   max: 100,
      //   message: "Tuổi phải từ 1 đến 100",
      // },
    ],
    className: "",
    allowClear: false,
  },
  tuoi_toi: {
    component: "input",
    name: "tuoi_toi",
    label: "Tuổi tới",
    placeholder: "Nhập tuổi tới",
    rules: [
      ruleInputMessage.required,
      // {
      //   type: "number",
      //   min: 1,
      //   max: 100,
      //   message: "Tuổi phải từ 1 đến 100",
      // },
    ],
    className: "",
    allowClear: false,
  },
  ma_ct: {component: "input", name: "ma_ct", label: "Mã gói bảo hiểm gốc", placeholder: "Nhập mã gói bảo hiểm gốc", rules: [], className: ""},
  trang_thai: {component: "select", name: "trang_thai", label: "Trạng thái", placeholder: "Chọn trạng thái", rules: [ruleInputMessage.required], className: "", allowClear: false},
};

export const listGioiTinhSelect: Array<{ma: string; ten: string}> = [
  {ma: "*", ten: "Tất cả giới tính"},
  {ma: "NAM", ten: "Nam"},
  {ma: "NU", ten: "Nữ"},
];
export const listTrangThaiGoiBaoHiemSelect: Array<{ma: string; ten: string}> = [
  {ma: "C", ten: "Chưa sử dụng"},
  {ma: "D", ten: "Đã đang sử dụng"},
];
export const listNghiepVuSelect: Array<{ma: string; ten: string}> = [{ma: "NG", ten: "Bảo hiểm con người"}];

/* TABS CẤU HÌNH QUYỀN LỢI */

export const listFieldFillTableQuyenLoi: string[] = [
  "gh_lan_ngay",
  "gh_tien_lan_ngay",
  "gh_tien_nam",
  "tgian_cho",
  "kieu_ad_ten",
  "kieu_ad",
  "gh_lan_ngay_dtri",
  "gh_tien_lan_ngay_dtri",
  "nt_tien_bh",
  "ma_qloi_tru_lui",
  "tl_dct",
  "phi_bh",
  "cap",
  "ma_qloi",
  "ghi_chu",
];
export interface ITabCauHinhQuyenLoiRef {
  setListQuyenLoi: (data: CommonExecute.Execute.IDieuKhoanQuyenLoiConNguoi[]) => void;
  getListQuyenLoi: () => CommonExecute.Execute.IDieuKhoanQuyenLoiConNguoi[];
  openModalThemQuyenLoi: () => void;
}

export interface TabCauHinhQuyenLoiProps {}

export interface IGoiBaoHiem {
  ma_doi_tac?: string;
  ma_doi_tac_ql?: string;
  id?: number;
  nv?: string;
  ma_sp?: string;
  ma?: string;
  ten?: string;
  ma_ct?: string;
  gioi_tinh?: string;
  tuoi_tu?: number;
  tuoi_toi?: number;
  ngay_ad?: number;
  stt?: number;
  trang_thai?: string;
  ngay_ad_date?: string;
}
export interface TableQuyenLoiColumnDataType {
  key: string;
  stt: number;
  ten_qloi: string; //TÊN QUYỀN LỢI
  gh_lan_ngay?: number; //GIỚI HẠN LẦN NGÀY
  gh_tien_lan_ngay?: number; //GIỚI HẠN TIỀN LẦN NGÀY
  gh_tien_nam?: number; //GIỜ HẠN TIỀN NĂM
  tgian_cho?: number; //THỜI GIAN CHỜ
  // kieu_ad_ten?: string; //KIỂU ÁP DỤNG
  kieu_ad?: string; //MÃ KIỂU ÁP DỤNG
  gh_lan_ngay_dtri?: number; //GIỚI HẠN LẦN NGÀY ĐIỀU TRỊ
  gh_tien_lan_ngay_dtri?: number; //GIỚI HẠN TIỀN LẦN NGÀY ĐIỀU TRỊ
  nt_tien_bh?: string; //NGUYÊN TỆ TIỀN BẢO HIỂM
  ma_qloi_tru_lui?: string; //MÃ QUYỀN LỢI TRỪ LÙI
  ma_qloi: string;
  tl_dct?: number; //Tỉ lệ đồng chi trả
  phi_bh?: number; //PHÍ BẢO HIỂM
  ghi_chu?: string; //GHI CHÚ
  cap?: number;
}

export const tableQuyenLoiColumn: TableProps<TableQuyenLoiColumnDataType>["columns"] = [
  {title: "STT", dataIndex: "stt", key: "stt", width: 70, ...defaultTableColumnsProps},
  {title: "Tên quyền lợi", dataIndex: "ten_qloi", key: "ten_qloi", width: 350, ...defaultTableColumnsProps, align: "left"},
  {title: "G.hạn số\nlần/ngày", dataIndex: "gh_lan_ngay", key: "gh_lan_ngay", width: 100, ...defaultTableColumnsProps, ellipsis: false},
  {title: "G.hạn tiền \nlần/ngày", dataIndex: "gh_tien_lan_ngay", key: "gh_tien_lan_ngay", width: 130, ...defaultTableColumnsProps, ellipsis: false},
  {title: "G.hạn \ntiền/năm", dataIndex: "gh_tien_nam", key: "gh_tien_nam", width: 130, ...defaultTableColumnsProps, ellipsis: false},
  {title: "T.gian \nchờ", dataIndex: "tgian_cho", key: "tgian_cho", width: 100, ...defaultTableColumnsProps, ellipsis: false},
  {title: "Kiểu áp dụng", dataIndex: "kieu_ad", key: "kieu_ad", width: 130, ...defaultTableColumnsProps},
  {title: "G.hạn lần/ngày \n1 lần đ.trị", dataIndex: "gh_lan_ngay_dtri", key: "gh_lan_ngay_dtri", width: 130, ...defaultTableColumnsProps, ellipsis: false},
  {title: "G.hạn tiền lần/ngày \n1 lần đ.trị", dataIndex: "gh_tien_lan_ngay_dtri", key: "gh_tien_lan_ngay_dtri", width: 130, ...defaultTableColumnsProps, ellipsis: false},
  {title: "N.tệ STBH", dataIndex: "nt_tien_bh", key: "nt_tien_bh", width: 110, ...defaultTableColumnsProps},
  {title: "Q.lợi \ntrừ lùi", dataIndex: "ma_qloi_tru_lui", key: "ma_qloi_tru_lui", width: 80, ...defaultTableColumnsProps, ellipsis: false},
  {title: "Tỉ lệ \n đồng chi trả", dataIndex: "tl_dct", key: "tl_dct", width: 80, ...defaultTableColumnsProps, ellipsis: false},
  {title: "Phí BH", dataIndex: "phi_bh", key: "phi_bh", width: 120, ...defaultTableColumnsProps},
  {title: "Ghi chú", dataIndex: "ghi_chu", key: "ghi_chu", width: 100, ...defaultTableColumnsProps},
];

//keyof: return ra key của inteface TableGoiBaoHiemColumnDataIndex;
export type TableQuyenLoiColumnDataIndex = keyof TableQuyenLoiColumnDataType;

export const listKieuApDung: Array<{ma: string; ten: string}> = [
  {ma: "TIEN", ten: "Tiền"},
  {ma: "LAN_NGAY", ten: "Lần ngày"},
  {ma: "THANG_LUONG", ten: "Tháng lương"},
  {ma: "NGAY_LUONG", ten: "Ngày lương"},
];

/* MODAL QUYỀN LỢI PHỤ THUỘC */
export interface IModalThemQuyenLoiRef {}

export interface ModalThemQuyenLoiProps {
  quyenLoiDangXem?: CommonExecute.Execute.IDieuKhoanQuyenLoiConNguoi;
  listQuyenLoiRoot: CommonExecute.Execute.IDanhSachBoMaQuyenLoi[];
  listQuyenLoi: CommonExecute.Execute.IDieuKhoanQuyenLoiConNguoi[];
  checkStrictly: boolean;
  setIsOpenQuyenLoiTruLui: Dispatch<SetStateAction<boolean>>;
  onPressLuuQuyenLoiTruLui: (listMaQuyenLoiSelect: string[], maQuyenLoiDangXem?: string) => void;
  onPressThemQuyenLoi: (quyenLoiDuocThem: string[]) => void;
}

/* TABS ĐIỀU KHOẢN BỔ SUNG */
export interface ITabDieuKhoanBoSungRef {
  setListDieuKhoan: (data: CommonExecute.Execute.IDieuKhoanQuyenLoiConNguoi[]) => void;
  getListDieuKhoan: () => CommonExecute.Execute.IDieuKhoanQuyenLoiConNguoi[];
  openModalThemQuyenLoi: () => void;
}

export interface TabDieuKhoanBoSungProps {}
export interface TableDieuKhoanColumnDataType {
  key: string;
  stt: number;
  ten_qloi: string; //TÊN QUYỀN LỢI
  gh_lan_ngay?: number; //GIỚI HẠN LẦN NGÀY
  gh_tien_lan_ngay?: number; //GIỚI HẠN TIỀN LẦN NGÀY
  gh_tien_nam?: number; //GIỜ HẠN TIỀN NĂM
  tgian_cho?: number; //THỜI GIAN CHỜ
  kieu_ad?: string; //MÃ KIỂU ÁP DỤNG
  gh_lan_ngay_dtri?: number; //GIỚI HẠN LẦN NGÀY ĐIỀU TRỊ
  gh_tien_lan_ngay_dtri?: number; //GIỚI HẠN TIỀN LẦN NGÀY ĐIỀU TRỊ
  nt_tien_bh?: string; //NGUYÊN TỆ TIỀN BẢO HIỂM
  ma_qloi_tru_lui?: string; //MÃ QUYỀN LỢI TRỪ LÙI
  ma_qloi: string;
  tl_dct?: number; //Tỉ lệ đồng chi trả
  phi_bh?: number; //PHÍ BẢO HIỂM
  ghi_chu?: string; //GHI CHÚ
  cap?: number;
}

export const tableDieuKhoanColumn: TableProps<TableDieuKhoanColumnDataType>["columns"] = [
  {title: "STT", dataIndex: "stt", key: "stt", width: 70, ...defaultTableColumnsProps},
  {title: "Tên quyền lợi", dataIndex: "ten_qloi", key: "ten_qloi", width: 350, ...defaultTableColumnsProps, align: "left"},
  {title: "G.hạn số\nlần/ngày", dataIndex: "gh_lan_ngay", key: "gh_lan_ngay", width: 100, ...defaultTableColumnsProps, ellipsis: false},
  {title: "G.hạn tiền \nlần/ngày", dataIndex: "gh_tien_lan_ngay", key: "gh_tien_lan_ngay", width: 130, ...defaultTableColumnsProps, ellipsis: false},
  {title: "G.hạn \ntiền/năm", dataIndex: "gh_tien_nam", key: "gh_tien_nam", width: 130, ...defaultTableColumnsProps, ellipsis: false},
  {title: "T.gian \nchờ", dataIndex: "tgian_cho", key: "tgian_cho", width: 100, ...defaultTableColumnsProps, ellipsis: false},
  {title: "Kiểu áp dụng", dataIndex: "kieu_ad", key: "kieu_ad", width: 130, ...defaultTableColumnsProps},
  {title: "G.hạn lần/ngày \n1 lần đ.trị", dataIndex: "gh_lan_ngay_dtri", key: "gh_lan_ngay_dtri", width: 130, ...defaultTableColumnsProps, ellipsis: false},
  {title: "G.hạn tiền lần/ngày \n1 lần đ.trị", dataIndex: "gh_tien_lan_ngay_dtri", key: "gh_tien_lan_ngay_dtri", width: 130, ...defaultTableColumnsProps, ellipsis: false},
  {title: "N.tệ STBH", dataIndex: "nt_tien_bh", key: "nt_tien_bh", width: 110, ...defaultTableColumnsProps},
  {title: "Q.lợi \ntrừ lùi", dataIndex: "ma_qloi_tru_lui", key: "ma_qloi_tru_lui", width: 80, ...defaultTableColumnsProps, ellipsis: false},
  {title: "Tỉ lệ \n đồng chi trả", dataIndex: "tl_dct", key: "tl_dct", width: 80, ...defaultTableColumnsProps, ellipsis: false},
  {title: "Phí BH", dataIndex: "phi_bh", key: "phi_bh", width: 120, ...defaultTableColumnsProps},
  {title: "Ghi chú", dataIndex: "ghi_chu", key: "ghi_chu", width: 100, ...defaultTableColumnsProps},
];

//keyof: return ra key của inteface TableGoiBaoHiemColumnDataIndex;
export type TableDieuKhoanColumnDataIndex = keyof TableDieuKhoanColumnDataType;

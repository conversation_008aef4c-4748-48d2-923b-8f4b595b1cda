import {Checkbox, FormInput, Highlighter, Select, TableFilterDropdown} from "@src/components";
import {defaultTableProps, fillRowTableEmpty} from "@src/hooks";
import {Row, Table, Input, InputRef} from "antd";
import {FilterDropdownProps} from "antd/es/table/interface";
import {useCallback, useEffect, useMemo, useRef, useState} from "react";
import {SearchOutlined} from "@ant-design/icons";
import {DataIndexDVQL, DataIndexMenu, donViQuanLyColumns, NHOM_MENU, phanQuyenMenuColumns, TableDonViQuanLyDataType, TableMenuNguoiDungDataType} from "./index.configs";
import {useQuanLyTaiKhoanNguoiDungContext} from "../index.context";
import {Col} from "antd/lib";
// import Highlighter from "react-highlight-words";

interface TabDonViQuanLyProps {
  chiTietNguoiSuDung: CommonExecute.Execute.IChiTietNguoiSuDung | null;
  onDataChange?: (data: TableDonViQuanLyDataType[]) => void;
  onDataChangeMenu?: (data: TableMenuNguoiDungDataType[]) => void;
}

const TabDonViQuanLy: React.FC<TabDonViQuanLyProps> = ({onDataChange, onDataChangeMenu}) => {
  const {loading, donViQuanLyNguoiSuDung, menuNguoiSuDung, setDonViQuanLySelected, setMenuSelected, donViSelected, menuSelected, listDoiTac} = useQuanLyTaiKhoanNguoiDungContext();
  const [searchedColumn, setSearchedColumn] = useState("");
  const [searchText, setSearchText] = useState("");
  const [dataSource, setDataSource] = useState<TableDonViQuanLyDataType[]>([]);
  const [dataSourceMenu, setDataSourceMenu] = useState<TableMenuNguoiDungDataType[]>([]);
  const [tableSearchText, setTableSearchText] = useState(""); // Ô tìm kiếm cho bảng Đơn Vị Quản Lý
  const [menuSearchText, setMenuSearchText] = useState(""); // Ô tìm kiếm cho bảng Menu
  const [isAllChecked, setIsAllChecked] = useState(false); // State để theo dõi checkbox chọn tất cả
  const [isAllCheckedMenu, setIsAllCheckedMenu] = useState(false);
  const refSearchInputTable = useRef<InputRef>(null);
  const [selectedDoiTac, setSelectedDoiTac] = useState<string | undefined>();
  const [selectedNhom, setSelectedNhom] = useState<string | undefined>();
  const filteredDonViQuanLy = dataSource.filter(item => {
    const textMatch = Object.values(item).some(value => typeof value === "string" && value.toLowerCase().includes(tableSearchText.toLowerCase()));
    const doiTacMatch = selectedDoiTac ? item.ma_doi_tac_ql === selectedDoiTac : true;
    return textMatch && doiTacMatch;
  });
  const filteredMenu = dataSourceMenu.filter(item => {
    const textMenuMatch = Object.values(item).some(value => typeof value === "string" && value.toLowerCase().includes(menuSearchText.toLowerCase()));
    const nhomMatch = selectedNhom ? item.nhom === selectedNhom : true; // nếu có trường nhom trong dữ liệu
    return textMenuMatch && nhomMatch;
  });
  // Trong component TabDonViQuanLy
  useEffect(() => {
    // Kiểm tra trạng thái checkbox trong filteredDonViQuanLy
    const allChecked = filteredDonViQuanLy.every(item => item.key.includes("empty") || item.is_checked === "1");
    setIsAllChecked(allChecked);
  }, [filteredDonViQuanLy]);

  useEffect(() => {
    // Kiểm tra trạng thái checkbox trong filteredMenu
    const allChecked = filteredMenu.every(item => item.key.includes("empty") || item.is_checked === "1");
    setIsAllCheckedMenu(allChecked);
  }, [filteredMenu]);
  const dataTableListDonViQuanLy = useMemo<Array<TableDonViQuanLyDataType>>(() => {
    try {
      const tableData = Array.isArray(donViQuanLyNguoiSuDung) ? donViQuanLyNguoiSuDung : [];
      const mappedData = tableData.map((item: any, index: number) => ({
        stt: item.stt ?? index + 1,
        ma_doi_tac_ql: item.ma_doi_tac_ql || "",
        ten_chi_nhanh_ql: item.ten_chi_nhanh_ql || "",
        ten_doi_tac_ql: item.ten_doi_tac_ql || "",
        ma_chi_nhanh_ql: item.ma_chi_nhanh_ql || "",
        is_checked: item.is_checked ? "1" : "0",
        key: `${item.ma_doi_tac_ql}_${item.ma_chi_nhanh_ql}`,
      }));

      // Lọc dữ liệu dựa trên tableSearchText
      // const filteredData = tableSearchText
      //   ? mappedData.filter(item => Object.values(item).some(value => typeof value === "string" && value.toLowerCase().includes(tableSearchText.toLowerCase())))
      //   : mappedData;

      const arrEmptyRow = fillRowTableEmpty(mappedData.length, 10);
      const finalData = [...mappedData, ...arrEmptyRow];
      setDataSource(finalData); // Cập nhật dataSource
      const filtered = finalData.filter(i => i.is_checked === "1" && !i.key.includes("empty"));
      onDataChange?.(filtered);
      // console.log("đơn vị quản lý đã được chọn", filtered);
      setDonViQuanLySelected(
        filtered.map(item => ({
          ma_doi_tac_ql: item.ma_doi_tac_ql,
          ma_chi_nhanh_ql: item.ma_chi_nhanh_ql,
          is_checked: item.is_check,
        })),
      );
      return finalData;
    } catch (error) {
      console.error("Lỗi khi xử lý dữ liệu bảng Đơn Vị Quản Lý:", error);
      return [];
    }
  }, [donViQuanLyNguoiSuDung]);

  // Dữ liệu bảng menu
  const dataTableListMenu = useMemo<Array<TableMenuNguoiDungDataType>>(() => {
    try {
      const tableData = Array.isArray(menuNguoiSuDung) ? menuNguoiSuDung : [];

      const mappedData = tableData.map((item: any, index: number) => ({
        stt: index + 1,
        ma_doi_tac: item.ma_doi_tac || "",
        ma: item.ma || "",
        ma_cha: item.ma_cha || "",
        ten: item.ten || "",
        url: item.url || "",
        nhom: item.nhom || "",
        is_checked: item.is_checked ? "1" : "0",
        key: item.ma ? `${item.ma}` : `empty_${Math.random()}`,
      }));

      const arrEmptyRow = fillRowTableEmpty(mappedData.length, 10);
      const finalData = [...mappedData, ...arrEmptyRow];
      setDataSourceMenu(finalData); // Cập nhật dataSourceMenu
      const filtered = finalData.filter(i => i.is_checked === "1" && !i.key.includes("empty"));
      onDataChange?.(filtered);
      // console.log("menu đã được chọn", filtered);
      setMenuSelected(
        filtered.map(item => ({
          is_checked: item.is_checked,
          ma: item.ma,
          // ma_chi_nhanh: item.ma_chi_nhanh_ql,
        })),
      );

      return finalData;
    } catch (error) {
      console.error("Lỗi khi xử lý dữ liệu bảng Menu:", error);
      return [];
    }
  }, [menuNguoiSuDung]);

  // Xử lý click vào hàng
  const handleRowClick = (record: TableDonViQuanLyDataType) => {
    if (!record.ma_chi_nhanh_ql || !record.ma_doi_tac_ql || !record.key) {
      // console.warn("Invalid record, skipping row click:", record);
      return;
    }
    const newChonValue = record.is_checked === "1" ? "0" : "1";
    handleInputChange(record.key, "is_checked", newChonValue);
  };

  const handleRowMenuClick = (record: TableMenuNguoiDungDataType) => {
    if (!record.ma || !record.key) {
      // console.warn("Invalid record, skipping row click:", record);
      return;
    }
    const newChonValue = record.is_checked === "1" ? "0" : "1";
    handleInputChangeMenu(record.key, "is_checked", newChonValue);
  };
  // Xử lý tìm kiếm
  const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: DataIndexDVQL | DataIndexMenu) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);

  const handleReset = useCallback(
    (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: DataIndexDVQL | DataIndexMenu) => {
      clearFilters();
      setSearchedColumn("");
      handleSearch([""], confirm, dataIndex);
    },
    [handleSearch],
  );

  // Hàm render cột tìm kiếm
  const getColumnSearchProps = (dataIndex: DataIndexDVQL | DataIndexMenu, title: string) => ({
    filterDropdown: (filterDropdownParams: FilterDropdownProps) => (
      <TableFilterDropdown
        ref={refSearchInputTable}
        title={title}
        dataIndex={dataIndex}
        handleSearch={(selectedKeys, confirm, dataIndex) => handleSearch(selectedKeys, confirm, dataIndex)}
        handleReset={(clearFilters, confirm, dataIndex) => clearFilters && handleReset(clearFilters, confirm, dataIndex)}
        {...filterDropdownParams}
      />
    ),
    // filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} />,
    onFilter: (value: string, record: any) => (record[dataIndex] ? record[dataIndex].toString().toLowerCase().includes(value.toLowerCase()) : false),
    render: (text: string) => (searchedColumn === dataIndex ? <Highlighter searchWords={[searchText]} textToHighlight={text ? text.toString() : ""} /> : text || ""),
  });

  // Xử lý thay đổi checkbox
  const handleInputChange = (key: string, field: keyof TableDonViQuanLyDataType, value: any) => {
    if (!key) {
      // console.warn("Key is undefined, skipping update");
      return;
    }
    setDataSource(prevDataSource => {
      const updated = prevDataSource.map(item => (item.key === key ? {...item, [field]: value} : item));
      const filtered = updated.filter(i => i.is_checked === "1");
      onDataChange?.(filtered);
      setDonViQuanLySelected(
        filtered.map(item => ({
          is_checked: item.is_checked ?? "",
          ma_doi_tac_ql: item.ma_doi_tac_ql,
          ma_chi_nhanh_ql: item.ma_chi_nhanh_ql,
        })),
      );
      // Kiểm tra xem tất cả checkbox có được chọn hay không
      const allChecked = updated.every(item => item.key.includes("empty") || item.is_checked === "1");
      setIsAllChecked(allChecked);
      return updated;
    });
  };

  const handleInputChangeMenu = (key: string, field: keyof TableMenuNguoiDungDataType, value: any) => {
    if (!key) {
      // console.warn("Key is undefined, skipping update");
      return;
    }
    setDataSourceMenu(prevDataSource => {
      const updated = prevDataSource.map(item => (item.key === key ? {...item, [field]: value} : item));
      const filtered = updated.filter(i => i.is_checked === "1");
      onDataChangeMenu?.(filtered);
      setMenuSelected(
        filtered.map(item => ({
          is_checked: item.is_checked ?? "",
          ma: item.ma,
        })),
      );
      // Kiểm tra xem tất cả checkbox có được chọn hay không
      const allChecked = updated.every(item => item.key.includes("empty") || item.is_checked === "1");
      setIsAllCheckedMenu(allChecked);
      return updated;
    });
  };
  // Xử lý tìm kiếm toàn bảng
  const handleTableSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTableSearchText(e.target.value);
  };

  const handleMenuSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setMenuSearchText(e.target.value);
  };
  const handleSelectAll = (checked: boolean) => {
    setIsAllChecked(checked);
    setDataSource(prevDataSource => {
      const updated = prevDataSource.map(item => {
        if (item.key.includes("empty")) return item;
        return {...item, is_checked: checked ? "1" : "0"};
      });
      const filtered = updated.filter(i => i.is_checked === "1" && !i.key.includes("empty"));
      onDataChange?.(filtered);
      setDonViQuanLySelected(
        filtered.map(item => ({
          ma_doi_tac_ql: item.ma_doi_tac_ql,
          ma_chi_nhanh_ql: item.ma_chi_nhanh_ql,
          is_checked: item.is_checked ?? "",
        })),
      );
      return updated;
    });
  };
  const handleSelectAllMenu = (checked: boolean) => {
    setIsAllCheckedMenu(checked);
    setDataSourceMenu(prevDataSource => {
      const updated = prevDataSource.map(item => {
        if (item.key.includes("empty")) return item;
        return {...item, is_checked: checked ? "1" : "0"};
      });
      const filtered = updated.filter(i => i.is_checked === "1" && !i.key.includes("empty"));
      onDataChangeMenu?.(filtered);
      setMenuSelected(
        filtered.map(item => ({
          is_checked: item.is_checked ?? "",
          ma: item.ma,
        })),
      );
      return updated;
    });
  };

  // Render cột cho bảng đơn vị quản lý
  const renderColumn = (column: any) => {
    if (column.dataIndex === "is_checked") {
      return {
        ...column,
        title: (
          <div style={{height: 19, alignItems: "center", verticalAlign: "middle"}}>
            <Checkbox checked={isAllChecked} onChange={e => handleSelectAll(e.target.checked)}></Checkbox>
          </div>
        ),
        render: (_: any, record: TableDonViQuanLyDataType) => (
          <div className="custom-checkbox-cell" style={{height: 19, alignItems: "center", verticalAlign: "midle"}}>
            <FormInput
              className="!mb-0"
              component="checkbox"
              checked={record.is_checked === "1"}
              onChange={e => {
                if (!record.key) {
                  // console.warn("Record missing key:", record);
                  return;
                }
                handleInputChange(record.key, "is_checked", e.target.checked ? "1" : "0");
              }}
            />
          </div>
        ),
      };
    }
    return {
      ...column,
      ...(column.key && typeof column.title === "string" && column.key !== "stt" ? getColumnSearchProps(column.key, column.title) : {}),
      render: (text: string) => text || "", // Đảm bảo hiển thị chuỗi rỗng nếu text undefined
    };
  };

  // Render cột cho bảng menu
  const renderColumnMenu = (column: any) => {
    if (column.dataIndex === "is_checked") {
      return {
        ...column,
        title: (
          <div style={{height: 19, alignItems: "center", verticalAlign: "middle"}}>
            <Checkbox checked={isAllCheckedMenu} onChange={e => handleSelectAllMenu(e.target.checked)}></Checkbox>
          </div>
        ),
        render: (_: any, record: TableMenuNguoiDungDataType) => (
          <div className="custom-checkbox-cell" style={{height: 19, alignItems: "center", verticalAlign: "midle"}}>
            <FormInput
              className="!mb-0"
              component="checkbox"
              checked={record.is_checked === "1"}
              onChange={e => {
                if (!record.key) {
                  console.warn("Record missing key:", record);
                  return;
                }
                handleInputChangeMenu(record.key, "is_checked", e.target.checked ? "1" : "0");
              }}
            />
          </div>
        ),
      };
    }
    return {
      ...column,
      ...(column.key && typeof column.title === "string" && column.key !== "stt" ? getColumnSearchProps(column.key, column.title) : {}),
      render: (text: string) => text || "", // Đảm bảo hiển thị chuỗi rỗng nếu text undefined
    };
  };

  return (
    <Row>
      <Col span={12}>
        <Row>
          <Col span={8} style={{marginRight: 16}}>
            <Input placeholder="Tìm kiếm đơn vị quản lý" value={tableSearchText} onChange={handleTableSearch} style={{marginBottom: 16}} prefix={<SearchOutlined />} />
          </Col>
          <Col span={8}>
            <Select
              allowClear
              style={{width: "100%"}}
              placeholder="Lọc theo đối tác"
              value={selectedDoiTac}
              onChange={value => setSelectedDoiTac(value)}
              options={listDoiTac.map(dt => ({label: dt.ten, value: dt.ma}))}
            />
          </Col>
        </Row>

        <Table<TableDonViQuanLyDataType>
          className="no-header-border-radius pr-2"
          {...defaultTableProps}
          style={{borderBottom: "1px solid #f0f0f0", marginBottom: "1rem"}}
          loading={loading}
          onRow={record => ({
            style: {cursor: loading ? "progress" : "pointer"},
            onClick: () => handleRowClick(record),
          })}
          columns={(donViQuanLyColumns || []).map(renderColumn)}
          // dataSource={dataSource.filter(item => Object.values(item).some(value => typeof value === "string" && value.toLowerCase().includes(tableSearchText.toLowerCase())))}
          dataSource={filteredDonViQuanLy}
          pagination={false}
          scroll={{y: 220}}
        />
      </Col>
      <Col span={12} className="table-right w-50">
        <Row>
          <Col span={8} style={{marginRight: 16}}>
            <Input placeholder="Tìm kiếm menu" value={menuSearchText} onChange={handleMenuSearch} style={{marginBottom: 16}} prefix={<SearchOutlined />} />
          </Col>
          <Col span={8}>
            <Select allowClear style={{width: "100%"}} placeholder="Lọc theo nhóm" value={selectedNhom} onChange={value => setSelectedNhom(value)} options={NHOM_MENU} />
          </Col>
        </Row>

        <Table<TableMenuNguoiDungDataType>
          className="no-header-border-radius"
          {...defaultTableProps}
          style={{borderBottom: "1px solid #f0f0f0", marginBottom: "1rem"}}
          loading={loading}
          onRow={record => ({
            style: {cursor: loading ? "progress" : "pointer"},
            onClick: () => handleRowMenuClick(record),
          })}
          columns={(phanQuyenMenuColumns || []).map(renderColumnMenu)} // Sử dụng phanQuyenMenuColumns
          // dataSource={dataSourceMenu.filter(item => Object.values(item).some(value => typeof value === "string" && value.toLowerCase().includes(menuSearchText.toLowerCase())))}
          dataSource={filteredMenu}
          pagination={false}
          scroll={{y: 220}}
        />
      </Col>
    </Row>
  );
};

export default TabDonViQuanLy;

// import React, {memo} from "react";
// import strings from "@src/assets/strings";
// import {FormInput, Typography} from "@src/components";
// import But<PERSON> from "@src/components/button";
// import {ROUTER_PATHS} from "@src/constants";
// import {Form, Space} from "antd";
// import {SignupFormConfigs} from "./index.configs";
// import {useSignupContext} from "./index.context";
// import {isEqual} from "lodash";

// const {email, password, confirmedPassword} = SignupFormConfigs;

// const SignupContent: React.FC = memo(() => {
//   const {onSubmit} = useSignupContext();

//   return (
//     <Form layout="vertical" onFinish={onSubmit}>
//       <FormInput {...email} />
//       <FormInput {...password} />
//       <FormInput {...confirmedPassword} />
//       <Space>
//         <Button htmlType="submit">{strings().label_sign_up}</Button>
//         <Typography
//           type="link"
//           linkProps={{
//             href: `${ROUTER_PATHS.LOGIN}`,
//           }}>
//           {`${strings().label_has_account} ${strings().label_click_here_to_sign_in}`}
//         </Typography>
//       </Space>
//     </Form>
//   );
// }, isEqual);

// SignupContent.displayName = "SignupContent";

// export default SignupContent;

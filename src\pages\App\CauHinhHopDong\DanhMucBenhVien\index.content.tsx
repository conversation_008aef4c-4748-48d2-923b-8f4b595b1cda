/**
 * T<PERSON><PERSON> dụng: <PERSON><PERSON><PERSON> thị giao diện bảng danh sách bệnh viện với tìm kiếm, phân trang, CRUD
 */
import React, {memo, useCallback, useMemo, useRef, useState} from "react";
import {Col, Form, Input, InputRef, Row, Table, TableColumnType, Tag} from "antd";
import {FilterDropdownProps} from "antd/es/table/interface";
import {PlusCircleOutlined, SearchOutlined} from "@ant-design/icons";

import {Button, FormInput, Highlighter, TableFilterDropdown} from "@src/components";
import {COLOR_PALETTE, ID_PAGE} from "@src/constants";
import {IFormInput, ReactQuery} from "@src/@types";
import {defaultPaginationTableProps, defaultTableProps, fillRowTableEmpty} from "@src/hooks";

import {useDanhMucBenhVienContext} from "./index.context";
import {
  FormTimKiemDanhMuc<PERSON>enhV<PERSON>, 
  tableBenhVien<PERSON>olumn, 
  TableBenhVienColumnDataType, 
  TableBenhVienColumnDataIndex, 
  radioItemTrangThaiBenhVienSelect,
  radioItemNhomBenhVienSelect
} from "./index.configs";
import {ModalChiTietBenhVien, IModalChiTietBenhVienRef} from "./Component";
import "./index.default.scss";

type DataIndex = keyof TableBenhVienColumnDataType;

//Destructuring form search values
const {
  nd_tim,
  nhom_bv,
  tinh_thanh,
  // tk_ngan_hang,
  // tk_chi_nhanh,
  trang_thai,
} = FormTimKiemDanhMucBenhVien;

const DanhMucBenhVienContent: React.FC = memo(() => {
  const {listBenhVien, listTinhThanh,listNganHang, listChiNhanhNganHang, loading, tongSoDong, filterParams,getListNganHang,getListChiNhanhNganHang, getChiTietBenhVien, setFilterParams} = useDanhMucBenhVienContext();

  //Quản lý từ khóa tìm kiếm trong cột và cột đang được tìm kiếm
  const [searchText, setSearchText] = useState("");
  const [searchedColumn, setSearchedColumn] = useState<DataIndex | "">("");

  //State để lưu current search parameters
  const [currentSearchParams, setCurrentSearchParams] = useState<ReactQuery.ITimKiemPhanTrangDanhMucBenhVienParams>({});

  //===== REFS =====
  const searchInput = useRef<InputRef>(null);
  const modalChiTietRef = useRef<IModalChiTietBenhVienRef>(null);

  //Tác dụng: Tạo danh sách tỉnh thành cho dropdown filter, chỉ hiển thị các tỉnh đang hoạt động
  const dropdownOptionsTinhThanh = useMemo(() => {
    if (!listTinhThanh || !Array.isArray(listTinhThanh)) {
      return [{ma: "", ten: "Chọn tỉnh/thành phố"}];
    }
    
    const allOption = [{ma: "", ten: "Tất cả"}];
    const validProvinces = listTinhThanh
      .filter(item => {
        const hasValidData = item && item.ma && item.ten;
        if (!hasValidData) return false;
        
        const trangThai = item.trang_thai?.toString();
        const isActive = trangThai === "1" || trangThai === "D" || trangThai === "Đang sử dụng" || !trangThai;
        
        return hasValidData && isActive;
      })
      .map(item => ({
        ma: item.ma?.toString() || "",
        ten: item.ten?.toString() || "",
      }));
    
    return [...allOption, ...validProvinces];
  }, [listTinhThanh]);


  const dropdownOptionsNganHang = useMemo(() => {
    if (!listNganHang || !Array.isArray(listNganHang)) {
      return [{ma: "", ten: "Chọn Ngân hàng"}];
    }
    
    const allOption = [{ma: "", ten: "Tất cả"}];
    const validProvinces = listNganHang
      .filter(item => {
        const hasValidData = item && item.ma && item.ten;
        if (!hasValidData) return false;
        
        const trangThai = item.trang_thai?.toString();
        const isActive = trangThai === "1" || trangThai === "D" || trangThai === "Đang sử dụng" || !trangThai;
        
        return hasValidData && isActive;
      })
      .map(item => ({
        ma: item.ma?.toString() || "",
        ten: item.ten?.toString() || "",
      }));
    
    return [...allOption, ...validProvinces];
  }, [listNganHang]);

  const dropdownOptionsChiNhanhNganHang = useMemo(() => {
    if (!listChiNhanhNganHang || !Array.isArray(listChiNhanhNganHang)) {
      return [{ma: "", ten: "Chọn Chi nhánh ngân hàng"}];
    }
    
    const allOption = [{ma: "", ten: "Tất cả"}];
    const validProvinces = listChiNhanhNganHang
      .filter(item => {
        const hasValidData = item && item.ma && item.ten;
        if (!hasValidData) return false;
        
        const trangThai = item.trang_thai?.toString();
        const isActive = trangThai === "1" || trangThai === "D" || trangThai === "Đang sử dụng" || !trangThai;
        
        return hasValidData && isActive;
      })
      .map(item => ({
        ma: item.ma?.toString() || "",
        ten: item.ten?.toString() || "",
      }));
    
    return [...allOption, ...validProvinces];
  }, [listChiNhanhNganHang]);

  //Tác dụng: Tạo data table với STT và xử lý ngày áp dụng
  const dataTableWithSTT = useMemo(() => {
    try {
      const currentPage = filterParams?.trang || 1;
      const currentPageSize = (filterParams?.so_dong || defaultPaginationTableProps.defaultPageSize) as number;
      
      const tableData =
        listBenhVien?.map((item, index) => {
          return {
            ...item,
            key: item.ma ? `${item.ma}` : `row-${index}`,
            sott: (currentPage - 1) * currentPageSize + index + 1,
          };
        }) || [];
      
      const arrEmptyRow = fillRowTableEmpty(tableData.length, currentPageSize);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      return [];
    }
  }, [listBenhVien, filterParams?.trang, filterParams?.so_dong]);

  //Tác dụng: Xử lý submit form tìm kiếm và cập nhật filter parameters
  const onSearchApi = useCallback(
    (values: ReactQuery.ITimKiemPhanTrangDanhMucBenhVienParams) => {
      setCurrentSearchParams(values);
      
      setFilterParams({
        nd_tim: values.nd_tim || "",
        nhom_bv: values.nhom_bv || "",
        tinh_thanh: values.tinh_thanh || "",
        // tk_ngan_hang: values.tk_ngan_hang || "",
        // tk_chi_nhanh: values.tk_chi_nhanh || "",
        trang_thai: values.trang_thai || "",
        trang: 1,
        so_dong: filterParams?.so_dong || defaultPaginationTableProps.defaultPageSize,
      });
    },
    [setFilterParams, filterParams?.so_dong],
  );

  //Tác dụng: Xử lý thay đổi trang và số dòng hiển thị trong pagination
  const onChangePage = useCallback(
    (newPage: number, newPageSize: number) => {
      setFilterParams(prev => ({
        ...prev,
        trang: newPage,
        so_dong: newPageSize,
      }));
    },
    [setFilterParams],
  );

  //Tác dụng: Xử lý thay đổi tỉnh thành trong form search để load quận huyện tương ứng
  // const handleSearchTinhThanhChange = useCallback((value: any) => {
  //   //Khi chọn tỉnh thành khác, load danh sách quận huyện tương ứng
  //   if (value && value !== "") {
  //     getListQuanHuyen({ma_tinh: value});
  //   } else {
  //     //Nếu chọn "Tất cả", clear danh sách quận huyện
  //     getListQuanHuyen({ma_tinh: ""});
  //   }
  // }, [getListQuanHuyen]);


  //Tác dụng: Xử lý thay đổi Ngân hàng trong form search để load quận huyện tương ứng
  const handleSearchNganHangChange = useCallback((value: any) => {
    //Khi chọn tỉnh thành khác, load danh sách quận huyện tương ứng
    if (value && value !== "") {
      getListChiNhanhNganHang({ma_ngan_hang: value});
    } else {
      //Nếu chọn "Tất cả", clear danh sách quận huyện
      getListChiNhanhNganHang({ma_ngan_hang: ""});
    }
  }, [getListChiNhanhNganHang]);

  const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: DataIndex) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);

  const handleReset = useCallback(
    (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: DataIndex) => {
      clearFilters();
      setSearchText("");
      handleSearch([""], confirm, dataIndex);
    },
    [handleSearch],
  );

  //Tác dụng: Xử lý click vào row để hiển thị modal chi tiết bệnh viện
  const handleRowClick = useCallback(async (record: TableBenhVienColumnDataType) => {
  console.log("Clicked row:", record); // 

  if (record.key?.toString().includes("empty") || loading) {
    console.log("Bỏ qua vì row là empty hoặc đang loading");
    return;
  }

  try {
    if (!record.ma) {
      console.log("Không có mã bệnh viện");
      return;
    }

    if (!modalChiTietRef.current) {
      console.log("modalChiTietRef chưa được khởi tạo");
      return;
    }

    const response = await getChiTietBenhVien({ ma: record.ma });
    console.log("Chi tiết bệnh viện từ API:", response);

    if (response && Object.keys(response).length > 0) {
      try {
        modalChiTietRef.current.open(response);
        console.log("Đã mở modal");
      } catch (modalError) {
        console.error("Lỗi khi gọi open modal:", modalError);
      }
    } else {
      console.warn("API trả về không có dữ liệu");
    }
  } catch (error) {
    console.error("Lỗi khi gọi getChiTietBenhVien:", error);
  }
}, [getChiTietBenhVien, loading]);


  //Tác dụng: Mở modal để tạo mới bệnh viện
  const handleThemMoi = useCallback(() => {
    modalChiTietRef.current?.open();
  }, []);

  //Tác dụng: Reload danh sách sau khi lưu thành công
  const handleAfterSave = useCallback(() => {
    setFilterParams(prev => ({...prev}));
  }, [setFilterParams]);

  const renderFormInputColumn = useCallback(
    (props: IFormInput, span = 4) => (
      <Col span={span}>
        <FormInput {...props} />
      </Col>
    ),
    [],
  );

  //Tác dụng: Render form tìm kiếm ở header table
  const renderHeaderTableBenhVien = useCallback(
    () => (
      <Form
        layout="vertical"
        className="[&_.ant-form-item]:mb-0"
        onFinish={onSearchApi}>
        <Row gutter={16} align="bottom">
          {renderFormInputColumn(nd_tim, 4)}
          {renderFormInputColumn({...nhom_bv, options: radioItemNhomBenhVienSelect}, 3)}
          {renderFormInputColumn({...tinh_thanh, options: dropdownOptionsTinhThanh}, 3)}
          {/* {renderFormInputColumn({...tk_ngan_hang, options: dropdownOptionsNganHang, onChange: handleSearchNganHangChange}, 3)}
          {renderFormInputColumn({...tk_chi_nhanh, options: dropdownOptionsChiNhanhNganHang}, 3)} */}
          {renderFormInputColumn({...trang_thai, options: radioItemTrangThaiBenhVienSelect}, 3)}
          <Col span={2}>
            <Button type="primary" htmlType="submit" loading={loading} icon={<SearchOutlined />} block>
              Tìm kiếm
            </Button>
          </Col>
          <Col span={2}>
            <Button type="primary" block icon={<PlusCircleOutlined />} onClick={handleThemMoi}>
              Tạo mới
            </Button>
          </Col>
        </Row>
      </Form>
    ),
    [nd_tim, nhom_bv, tinh_thanh, trang_thai, dropdownOptionsTinhThanh,dropdownOptionsNganHang,dropdownOptionsChiNhanhNganHang, loading, onSearchApi, handleThemMoi, renderFormInputColumn],
  );

  //Tác dụng: Tạo cấu hình search cho các cột table
  const getColumnSearchProps = useCallback(
    (dataIndex: DataIndex, title: string): TableColumnType<TableBenhVienColumnDataType> => ({
      filterDropdown: ({setSelectedKeys, selectedKeys, confirm, clearFilters}) => (
        <TableFilterDropdown
          ref={searchInput}
          title={title}
          selectedKeys={selectedKeys}
          dataIndex={dataIndex}
          setSelectedKeys={setSelectedKeys}
          handleSearch={handleSearch}
          confirm={confirm}
          clearFilters={clearFilters}
          handleReset={handleReset}
        />
      ),
      filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? COLOR_PALETTE.blue[60] : undefined}} />,
      onFilter: (value, record) => record[dataIndex]?.toString().toLowerCase().includes((value as string).toLowerCase()) || false,
      onFilterDropdownOpenChange: (visible) => {
        if (visible) {
          setTimeout(() => searchInput.current?.select(), 100);
        }
      },
      render: (text, record) => {
        if (record.key?.toString().includes("empty")) return <span style={{height: '22px', display: 'inline-block'}}>&nbsp;</span>;
        
        if (dataIndex === "trang_thai_ten") {
          if (!text) return "";
          const color = text === "Đang sử dụng" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];
          return (
            <Tag color={color} className="text-[11px]">
              {text}
            </Tag>
          );
        }
        
        return searchedColumn === dataIndex ? (
          <Highlighter
            searchWords={[searchText]}
            textToHighlight={text ? text.toString() : ""}
          />
        ) : text !== undefined ? (
          text
        ) : (
          <Tag color={"transparent"} className="!text-white text-[11px]">
            {"\u00A0"}
          </Tag>
        );
      },
    }),
    [handleSearch, handleReset, searchText, searchedColumn],
  );

  //Tác dụng: Tạo columns table với tính năng search
  const columnsWithSearch = useMemo(() => {
    return tableBenhVienColumn?.map((col: any) => {
      if (col.dataIndex && ["ma", "ten", "mst", "dia_chi", "dthoai", "email", "ten_nhom_bv", "ten_loai", "tinh_thanh", "trang_thai_ten", "ngay_tao", "nguoi_tao"].includes(col.dataIndex)) {
        return {
          ...col,
          ...getColumnSearchProps(col.dataIndex as DataIndex, col.title as string),
        };
      }
      return col;
    });
  }, [getColumnSearchProps]);

  return (
    <div id={ID_PAGE.DANH_MUC_BENH_VIEN} className="[&_.ant-space]:w-full">
      <Table<TableBenhVienColumnDataType>
        {...defaultTableProps}
        dataSource={dataTableWithSTT}
        columns={columnsWithSearch}
        loading={loading}
        pagination={{
          ...defaultPaginationTableProps,
          current: filterParams?.trang || 1,
          pageSize: (filterParams?.so_dong || defaultPaginationTableProps.defaultPageSize) as number,
          total: tongSoDong,
          onChange: onChangePage,
          showQuickJumper: true,
          showTotal: (total, range) => {
            return `${range[0]}-${range[1]} của ${total} bản ghi`;
          },
        }}
        title={renderHeaderTableBenhVien}
        onRow={record => ({
          style: {cursor: loading ? "progress" : "pointer"},
          onClick: () => handleRowClick(record),
        })}
      />
      <ModalChiTietBenhVien
        ref={modalChiTietRef}
        onAfterSave={handleAfterSave}
        danhSachTinhThanh={dropdownOptionsTinhThanh}
        // danhSachQuanHuyen={listQuanHuyen}
        danhSachNganHang={dropdownOptionsNganHang}
        danhSachChiNhanhNganHang={dropdownOptionsChiNhanhNganHang}
      />
    </div>
  );
});

DanhMucBenhVienContent.displayName = "DanhMucBenhVienContent";
export default DanhMucBenhVienContent;

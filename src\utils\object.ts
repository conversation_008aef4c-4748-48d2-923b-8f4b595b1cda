import {LooseObject} from "@src/@types";
import dayjs from "dayjs";

export const isNumString = (str: string) => !isNaN(Number(str));

export const deepParse = (jsonString: any): any => {
  // if not stringified json rather a simple string value then JSON.parse will throw error
  // otherwise continue recursion
  if (typeof jsonString === "string") {
    if (isNumString(jsonString)) {
      // if a numeric string is received, return itself
      // otherwise JSON.parse will convert it to a number
      return jsonString;
    }
    try {
      return deepParse(JSON.parse(jsonString));
    } catch (err) {
      return jsonString;
    }
  } else if (Array.isArray(jsonString)) {
    // if an array is received, map over the array and deepParse each value
    return jsonString.map(val => deepParse(val));
  } else if (typeof jsonString === "object" && jsonString !== null) {
    // if an object is received then deepParse each element in the object
    // typeof null returns 'object' too, so we have to eliminate that
    return Object.keys(jsonString).reduce((obj: LooseObject, key) => {
      const val = jsonString[key];
      obj[key] = isNumString(val) ? val : deepParse(val);
      return obj;
    }, {});
  } else {
    // otherwise return whatever was received
    return jsonString;
  }
};

export const parseDateTime = (value: any): dayjs.Dayjs | null => {
  try {
    // Xử lý các trường hợp null, undefined, empty string
    if (value == null || value === "") return null;

    // Nếu đã là dayjs object, trả về luôn
    if (dayjs.isDayjs(value)) {
      return value.isValid() ? value : null;
    }

    // Nếu là Date object, convert sang dayjs
    if (value instanceof Date) {
      const converted = dayjs(value);
      return converted.isValid() ? converted : null;
    }

  // Nếu là string
  if (typeof value === "string") {
    const trimmed = value.trim();
    if (trimmed === "") return null;

    // Thử parse với format DD/MM/YYYY
    if (/^\d{2}\/\d{2}\/\d{4}$/.test(trimmed)) {
      const parsed = dayjs(trimmed, "DD/MM/YYYY");
      return parsed.isValid() ? parsed : null;
    }

    // Thử parse với format YYYY-MM-DD
    if (/^\d{4}-\d{2}-\d{2}$/.test(trimmed)) {
      const parsed = dayjs(trimmed, "YYYY-MM-DD");
      return parsed.isValid() ? parsed : null;
    }

    // Thử parse với format DD-MM-YYYY
    if (/^\d{2}-\d{2}-\d{4}$/.test(trimmed)) {
      const parsed = dayjs(trimmed, "DD-MM-YYYY");
      return parsed.isValid() ? parsed : null;
    }

    // Thử parse với format MM/DD/YYYY
    if (/^\d{2}\/\d{2}\/\d{4}$/.test(trimmed)) {
      const parsed = dayjs(trimmed, "MM/DD/YYYY");
      return parsed.isValid() ? parsed : null;
    }

    // Thử parse với format giờ HH:mm
    if (/^\d{2}:\d{2}$/.test(trimmed)) {
      const parsed = dayjs(trimmed, "HH:mm");
      return parsed.isValid() ? parsed : null;
    }

    // Thử parse với format ISO
    try {
      const parsed = dayjs(trimmed);
      return parsed.isValid() ? parsed : null;
    } catch (error) {
      console.warn("parseDateTime: Failed to parse ISO format:", trimmed, error);
      return null;
    }
  }

  // Nếu là number (định dạng YYYYMMDD)
  if (typeof value === "number") {
    const dateStr = value.toString();

    // Kiểm tra nếu là 8 chữ số (YYYYMMDD)
    if (dateStr.length === 8 && /^\d{8}$/.test(dateStr)) {
      const year = dateStr.slice(0, 4);
      const month = dateStr.slice(4, 6);
      const day = dateStr.slice(6, 8);

      const parsed = dayjs(`${year}-${month}-${day}`);
      return parsed.isValid() ? parsed : null;
    }

    // Kiểm tra nếu là 6 chữ số (YYMMDD)
    if (dateStr.length === 6 && /^\d{6}$/.test(dateStr)) {
      const year = "20" + dateStr.slice(0, 2);
      const month = dateStr.slice(2, 4);
      const day = dateStr.slice(4, 6);

      const parsed = dayjs(`${year}-${month}-${day}`);
      return parsed.isValid() ? parsed : null;
    }
  }

  return null;
  } catch (error) {
    console.warn("parseDateTime: Unexpected error:", error, "for value:", value);
    return null;
  }
};

//Hàm convert date time thành string với format tùy chỉnh
export const formatDateTime = (value: any, format = "DD/MM/YYYY"): string => {
  const parsed = parseDateTime(value);
  return parsed ? parsed.format(format) : "";
};

//Hàm convert date time thành number format YYYYMMDD
export const formatDateTimeToNumber = (value: any): number => {
  const parsed = parseDateTime(value);
  return parsed ? parseInt(parsed.format("YYYYMMDD")) : 0;
};

import {CommonExecuteResponse, useCommonExecute} from "@src/services/react-queries";
import React, {PropsWithChildren, useEffect, useMemo, useCallback} from "react";
import {useState} from "react";
import {BoMaCauHoiContextProps} from "./index.model";
import {BoMaCauHoiContext} from "./index.context";
import {ACTION_CODE} from "@src/constants";
import {ReactQuery} from "@src/@types";
import {message} from "antd";
// import {defaultFormValue} from "./index.configs";

const BoMaCauHoiProvider: React.FC<PropsWithChildren> = props => {
  const {children} = props;
  const mutateUseCommonExecute = useCommonExecute();
  const [listDoiTac, setListDoiTac] = useState<Array<CommonExecute.Execute.IDoiTac>>([]);
  const [danhSachCauHoiApDung, setDanhSachCauHoiApDung] = useState<Array<CommonExecute.Execute.ICauHoiApDung>>([]);
  const [danhSachCauHoi, setDanhSachCauHoi] = useState<Array<CommonExecute.Execute.ICauHoi>>([]);
  const [danhSachCauHoiCT, setDanhSachCauHoiCT] = useState<Array<CommonExecute.Execute.ICauHoiCT>>([]);
  const [tongSoDong, setTongSoDong] = useState<number>(0);
  const [selecteCauHoiApDung, setSelectedCauHoiApDung] = useState<number | null>(null);
  const [listNghiepVu, setListNghiepVu] = useState<Array<CommonExecute.Execute.IDanhMucNghiepVu>>([]);
  const [danhSachSanPhamPhanTrang, setDanhSachSanPham] = useState<Array<CommonExecute.Execute.IDanhMucSanPham>>([]);
  const [filterParams, setFilterParams] = useState<ReactQuery.ITimKiemPhanTrangDanhSachSanPhamParams & ReactQuery.IPhanTrang>({
    ma_doi_tac_ql: "",
    ma: "",
    ten: "",
    nv: "",
    trang_thai: "",
  });
  useEffect(() => {
    initData();
  }, []);
  const initData = () => {
    layDanhSachSanPhamPhanTrang(filterParams);
    getListDoiTac();
    getListNghiepVu();
  };
  useEffect(() => {
    layDanhSachSanPhamPhanTrang(filterParams);
  }, [filterParams]);
  /* ĐỐI TÁC */
  const getListDoiTac = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        actionCode: ACTION_CODE.LIET_KE_DANH_SACH_DOI_TAC,
      });
      setListDoiTac(response?.data?.map(item => ({...item, ten: item.ma + " - " + item.ten})));
    } catch (error) {
      console.log("getListDoiTac error ", error);
    }
  }, [mutateUseCommonExecute]);
  const getListNghiepVu = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        actionCode: ACTION_CODE.LIET_KE_DANH_SACH_NGHIEP_VU,
      });
      setListNghiepVu(response?.data as Array<CommonExecute.Execute.IDanhMucNghiepVu>);
    } catch (error) {
      console.log("getListNghiepVu error ", error);
    }
  }, [mutateUseCommonExecute]);

  //Câu hỏi áp dụng liệt kê
  const layDanhSachCauHoiApDung = useCallback(
    async (body: ReactQuery.ILietKeCauHoiApDungParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.LIET_KE_CAU_HOI_AP_DUNG,
        };
        console.log("params câu hỏi áp dụng", params);
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        console.log("response câu hỏi áp dụng", response);
        const data = Object.values(response.data);
        console.log("data", data);
        setDanhSachCauHoiApDung(data);
      } catch (error: any) {
        console.log("Lấy danh sách sản phẩm error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );
  const layDanhSachSanPhamPhanTrang = useCallback(
    async (body: ReactQuery.ITimKiemPhanTrangDanhSachSanPhamParams & ReactQuery.IPhanTrang) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_DS_SAN_PHAM,
        };

        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);

        const data = response.data.data;
        console.log("data", data);
        setDanhSachSanPham(data);
        setTongSoDong(response.data.tong_so_dong);
      } catch (error: any) {
        console.log("Lấy danh sách sản phẩm error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );
  //Lấy chi tiết 1 sản phẩm
  const layChiTietSanPham = useCallback(
    async (item: ReactQuery.IChiTietDanhMucSanPhamParams): Promise<CommonExecute.Execute.IDanhMucSanPham | null> => {
      try {
        const params = {
          ma: item.ma,
          ma_doi_tac_ql: item.ma_doi_tac_ql,
          nv: item.nv,
          actionCode: ACTION_CODE.GET_CHI_TIET_SAN_PHAM,
        };
        console.log("params", params);
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        console.log("responseData", responseData);
        return responseData.data as CommonExecute.Execute.IDanhMucSanPham;
      } catch (error: any) {
        console.log("layChiTietSanPham error ", error.message || error);
        return null;
      }
    },
    [mutateUseCommonExecute],
  );
  // //Cập nhật hoặc tạo mới 1 phòng ban
  const onUpdateCauHoiApDung = useCallback(
    async (body: ReactQuery.IUpdateCauHoiApDungParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.CAP_NHAT_CAU_HOI_AP_DUNG,
        };
        console.log("params cập nhật", params);

        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params as any);
        if (responseData && (responseData.data as unknown as number) === -1) {
          console.log("responseData cập nhật", responseData);
          message.success("Cập nhật thông tin thành công!");
          initData();
          return responseData.data as unknown as number; // Chuyển đổi responseData.data thành number
        }
      } catch (error: any) {
        console.log("Cập nhật phân cấp phê duyệt error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );
  //xóa câu hỏi áp dụng
  const onDeleteCauHoiApDung = useCallback(
    async (body: ReactQuery.IDeleteCauHoiApDungParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.XOA_CAU_HOI_AP_DUNG,
        };
        console.log("params Xóa CHAD", params);

        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        if (responseData && (responseData.data as unknown as number) === -1) {
          console.log("responseData Xóa", responseData);
          message.success("Xóa thông tin thành công!");
          initData();
          return responseData.data as unknown as number; // Chuyển đổi responseData.data thành number
        }
      } catch (error: any) {
        console.log("Xóa câu hỏi áp dụng error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );
  //liệt kê danh sách câu hỏi
  const layDanhSachCauHoi = useCallback(
    async (body: ReactQuery.ILietKeCauHoiParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.LIET_KE_CAU_HOI,
        };
        console.log("params câu hỏi ", params);
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        console.log("response câu hỏi ", response);
        const data = response.data;
        console.log("data", data);
        setDanhSachCauHoi(data);
        return data;
      } catch (error: any) {
        console.log("Lấy danh sách câu hỏi error ", error.message | error);
        return [];
      }
    },
    [mutateUseCommonExecute],
  );
  // //Cập nhật hoặc tạo mới 1 phòng ban
  const onUpdateCauHoi = useCallback(
    async (body: ReactQuery.IUpdateCauHoiParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.CAP_NHAT_CAU_HOI,
        };
        console.log("params cập nhật", params);

        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        if (responseData && (responseData.data as unknown as number) === -1) {
          console.log("responseData cập nhật", responseData);
          message.success("Cập nhật thông tin thành công!");
          initData();
          return responseData.data as unknown as number; // Chuyển đổi responseData.data thành number
        }
      } catch (error: any) {
        console.log("Cập nhật câu hỏi error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );
  //xóa câu hỏi
  const onDeleteCauHoi = useCallback(
    async (body: ReactQuery.IDeleteCauHoiParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.XOA_CAU_HOI,
        };
        console.log("params Xóa CHAD", params);

        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        if (responseData && (responseData.data as unknown as number) === -1) {
          console.log("responseData Xóa", responseData);
          message.success("Xóa thông tin thành công!");
          initData();
          return responseData.data as unknown as number; // Chuyển đổi responseData.data thành number
        }
      } catch (error: any) {
        console.log("Xóa câu hỏi  error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );
  //Lấy chi tiết câu hỏi
  const layChiTietCauHoi = useCallback(
    async (item: ReactQuery.IChiTietCauHoiParams): Promise<CommonExecute.Execute.ICauHoi | null> => {
      try {
        const params = {
          ...item,
          actionCode: ACTION_CODE.CHI_TIET_CAU_HOI,
        };
        console.log("params", params);
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        console.log("responseData", responseData);
        // Type cast để truy cập property lke
        const dataArray = (responseData.data as any).lke;
        const data = Array.isArray(dataArray) && dataArray.length > 0 ? dataArray[0] : null;
        // const data = responseData.data.lke as CommonExecute.Execute.ICauHoi;
        const dataCT = (responseData.data as any)?.ch_ct;
        setDanhSachCauHoiCT(dataCT);
        return data;
      } catch (error: any) {
        console.log("layChiTietCauHoi error ", error.message || error);
        return null;
      }
    },
    [mutateUseCommonExecute],
  );
  //liệt kê danh sách câu hỏi
  // const layDanhSachCauHoiCT = useCallback(
  //   async (body: ReactQuery.ILietKeCauHoiCTParams) => {
  //     try {
  //       const params = {
  //         ...body,
  //         actionCode: ACTION_CODE.LIET_KE_CAU_HOI_CT,
  //       };
  //       console.log("params câu hỏi ", params);
  //       const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
  //       console.log("response câu hỏi ", response);
  //       const data = response.data;
  //       console.log("data", data);
  //       setDanhSachCauHoiCT(data);
  //       return data;
  //     } catch (error: any) {
  //       console.log("Lấy danh sách câu hỏi error ", error.message | error);
  //       return [];
  //     }
  //   },
  //   [mutateUseCommonExecute],
  // );
  //khai báo giá trị của context để truyền vào Component
  const value = useMemo<BoMaCauHoiContextProps>(
    () => ({
      loading: mutateUseCommonExecute.isLoading,
      tongSoDong,
      // defaultFormValue,
      filterParams,
      danhSachSanPhamPhanTrang,
      listDoiTac,
      danhSachCauHoiApDung,
      danhSachCauHoi,
      danhSachCauHoiCT,
      selecteCauHoiApDung,
      setSelectedCauHoiApDung,
      setDanhSachCauHoiCT,
      layChiTietCauHoi,
      setDanhSachCauHoi,
      setDanhSachCauHoiApDung,
      onDeleteCauHoi,
      onUpdateCauHoi,
      layDanhSachCauHoi,
      onDeleteCauHoiApDung,
      onUpdateCauHoiApDung,
      layDanhSachCauHoiApDung,
      setFilterParams,
      layDanhSachSanPhamPhanTrang,
      layChiTietSanPham,
      getListDoiTac,
      listNghiepVu,
      getListNghiepVu,
    }),
    [
      danhSachSanPhamPhanTrang,
      tongSoDong,
      mutateUseCommonExecute,
      filterParams,
      danhSachCauHoiApDung,
      listDoiTac,
      danhSachCauHoi,
      danhSachCauHoiCT,
      selecteCauHoiApDung,
      listNghiepVu,
      getListNghiepVu,
      setSelectedCauHoiApDung,
      setDanhSachCauHoiCT,
      layChiTietCauHoi,
      setDanhSachCauHoi,
      setDanhSachCauHoiApDung,
      onDeleteCauHoi,
      onUpdateCauHoi,
      layDanhSachCauHoi,
      onDeleteCauHoiApDung,
      onUpdateCauHoiApDung,
      setFilterParams,
      layDanhSachSanPhamPhanTrang,
      layDanhSachCauHoiApDung,
      layChiTietSanPham,
      getListDoiTac,
    ],
  );

  return <BoMaCauHoiContext.Provider value={value}>{children}</BoMaCauHoiContext.Provider>;
};
export default BoMaCauHoiProvider;

import {ReactQuery} from "@src/@types";
import {Authen} from "@src/@types/Authentication";
import {ACTION_CODE, URL_API} from "@src/constants";
import {axiosInstance} from "..";

type DangNhapResponse = {
  data: Authen.Profile.IMetaData;
  output: any;
};

export const dangNhap = async (params: ReactQuery.ILoginParams = {tai_khoan: "", mat_khau: "", actionCode: ACTION_CODE.DANG_NHAP}): Promise<DangNhapResponse> => {
  return await axiosInstance.post(URL_API.LOGIN, params);
};

export const rereshAccessToken = async (params: ReactQuery.IRefreshAcessTokenParams = {token: "", refresh_token: ""}): Promise<any> => {
  try {
    const response: any = await axiosInstance.post(URL_API.REFRESH_ACCESS_TOKEN, params);
    return response;
  } catch (error) {
    console.log("rereshAccessToken error", error);
    return error;
  }
};

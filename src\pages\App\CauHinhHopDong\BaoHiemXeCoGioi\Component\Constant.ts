import {IFormInput, ReactQuery} from "@src/@types";
import {colWidthByKey, defaultTableColumnsProps} from "@src/hooks";
import {formatCurrencyUS, parseDateTime} from "@src/utils";
import {DatePickerProps, FormInstance, StepProps, TableProps, Tag} from "antd";
import {TimePickerProps} from "antd/lib";
import React from "react";

/** MODAL TÌM NHẬP HĐ XE */

export const initFormFields = (form: any, chiTietHopDongBaoHiemXe: any) => {
  if (!chiTietHopDongBaoHiemXe) return;

  const fields = Object.entries(chiTietHopDongBaoHiemXe).map(([name, value]) => {
    const DATE_TIME_FIELDS = ["ngay_cap", "ngay_hl", "ngay_kt", "gio_hl", "gio_kt"];
    if (DATE_TIME_FIELDS.includes(name)) {
      if (value) {
        return {
          name,
          value: parseDateTime(value),
        };
      }
    }
    return {
      name,
      value: value ?? null,
    };
  });

  form.setFields(fields);
};

//ĐỊNH NGHĨA CẤU HÌNH TABLE ĐỐI TÁC GỒM NHỮNG CỘT NÀO, THỨ TỰ CỘT SẼ HIỂN THỊ THEO THỨ TỰ KHAI BÁO Ở DƯỚI
// ĐỊNH NGHĨA Column RENDER RA TỪNG Ô TRONG CỘT
//đọc https://ant.design/components/table#column để biết thêm về column

//radio trong table
export const radioItemTrangThaiKhachHangTable = [
  {value: "Đang sử dụng", text: "Đang sử dụng"},
  {value: "Ngưng sử dụng", text: "Ngưng sử dụng"},
];

export const radioItemLoaiKhachHangTable = [
  {value: "Cá nhân", text: "Cá nhân"},
  {value: "Tổ chức", text: "Tổ chức"},
];

// FORM TÌM KIẾM KHÁCH HÀNG

export interface IFormTimKiemFieldsConfig {
  ma_doi_tac_ql: IFormInput;
  ma_chi_nhanh_ql: IFormInput;
  ma: IFormInput;
  ten: IFormInput;
  loai_kh: IFormInput;
  dthoai: IFormInput;
  cmt: IFormInput;
  mst: IFormInput;
  nd_tim: IFormInput;
}

export const ruleRequired = {
  required: true,
  message: "Thông tin bắt buộc",
};

export const FormTimKiemKhachHang: IFormTimKiemFieldsConfig = {
  ma_doi_tac_ql: {
    label: "Đối tác",
    name: "ma_doi_tac_ql",
    rules: [ruleRequired],
    optionFilterProp: "label", // tìm kiếm theo label
    autoClearSearchValue: true,
  },
  ma_chi_nhanh_ql: {
    label: "Chi nhánh",
    name: "ma_chi_nhanh_ql",
    autoClearSearchValue: true,
    allowClear: true,
    optionFilterProp: "label", // tìm kiếm theo label
    // rules: [ruleRequired],
  },
  ma: {
    label: "Mã",
    name: "ma",
    placeholder: "Mã khách hàng",
    // rules: [ruleRequired],
  },
  ten: {
    label: "Tên",
    name: "ten",
    placeholder: "Tên khách hàng",
    // rules: [ruleRequired],
  },
  loai_kh: {
    label: "Loại khách hàng",
    name: "loai_kh",
    placeholder: "Chọn loại khách hàng",
    // rules: [ruleRequired],
  },
  dthoai: {
    label: "Điện thoại",
    name: "dthoai",
    placeholder: "Nhập số điện thoại",
    // rules: [ruleRequired],
  },
  cmt: {
    label: "CCCD/CMT",
    name: "cccd",
    placeholder: "CCCD/CMT",
    // rules: [ruleRequired],
  },
  mst: {
    label: "Mã số thuế",
    name: "mst",
    placeholder: "Nhập mã số thuế",
    // rules: [ruleRequired],
  },
  nd_tim: {
    label: "Nội dung tìm",
    name: "nd_tim",
    placeholder: "Nhập nội dung tìm",
    // rules: [ruleRequired],
  },
};

export const radioItemLoaiKhachHangSelect = [
  {value: "", label: "Tất cả"},
  {value: "T", label: "Tổ chức"},
  {value: "C", label: "Cá nhân"},
];
export interface IModalTimKhachHangRef {
  open: () => void;
  close: () => void;
}

// MODAL TẠO MỚI HỢP ĐỒNG
export interface IModalThemHopDongRef {
  open: (data?: CommonExecute.Execute.IChiTietChucDanh) => void;
  close: () => void;
}

export interface IFormTaoMoiHopDongFieldsConfig {
  ma_doi_tac_ql: IFormInput; // Đối tác cấp đơn
  ma_chi_nhanh_ql: IFormInput; //Chi nhánh cấp đơn
  phong_ql: IFormInput; //phòng ban
  ma_kh: IFormInput; //khách hàng
  kieu_hd: IFormInput; //kiểu hợp đồng
  so_hd_g: IFormInput; //Số HD gốc
  ma_cb_ql: IFormInput; //cán bộ quản lý
  ngay_cap: IFormInput & DatePickerProps; //ngày cấp
  gio_hl: IFormInput & TimePickerProps; //giờ hiệu lực
  ngay_hl: IFormInput & DatePickerProps; //ngày hiệu lực
  gio_kt: IFormInput & TimePickerProps; //giờ kết thúc
  ngay_kt: IFormInput & DatePickerProps; //ngày kết thúc
  ma_sp: IFormInput; //sản phẩm
  nv: IFormInput; //sản phẩm
  ma_ctbh: IFormInput; //chương trình bảo hiểm
  pt_kt: IFormInput; //phương thức khai thác
  daily_kt: IFormInput; //đại lý khai thác
  ma_nha_tpa: IFormInput; //đơn vị bồi thường - chưa có tham số
  vip: IFormInput; //hợp đồng VIP
  so_hd: IFormInput; //hợp đồng VIP
}

export const FormTaoMoiHopDongBaoHiemXeCoGioi: IFormTaoMoiHopDongFieldsConfig = {
  ma_doi_tac_ql: {
    component: "select",
    label: "Đối tác cấp đơn",
    name: "ma_doi_tac_ql",
    placeholder: "Chọn đối tác cấp đơn",
    rules: [ruleRequired],
  },
  ma_chi_nhanh_ql: {
    component: "select",
    label: "Chi nhánh cấp đơn",
    name: "ma_chi_nhanh_ql",
    placeholder: "Chọn chi nhánh cấp đơn",
    rules: [ruleRequired],
    // notFoundContent: "Vui lòng chọn Đối tác cấp đơn",
  },
  phong_ql: {
    component: "select",
    label: "Phòng ban",
    name: "phong_ql",
    placeholder: "Chọn phòng ban",
    // notFoundContent: "Vui lòng chọn Chi nhánh cấp đơn",
    rules: [ruleRequired],
  },
  ma_kh: {
    component: "select",
    label: "Khách hàng",
    name: "ma_kh",
    open: false,
    placeholder: "Chọn khách hàng",
    rules: [ruleRequired],
  },
  kieu_hd: {
    component: "select",
    label: "Kiểu hợp đồng",
    name: "kieu_hd",
    placeholder: "Chọn kiểu hợp đồng",
    rules: [ruleRequired],
  },
  so_hd_g: {
    component: "input",
    label: "Số hợp đồng gốc",
    name: "so_hd_g",
    placeholder: "Nhập số hợp đồng gốc",
    rules: [ruleRequired],
  },
  ma_cb_ql: {
    component: "select",
    label: "Cán bộ quản lý",
    name: "ma_cb_ql",
    placeholder: "Chọn cán bộ quản lý",
    rules: [ruleRequired],
    filterOption: false, //tắt lọc nội bộ
    open: false,
  },
  ngay_cap: {
    component: "date-picker",
    label: "Ngày cấp",
    name: "ngay_cap",
    placeholder: "Chọn ngày cấp",
    className: "w-full",
    rules: [ruleRequired],
  },
  gio_hl: {
    component: "time-picker",
    label: "Giờ hiệu lực",
    name: "gio_hl",
    placeholder: "Chọn giờ hiệu lực",
    className: "w-full",
    // rules: [ruleRequired],
  },
  ngay_hl: {
    component: "date-picker",
    label: "Ngày hiệu lực",
    name: "ngay_hl",
    placeholder: "Chọn ngày hiệu lực",
    className: "w-full",
    rules: [ruleRequired],
  },
  gio_kt: {
    component: "time-picker",
    label: "Giờ kết thúc",
    name: "gio_kt",
    className: "w-full",
    placeholder: "Chọn giờ kết thúc",
    // rules: [ruleRequired],
  },
  ngay_kt: {
    component: "date-picker",
    label: "Ngày kết thúc",
    name: "ngay_kt",
    className: "w-full",
    placeholder: "Chọn ngày kết thúc",
    rules: [ruleRequired],
  },
  ma_sp: {
    component: "select",
    label: "Sản phẩm",
    name: "ma_sp",
    placeholder: "Chọn sản phẩm",
    rules: [ruleRequired],
  },
  nv: {
    component: "select",
    label: "Nghiệp vụ",
    name: "nv",
    placeholder: "Chọn nghiệp vụ",
    rules: [ruleRequired],
  },
  ma_ctbh: {
    component: "select",
    label: "Chương trình bảo hiểm",
    name: "ma_ctbh",
    placeholder: "Chọn chương trình bảo hiểm",
    rules: [ruleRequired],
  },
  pt_kt: {
    component: "select",
    label: "Phương thức khai thác",
    name: "pt_kt",
    placeholder: "Chọn phương thức khai thác",
    rules: [ruleRequired],
  },
  daily_kt: {
    component: "select",
    label: "Đại lý khai thác",
    name: "daily_kt",
    placeholder: "Chọn đại lý khai thác",
    rules: [ruleRequired],
    open: false,
  },
  ma_nha_tpa: {
    component: "select",
    label: "Đơn vị bồi thường",
    name: "ma_nha_tpa",
    placeholder: "Chọn đơn vị bồi thường",
  },
  vip: {
    component: "select",
    label: "Hợp đồng VIP",
    name: "vip",
    placeholder: "Chọn hợp đồng VIP",
    // rules: [ruleRequired],
  },
  so_hd: {
    component: "input",
    label: "Số hợp đồng",
    name: "so_hd",
    placeholder: "Nhập số hợp đồng",
    rules: [ruleRequired],
  },
};
export interface IFormTimKiemPhanTrangDoiTuongBaoHiemXeCoGioiFieldsConfig {
  so_id?: IFormInput;
  gcn?: IFormInput;
  ten?: IFormInput;
  nd_tim?: IFormInput;
}

export const FormTimKiemDoiTuongBaoHiemXeCoGioi: IFormTimKiemPhanTrangDoiTuongBaoHiemXeCoGioiFieldsConfig = {
  so_id: {
    // label: "Phòng ban",
    name: "so_id",
    // placeholder: "Chọn phòng ban",
    // notFoundContent: "Vui lòng chọn Chi nhánh cấp đơn",
    // rules: [ruleRequired],
  },
  gcn: {
    component: "input",
    label: "Số GCN",
    name: "gcn",
    open: false,
    placeholder: "Nhập số GCN",
  },
  ten: {
    component: "input",
    label: "Tên đối tượng",
    name: "ten",
    placeholder: "Tên đối tượng",
  },
  nd_tim: {
    component: "input",
    label: "BSX/số khung/số máy",
    name: "nd_tim",
    placeholder: "Biển số xe/số khung/số máy",
  },
};

//option select nghiệp vụ
export const listKieuHopDongSelect = [
  {ma: "G", ten: "Hợp đồng gốc"},
  {ma: "B", ten: "Hợp đồng sửa đổi bố sung"},
  {ma: "T", ten: "Hợp đồng tái tục"},
];

//option select hợp đồng VIP
export const listHDVipSelect = [
  {ma: "VIP", ten: "VIP"},
  {ma: "", ten: "Không"},
];

export const STEP_THEM_HOP_DONG: StepProps[] = [
  {
    title: "Thông tin hợp đồng",
    // description: "Nhập thông tin hợp đồng",
  },
  {
    title: "Nhập đối tượng bảo hiểm",
    // description: "Nhập đối tượng bảo hiểm",
  },
  {
    title: "Thông tin thanh toán",
    // description: "Nhập thông tin thanh toán",
  },
  {
    title: "Thông tin cấu hình",
    // description: "Nhập thông tin cấu hình",
  },
  {
    title: "Phê duyệt hợp đồng",
    // description: "Nhập thông tin cấu hình",
  },
];

export interface IFormTimKiemDanhMucDaiLyFieldsConfig {
  ma_doi_tac_ql: IFormInput;
  ma: IFormInput;
  ten: IFormInput;
  loai: IFormInput;
  trang_thai: IFormInput;
}
// defaultFormValue tìm kiếm phân trang đại lý
export const defaultFormValue: ReactQuery.ITimKiemPhanTrangDanhSachDaiLyParams = {
  ma_doi_tac_ql: "",
  ma: "",
  ten: "",
  loai: "",
  trang_thai: "",
  trang: 1,
  so_dong: 10,
};

export const FormTimKiemPhanTrangKhachHangConfigs = {
  nd_tim: {
    component: "input",
    name: "nd_tim",
    placeholder: "Thông tin khách hàng",
    label: "Thông tin khách hàng", // cho label vào thì sẽ thành input with label
  },
  ma_doi_tac_ql: {
    component: "select",
    name: "ma_doi_tac_ql",
    label: "Đối tác cấp đơn",
    placeholder: "Chọn đối tác",
    rules: [ruleRequired],
    disabled: true,
  },
  ma_chi_nhanh_ql: {
    component: "select",
    name: "ma_chi_nhanh_ql",
    label: "Chi nhánh cấp đơn",
    placeholder: "Chọn chi nhánh",
    rules: [ruleRequired],
    disabled: true,
  },
  loai_kh: {
    component: "select",
    name: "loai_kh",
    label: "Loại khách hàng",
    placeholder: "Chọn loại khách hàng",
  },
};
export const radioItemTrangThaiDaiLy = [
  {value: "Đang sử dụng", text: "Đang sử dụng"},
  {value: "Ngừng sử dụng", text: "Ngừng sử dụng"},
];

export const dataOptionLoaiKhachHang = [
  {ma: "C", ten: "Cá nhân"},
  {ma: "T", ten: "Tổ chức"},
];

export interface TableKhachHangDataType {
  key: string;
  sott?: number;
  ten?: string;
  ten_loai_kh?: string;
  ma?: string;
  dchi?: string;
  mst?: string;
  dthoai?: string;
  trang_thai_ten?: string;
}

export const dataKhachHangColumns: TableProps<TableKhachHangDataType>["columns"] = [
  {...defaultTableColumnsProps, title: "STT", dataIndex: "sott", key: "sott", width: colWidthByKey.sott},
  {...defaultTableColumnsProps, title: "Khách hàng", dataIndex: "ten_loai_kh", key: "ten_loai_kh", width: 120},
  {...defaultTableColumnsProps, title: "Mã khách hàng", dataIndex: "ma", key: "ma", width: 120},
  {...defaultTableColumnsProps, title: "Tên", dataIndex: "ten", key: "ten", width: 400, align: "left", ellipsis: true},
  {...defaultTableColumnsProps, title: "Địa chỉ", dataIndex: "dchi", key: "dchi", width: 400, ellipsis: true, align: "left"},
  {...defaultTableColumnsProps, title: "Điện thoại", dataIndex: "dthoai", key: "dthoai", width: 120},
  {...defaultTableColumnsProps, title: "Email", dataIndex: "email", key: "email", width: 260, ellipsis: true},
  {...defaultTableColumnsProps, title: "CMT/CCCD", dataIndex: "cmt", key: "cmt", width: 140},
  {...defaultTableColumnsProps, title: "Mã số thuế", dataIndex: "mst", key: "mst", width: 140},
  {...defaultTableColumnsProps, title: "Đối tác", dataIndex: "ma_doi_tac", key: "ma_doi_tac", width: 200},
  {...defaultTableColumnsProps, title: "Đơn vị", dataIndex: "ma_chi_nhanh_ql", key: "ma_chi_nhanh_ql", width: 200},
  {...defaultTableColumnsProps, title: "Trạng thái", dataIndex: "trang_thai_ten", key: "trang_thai_ten", width: 120},
];

export const defaultFormTimKiemPhanTrangKhachHang: ReactQuery.ILayDanhSachKhachHangPhanTrangParams = {
  ma_doi_tac_ql: "",
  ma_chi_nhanh_ql: "",
  loai_kh: "",
  nd_tim: "",
  trang: 1,
  so_dong: 10,
};

//MODAL ĐẠI LÝ CONFIG
export interface TableDanhMucDaiLyDataType {
  key: string;
  stt?: number;
  ma?: string;
  ma_doi_tac_ql?: string;
  doi_tac_ql_ten_tat?: string;
  ten?: string;
  doi_tac_ql?: string;
  loai?: string;
  ten_loai?: string;
  ten_dd?: string;
  so_dien_thoai?: string;
  email?: string;
  mst?: string;
  cmt?: string;
  ngay_tao?: string;
  nguoi_tao?: string;
  ngay_cap_nhat?: string;
  nguoi_cap_nhat?: string;
  trang_thai_ten?: string;
}

// ĐỊNH NGHĨA Column RENDER RA TỪNG Ô TRONG CỘT
//đọc https://ant.design/components/table#column để biết thêm về column
export const danhMucDaiLyColumns: TableProps<TableDanhMucDaiLyDataType>["columns"] = [
  {...defaultTableColumnsProps, title: "STT", dataIndex: "stt", key: "stt", width: colWidthByKey.sott},
  {...defaultTableColumnsProps, title: "Mã", dataIndex: "ma", key: "ma", width: 200},
  {...defaultTableColumnsProps, title: "Tên", dataIndex: "ten", key: "ten", width: 300, align: "left"},
  {...defaultTableColumnsProps, title: "Đối tác", dataIndex: "doi_tac_ql_ten_tat", key: "doi_tac_ql_ten_tat", width: 300},
  {...defaultTableColumnsProps, title: "Loại đại lý", dataIndex: "ten_loai", key: "ten_loai", width: 150},
  {...defaultTableColumnsProps, title: "Tên đại diện", dataIndex: "nguoi_dd", key: "nguoi_dd", width: 300},
  {...defaultTableColumnsProps, title: "Số điện thoại", dataIndex: "dthoai_dd", key: "dthoai_dd", width: 120},
  {...defaultTableColumnsProps, title: "Email", dataIndex: "email_dd", key: "email_dd", width: 260},
  {...defaultTableColumnsProps, title: "Mã số thuế", dataIndex: "mst_dd", key: "mst_dd", width: 140},
  {...defaultTableColumnsProps, title: "CMT/CCCD", dataIndex: "cmt_dd", key: "cmt_dd", width: 140},
  {...defaultTableColumnsProps, title: "Ngày tạo", dataIndex: "ngay_tao", key: "ngay_tao", width: 140},
  {...defaultTableColumnsProps, title: "Người tạo", dataIndex: "nguoi_tao", key: "nguoi_tao", width: 140},
  {...defaultTableColumnsProps, title: "Ngày cập nhật", dataIndex: "ngay_cap_nhat", key: "ngay_cap_nhat", width: 140},
  {...defaultTableColumnsProps, title: "Người cập nhật", dataIndex: "nguoi_cap_nhat", key: "nguoi_cap_nhat", width: 140},
  {...defaultTableColumnsProps, title: "Trạng Thái", dataIndex: "trang_thai_ten", key: "trang_thai_ten", width: 140},
];
//Form
// MODAL ĐẠI LÝ KHAI THÁC
export interface IFormTimKiemDanhMucDaiLyFieldsConfig {
  ma_doi_tac_ql: IFormInput;
  ma: IFormInput;
  ten: IFormInput;
  loai: IFormInput;
  // chi_nhanh: IFormInput;
  // nghiep_vu: IFormInput;
  trang_thai: IFormInput;
  // ngay_d: IFormInput;
  // ngay_c: IFormInput;
  // bien_xe: IFormInput;
  // nd: IFormInput;
}
export const FormTimKiemDanhMucDaiLy: IFormTimKiemDanhMucDaiLyFieldsConfig = {
  ma_doi_tac_ql: {
    component: "select",
    name: "ma_doi_tac_ql",
    label: "Đối tác",
    placeholder: "Chọn đối tác",
    // className: "!mb-0",
    disabled: true,
  },
  ma: {
    component: "input",
    name: "ma",
    label: "Mã đại lý",
    placeholder: "Mã đại lý",
    // className: "!mb-0",
  },
  ten: {
    component: "input",
    name: "ten",
    label: "Tên đại lý",
    placeholder: "Tên đại lý",
    // className: "!mb-0",
  },
  loai: {
    component: "select",
    name: "loai",
    label: "Loại đại lý",
    placeholder: "Chọn loại đại lý",
    // className: "!mb-0",
  },
  trang_thai: {
    component: "select",
    name: "trang_thai",
    label: "Trạng thái",
    placeholder: "Chọn trạng thái",
    // className: "!mb-0",
  },
};
// defaultFormValue tìm kiếm phân trang đại lý
export const defaultFormTimKiemPhanTrangDaiLyKhaiThac: ReactQuery.ITimKiemPhanTrangDanhSachDaiLyParams = {
  ma_doi_tac_ql: "",
  ma: "",
  ten: "",
  loai: "",
  trang_thai: "",
  trang: 1,
  so_dong: 10,
};

export const TRANG_THAI_CHI_TIET_DAI_LY = [
  {ten: "Đang sử dụng", ma: "D"},
  {ten: "Ngừng sử dụng", ma: "K"},
];
export const LOAI_DAI_LY = [
  {ten: "Đại lý tổ chức", ma: "T"},
  {ten: "Đại lý cá nhân", ma: "C"},
];
export const radioItemTrangThaiDaiLyTable = [
  {value: "Đang sử dụng", text: "Đang sử dụng"},
  {value: "Ngưng sử dụng", text: "Ngưng sử dụng"},
];
export const LOAI_DAI_LY_TK = [
  {ten: "Đại lý tổ chức", ma: "T"},
  {ten: "Đại lý cá nhân", ma: "C"},
];

/// MODAL CÁN BỘ QUẢN LÝ
export interface IFormTimKiemPhanTrangCanBoQuanLyFieldsConfig {
  ma_doi_tac_ql: IFormInput;
  ma_chi_nhanh_ql: IFormInput;
  phong_ql: IFormInput;
  ma: IFormInput;
  ten: IFormInput;
  cmt: IFormInput;
  dthoai: IFormInput;
  email: IFormInput;
  nd_tim: IFormInput;
  trang_thai: IFormInput;
}
export const FormTimKiemPhanTrangCanBoQuanLy: IFormTimKiemPhanTrangCanBoQuanLyFieldsConfig = {
  ma_doi_tac_ql: {
    component: "select",
    name: "ma_doi_tac_ql",
    label: "Đối tác",
    placeholder: "Chọn đối tác",
    rules: [ruleRequired],
    disabled: true,
  },
  ma_chi_nhanh_ql: {
    component: "select",
    name: "ma_chi_nhanh_ql",
    label: "Chi nhánh",
    placeholder: "Chọn chi nhánh",
  },
  phong_ql: {
    component: "select",
    name: "phong_ql",
    label: "Phòng ban",
    placeholder: "Chọn phòng ban",
  },
  ma: {
    component: "input",
    name: "ma",
    label: "Mã cán bộ",
    placeholder: "Mã cán bộ",
  },
  ten: {
    component: "input",
    name: "ten",
    label: "Tên cán bộ quản lý ",
    placeholder: "Tên cán bộ quản lý",
  },
  cmt: {
    component: "input",
    name: "cmt",
    label: "CMT/CCCD",
    placeholder: "CMT/CCCD",
  },
  dthoai: {
    component: "input",
    name: "dthoai",
    // label: "Số điện thoại",
    placeholder: "Số điện thoại",
    className: "!mb-1",
  },
  email: {
    component: "input",
    name: "email",
    // label: "Email",
    placeholder: "Email",
    className: "!mb-1",
  },
  nd_tim: {
    component: "input",
    name: "nd_tim",
    // label: "Nội dung tìm",
    placeholder: "Nội dung tìm",
    className: "!mb-1",
  },
  trang_thai: {
    component: "select",
    name: "trang_thai",
    // label: "Trạng thái",
    placeholder: "Chọn trạng thái",
    className: "!mb-1",
  },
};
// defaultFormValue tìm kiếm phân trang cán bộ ql
export const defaultFormTimKiemPhanTrangCanBoQuanLy = {
  ma_doi_tac_ql: "",
  ma_chi_nhanh_ql: "",
  phong_ql: "",
  ma: "",
  ten: "",
  cmt: "",
  dthoai: "",
  email: "",
  nd_tim: "",
  trang_thai: "",
  trang: 1,
  so_dong: 10,
};

export interface TableCanBoQuanLyDataType {
  key: string;
  sott?: number;
  ma?: string;
  ma_doi_tac_ql?: string;
  ten_doi_tac_ql?: string;
  ten?: string;
  dthoai?: string;
  cmt?: string;
  trang_thai_ten?: string;
}

// ĐỊNH NGHĨA Column RENDER RA TỪNG Ô TRONG CỘT
//đọc https://ant.design/components/table#column để biết thêm về column
export const listCanBoColumns: TableProps<TableCanBoQuanLyDataType>["columns"] = [
  {...defaultTableColumnsProps, title: "STT", dataIndex: "sott", key: "sott", width: 80},
  {...defaultTableColumnsProps, title: "Mã", dataIndex: "ma", key: "ma", width: 140},
  {...defaultTableColumnsProps, title: "Tên", dataIndex: "ten", key: "ten", width: 300, align: "left"},
  {...defaultTableColumnsProps, title: "Đối tác", dataIndex: "ten_doi_tac_ql", key: "ten_doi_tac_ql", width: 300},
  {...defaultTableColumnsProps, title: "Số điện thoại", dataIndex: "dthoai", key: "dthoai", width: 140},
  {...defaultTableColumnsProps, title: "Email", dataIndex: "email", key: "email", width: 260},
  {...defaultTableColumnsProps, title: "CMT/CCCD", dataIndex: "cmt", key: "cmt", width: 140},
  {...defaultTableColumnsProps, title: "Trạng Thái", dataIndex: "trang_thai_ten", key: "trang_thai_ten", width: 200},
];

export interface ThongTinHopDongXeStepProps {
  khachHangSelected: CommonExecute.Execute.IKhachHang;
  setKhachHangSelected: (data: CommonExecute.Execute.IKhachHang) => void;
  daiLyKhaiThacSelected: CommonExecute.Execute.IChiTietDanhMucDaiLy;
  setDaiLyKhaiThacSelected: (data: CommonExecute.Execute.IChiTietDanhMucDaiLy) => void;
  listDaiLyKhaiThac: Array<CommonExecute.Execute.IDanhMucDaiLy>;
  formNhapHopDongXe: FormInstance<any>;
  formNhapDoiTuongBaoHiemXe?: FormInstance<any>;
  initListDaiLyKhaiTac: (params: ReactQuery.ILietKeDanhSachDaiLyParams) => Promise<any>;
  canBoSeleceted: CommonExecute.Execute.IDoiTacNhanVien;
  setCanBoSelected: (data: CommonExecute.Execute.IDoiTacNhanVien) => void;
}

export interface ThongTinDoiTuongBaoHiemXeStepProps {
  formNhapDoiTuongBaoHiemXe: FormInstance<any>;
}
// export interface ThongTinKyThanhToanStepProps {
//   chiTietHopDongBaoHiem: CommonExecute.Execute.IHopDongXe;
// }
// export interface ThongTinCauHinhDongTaiBHProps {
//   chiTietHopDongBaoHiem: CommonExecute.Execute.IHopDongXe;
// }

//
export interface IFormTimKiemPhanTrangNguoiPheDuyetFieldsConfig {
  nd_tim?: IFormInput;
}

export const FormTimKiemNguoiPheDuyet: IFormTimKiemPhanTrangNguoiPheDuyetFieldsConfig = {
  nd_tim: {
    component: "input",
    label: "Nội dung tìm",
    name: "nd_tim",
    placeholder: "Mã/Tên người duyệt",
  },
};
export interface IFormTrinhDuyetFieldsConfig {
  nd_trinh?: IFormInput;
}

export const FormTrinhDuyet: IFormTrinhDuyetFieldsConfig = {
  nd_trinh: {
    component: "textarea",
    label: "Nội dung trình",
    name: "nd_trinh",
    placeholder: "Nhập nội dung trình",
    rules: [ruleRequired],
  },
};

export interface TableNguoiPheDuyetColumnDataType {
  ma_doi_tac?: string;
  nsd_duyet?: string;
  nhom_duyet?: string;
  ma_doi_tac_ql?: string;
  ma_chi_nhanh_ql?: string;
  nv?: string;
  ma_sp?: string;
  ngay_ad?: number;
  tien_tu?: number;
  tien_toi?: number;
  ten_nsd_duyet?: string;
  key?: string;
  sott?: number;
  bt?: number;
}

// ĐỊNH NGHĨA Column RENDER RA TỪNG Ô TRONG CỘT
//đọc https://ant.design/components/table#column để biết thêm về column
export const tableNguoiPheDuyetColumn: TableProps<TableNguoiPheDuyetColumnDataType>["columns"] = [
  {
    ...defaultTableColumnsProps,
    title: "STT ",
    dataIndex: "sott",
    key: "sott",
    width: colWidthByKey.sott,
    ellipsis: true,
  },
  {
    ...defaultTableColumnsProps,
    title: "Mã người duyệt",
    dataIndex: "nsd_duyet",
    key: "nsd_duyet",
    width: 150,
  },
  {
    ...defaultTableColumnsProps,
    title: "Tên người duyệt",
    dataIndex: "ten_nsd_duyet",
    key: "ten_nsd_duyet",
    width: 150,
    render: (text: any, record: any, index: number) => {
      // if (record.key && record.key.toString().includes("empty")) return "";
      return text !== undefined ? text : React.createElement(Tag, {color: "transparent", className: "!text-white text-[11px]"}, "");
    },
  },
  {
    ...defaultTableColumnsProps,
    title: "Chức danh",
    dataIndex: "ten_chuc_danh",
    key: "ten_chuc_danh",
    width: 120,
  },
  {
    ...defaultTableColumnsProps,
    title: "Số tiền phân cấp",
    dataIndex: "tien_toi",
    key: "tien_toi",
    align: "right",
    width: 120,
    render: (text: any) => {
      if (typeof text === "number") {
        return formatCurrencyUS(text);
      }
      return text;
    },
  },
  {
    ...defaultTableColumnsProps,
    title: "Đơn vị",
    dataIndex: "ten_chi_nhanh",
    key: "ten_chi_nhanh",
    width: 200,
  },
  {
    ...defaultTableColumnsProps,
    title: "Đối tác",
    dataIndex: "ten_doi_tac",
    key: "ten_doi_tac",
  },
];

import {createContext, useContext} from "react";

import {IDanhMucQuocGiaProvider} from "./index.model";

// Tạo React Context để chia sẻ state và methods giữa các component
export const DanhMucQuocGiaContext = createContext<IDanhMucQuocGiaProvider>({
  // Dữ liệu hiển thị
  listQuocGia: [],
  listChauLuc: [],
  listKhuVuc: [],
  tongSoDong: 0,
  loading: false,
  
  // Tham số filter và phân trang
  filterParams: {
    ma_chau_luc: "",
    ma_khu_vuc: "",           
    ma: "",
    ten: "",
    trang_thai: "",
    trang: 1,
    so_dong: 13,
  },
  
  // Actions
  getListQuocGia: async () => {},
  getListChauLuc: async () => {},
  getListKhuVuc: async () => {},
  getChiTietQuocGia: async () => ({} as CommonExecute.Execute.IDanhMucQuocGia),
  capNhatChiTietQuocGia: async () => {},
  setFilterParams: () => {},
});

// Chức năng: Custom hook để các component con có thể dễ dàng access context
// Tự động handle việc kiểm tra context existence và type safety
export const useDanhMucQuocGiaContext = () => useContext(DanhMucQuocGiaContext);

import dayjs from "dayjs";
import {isEqual} from "lodash";
import {forwardRef, memo, useImperativeHandle} from "react";
import {useHopDongConNguoiContext} from "@src/pages/App/BaoHiemConNguoi/HopDongConNguoi/index.context";
import {IPheDuyetHopDongStep1Ref, listHDVipSelect, listKieuHopDongSelect, PheDuyetHopDongStep1Props} from "./Constant";
import {useChiNhanh, useDoiTac, usePhongBan} from "@src/hooks";

const PheDuyetHopDongStep1Component = forwardRef<IPheDuyetHopDongStep1Ref, PheDuyetHopDongStep1Props>(({chiTietHopDong}: PheDuyetHopDongStep1Props, ref) => {
  console.log("🚀 ~ chiTietHopDong:", chiTietHopDong);
  useImperativeHandle(ref, () => ({}));

  const {listDoiTac} = useDoiTac();
  const {listPhongBan} = usePhongBan();
  const {listChiNhanh} = useChiNhanh();
  const {listSanPham, listChuongTrinhBaoHiem, listPhuongThucKhaiThac, listDonViBoiThuongTPA} = useHopDongConNguoiContext();

  // Helper function để tìm tên từ danh sách options
  const findOptionName = (value: string | undefined, options: any[], keyField = "ma", nameField = "ten") => {
    if (!value || !options) return value;
    const found = options.find(item => item[keyField] === value);
    return found ? found[nameField] : value;
  };

  // Helper function để format ngày
  const formatDate = (dateValue: string | number | undefined) => {
    if (!dateValue) return undefined;
    try {
      return dayjs(dateValue).format("DD/MM/YYYY");
    } catch {
      return String(dateValue);
    }
  };

  // Helper function để lấy tên VIP
  const getVipName = (vipValue: string | undefined) => {
    if (!vipValue) return undefined;
    const vipOption = listHDVipSelect.find(item => item.ma === vipValue);
    return vipOption ? vipOption.ten : vipValue;
  };

  // Helper function để lấy tên kiểu hợp đồng
  const getKieuHopDongName = (kieuValue: string | undefined) => {
    if (!kieuValue) return undefined;
    const kieuOption = listKieuHopDongSelect.find(item => item.ma === kieuValue);
    return kieuOption ? kieuOption.ten : kieuValue;
  };

  return (
    <div className="max-h-[70vh] overflow-y-auto p-4">
      {chiTietHopDong && (
        <div className="rounded border border-[#d9d9d9] bg-[#f6f6f6] p-3 shadow-sm">
          {(() => {
            // Chia thành 2 cột
            const col1 = [
              {label: "Số hợp đồng", value: chiTietHopDong.so_hd || "-"},
              {label: "Đối tác cấp đơn", value: chiTietHopDong.ten_doi_tac_ql || findOptionName(chiTietHopDong?.ma_doi_tac_ql, listDoiTac) || "-"},
              {label: "Chi nhánh cấp đơn", value: chiTietHopDong.ten_chi_nhanh_ql || findOptionName(chiTietHopDong?.ma_chi_nhanh_ql, listChiNhanh) || "-"},
              {label: "Phòng ban", value: chiTietHopDong.ten_phong_ql || findOptionName(chiTietHopDong?.phong_ql, listPhongBan) || "-"},
              {label: "Cán bộ quản lý", value: chiTietHopDong.ten_cb_ql || "-"},
              {label: "Khách hàng", value: chiTietHopDong.ten_kh || "-"},
              {label: "Số hợp đồng gốc", value: chiTietHopDong.so_hd_g || "-"},
              {label: "Kiểu hợp đồng", value: chiTietHopDong.kieu_hd_ten || getKieuHopDongName(chiTietHopDong?.kieu_hd) || "-"},
              {label: "Hợp đồng VIP", value: getVipName(chiTietHopDong.vip) || "Không"},
              {label: "Đại lý khai thác", value: chiTietHopDong.ten_daily_kt || "-"},
            ];

            const col2 = [
              {label: "Ngày cấp", value: chiTietHopDong.ngay_cap_date || formatDate(chiTietHopDong?.ngay_cap) || "-"},
              {label: "Giờ hiệu lực", value: chiTietHopDong.gio_hl || "-"},
              {label: "Ngày hiệu lực", value: chiTietHopDong.ngay_hl_date || formatDate(chiTietHopDong?.ngay_hl) || "-"},
              {label: "Giờ kết thúc", value: chiTietHopDong.gio_kt || "-"},
              {label: "Ngày kết thúc", value: chiTietHopDong.ngay_kt_date || formatDate(chiTietHopDong?.ngay_kt) || "-"},
              {label: "Sản phẩm", value: chiTietHopDong.ten_sp || findOptionName(chiTietHopDong?.ma_sp, listSanPham) || "-"},
              {label: "Chương trình bảo hiểm", value: chiTietHopDong.ten_ctbh || findOptionName(chiTietHopDong?.ma_ctbh, listChuongTrinhBaoHiem) || "-"},
              {label: "Phương thức khai thác", value: findOptionName(chiTietHopDong?.pt_kt, listPhuongThucKhaiThac) || "-"},
              {label: "Đơn vị bồi thường", value: findOptionName(chiTietHopDong?.ma_nha_tpa, listDonViBoiThuongTPA) || "-"},
            ];

            return (
              <div>
                <div className="grid grid-cols-2 gap-[100px]">
                  {/* Cột 1 */}
                  <div className="flex flex-col gap-4">
                    {col1.map((item: any, idx: number) => (
                      <div key={idx} className="flex items-center justify-between text-[13px]">
                        <span>{item.label}:</span>
                        <b>{item.value}</b>
                      </div>
                    ))}
                  </div>

                  {/* Cột 2 */}
                  <div className="flex flex-col gap-4">
                    {col2.map((item: any, idx: number) => (
                      <div key={idx} className="flex items-center justify-between text-[13px]">
                        <span>{item.label}:</span>
                        <b>{item.value}</b>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            );
          })()}
        </div>
      )}
    </div>
  );
});

PheDuyetHopDongStep1Component.displayName = "PheDuyetHopDongStep1Component";
export const PheDuyetHopDongStep1 = memo(PheDuyetHopDongStep1Component, isEqual);

import React, {PropsWithChildren, useCallback, useEffect, useMemo, useState} from "react";

import {XayDungGoiBaoHiemContext} from "./index.context";
import {IXayDungGoiBaoHiemContextProps} from "./index.model";
import {CommonExecuteResponse, useCommonExecute} from "@src/services/react-queries";
import {ReactQuery} from "@src/@types";
import {ACTION_CODE} from "@src/constants";
import {defaultParamsTimKiemPhanTrangGoiBaoHiem} from "./index.configs";
import dayjs from "dayjs";
import {message} from "antd";

const XayDungGoiBaoHiemProvider: React.FC<PropsWithChildren> = props => {
  const {children} = props;

  const mutateUseCommonExecute = useCommonExecute();
  const [filterGoiBaoHiemParams, setFilterGoiBaoHiemParams] = useState<ReactQuery.ITimKiemPhanTrangGoiBaoHiemParams>(defaultParamsTimKiemPhanTrangGoiBaoHiem);
  const [tongSoDongGoiBaoHiem, setTongSoDongGoiBaoHiem] = useState<number>(0);

  const [listDoiTac, setListDoiTac] = useState<Array<CommonExecute.Execute.IDoiTac>>([]);
  const [listSanPham, setListSanPham] = useState<Array<CommonExecute.Execute.ISanPham>>([]);
  const [listGoiBaoHiem, setLisGoiBaoHiem] = useState<Array<CommonExecute.Execute.IGoiBaoHiemConNguoi>>([]);
  const [listNguyenTe, setListNguyenTe] = useState<Array<{ma?: string; ten?: string}>>([]);

  const [listQuyenLoiRoot, setListQuyenLoiRoot] = useState<CommonExecute.Execute.IDanhSachBoMaQuyenLoi[]>([]);

  const [windowHeight, setWindowHeight] = useState(window.innerHeight);
  useEffect(() => {
    const onResize = () => setWindowHeight(window.innerHeight); //kích thước màn hình
    window.addEventListener("resize", onResize);
    return () => window.removeEventListener("resize", onResize);
  }, []);

  useEffect(() => {
    initData();
  }, []);

  const initData = async () => {
    const response = await getListDoiTac();
    if (!response) return;
    getListSanPham();
    getListNguyenTe();
  };

  /* ĐỐI TÁC */
  const getListDoiTac = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        actionCode: ACTION_CODE.LIET_KE_DANH_SACH_DOI_TAC,
      });
      if (response.data) {
        setListDoiTac(response?.data?.map(item => ({...item, ten: item.ma + " - " + item.ten})));
        return true;
      }
      return false;
    } catch (error) {
      console.log("getListDoiTac error ", error);
      return false;
    }
  }, [mutateUseCommonExecute]);

  /* NGUYÊN TỆ */
  const getListNguyenTe = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        actionCode: ACTION_CODE.LIET_KE_BO_MA_NGUYEN_TE,
      });
      if (response.data) {
        setListNguyenTe(response?.data.map(item => ({ma: item.ma, ten: item.ma + " - " + item.ten})));
      }
    } catch (error) {
      console.log("getListNguyenTe error ", error);
    }
  }, [mutateUseCommonExecute]);

  // TÌM KIẾM PHÂN TRANG GÓI BẢO HIỂM
  const timKiemPhanTrangGoiBaoHiem = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        ...filterGoiBaoHiemParams,
        ngay_ad: filterGoiBaoHiemParams.ngay_ad ? +dayjs(filterGoiBaoHiemParams.ngay_ad).format("YYYYMMDD") : undefined,
        actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_GOI_BAO_HIEM,
      });
      setLisGoiBaoHiem(response?.data?.data);
      setTongSoDongGoiBaoHiem(response?.data?.tong_so_dong);
    } catch (error) {
      console.log("timKiemPhanTrangGoiBaoHiem error ", error);
    }
  }, [mutateUseCommonExecute]);

  // GET LIST SẢN PHẨM
  const getListSanPham = useCallback(
    async (params?: ReactQuery.ILietKeSanPhamParams) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...params,
          actionCode: ACTION_CODE.LIET_KE_SAN_PHAM,
        });
        setListSanPham(response.data);
      } catch (error) {
        console.log("getListSanPham error ", error);
      }
    },
    [mutateUseCommonExecute],
  );

  // TÌM KIẾM PHÂN TRANG GÓI BẢO HIỂM
  const getChiTietGoiBaoHiem = useCallback(
    async (data: ReactQuery.IChiTietGoiBaoHiemParams) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          id: data.id,
          actionCode: ACTION_CODE.CHI_TIET_GOI_BAO_HIEM,
        });
        return response.data as CommonExecute.Execute.IChiTietGoiBaoHiemConNguoi;
      } catch (error) {
        console.log("getChiTietGoiBaoHiem error ", error);
        return {} as CommonExecute.Execute.IChiTietGoiBaoHiemConNguoi;
      }
    },
    [mutateUseCommonExecute],
  );

  const timKiemPhanTrangBoMaQuyenLoi = useCallback(
    async (body: ReactQuery.ITimKiemPhanTrangBoMaQuyenLoiParams) => {
      try {
        const params = {
          ...body,
          nv: "NG",
          trang_thai: "D",
          trang: 1,
          so_dong: 1000,
          actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_BO_MA_QUYEN_LOI,
        };
        const response = await mutateUseCommonExecute.mutateAsync(params);
        const data = response.data.data;
        setListQuyenLoiRoot(data);
      } catch (error: any) {
        console.log("timKiemPhanTrangBoMaQuyenLoi error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );

  const updateGoiBaoHiem = useCallback(
    async (params: ReactQuery.ICapNhatGoiBaoHiemParams) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...params,
          actionCode: ACTION_CODE.LUU_THONG_TIN_GOI_BAO_HIEM,
        });
        return response;
        // return response.data as CommonExecute.Execute.IChiTietGoiBaoHiemConNguoi;
      } catch (error) {
        console.log("updateGoiBaoHiem error ", error);
        // return {};
      }
    },
    [mutateUseCommonExecute],
  );

  // TÌM KIẾM BỆNH VIỆN PHÂN TRANG
  const timKiemBenhVienPhanTrang = useCallback(
    async (params: ReactQuery.ITimKiemBenhVienParams) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...params,
          actionCode: ACTION_CODE.TIM_KIEM_PTRANG_BENH_VIEN_XD_GOI_BH,
        });
        return response.data;
      } catch (error) {
        console.log("timKiemBenhVienPhanTrang error ", error);
        return {};
      }
    },
    [mutateUseCommonExecute],
  );

  // LẤY DANH SÁCH BỆNH VIỆN ĐÃ LƯU
  const layDanhSachBenhVienDaLuu = useCallback(
    async (params: ReactQuery.ILayDanhSachBenhVienDaLuuParams) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...params,
          actionCode: ACTION_CODE.LAY_DANH_SACH_BENH_VIEN_DA_LUU_XD_GOI_BH,
        });
        return response.data;
      } catch (error) {
        console.log("layDanhSachBenhVienDaLuu error ", error);
        return {};
      }
    },
    [mutateUseCommonExecute],
  );

  // LƯU CẤU HÌNH BỆNH VIỆN
  const luuCauHinhBenhVien = useCallback(
    async (params: ReactQuery.ILuuCauHinhBenhVienParams) => {
      try {
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync({
          ...params,
          actionCode: ACTION_CODE.LUU_CAU_HINH_BENH_VIEN_XD_GOI_BH,
        });

        if ((response.data as unknown as number) === -1) {
          message.success("Lưu cấu hình bệnh viện thành công!");
          return true;
        } else {
          return false;
        }
      } catch (error: any) {
        console.log("luuCauHinhBenhVien error ", error.message || error);
        message.error("Có lỗi xảy ra khi lưu cấu hình bệnh viện!");
        return false;
      }
    },
    [mutateUseCommonExecute],
  );

  // TÌM KIẾM MÃ BỆNH PHÂN TRANG CHO XÂY DỰNG GÓI BẢO HIỂM
  const timKiemMaBenhPhanTrangHopDongConNguoi = useCallback(
    async (params: ReactQuery.ITimKiemMaBenhHopDongConNguoiParams) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...params,
          actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_MA_BENH_XD_GOI_BH,
        });
        return response?.data || {};
      } catch (error) {
        console.log("timKiemMaBenhPhanTrangHopDongConNguoi error:", error);
        return {};
      }
    },
    [mutateUseCommonExecute],
  );

  // LẤY DANH SÁCH MÃ BỆNH ĐÃ LƯU CHO XÂY DỰNG GÓI BẢO HIỂM
  const layDanhSachMaBenhDaLuuHopDongConNguoi = useCallback(
    async (params: ReactQuery.ILayDanhSachMaBenhDaLuuHopDongConNguoiParams) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...params,
          actionCode: ACTION_CODE.LKE_DANH_SACH_MA_BENH_DA_LUU_XD_GOI_BH,
        });
        return response?.data || {};
      } catch (error) {
        console.log("layDanhSachMaBenhDaLuuHopDongConNguoi error:", error);
        return {};
      }
    },
    [mutateUseCommonExecute],
  );

  // LƯU CẤU HÌNH MÃ BỆNH CHO XÂY DỰNG GÓI BẢO HIỂM
  const luuCauHinhMaBenhHopDongConNguoi = useCallback(
    async (params: ReactQuery.ILuuCauHinhMaBenhGoiBaoHiemParams) => {
      try {
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync({
          ...params,
          actionCode: ACTION_CODE.LUU_CAU_HINH_MA_BENH_XD_GOI_BH,
        });

        if ((response.data as unknown as number) === -1) {
          message.success("Lưu cấu hình mã bệnh thành công!");
          return true;
        } else {
          return false;
        }
      } catch (error: any) {
        console.log("luuCauHinhMaBenhHopDongConNguoi error:", error.message || error);
        message.error("Có lỗi xảy ra khi lưu cấu hình mã bệnh!");
        return false;
      }
    },
    [mutateUseCommonExecute],
  );

  useEffect(() => {
    timKiemPhanTrangGoiBaoHiem();
  }, [filterGoiBaoHiemParams]);

  const value = useMemo<IXayDungGoiBaoHiemContextProps>(
    () => ({
      loading: mutateUseCommonExecute.isLoading,
      listDoiTac,
      listSanPham,
      listGoiBaoHiem,
      listNguyenTe,
      listQuyenLoiRoot,
      tongSoDongGoiBaoHiem,
      filterGoiBaoHiemParams,
      windowHeight,
      timKiemPhanTrangGoiBaoHiem,
      setFilterGoiBaoHiemParams,
      getChiTietGoiBaoHiem,
      timKiemPhanTrangBoMaQuyenLoi,
      updateGoiBaoHiem,

      // Bệnh viện API functions
      timKiemBenhVienPhanTrang,
      layDanhSachBenhVienDaLuu,
      luuCauHinhBenhVien,

      // Mã bệnh API functions cho Hợp đồng con người
      timKiemMaBenhPhanTrangHopDongConNguoi,
      layDanhSachMaBenhDaLuuHopDongConNguoi,
      luuCauHinhMaBenhHopDongConNguoi,

      // Wrapper functions for XayDungGoiBaoHiem API compatibility
      timKiemPhanTrangMaBenhGoiBH: async (params: ReactQuery.ITimKiemMaBenhGoiBaoHiemParams) => {
        try {
          const response = await mutateUseCommonExecute.mutateAsync({
            ...params,
            actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_MA_BENH_XD_GOI_BH,
          });
          return response.data;
        } catch (error) {
          console.log("timKiemPhanTrangMaBenhGoiBH error:", error);
          return {};
        }
      },
      layDanhSachMaBenhDaLuuGoiBaoHiem: async (params: ReactQuery.ILayDanhSachMaBenhDaLuuGoiBaoHiemParams) => {
        try {
          const response = await mutateUseCommonExecute.mutateAsync({
            ...params,
            actionCode: ACTION_CODE.LKE_DANH_SACH_MA_BENH_DA_LUU_XD_GOI_BH,
          });
          return response.data;
        } catch (error) {
          console.log("layDanhSachMaBenhDaLuuGoiBaoHiem error:", error);
          return {};
        }
      },
      luuCauHinhMaBenhGoiBaoHiem: luuCauHinhMaBenhHopDongConNguoi,
    }),
    [
      listDoiTac,
      listSanPham,
      listGoiBaoHiem,
      listNguyenTe,
      listQuyenLoiRoot,
      tongSoDongGoiBaoHiem,
      filterGoiBaoHiemParams,
      mutateUseCommonExecute,
      windowHeight,
      timKiemPhanTrangGoiBaoHiem,
      setFilterGoiBaoHiemParams,
      getChiTietGoiBaoHiem,
      timKiemPhanTrangBoMaQuyenLoi,
      updateGoiBaoHiem,
      timKiemBenhVienPhanTrang,
      layDanhSachBenhVienDaLuu,
      luuCauHinhBenhVien,
      timKiemMaBenhPhanTrangHopDongConNguoi,
      layDanhSachMaBenhDaLuuHopDongConNguoi,
      luuCauHinhMaBenhHopDongConNguoi,
    ],
  );

  return <XayDungGoiBaoHiemContext.Provider value={value}>{children}</XayDungGoiBaoHiemContext.Provider>;
};

export default XayDungGoiBaoHiemProvider;

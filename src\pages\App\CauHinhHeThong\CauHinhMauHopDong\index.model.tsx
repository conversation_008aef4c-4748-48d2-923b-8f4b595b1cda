import {ReactQuery} from "@src/@types";

/**
 * Interface định nghĩa các props và methods cung cấp bởi CauHinhMauHopDongProvider
 */
export interface ICauHinhMauHopDongProvider {
  // ===== STATE VALUES =====
  danhSachCauHinhMauHopDong: Array<CommonExecute.Execute.ICauHinhMauHopDong>;
  listSanPham: Array<CommonExecute.Execute.ISanPham>;
  tongSoDong: number;
  loading: boolean;
  filterParams: ReactQuery.ITimKiemPhanTrangCauHinhMauHopDongParams & ReactQuery.IPhanTrang;

  // ===== API FUNCTIONS =====
  searchCauHinhMauHopDong: () => Promise<void>;
  getChiTietCauHinhMauHopDong: (data: {bt: number}) => Promise<CommonExecute.Execute.ICauHinhMauHopDong>;
  capNhatChiTietCauHinhMauHopDong: (data: ReactQuery.ICapNhatCauHinhMauHopDongParams) => Promise<any>;
  getListSanPham: () => Promise<void>;
  
  // ===== STATE SETTERS =====
  setFilterParams: React.Dispatch<React.SetStateAction<ReactQuery.ITimKiemPhanTrangCauHinhMauHopDongParams & ReactQuery.IPhanTrang>>;
}

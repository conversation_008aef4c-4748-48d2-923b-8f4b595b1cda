/**
 * - Tạo React Context để chia sẻ state và methods giữa các component
 * - Định nghĩa interface cho context props
 * - C<PERSON> cấp custom hook để các component con dễ dàng access context
 */
import {createContext, useContext} from "react";

import {IQuanLyTinhThanhContextProps} from "./index.model";

// Tạo React Context để chia sẻ state và methods giữa các component
//khởi tạo giá trị mặc định
export const QuanLyTinhThanhContext = createContext<IQuanLyTinhThanhContextProps>({
  listTinhThanh: [],
  tongSoDong: 0,
  loading: false,
  filterParams: {},
  getListTinhThanh: async () => Promise.resolve(),
  getChiTietTinhThanh: async () => Promise.resolve({} as CommonExecute.Execute.IDanhMucTinhThanh),
  capNhatChiTietTinhThanh: async () => Promise.resolve(),
  setFilterParams: () => {},
});

// Custom hook để các component con có thể dễ dàng access context
export const useQuanLyTinhThanhContext = () => useContext(QuanLyTinhThanhContext);

import {FileOutlined, FolderOutlined} from "@ant-design/icons";
import {UploadFile} from "antd";

export interface IModalQuanLyFileCaNhanRef {
  open: () => void;
  close: () => void;
}
export interface IModalQuanLyFileCaNhanProps {
  onClickChonFile?: (fileSelected: File.GetFolder.IGetFolder[]) => void;
}

export const fileIcon = {
  FILE: <FileOutlined />,
  FOLDER: <FolderOutlined />,
};

export const arrExtensionImage = [".jpg", ".jpeg", ".png"];
export const arrExtensionPdf = [".pdf"];
export const arrExtensionExcel = [".xls", ".xlsx"];
export const arrExtensionWord = [".doc", ".docx"];
export const arrExtensionGif = [".pdf"];
export const arrExtensionXml = [".xml"];

export interface IGetFolderExtend {
  openPopover?: boolean;
  selected?: boolean;
}

export interface ModalProcessUploadRef {
  show: () => void;
}

export interface IModalProcessUploadProps {
  visible: boolean;
  listFileUpload: UploadFile[];
  onClose?: () => void;
}

import {createContext, useContext} from "react";
import {BaoHiemXeCoGioiContextProps} from "./index.model";
//createContext : khai báo 1 context mà component có thể cung cấp hoặc đọc
export const BaoHiemXeCoGioiContext = createContext<BaoHiemXeCoGioiContextProps>({
  //khởi tạo các giá trị mặc định, trong context này có giá trị là onSubmit
  danhSachHopDongBaoHiemXeCoGioi: [],
  danhSachKhachHang: [],
  loading: false,
  layDanhSachHopDongBaoHiemXePhanTrang: () => Promise.resolve({data: [], tong_so_dong: 0}),
  timKiemPhanTrangKhachHang: () => Promise.resolve(),
  timKiemPhanTrangDaiLy: () => Promise.resolve(),
  tongSoDong: 0,
  layChiTietHopDongBaoHiemXe: () => Promise.resolve(null),
  defaultFormValue: {},
  getListCanBoQuanLy: params => Promise.resolve({data: [], tong_so_dong: 0}),
  getListDaiLyKhaiThac: params => Promise.resolve({data: []}),
  searchKhachHang: params => Promise.resolve({tong_so_dong: 0, data: []}),
  danhSachDaiLy: [],
  updateHopDongXe: () => Promise.resolve({success: false, isNewContract: false}),
  updateDoiTuongBaoHiemXe: () => Promise.resolve(false),
  timKiemPhanTrangDoiTuongBaoHiemXe: () => Promise.resolve(),
  danhSachDoiTuongBaoHiemXe: [],
  tongSoDongDoiTuongBaoHiemXe: 0,
  tongPhiBaoHiemXeFromAPI: 0,
  tongSoDongCanBoQuanLy: 0,
  listDoiTac: [],
  listPhuongThucKhaiThac: [],
  listSanPham: [],
  listChiNhanh: [],
  listPhongBan: [],
  listDonViBoiThuong: [],
  listChuongTrinhBaoHiem: [],
  listCanBo: [],
  listLoaiXe: [],
  listHangXe: [],
  listHieuXe: [],
  layChiTietDoiTuongBaoHiemXe: () => Promise.resolve(null),
  listLoaiHinhNghiepVuXeCoGioi: [],
  listDieuKhoanBoSungXeCoGioi: [],
  layThongTinThanhToanCuaHopDongBaoHiemXe: () => Promise.resolve(),
  listThongTinThanhToanHopDongXe: [],
  chiTietHopDongBaoHiemXe: {},
  updateKyThanhToan: () => Promise.resolve(false),
  layChiTietKyThanhToan: () => Promise.resolve(null),
  tongSoDongDataKhachHang: 0,
  tongSoDongDataLDaiLy: 0,
  layDanhSachCauHinhDongCuaHopDongBaoHiemXe: () => Promise.resolve(),
  listDongBaoHiemHopDongXe: [],
  updateCauHinhDongBaoHiem: () => Promise.resolve(false),
  listDonViDongTai: [],
  layChiTietThongTinCauHinhDongBH: () => Promise.resolve(null),
  xoaThongTinDongBH: () => Promise.resolve(false),
  layDanhSachCauHinhTaiBHCuaHopDongBaoHiemXe: () => Promise.resolve(),
  listTaiBaoHiemHopDongXe: [],
  updateCauHinhTaiBaoHiem: () => Promise.resolve(false),
  xoaThongTinTaiBH: () => Promise.resolve(false),
  layChiTietThongTinCauHinhTaiBH: () => Promise.resolve(null),
  updateDoiTuongApDungTyLeDongBH: () => Promise.resolve(false),
  updateDoiTuongApDungTaiBH: () => Promise.resolve(false),
  lietKeDanhSachCacDoiTuongDaDuocApDungDongBH: () => Promise.resolve(),
  lietKeDanhSachCacDoiTuongDaDuocApDungTaiBH: () => Promise.resolve(),
  huyHopDongXe: () => Promise.resolve(false),
  goHuyHopDongXe: () => Promise.resolve(false),
  getDanhSachFileThumbnailTheoDoiTuong: () => Promise.resolve(),
  uploadFileTheoDoiTuong: () => Promise.resolve(false),
  deleteFileTheoDoiTuong: () => Promise.resolve(false),
  phanLoaiFileTheoHangMucXe: () => Promise.resolve(false),
  getListHangMucXeTheoNhom: () => Promise.resolve(),
  listHangMucXeTheoNhom: [],
  exportPdfHopDong: () => Promise.resolve(null),
  exportExcel: () => Promise.resolve(null),
  getListMucDoTonThatXe: () => Promise.resolve(),
  listMucDoTonThatXe: [],
  layDanhSachHangMucTonThatTheoDoiTuongXe: () => Promise.resolve(),
  timKiemPhanTrangHangMucTonThat: () => Promise.resolve(),
  danhSachHangMucTonThat: [],

  tongSoDongHangMucTonThat: 0,
  luuHangMucTonThat: () => Promise.resolve(false),
  luuDanhGiaTonThatXe: () => Promise.resolve(false),
  chiTietDoiTuongBaoHiemXe: {},
  dataHangMucTonThatTheoDoiTuong: [],
  taoHopDongSuaDoiBoSung: () => Promise.resolve(null),
  resetChiTietHopDongBaoHiemXe: () => {},
});

//useContext(someContext): là React Hook cho phép bạn đọc và subscribe context từ component của mình.
// file .context muốn truy câp context thì sử dụng useLoginContext : const {onSubmit} = useLoginContext();
export const useBaoHiemXeCoGioiContext = () => useContext(BaoHiemXeCoGioiContext);

#DANG_NHAP {
  .layout-sign-up .ant-form-item-control-input button.ant-switch {
    margin-right: 5px;
  }
  .layout-sign-up-custom {
    margin-left: 0 !important;
  }

  .layout-default.ant-layout.layout-sign-up .header-col.header-brand h5 {
    color: #fff;
  }

  .layout-default.ant-layout.layout-sign-up .header-col.header-nav a {
    color: #fff;
  }

  .layout-default.ant-layout.layout-sign-up .header-col.header-nav svg path.fill-muted {
    fill: #fff;
  }

  .sign-up-header .content {
    padding-top: 40px;
    max-width: 480px;
    margin: auto;
  }
  .sign-up-header * {
    color: #fff;
  }
  .sign-up-header .content h1 {
    font-size: 48px;
    color: #fff;
  }

  .sign-up-header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    margin: 0;
    padding: 0;
    border-radius: 0;
    box-shadow: none;
    background: url("@src/assets/images/img_bg_signup_2.png") no-repeat center center;
    background-size: cover;
    background-attachment: fixed;
    text-align: center;
    z-index: 1;
  }

  .layout-default.ant-layout.layout-sign-up header.ant-layout-header {
    background-color: transparent;
    color: #fff;
    margin: 0;
    padding: 0 20px;
    z-index: 1;
  }

  .layout-default .ant-layout-header {
    display: flex;
  }

  .header-col.header-nav {
    margin-left: auto;
    width: 100%;
    margin-right: auto;
  }
  .header-col.header-nav .ant-menu-overflow {
    justify-content: center;
  }

  .header-col.header-nav .ant-menu-horizontal {
    background: transparent;
    box-shadow: none;
    border: 0px;
  }

  .layout-default.ant-layout.layout-sign-up {
    padding: 0;
    min-height: 100vh;
    position: relative;
    overflow: hidden;
  }
  .header-col.header-nav li:after {
    display: none;
  }

  .header-col.header-brand {
    white-space: nowrap;
  }

  .header-col.header-brand h5 {
    margin: 0px;
    color: #fff;
    font-size: 14px;
    font-weight: 600;
  }

  .header-col.header-nav {
    color: #fff;
    font-weight: 600;
  }

  .header-col.header-nav ul li {
    color: #fff !important;
  }

  .header-col.header-nav ul li img {
    margin-top: -2px;
    margin-right: 5px;
  }
  .header-col.header-btn button {
    height: 34px;
    padding: 0 16px;
    border-radius: 34px;
    line-height: 33px;
  }
  .text-lg {
    font-size: 16px;
  }
  .card-signup .sign-up-gateways .ant-btn {
    margin-right: 10px;
    margin-left: 10px;
    height: 60px;
    width: 70px;
    box-shadow: none;
    border-radius: 6px;
  }
  .card-signup {
    width: 90%;
    max-width: 450px;
    height: auto;
    margin: 0;
    box-shadow: 0 20px 27px rgb(0 0 0 / 15%);
    border-radius: 12px;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
  }
  .card-signup h5 {
    text-align: center;
    font-size: 20px;
    font-weight: 600;
    color: #141414;
    margin: 0px;
  }
  .header-solid .ant-card-head {
    border-bottom: 0;
  }
  .card-signup .sign-up-gateways .ant-btn img {
    width: 20px;
  }
  .sign-up-gateways {
    text-align: center;
  }
  .m-25,
  .mb-25,
  .my-25 {
    margin-bottom: 25px;
  }
  .m-25,
  .mt-25,
  .my-25 {
    margin-top: 25px;
  }
  .text-center {
    text-align: center;
  }
  .font-semibold {
    font-weight: 600;
  }
  .text-muted {
    color: #8c8c8c;
  }
  .card-signup .ant-input {
    // border-radius: 6px;
    // font-weight: 600;
    // color: #8c8c8c;
    // height: 40px;
    // border: 1px solid #d9d9d9;
  }
  .font-bold {
    font-weight: 700;
  }
  .text-dark {
    color: #141414;
  }
  .ant-btn,
  .ant-radio-group .ant-radio-button-wrapper {
    font-weight: 600;
    font-size: 12px;
    // height: 40px;
    padding: 0 15px;
    line-height: 40px;
  }
  .ant-btn {
    box-shadow: 0 2px 4px rgb(0 0 0 / 7%);
    border-radius: 6px;
  }
  .layout-sign-up .ant-layout-footer {
    padding: 24px 50px;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 15;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
  }

  .layout-default .ant-layout-footer {
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    background: transparent;
    text-align: center;
  }
  .layout-default .ant-layout-footer .ant-menu-horizontal {
    border: none;
    line-height: 1.5;
    margin: 0 0 30px;
    background-color: transparent;
    font-size: 16px;
  }
  .layout-default .ant-layout-footer .ant-menu-horizontal {
    text-align: center;
    display: block;
  }

  .layout-default .ant-layout-footer .ant-menu-horizontal > .ant-menu-item,
  .layout-default .ant-layout-footer .ant-menu-horizontal > .ant-menu-item-active,
  .layout-default .ant-layout-footer .ant-menu-horizontal > .ant-menu-item-selected,
  .layout-default .ant-layout-footer .ant-menu-horizontal > .ant-menu-item:hover,
  .layout-default .ant-layout-footer .ant-menu-horizontal > .ant-menu-submenu,
  .layout-default .ant-layout-footer .ant-menu-horizontal > .ant-menu-submenu-active,
  .layout-default .ant-layout-footer .ant-menu-horizontal > .ant-menu-submenu-selected,
  .layout-default .ant-layout-footer .ant-menu-horizontal > .ant-menu-submenu:hover {
    color: #8c8c8c;
    border: none;
  }
  .layout-default .ant-layout-footer .ant-menu-horizontal > .ant-menu-item-active,
  .layout-default .ant-layout-footer .ant-menu-horizontal > .ant-menu-item-selected,
  .layout-default .ant-layout-footer .ant-menu-horizontal > .ant-menu-item:hover,
  .layout-default .ant-layout-footer .ant-menu-horizontal > .ant-menu-submenu-active,
  .layout-default .ant-layout-footer .ant-menu-horizontal > .ant-menu-submenu-selected,
  .layout-default .ant-layout-footer .ant-menu-horizontal > .ant-menu-submenu:hover {
    color: #262626;
  }
  .layout-default.ant-layout.layout-sign-up .ant-layout-footer ul li:after {
    display: none;
  }
  .layout-default.ant-layout.layout-sign-up footer.ant-layout-footer ul li a svg {
    width: 18px;
    height: 18px;
  }
  .layout-default .ant-layout-footer .menu-nav-social a svg {
    fill: #8c8c8c;
    vertical-align: middle;
  }
  .layout-default .ant-layout-footer .menu-nav-social a:hover svg {
    fill: #1890ff;
  }
  .layout-default .ant-layout-footer .copyright a {
    color: inherit;
    font-weight: normal;
  }

  .img_logo_login {
    width: 250px;
    height: 80px;
    object-fit: contain;
  }
  .div_img_logo_login {
    text-align: center; //cho image vào giữa div
    margin: 15px 0 25px 0;
  }
  .ant-layout {
    background: transparent;
    min-height: 100vh;
  }

  .content-ant {
    position: relative;
    z-index: 5;
    padding: 0;
    min-height: 100vh;
  }
}

import {ReactQuery} from "@src/@types";

export interface IPheDuyetHopDongConNguoiContextProps {
  loading: boolean;
  tongSoDong: number;
  danhSachHopDongTrinhDuyetConNguoi: Array<CommonExecute.Execute.IDoiTac>;
  timKiemPhanTrangHopDongTrinhDuyetConNguoi: (params?: ReactQuery.ITimKiemPhanTrangHopDongTrinhDuyetParams & ReactQuery.IPhanTrang) => Promise<void>;
  xemChiTietHopDongTrinhDuyet: (params: ReactQuery.IXemChiTietHopDongTrinhDuyetParams) => Promise<CommonExecute.Execute.IHopDongTrinhDuyetXCG>;
  handlePheDuyetHopDong: (params: ReactQuery.ICapNhatHopDongTrinhDuyetParams) => Promise<boolean>;
  handleGoDuyetHopDong: (params: ReactQuery.ICapNhatHopDongTrinhDuyetParams) => Promise<boolean>;
  handleTuChoiDuyetHopDong: (params: ReactQuery.ICapNhatHopDongTrinhDuyetParams) => Promise<boolean>;
}

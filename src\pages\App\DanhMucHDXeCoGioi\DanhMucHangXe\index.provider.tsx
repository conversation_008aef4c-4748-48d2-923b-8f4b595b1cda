import {CommonExecuteResponse, useCommonExecute} from "@src/services/react-queries";
import {PropsWithChildren, useCallback, useEffect, useMemo, useState} from "react";
import {defaultFormValue} from "./index.configs";
import {ReactQuery} from "@src/@types";
import {ACTION_CODE} from "@src/constants";
import {DanhMucHangXeProps} from "./index.model";
import {DanhMucHangXeContext} from "./index.context";
import {message} from "antd";

const DanhMucHangXeProvider: React.FC<PropsWithChildren> = props => {
  const {children} = props;
  const mutateUseCommonExecute = useCommonExecute();
  const [loading, setLoading] = useState<boolean>(false);
  const [danhSachDanhMucHangXe, setDanhSachDanhMucHangXe] = useState<Array<CommonExecute.Execute.IDanhMucHangXe>>([]);
  const [tongSoDong, setTongSoDong] = useState<number>(0);
  const [listDoiTac, setListDoiTac] = useState<Array<CommonExecute.Execute.IDoiTac>>([]);

  useEffect(() => {
    initData();
  }, []);
  const initData = () => {
    getListDoiTac();
    layDanhSachDanhMucHangXe(defaultFormValue);
  };

  /* ĐỐI TÁC */
  const getListDoiTac = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        actionCode: ACTION_CODE.LIET_KE_HANG_XE,
      });

      setListDoiTac(response?.data?.map(item => ({...item, ten: item.ma + " - " + item.ten})));
    } catch (error) {}
  }, [mutateUseCommonExecute]);
  //TÌM KIẾM PHÂN TRANG DANH MỤC HÃNG XE
  const layDanhSachDanhMucHangXe = useCallback(
    async (body: ReactQuery.ITimKiemPhanTrangDanhMucHangXeParams & ReactQuery.IPhanTrang) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_DANH_MUC_HANG_XE,
        };

        console.log("dữ liệu", params);

        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);

        const data = response.data.data;

        setDanhSachDanhMucHangXe(data);
        setTongSoDong(response.data.tong_so_dong);
      } catch (error: any) {
        console.log("layDanhSachTimKiemPhanTrangDAnhMucHangXe error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );
  //LẤY CHI TIẾT DANH MỤC HÃNG XE
  const layChiTietDanhMucHangXe = useCallback(
    async (item: ReactQuery.ILayChiTietDanhMucHangXeParams): Promise<CommonExecute.Execute.IChiTietDanhMucHangXe | null> => {
      try {
        const params = {
          ma: item.ma,
          actionCode: ACTION_CODE.CHI_TIET_DANH_MUC_HANG_XE,
        };

        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);

        // Type cast để truy cập property lke
        const dataArray = (responseData.data as any).lke;
        const data = Array.isArray(dataArray) && dataArray.length > 0 ? dataArray[0] : null;

        return data as CommonExecute.Execute.IChiTietDanhMucHangXe;
      } catch (error: any) {
        console.log("laychitietchucnang error", error.message || error);
        return null;
      }
    },
    [mutateUseCommonExecute],
  );
  // UPDATE DANH MỤC HÃNG XE
  const onUpdateDanhMucHangXe = useCallback(async (body: ReactQuery.IUpdateDanhMuchangXeParams) => {
    try {
      setLoading(true);
      if (!body.ma) {
        message.error("Mã hãng xe là bắt buộc");
        return null;
      }
      if (!body.nv) {
        message.error("Nghiệp vụ hãng xe là bắt buộc");
        return null;
      }
      if (!body.stt) {
        message.error("Thứ tự hiển thị là bắt buộc");
        return null;
      }
      if (!body.ten) {
        message.error("Tên hãng xe là bắt buộc");
        return null;
      }
      if (!body.trang_thai) {
        message.error("Trạng thái là bắt buộc");
        return null;
      }
      const params = {
        ...body,
        actionCode: ACTION_CODE.UPDATE_DANH_MUC_HANG_XE,
      };

      const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
      if (responseData.data === -1) {
        // Xác định đây là thêm mới hay chỉnh sửa
        const isEdit = params.ma && params.ma.trim() !== "";

        // Hiển thị message thành công
        message.success(`${isEdit ? "Cập nhật" : "Thêm mới"} hãng xe thành công`);
        initData();
        //chuyển đổi responData thành number
        return responseData.data as unknown as number;
      }

      //set updateDanhMucHangXe(responseData.data)
      return responseData.data;
    } catch (error: any) {
      console.log("updateDanhMucHangXe", error.message || error);
      return null;
    }
  }, []);
  const value = useMemo<DanhMucHangXeProps>(
    () => ({
      listDoiTac,

      getListDoiTac,

      danhSachDanhMucHangXe,
      loading: mutateUseCommonExecute.isLoading,
      tongSoDong,
      layDanhSachDanhMucHangXe,
      layChiTietDanhMucHangXe,
      defaultFormValue,
      onUpdateDanhMucHangXe,
    }),
    [listDoiTac, danhSachDanhMucHangXe, mutateUseCommonExecute, tongSoDong, getListDoiTac, layDanhSachDanhMucHangXe, onUpdateDanhMucHangXe],
  );
  return <DanhMucHangXeContext.Provider value={value}>{children}</DanhMucHangXeContext.Provider>;
};

export default DanhMucHangXeProvider;

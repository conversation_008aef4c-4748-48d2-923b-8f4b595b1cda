import React, { memo, PropsWithChildren } from "react";
import { Skeleton as AntSkeleton } from "antd";
import { SkeletonImageProps } from "antd/es/skeleton/Image";
import { twMerge } from "tailwind-merge";

import { isEqual } from "lodash";

export interface SkeletonProps extends SkeletonImageProps {
  loading?: boolean;
}

const ImageSkeletonComponent: React.FC<PropsWithChildren<SkeletonProps>> = (
  props,
) => {
  const {
    className = "",
    loading = true,
    children,
    active = true,
    ...etc
  } = props;

  return (
    <React.Fragment>
      {loading ? (
        <AntSkeleton.Image
          className={twMerge("custom-image-skeleton", className)}
          active={active}
          {...etc}
        />
      ) : (
        children
      )}
    </React.Fragment>
  );
};

const ImageSkeleton = memo(ImageSkeletonComponent, isEqual);

export default ImageSkeleton;

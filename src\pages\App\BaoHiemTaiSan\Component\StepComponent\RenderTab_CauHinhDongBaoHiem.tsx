import {CloseOutlined, PlusCircleOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {Button, Popcomfirm} from "@src/components";
import {defaultTableProps, fillRowTableEmpty} from "@src/hooks";
import {formatCurrencyUS} from "@src/utils/number";
import {Table, TableColumnType} from "antd";
import {isEqual} from "lodash";
import {forwardRef, memo, useImperativeHandle, useMemo, useRef} from "react";
import {useBaoHiemTaiSanContext} from "../../index.context";
import {tableCauHinhDongBaoHiemColumn, TableCauHinhDongBaoHiemDataType} from "./Constant";
import {ModalDoiTuongApDungDongBH} from "./Modal_DoiTuongApDungDongBH";
import {ModalNhapCauHinhDong} from "./Modal_NhapCauHinhDong";

const RenderTabCauHinhDongBaoHiemComponent = forwardRef<any, {disabled?: boolean; pageSize?: number}>(({disabled, pageSize = 20}, ref) => {
  useImperativeHandle(ref, () => ({
    resetForm: () => {},
  }));

  const {loading, listDongBaoHiemHopDongTaiSan, layChiTietThongTinCauHinhDongBH, xoaThongTinDongBH, lietKeDanhSachCacDoiTuongDaDuocApDungDongBH} = useBaoHiemTaiSanContext();

  const refModalCauHinhDong = useRef<any>(null);
  const refModalDoiTuongApDungDongBH = useRef<any>(null);

  //bấm xoá 1 dòng trong bảng ngày thanh toán
  const handleDeleteRow = async (key: string, ma_dvi_dong: string) => {
    if (disabled) return;
    await xoaThongTinDongBH({ma_dvi_dong});
  };

  // Tính tổng tiền nhượng đồng và tiền giữ lại
  const totals = useMemo(() => {
    return listDongBaoHiemHopDongTaiSan.reduce(
      (acc, curr: any) => ({
        tien_nhuong_dong: acc.tien_nhuong_dong + (curr.tien_nhuong_dong || 0),
        tien_con: acc.tien_con + (curr.tien_con || 0),
      }),
      {tien_nhuong_dong: 0, tien_con: 0},
    );
  }, [listDongBaoHiemHopDongTaiSan]);

  //DATA TABLE CẤU HÌNH ĐỒNG
  const dataTableListDongBaoHiem = useMemo<Array<TableCauHinhDongBaoHiemDataType>>(() => {
    try {
      const tableData = listDongBaoHiemHopDongTaiSan.map((item, index) => {
        return {
          ...item,
          key: index.toString(),
          sott: item.sott || index + 1,
        };
      });
      const arrEmptyRow: Array<TableCauHinhDongBaoHiemDataType> = fillRowTableEmpty(tableData.length, pageSize);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      console.log("dataTableListDongBaoHiem error", error);
      return [];
    }
  }, [listDongBaoHiemHopDongTaiSan, pageSize]);

  // RENDER

  // //render header table thông tin các kỳ thanh toán
  // const renderHeaderTable = () => {
  //   return (
  //     <Row gutter={16} justify="end">
  //       <Col>
  //         <Button type="primary" onClick={() => refModalCauHinhDong.current.open()} icon={<PlusCircleOutlined />} className="w-full" disabled={disabled}>
  //           Thêm đồng BH
  //         </Button>
  //       </Col>
  //     </Row>
  //   );
  // };

  const renderColumn = (column: TableColumnType<TableCauHinhDongBaoHiemDataType>) => {
    if (!("dataIndex" in column)) return column;

    if (column.dataIndex === "action") {
      return {
        ...column,
        render: (_: any, record: TableCauHinhDongBaoHiemDataType, index: number) =>
          record.key && !record.key.toString().startsWith("empty") ? (
            <span onClick={e => e.stopPropagation()}>
              <Popcomfirm
                title="Thông báo"
                onConfirm={() => record.ma_dvi_dong && handleDeleteRow(record.key, record.ma_dvi_dong)}
                htmlType="button"
                okText="Đồng ý"
                description="Bạn có chắc chắn muốn xoá thông tin cấu hình đồng BH không?"
                buttonTitle={""}
                buttonColor="red"
                variant="text"
                className="h-auto"
                buttonDisable={disabled}
                icon={<CloseOutlined />}
              />
            </span>
          ) : null,
      };
    }
    if (column.dataIndex === "sl_dt_bh") {
      return {
        ...column,
        render: (text: any, record: TableCauHinhDongBaoHiemDataType, index: number) => {
          if (typeof record.sl_dt_bh === "number" && record.key && !record.key.toString().startsWith("empty")) {
            if (record.sl_dt_bh > 0) {
              return (
                <Button
                  className="h-auto"
                  type="link"
                  onClick={async e => {
                    e.stopPropagation();
                    const listDoiTuongDaApDungDongBH = await lietKeDanhSachCacDoiTuongDaDuocApDungDongBH(record as ReactQuery.IChiTietThongTinCauHinhDongBHParams);
                    refModalDoiTuongApDungDongBH.current?.open(record, listDoiTuongDaApDungDongBH);
                  }}
                  style={{padding: 0}}>
                  {`Có ${record.sl_dt_bh} đối tượng áp dụng`}
                </Button>
              );
            } else if (record.sl_dt_bh <= 0) {
              return (
                <Button
                  className="h-auto"
                  type="link"
                  onClick={async e => {
                    e.stopPropagation();
                    const listDoiTuongDaApDungDongBH = await lietKeDanhSachCacDoiTuongDaDuocApDungDongBH(record as ReactQuery.IChiTietThongTinCauHinhDongBHParams);
                    refModalDoiTuongApDungDongBH.current?.open(record, listDoiTuongDaApDungDongBH);
                  }}
                  style={{padding: 0}}>
                  Áp dụng tất cả
                </Button>
              );
            }
          }
          return text;
        },
      };
    }
    return column;
  };

  //render tổng cộng của cột
  const renderSummaryCell = (index: number, value: string) => {
    return (
      <Table.Summary.Cell index={index} className="!p-[8px] !pr-[24px]">
        <div className="text-right font-medium">{value}</div>
      </Table.Summary.Cell>
    );
  };

  //render bảng thông tin đồng
  const renderTable = () => (
    <div>
      <Table<TableCauHinhDongBaoHiemDataType>
        {...defaultTableProps}
        className={`no-header-border-radius mt-3`}
        dataSource={dataTableListDongBaoHiem} //mảng dữ liệu record được hiển thị
        columns={
          (tableCauHinhDongBaoHiemColumn || []).map(item => {
            return renderColumn(item);
          }) || []
        } //định nghĩa cột của table
        loading={loading} //hiển thị loading khi đang gọi API để loading data
        sticky
        onRow={record => {
          return {
            style: {cursor: loading ? "progress" : "pointer"}, //  thể hiện vị trí con trỏ là button(👈)
            onClick: async () => {
              if (!record.ma_dvi_dong || disabled) return;
              const response = await layChiTietThongTinCauHinhDongBH(record as ReactQuery.IChiTietThongTinCauHinhDongBHParams);
              if (response) refModalCauHinhDong.current?.open(response);
            },
          };
        }}
        pagination={false}
        scroll={dataTableListDongBaoHiem.length > pageSize ? {x: "max-content", y: 350} : {x: "max-content"}}
        summary={() => (
          <Table.Summary fixed>
            <Table.Summary.Row>
              <Table.Summary.Cell className="!p-[8px]" index={0} colSpan={6}>
                <div className="text-center font-medium">Tổng cộng</div>
              </Table.Summary.Cell>
              {renderSummaryCell(6, formatCurrencyUS(totals.tien_nhuong_dong))}
              {renderSummaryCell(7, formatCurrencyUS(totals.tien_con))}
              <Table.Summary.Cell index={8} className="!p-[8px]" colSpan={2}>
                <div className="text-center">
                  <Button type="link" onClick={() => refModalCauHinhDong.current.open()} icon={<PlusCircleOutlined />} className="!h-auto !p-0" disabled={disabled}>
                    Thêm đồng BH
                  </Button>
                </div>
              </Table.Summary.Cell>
            </Table.Summary.Row>
          </Table.Summary>
        )}
      />
    </div>
  );

  return (
    <>
      {/* {renderHeaderTable()} */}
      {renderTable()}
      <ModalNhapCauHinhDong disabled={disabled} ref={refModalCauHinhDong} />
      <ModalDoiTuongApDungDongBH disabled={disabled} ref={refModalDoiTuongApDungDongBH} />
    </>
  );
});

RenderTabCauHinhDongBaoHiemComponent.displayName = "RenderTabCauHinhDongBaoHiemComponent";
export const RenderTabCauHinhDongBaoHiem = memo(RenderTabCauHinhDongBaoHiemComponent, isEqual);

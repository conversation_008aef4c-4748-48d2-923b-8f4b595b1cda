import {memo, useContext} from "react";
import {ConfigProvider, Typography as AntTypography} from "antd";
import {LinkProps} from "antd/es/typography/Link";
import {ParagraphProps} from "antd/es/typography/Paragraph";
import {TextProps} from "antd/es/typography/Text";
import {TitleProps} from "antd/es/typography/Title";

import {isEqual} from "lodash";

export interface TypographyProps {
  type?: "link" | "text" | "title" | "paragraph";
  children?: string;
  linkProps?: LinkProps;
  textProps?: TextProps;
  paragraphProps?: ParagraphProps;
  titleProps?: TitleProps;
  className?: string;
}

const TypographyComponent: React.FC<TypographyProps> = (props: TypographyProps) => {
  const {type = "text", children, linkProps, textProps, paragraphProps, titleProps, className} = props;
  const {theme} = useContext(ConfigProvider.ConfigContext);
  switch (type) {
    case "link":
      return (
        <AntTypography.Link {...linkProps} style={{...linkProps?.style, color: theme?.token?.colorPrimary}} href={linkProps?.href} className={className ?? linkProps?.className}>
          {children}
        </AntTypography.Link>
      );
    case "paragraph":
      return (
        <AntTypography.Paragraph {...paragraphProps} className={className ?? paragraphProps?.className}>
          {children}
        </AntTypography.Paragraph>
      );
    case "title":
      return (
        <AntTypography.Title {...titleProps} className={className ?? titleProps?.className}>
          {children}
        </AntTypography.Title>
      );
    case "text":
    default:
      return (
        <AntTypography.Text color="red" {...textProps} className={className ?? textProps?.className}>
          {children}
        </AntTypography.Text>
      );
  }
};

const Typography = memo(TypographyComponent, isEqual);

export default Typography;

import "@react-pdf-viewer/core/lib/styles/index.css";
import {isEqual} from "lodash";
import {forwardRef, memo, useEffect, useImperativeHandle} from "react";
import {useHopDongConNguoiContext} from "../index.context";
import {IThemHopDongStep4Ref, ThemHopDongStep4Props} from "./Constant";
import {Tabs} from "antd";
import {ThongTinThanhToanStep4} from "./ThemHopDongStep4_TabThongTinThanhToan";
import {ThongTinDongBaoHiemStep4} from "./ThemHopDongStep4_TabThongTinDongBaoHiem";
import {ThongTinTaiBaoHiemStep4} from "./ThemHopDongStep4_TabThongTinTaiBaoHiem";

const ThemHopDongStep4Component = forwardRef<IThemHopDongStep4Ref, ThemHopDongStep4Props>(({}: ThemHopDongStep4Props, ref) => {
  useImperativeHandle(ref, () => ({}));

  const {layDanhSachCauHinhDongCuaHopDongBaoHiemConNguoi, layDanhSachCauHinhTaiBHCuaHopDongBaoHiemConNguoi, chiTietHopDong} = useHopDongConNguoiContext();

  useEffect(() => {
    layDanhSachCauHinhDongCuaHopDongBaoHiemConNguoi({so_id: chiTietHopDong?.so_id}); // KHỞI TẠO DATA CHO TAB ĐỒNG BẢO HIỂM
    layDanhSachCauHinhTaiBHCuaHopDongBaoHiemConNguoi({so_id: chiTietHopDong?.so_id}); //KHỞI TẠO DATA CHO TAB THÔNG TIN TÁI BẢO HIỂM
  }, [chiTietHopDong]);

  return (
    <Tabs
      className="mt-3 [&_.ant-tabs-nav]:mb-0"
      animated={false}
      size="small"
      // tabPosition={"left"}
      defaultActiveKey="1"
      // type="card"
      items={[
        {
          key: "1",
          label: "Thông tin thanh toán",
          children: <ThongTinThanhToanStep4 />,
        },
        {
          key: "2",
          label: "Thông tin đồng bảo hiểm",
          children: <ThongTinDongBaoHiemStep4 />,
        },
        {
          key: "3",
          label: "Thông tin tái bảo hiểm",
          children: <ThongTinTaiBaoHiemStep4 />,
        },
      ]}
      // onChange={onChange}
    />
  );
});

ThemHopDongStep4Component.displayName = "ThemHopDongStep4Component";
export const ThemHopDongStep4 = memo(ThemHopDongStep4Component, isEqual);

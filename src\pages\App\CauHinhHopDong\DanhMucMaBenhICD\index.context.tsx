import {createContext, useContext} from "react";

import {IQuanLyDanhMucMaBenhICDContextProps} from "./index.model";

//khởi tạo giá trị mặc định
export const QuanLyDanhMucMaBenhICDContext = createContext<IQuanLyDanhMucMaBenhICDContextProps>({
  listDanhMucMaBenhICD: [],
  tongSoDong: 0,
  loading: false,
  filterParams: {},
  getListDanhMucMaBenhICD: async () => Promise.resolve(),
  getChiTietDanhMucMaBenhICD: async () => Promise.resolve({} as CommonExecute.Execute.IDanhMucMaBenhICD),
  capNhatChiTietDanhMucMaBenhICD: async () => Promise.resolve(),
  setFilterParams: () => {},
});

export const useQuanLyDanhMucMaBenhICDContext = () => useContext(QuanLyDanhMucMaBenhICDContext);
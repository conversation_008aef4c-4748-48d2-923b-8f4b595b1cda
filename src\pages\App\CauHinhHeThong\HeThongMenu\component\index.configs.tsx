import {IFormInput} from "@src/@types";
import {defaultTableColumnsProps} from "@src/hooks";
import {TableProps} from "antd";

const ruleRequired = {
  required: true,
  message: "Thông tin bắt buộc",
};

export interface IFormChiTietHeThongMenuFieldsConfig {
  ma: IFormInput;
  ten: IFormInput;
  ma_cha: IFormInput;
  stt: IFormInput;
  nhom: IFormInput;
  trang_thai: IFormInput;
  trang_thai_ten: IFormInput;
  url: IFormInput;
  app_name: IFormInput;
}
export const FormChiTietHeThongMenu: IFormChiTietHeThongMenuFieldsConfig = {
  ma: {
    component: "input",
    label: "Mã menu",
    name: "ma",
    placeholder: "Mã menu",
    // rules: [ruleRequired],
    disabled: true,
  },
  nhom: {
    component: "select",
    label: "Nhóm menu",
    name: "nhom",
    placeholder: "Chọn nhóm menu",
    rules: [ruleRequired],
  },
  ten: {
    component: "input",
    label: "Tên menu",
    name: "ten",
    placeholder: "Tên menu",
    rules: [ruleRequired],
  },
  ma_cha: {
    component: "select",
    label: "Menu cha",
    name: "ma_cha",
    placeholder: "Chọn menu cha",
    showSearch: false,
  },
  app_name: {
    component: "select",
    label: "App",
    name: "app_name",
    placeholder: "Chọn app",
    rules: [ruleRequired],
  },
  stt: {
    component: "input",
    label: "Thứ tự hiển thị",
    name: "stt",
    placeholder: "Thứ tự hiển thị",
    rules: [ruleRequired],
  },
  trang_thai: {
    component: "select",
    label: "Trạng thái",
    name: "trang_thai",
    placeholder: "Chọn trạng thái",
    rules: [ruleRequired],
  },
  trang_thai_ten: {
    component: "select",
    label: "Trạng thái",
    name: "trang_thai_ten",
    placeholder: "Chọn trạng thái",
  },
  url: {
    component: "input",
    label: "Url menu",
    name: "url",
    placeholder: "nhập URL",
  },
};
export interface TableHeThongMenuChaDataType {
  key: string;
  stt?: number;
  ma?: string;
  ten?: string;
  ma_cha?: string;
  url?: string;
  nhom?: string;
  trang_thai?: string;
  trang_thai_ten?: string;
}

// ĐỊNH NGHĨA Column RENDER RA TỪNG Ô TRONG CỘT
//đọc https://ant.design/components/table#column để biết thêm về column
export const heThongMenuChaColumns: TableProps<TableHeThongMenuChaDataType>["columns"] = [
  {title: "STT", dataIndex: "stt", key: "stt", width: 60, align: "center", ...defaultTableColumnsProps},
  {title: "Mã", dataIndex: "ma", key: "ma", width: 130, align: "center", ...defaultTableColumnsProps},
  {title: "Tên", dataIndex: "ten", key: "ten", width: 150, align: "left", ...defaultTableColumnsProps},
  {title: "Mã cha", dataIndex: "ma_cha", key: "ma_cha", width: 130, align: "center", ...defaultTableColumnsProps},
  {title: "Url", dataIndex: "url", key: "url", width: 150, align: "center", ...defaultTableColumnsProps},
  {title: "Nhóm menu", dataIndex: "nhom", key: "nhom", width: 150, align: "center", ...defaultTableColumnsProps},
  // {title: "Trạng Thái", dataIndex: "trang_thai_ten", key: "trang_thai_ten", width: 200, align: "center", ...defaultTableColumnsProps},
];

export const TRANG_THAI_MENU_CHA = [
  {ten: "Đang sử dụng", ma: "D"},
  {ten: "Ngừng sử dụng", ma: "K"},
];
//keyof: return ra key của inteface TableKhachHangColumnDataType;
export type TableHeThongMenuChaColumnDataIndex = keyof TableHeThongMenuChaDataType;
export interface IMenuSelected {
  ma?: string;
  ten?: string;
}
export interface IModalThemHeThongMenuRef {
  open: (data?: CommonExecute.Execute.IHeThongMenu) => void;
  close: () => void;
}
export interface ChiTietMenuProps {
  listMenu: Array<CommonExecute.Execute.IHeThongMenu>;
}
export interface MenuChaProps {
  onSelectMenuCha: (ma_ct: CommonExecute.Execute.IHeThongMenu | null) => void;
}
export interface IModalTimMenuChaRef {
  open: (data?: CommonExecute.Execute.IHeThongMenu) => void;
  close: () => void;
}
export type DataIndex = keyof TableHeThongMenuChaDataType;

export const APP_NAME = [
  {ten: " Core bảo hiểm", ma: "CORE"},
  {ten: "Bảo hiểm xã hội - TVV", ma: "BHXH"},
];

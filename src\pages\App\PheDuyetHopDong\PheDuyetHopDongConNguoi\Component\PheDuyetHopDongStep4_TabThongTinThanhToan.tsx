import {PlusCircleOutlined} from "@ant-design/icons";
import "@react-pdf-viewer/core/lib/styles/index.css";
import {ReactQuery} from "@src/@types";
import {Button} from "@src/components";
import {defaultTableProps} from "@src/hooks";
import {formatCurrencyUS} from "@src/utils";
import {Table} from "antd";
import {isEqual} from "lodash";
import {forwardRef, memo, useEffect, useImperativeHandle, useMemo, useRef, useState} from "react";
import {useHopDongConNguoiContext} from "@src/pages/App/BaoHiemConNguoi/HopDongConNguoi/index.context";
import {IModalKyThanhToanRef, IThongTinThanhToanStep4Ref, TableThongTinKyThanhToanDataType, tableThongTinThanhToanColumn, ThongTinThanhToanStep4Props} from "./Constant";
import {ModalKyThanhToan} from "./PheDuyetHopDongStep4_ModalKyThanhToan";

const ThongTinThanhToanStep4Component = forwardRef<IThongTinThanhToanStep4Ref, ThongTinThanhToanStep4Props>(({}: ThongTinThanhToanStep4Props, ref) => {
  useImperativeHandle(ref, () => ({}));

  const {listThongTinThanhToanHopDongConNguoi, loading, chiTietHopDong, layThongTinThanhToanCuaHopDongBaoHiemConNguoi, layChiTietKyThanhToan} = useHopDongConNguoiContext();
  const [hoveredKyTT, setHoveredKyTT] = useState<number | null>(null);
  const refModalKyThanhToan = useRef<IModalKyThanhToanRef>(null);

  useEffect(() => {
    layThongTinThanhToanCuaHopDongBaoHiemConNguoi({so_id: chiTietHopDong?.so_id});
  }, [chiTietHopDong]);

  //DATA TABLE TT KỲ THANH TOÁN
  const dataTableListThongTinThanhToanBaoHiemXe = useMemo<Array<TableThongTinKyThanhToanDataType & {rowSpanKyTT?: number; rowSpanSoTien?: number; sttKyTT?: number}>>(() => {
    try {
      // Nhóm các dòng theo ky_tt
      const kyTTGroups: Record<string, any[]> = {};
      listThongTinThanhToanHopDongConNguoi.forEach((item: any) => {
        if (!kyTTGroups[item.ky_tt]) kyTTGroups[item.ky_tt] = [];
        kyTTGroups[item.ky_tt].push(item);
      });

      // Gán số thứ tự cho từng nhóm ky_tt
      const tableData: any[] = [];
      let stt = 1;
      Object.keys(kyTTGroups).forEach(ky_tt => {
        kyTTGroups[ky_tt].forEach((item, idx) => {
          tableData.push({
            ...item,
            key: tableData.length.toString(),
            sttKyTT: stt,
          });
        });
        stt++;
      });

      //Merge các dòng có chung kỳ thanh toán ky_tt
      let groupCount = 0;
      const mergedData = tableData.map((row, idx, arr) => {
        const isNewGroup = idx === 0 || row.ky_tt !== arr[idx - 1].ky_tt || row.so_tien !== arr[idx - 1].so_tien;
        if (isNewGroup) {
          groupCount = 1;
          for (let j = idx + 1; j < arr.length; j++) {
            if (arr[j].ky_tt === row.ky_tt && arr[j].so_tien === row.so_tien) {
              groupCount++;
            } else break;
          }
        }
        return {
          ...row,
          rowSpanKyTT: isNewGroup ? groupCount : 0,
          rowSpanSoTien: isNewGroup ? groupCount : 0,
        };
      });

      // Fill empty rows
      const fillRowTableEmpty = (tableCurrentLength = 0, tableRowMaxLength = 13) => {
        let arrEmptyRow: Array<any> = [];
        try {
          if (tableCurrentLength < tableRowMaxLength) {
            arrEmptyRow = Array.from({length: tableRowMaxLength - tableCurrentLength}, (item, index) => ({
              key: `empty-${index}`,
              rowSpanKyTT: 1,
              rowSpanSoTien: 1,
              // sttKyTT: null,
            }));
          }
          return arrEmptyRow;
        } catch (error) {
          console.log("fillRowTableEmpty", error);
          return arrEmptyRow;
        }
      };
      return [...mergedData, ...fillRowTableEmpty(mergedData.length, 13)];
    } catch (error) {
      console.log("dataTableListThongTinThanhToanBaoHiemXe error", error);
      return [];
    }
  }, [listThongTinThanhToanHopDongConNguoi]);

  // Tính tổng số tiền và tổng số tiền đã thanh toán
  const totals = useMemo(() => {
    // Tạo map để chỉ lấy mỗi kỳ một lần cho so_tien
    const kyTTMap = new Map();
    dataTableListThongTinThanhToanBaoHiemXe.forEach(curr => {
      if (curr && curr.so_tien != null && curr.key && !curr.key.toString().startsWith("empty") && !kyTTMap.has(curr.ky_tt)) {
        kyTTMap.set(curr.ky_tt, curr);
      }
    });

    // Tổng số tiền của các kỳ khác nhau
    let so_tien = 0;
    kyTTMap.forEach(item => {
      so_tien += Number(item.so_tien) || 0;
    });

    // Tổng số tiền đã thanh toán vẫn tính tất cả các dòng
    let so_tien_da_tt = 0;
    dataTableListThongTinThanhToanBaoHiemXe.forEach(curr => {
      if (curr && curr.so_tien_da_tt != null && curr.key && !curr.key.toString().startsWith("empty")) {
        so_tien_da_tt += Number(curr.so_tien_da_tt) || 0;
      }
    });

    return {so_tien, so_tien_da_tt};
  }, [dataTableListThongTinThanhToanBaoHiemXe]);

  /* */
  //render tổng cộng của cột
  const renderSummaryCell = (index: number, value: string) => {
    return (
      <Table.Summary.Cell index={index} className="!py-[8px]">
        <div className="text-right font-medium">{value}</div>
      </Table.Summary.Cell>
    );
  };

  //render bảng danh sách đối tượng XE
  const renderTableThongTinKyThanhToan = () => (
    <div>
      <Table<TableThongTinKyThanhToanDataType>
        {...defaultTableProps}
        className="no-header-border-radius table-thong-tin-ky-thanh-toan mt-3"
        dataSource={dataTableListThongTinThanhToanBaoHiemXe} //mảng dữ liệu record được hiển thị
        columns={
          (tableThongTinThanhToanColumn || []).map(item => {
            return {...item};
          }) || []
        } //định nghĩa cột của table
        loading={loading} //hiển thị loading khi đang gọi API để loading data
        sticky
        rowClassName={record => {
          if (hoveredKyTT !== null && typeof record.ky_tt === "number" && record.ky_tt === hoveredKyTT && !(record.key && record.key.toString().startsWith("empty"))) {
            return "table-row-hover-merge";
          }
          return "";
        }}
        onRow={record => {
          return {
            style: {cursor: loading ? "progress" : "pointer"}, //  thể hiện vị trí con trỏ là button(👈)
            onClick: async () => {
              if (!record.so_id_ky_ttoan) return;
              const response = await layChiTietKyThanhToan(record as ReactQuery.IChiTietKyThanhToanParams);
              if (response) refModalKyThanhToan.current?.open(response.ky_ttoan, response.ky_ttoan_ct);
            },
            onMouseEnter: () => {
              if (typeof record.ky_tt === "number") setHoveredKyTT(record.ky_tt);
            },
            onMouseLeave: () => {
              setHoveredKyTT(null);
            },
          };
        }}
        // title={() => <></>}
        pagination={false}
        scroll={{x: "max-content", y: 360}}
        summary={() => (
          <Table.Summary fixed="bottom">
            <Table.Summary.Row className="!p-[8px]">
              {/* Cột đầu tiên: merge các cột trước cột số tiền */}
              <Table.Summary.Cell index={0} colSpan={2} className="!p-[8px]">
                <div className="text-center font-medium">Tổng cộng</div>
              </Table.Summary.Cell>
              {renderSummaryCell(2, formatCurrencyUS(totals.so_tien))}
              {/* Cột loại */}
              {renderSummaryCell(3, "")}
              {/* Cột ngày thanh toán */}
              {renderSummaryCell(4, "")}
              {/* Cột số tiền đã thanh toán */}
              {renderSummaryCell(5, formatCurrencyUS(totals.so_tien_da_tt))}
              {/* Các cột còn lại */}
              {renderSummaryCell(6, "")}
              <Table.Summary.Cell index={7} className="!p-[8px]" colSpan={5}>
                {/* <div className="text-center">
                  <Button type="link" onClick={() => refModalKyThanhToan.current?.open()} icon={<PlusCircleOutlined />} className="!h-auto !p-0">
                    Thêm kỳ thanh toán
                  </Button>
                </div> */}
              </Table.Summary.Cell>
            </Table.Summary.Row>
          </Table.Summary>
        )}
      />
    </div>
  );

  return (
    <>
      {renderTableThongTinKyThanhToan()}
      <ModalKyThanhToan ref={refModalKyThanhToan} />
    </>
  );
});

ThongTinThanhToanStep4Component.displayName = "ThongTinThanhToanStep4Component";
export const ThongTinThanhToanStep4 = memo(ThongTinThanhToanStep4Component, isEqual);

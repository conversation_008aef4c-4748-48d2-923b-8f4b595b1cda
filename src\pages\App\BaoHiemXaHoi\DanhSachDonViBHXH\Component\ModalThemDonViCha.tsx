import {CheckCircleOutlined, ClearOutlined, SearchOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {Button, FormInput, Highlighter} from "@src/components";
import {defaultPaginationTableProps, defaultTableProps, fillRowTableEmpty} from "@src/hooks";
import {Col, Flex, Form, Input, InputRef, Modal, Row, Table, TableColumnType, Tag, Tooltip} from "antd";
import {FilterDropdownProps} from "antd/es/table/interface";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useImperativeHandle, useMemo, useRef, useState} from "react";
import {defaultFormValue, FormTimKiemDanhSachDonViBHXH, TRANG_THAI_CHI_TIET_DAI_LY} from "../index.configs";
import {useDanhSachDonViBHXHContext} from "../index.context";
import "../index.default.scss";
import {DonViBHXHChaProps, danhSachDonViBHXHChaColumns, DataIndexCha, IModalThemDonViChaRef, TableDonViBHXHChaDataType} from "./index.configs";
const {ma, ten} = FormTimKiemDanhSachDonViBHXH;
const ModalThemDonViChaComponent = forwardRef<IModalThemDonViChaRef, DonViBHXHChaProps>(({onSelectDonViBHXHCha, chiTietDonViBHXH}: DonViBHXHChaProps, ref) => {
  useImperativeHandle(ref, () => ({
    open: () => {
      setIsOpen(true);
      initData();
    },
    close: () => setIsOpen(false),
  }));
  const initData = async () => {
    try {
      // await layDanhSachDonViBHXH;
      await onSearchApi(defaultFormValue);
    } catch (error) {
      console.log("error", error);
    }
  };
  const {layDanhSachDonViBHXH, loading, listDoiTac} = useDanhSachDonViBHXHContext();

  const [donViBHXHChaSelected, setDonViBHXHChaSelected] = useState<CommonExecute.Execute.IDanhSachDonViBHXH | null>(null);
  // const [tongSoDongDaiLyCha, setTongSoDongDaiLyCha] = useState<number>(0);
  const [tongSoDongDaiLy, setTongSoDongDaiLy] = useState<number>(0);
  const [IsOpen, setIsOpen] = useState(false);
  const [searchParams, setSearchParams] = useState<ReactQuery.ITimKiemPhanTrangDanhSachDonViBHXHParams>(defaultFormValue);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchText, setSearchText] = useState("");
  const [searchedColumn, setSearchedColumn] = useState<DataIndexCha | "">("");
  const searchInput = useRef<InputRef>(null);
  const [danhSachDonViBHXHCha, setDanhSachDonViBHXHCha] = useState<Array<CommonExecute.Execute.IDanhSachDonViBHXH>>([]);
  const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: DataIndexCha) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);
  const handleReset = useCallback((clearFilters: () => void, confirm: () => void, dataIndex: DataIndexCha) => {
    clearFilters();
    confirm();
    setSearchText("");
    setSearchedColumn(dataIndex);
  }, []);

  const dataTableListDaiLy = useMemo<Array<TableDonViBHXHChaDataType>>(() => {
    try {
      const filteredData = danhSachDonViBHXHCha.filter(item => item.trang_thai === "D");
      setTongSoDongDaiLy(filteredData.length);
      const tableData = filteredData.map((item: any, index: number) => {
        const prefix =
          Array(item.cap || 0 * 3)
            .fill("\u00A0")
            .join("") + Array(item.cap).fill("+").join("");
        return {
          // stt: item.stt ?? index + 1,
          stt: index + 1,
          ma: item.ma,
          ten: prefix + item.ten,
          ten_tat: item.ten_tat,
          ten_e: item.ten_e,
          mst: item.mst,
          dchi: item.dchi,
          dthoai: item.dthoai,
          trang_thai: item.trang_thai,
          trang_thai_ten: item.trang_thai_ten,
          ma_ct: item.ma_ct,
          key: index.toString(),
          cap: item.cap,
        };
      });
      const arrEmptyRow: Array<TableDonViBHXHChaDataType> = fillRowTableEmpty(tableData.length, 10);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      console.log("dataTableListDaiLy error", error);
      return [];
    }
  }, [danhSachDonViBHXHCha]);

  //Bấm tìm kiếm
  const onSearchApi = async (values: ReactQuery.ITimKiemPhanTrangDanhSachDonViBHXHParams & ReactQuery.IPhanTrang) => {
    // đoạn này để sửa các giá trị undefined => '' trước khi request lên API
    // Ant Design Select không coi "" là một giá trị "chưa chọn", mà là một giá trị đã chọn hợp lệ (dù nó rỗng), nên nó sẽ không hiển thị placeholder nữa.
    const cleanedValues = {
      ...values,
      // Gửi ma_doi_tac_ql lên API,
      ma: values.ma ?? "",
      ten: values.ten ?? "",
      ma_khac: chiTietDonViBHXH?.ma,
      trang_thai: "D", // Gửi trạng thái D lên API
      trang: values.trang ?? 1,
      so_dong: values.so_dong ?? pageSize,
    };
    setSearchParams(cleanedValues);
    setPage(1); // reset về trang 1 khi search mới
    const result = await layDanhSachDonViBHXH({...cleanedValues, trang: 1, so_dong: pageSize});
    if (result) {
      setDanhSachDonViBHXHCha(result.data);
      setTongSoDongDaiLy(result.tong_so_dong);
    } else {
      setDanhSachDonViBHXHCha([]);
      setTongSoDongDaiLy(0);
    }
  };
  //Bấm xác nhận chọn
  const onPressXacNhan = () => {
    try {
      if (donViBHXHChaSelected) {
        // const formattedDaiLyCha = {
        //   ten: daiLyChaSelected.ten || "",
        //   ma: daiLyChaSelected.ma || "",
        // };
        // console.log("formattedDaiLyCha", formattedDaiLyCha);
        onSelectDonViBHXHCha(donViBHXHChaSelected);
        // console.log("Formatted daiLyChaSelected", formattedDaiLyCha);
      } else {
        onSelectDonViBHXHCha(null);
      }
      setIsOpen(false);
    } catch (error) {
      console.log("onPressXacNhan error", error);
    }
  };
  //Gọi api khi chuyển trang
  const onChangePage = useCallback(
    async (page: number, pageSize: number) => {
      try {
        setPage(page);
        setPageSize(pageSize);
        const result = await layDanhSachDonViBHXH({...searchParams, trang: page, so_dong: pageSize});
        if (result) {
          setDanhSachDonViBHXHCha(result.data);
          setTongSoDongDaiLy(result.tong_so_dong);
        } else {
          setDanhSachDonViBHXHCha([]);
          setTongSoDongDaiLy(0);
        }
      } catch (error) {
        console.log("onChangePage error:", error);
        setDanhSachDonViBHXHCha([]);
        setTongSoDongDaiLy(0);
      }
    },
    [searchParams],
  ); //tạo cột tìm kiếm
  //   const daiLyChaSelected = useRef<CommonExecute.Execute.IDanhSachDonViBHXH | null>(null);
  const getColumnSearchProps = (dataIndex: DataIndexCha, title: string): TableColumnType<TableDonViBHXHChaDataType> => ({
    filterDropdown: ({setSelectedKeys, selectedKeys, confirm, clearFilters, close}) => (
      <div style={{padding: 8}} onKeyDown={e => e.stopPropagation()} className="header-cell-custom flex flex-row items-center p-2">
        <Input
          ref={searchInput}
          placeholder={`Tìm theo ${title}`}
          value={selectedKeys[0]}
          onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
          onPressEnter={() => handleSearch(selectedKeys as string[], confirm, dataIndex)}
          style={{display: "block", marginRight: 8}}
        />
        <Tooltip title="Tìm kiếm">
          <Button type="primary" shape="circle" icon={<SearchOutlined />} onClick={() => handleSearch(selectedKeys as string[], confirm, dataIndex)} className="mr-2" />
        </Tooltip>
        <Tooltip title="Xoá">
          <Button type="primary" shape="circle" icon={<ClearOutlined />} onClick={() => clearFilters && handleReset(clearFilters, confirm, dataIndex)} />
        </Tooltip>
      </div>
    ),
    filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} />,
    render: (text, record, index) => {
      return searchedColumn === dataIndex.toString() ? (
        <Highlighter searchWords={[searchText]} textToHighlight={text ? text.toString() : ""} />
      ) : text !== undefined ? (
        text
      ) : (
        <Tag color={"transparent"} className="!text-white text-[11px]">
          {"\u00A0"}
        </Tag>
      );
    },
    onCell: data => {
      let className = "";
      if (dataIndex === "ten") {
        if (data.cap === 1) className = "font-bold";
        else if (data.cap === 3) className = "italic";
      }
      return {
        className: `!py-1 text-[11px] ${className}`,
      };
    },
  });

  const renderTable = () => {
    return (
      <Table<TableDonViBHXHChaDataType>
        {...defaultTableProps}
        className="Don-vi-cha no-header-border-radius"
        // style={{borderBottom: "1px solid #f0f0f0"}}
        loading={loading}
        onRow={record => {
          return {
            style: {cursor: loading ? "progress" : "pointer"},
            onClick: () => {
              if (record.key.toString().includes("empty")) return;
              setDonViBHXHChaSelected(record as CommonExecute.Execute.IDanhSachDonViBHXH);
            },
            onDoubleClick: () => onPressXacNhan(),
          };
        }}
        columns={(danhSachDonViBHXHChaColumns || []).map(item => {
          // Đảm bảo item.key được định nghĩa, item.title là chuỗi và item.key không phải là "stt" trước khi thêm ô tìm kiếm
          return {
            ...item,
            ...(item.key && typeof item.title === "string" && item.key !== "stt" ? getColumnSearchProps(item.key as keyof TableDonViBHXHChaDataType, item.title) : {}),
          };
        })} //định nghĩa cột của table
        rowClassName={record => (record.ma && record.ma === donViBHXHChaSelected?.ma ? "custom-row-selected" : "")}
        dataSource={dataTableListDaiLy}
        // title={renderHeaderTableDanhMucDaiLyCha}
        pagination={{
          ...defaultPaginationTableProps,
          total: tongSoDongDaiLy,
          defaultPageSize: pageSize,
          onChange: (page, pageSize) => {
            onChangePage(page, pageSize);
          },
          // showLessItems: true, //Hiển thị ít mục trang hơn
          // defaultPageSize: 2, //Số lượng item dữ liệu mặc định trên mỗi trang
          // onShowSizeChange : Được gọi khi pageSize thay đổi
          // showTitle: false,// Hiển thị tiêu đề của item trang
          //showTotal : hiển thị UI bao nhiêu / bao nhiêu bản ghi
        }}
      />
    );
  };
  const renderFormInputColum = (props?: any, span = 4) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  //render header table
  const renderHeaderTableDanhMucDaiLyCha = () => {
    return (
      <div
        style={{
          margin: "16px 0",
        }}>
        <Form initialValues={{trang_thai: TRANG_THAI_CHI_TIET_DAI_LY[0].ma}} layout="vertical" className="[&_.ant-form-item]:!mb-0" onFinish={onSearchApi}>
          <div className="flex flex-col gap-5">
            <Row gutter={16} align={"bottom"}>
              {/* {renderFormInputColum({...ma_doi_tac_ql, options: listDoiTac})} */}
              {renderFormInputColum(ma)}
              {renderFormInputColum(ten)}
              {/* {renderFormInputColum({...trang_thai, options: TRANG_THAI_CHI_TIET_DAI_LY})} */}
              <Col span={2}>
                <Button type="primary" htmlType="submit" loading={loading} icon={<SearchOutlined />} className="mb-0 w-full">
                  Tìm kiếm
                </Button>
              </Col>
            </Row>
          </div>
        </Form>
      </div>
    );
  };

  const renderFooter = () => {
    return (
      <Form.Item>
        <Tooltip title={donViBHXHChaSelected ? "" : "Vui lòng chọn đơn vị cấp trên"}>
          <Button type={"primary"} onClick={() => onPressXacNhan()} className="mr-2" iconPosition="end" icon={<CheckCircleOutlined />} disabled={donViBHXHChaSelected ? false : true}>
            Chọn
          </Button>
        </Tooltip>
      </Form.Item>
    );
  };
  return (
    <Flex vertical gap="middle" align="flex-start ">
      <Modal
        maskClosable={false}
        title="Chọn đơn vị cấp trên"
        // centered
        // bodyStyle={{maxHeight: "70vh", overflowY: "auto"}}
        className="modal-chon-don-vi-cha"
        open={IsOpen}
        onOk={() => setIsOpen(false)}
        onCancel={() => setIsOpen(false)}
        width={{
          xs: "85%",
          sm: "85%",
          md: "85%",
          lg: "85%",
          xl: "85%",
          xxl: "85%",
        }}
        style={{top: 10}}
        footer={renderFooter}>
        {renderHeaderTableDanhMucDaiLyCha()}
        {renderTable()}
      </Modal>
    </Flex>
  );
});
//   const tableHeight = useTableHeight(["footer", "header"]);
ModalThemDonViChaComponent.displayName = "ModalThemDonViChaComponent";
export const ModalThemDonViCha = memo(ModalThemDonViChaComponent, isEqual);

import {EditFilled} from "@ant-design/icons";
import {COLOR_PALETTE} from "@src/constants";
import {Input, Popover} from "antd";
import {isEqual} from "lodash";
import React, {memo, useEffect, useState} from "react";

interface ButtonPopoverCellTableProps {
  className?: string;
  placement?: "top" | "left" | "right" | "bottom" | "topLeft" | "topRight" | "bottomLeft" | "bottomRight" | "leftTop" | "leftBottom" | "rightTop" | "rightBottom";
  onClickApDung: (text: string) => void;
  value: string;
}

const {TextArea} = Input;

const InputPopoverCellTableComponent = React.memo(
  ({onClickApDung, placement = "leftBottom", value}: ButtonPopoverCellTableProps) => {
    const [innerValue, setInnerValue] = useState(value);

    useEffect(() => {
      if (value !== innerValue) setInnerValue(value); // đồng bộ từ cha khi giá trị thay đổi
    }, [value]);

    const handleChange = (event: any) => {
      const newVal = event?.target.value;
      setInnerValue(newVal);
    };

    return (
      <Popover
        content={<TextArea rows={3} placeholder="Nhập nội dung ghi chú" value={innerValue} onChange={handleChange} />}
        title={"Nhập ghi chú"}
        trigger="click"
        placement={placement}
        // open={open}
        onOpenChange={() => {
          onClickApDung(innerValue);
        }}>
        <EditFilled style={{color: value && COLOR_PALETTE.green[100]}} />
      </Popover>
    );
  },
  (prevProps, nextProps) => prevProps.value === nextProps.value,
);

InputPopoverCellTableComponent.displayName = "InputPopoverCellTableComponent";

export default memo(InputPopoverCellTableComponent, isEqual);

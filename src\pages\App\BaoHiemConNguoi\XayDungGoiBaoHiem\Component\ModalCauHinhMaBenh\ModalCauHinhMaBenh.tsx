import {forwardRef, memo, useCallback, useImperativeHandle, useRef, useState} from "react";
// import {Mo<PERSON>, Ta<PERSON>, Button, Flex} from "antd";
import {CloseOutlined, CheckOutlined} from "@ant-design/icons";
import {isEqual, debounce} from "lodash";

import {HeaderModal} from "@src/components/";
import {IModalCauHinhMaBenhRef, ModalCauHinhMaBenhProps, LOAI_AP_DUNG, ITableMaBenhWhitelistRef, ITableMaBenhBlacklistRef} from "./Constant";
import {TableMaBenhWhitelist, TableMaBenhBlacklist} from ".";
import {useXayDungGoiBaoHiemContext} from "../../index.context";
import {useAsyncAction} from "@src/hooks";

import "./ModalCauHinhMaBenh.scss";
import {Flex, Tabs} from "antd";
import Modal from "antd/es/modal/Modal";
import Button from "@src/components/Button";

const ModalCauHinhMaBenhComponent = forwardRef<IModalCauHinhMaBenhRef, ModalCauHinhMaBenhProps>(({}: ModalCauHinhMaBenhProps, ref) => {
  // Context
  const {luuCauHinhMaBenhGoiBaoHiem} = useXayDungGoiBaoHiemContext();

  // State management
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [goiBaoHiemId, setGoiBaoHiemId] = useState<number | null>(null);
  const [activeTab, setActiveTab] = useState<string>("1");

  // Refs for table components
  const refTableWhitelist = useRef<ITableMaBenhWhitelistRef>(null);
  const refTableBlacklist = useRef<ITableMaBenhBlacklistRef>(null);

  // Async action for saving disease code configuration
  const saveAsyncFn = useCallback(async () => {
    if (!goiBaoHiemId) {
      console.error("Không có ID gói bảo hiểm");
      return;
    }

    // Xác định tab hiện tại và loại áp dụng
    const isWhitelistTab = activeTab === "1";
    const loaiApDung = isWhitelistTab ? "WL" : "BL"; // WL = Whitelist, BL = Blacklist
    const currentTableRef = isWhitelistTab ? refTableWhitelist : refTableBlacklist;

    // Lấy data từ table hiện tại
    const tableData = currentTableRef.current?.getData();
    if (!tableData) {
      console.error("Không thể lấy dữ liệu từ table");
      return;
    }

    // Lọc các mã bệnh được chọn
    const selectedMaBenh = tableData
      .filter(item => !item.key.includes("empty") && item.is_selected)
      .map(item => ({
        ma: item.ma || "",
        hinh_thuc_ad: item.hinh_thuc_ap_dung || "",
      }));

    // Gọi API lưu cho xây dựng gói bảo hiểm
    const success = await luuCauHinhMaBenhGoiBaoHiem({
      id: goiBaoHiemId,
      loai_ad: loaiApDung,
      benh: selectedMaBenh,
    });

    if (success) {
      // Refresh data sau khi lưu thành công
      // Reload data cho tab hiện tại (giữ nguyên trang)
      if (isWhitelistTab && refTableWhitelist.current) {
        refTableWhitelist.current.refreshData();
      } else if (!isWhitelistTab && refTableBlacklist.current) {
        refTableBlacklist.current.refreshData();
      }
    }
  }, [goiBaoHiemId, activeTab, luuCauHinhMaBenhGoiBaoHiem]);

  // Use async action for save operation with separate loading state
  const [handleSave, isSaving] = useAsyncAction(saveAsyncFn);

  // Debounced tab change handler
  const debouncedTabChange = useCallback(
    debounce((key: string) => {
      setActiveTab(key);
    }, 150),
    [],
  );

  // Expose methods via ref
  useImperativeHandle(ref, () => ({
    open: (id?: number) => {
      setIsOpen(true);
      setGoiBaoHiemId(id || null);
      setActiveTab("1"); // Reset to first tab

      // Reset tất cả selections tạm thời trong cả hai table
      setTimeout(() => {
        refTableWhitelist.current?.resetSelections();
        refTableBlacklist.current?.resetSelections();
      }, 100); // Delay nhỏ để đảm bảo components đã được render
    },
    close: () => {
      setIsOpen(false);
      setGoiBaoHiemId(null);
      setActiveTab("1");

      // Reset selections khi đóng modal
      // refTableWhitelist.current?.resetSelections();
      // refTableBlacklist.current?.resetSelections();
    },
  }));

  // Event handlers
  const closeModal = useCallback(() => {
    setIsOpen(false);
    setGoiBaoHiemId(null);
    setActiveTab("1");

    // Reset selections khi đóng modal
    // refTableWhitelist.current?.resetSelections();
    // refTableBlacklist.current?.resetSelections();
  }, []);

  const handleTabChange = useCallback(
    (activeKey: string) => {
      if (activeKey !== activeTab) {
        debouncedTabChange(activeKey);

        // Chỉ refresh data khi thực sự cần thiết
        setTimeout(() => {
          if (activeKey === "1" && refTableWhitelist.current) {
            refTableWhitelist.current.refreshData();
          } else if (activeKey === "2" && refTableBlacklist.current) {
            refTableBlacklist.current.refreshData();
          }
        }, 200);
      }
    },
    [activeTab, debouncedTabChange],
  );

  // Render methods
  const renderTabs = () => {
    const tabItems = [
      {
        key: "1",
        label: "Mã bệnh white list",
        children: <TableMaBenhWhitelist ref={refTableWhitelist} goiBaoHiemId={goiBaoHiemId || undefined} loaiApDung={LOAI_AP_DUNG.WHITELIST} />,
      },
      {
        key: "2",
        label: "Mã bệnh black list",
        children: <TableMaBenhBlacklist ref={refTableBlacklist} goiBaoHiemId={goiBaoHiemId || undefined} loaiApDung={LOAI_AP_DUNG.BLACKLIST} />,
      },
    ];

    return <Tabs animated={false} size="small" defaultActiveKey="1" activeKey={activeTab} onChange={handleTabChange} items={tabItems} />;
  };

  // Custom footer với căn chỉnh bên phải
  const renderFooter = () => (
    <Flex justify="flex-end" gap="small">
      <Button type="default" icon={<CloseOutlined />} onClick={closeModal}>
        Đóng
      </Button>
      <Button type="primary" icon={<CheckOutlined />} loading={isSaving} onClick={handleSave}>
        Lưu
      </Button>
    </Flex>
  );

  return (
    <Modal
      centered
      maskClosable={false}
      title={<HeaderModal title="Cấu hình mã bệnh" trang_thai_ten="" trang_thai="" />}
      open={isOpen}
      onCancel={closeModal}
      width={"80%"}
      styles={{
        body: {
          height: "78vh",
          // overflow: "auto",
        },
      }}
      footer={renderFooter()}
      className="modal-cau-hinh-ma-benh [&_.ant-space]:w-full">
      {renderTabs()}
    </Modal>
  );
});

ModalCauHinhMaBenhComponent.displayName = "ModalCauHinhMaBenhComponent";
export const ModalCauHinhMaBenh = memo(ModalCauHinhMaBenhComponent, isEqual);

import React from "react";
import {TableColumnsType, TableProps} from "antd";

import {IFormInput, ReactQuery} from "@src/@types";
import {colWidthByKey, defaultTableColumnsProps, ruleInputMessage, validationRules} from "@src/hooks";

//Kiểm tra value có phải là empty hay không (null, undefined, chuỗi rỗng)
const isEmptyValue = (value: any): boolean => {
  return value === null || value === undefined || value === '' || (typeof value === 'string' && value.trim() === '');
};

//Tác dụng: Convert ngày áp dụng từ số hoặc string sang text hiển thị
// DEPRECATED: Logic này đã được move vào content để tương thích với search
// const convertNgayApDungToText = (ngayAd: number | string | null | undefined): string => {
//   //Ki<PERSON><PERSON> tra null, undefined hoặc empty string
//   if (ngayAd === null || ngayAd === undefined || ngayAd === '') {
//     return '';
//   }
  
//   const ngayAdStr = String(ngayAd);
  
//   //Xử lý format number (19000101, 20250701)
//   if (ngayAdStr === '19000101') {
//     return 'Áp dụng từ ngày 01/01/1900';
//   } else if (ngayAdStr === '20250701') {
//     return 'Áp dụng từ ngày 01/07/2025';
//   }
  
//   //Xử lý format string ("01/01/1900", "01/07/2025")  
//   if (ngayAdStr === '01/01/1900') {
//     return 'Áp dụng từ ngày 01/01/1900';
//   } else if (ngayAdStr === '01/07/2025') {
//     return 'Áp dụng từ ngày 01/07/2025';
//   }
  
//   //Nếu không match với các case trên, hiển thị ngày gốc hoặc empty
//   if (ngayAdStr === 'null' || ngayAdStr === 'undefined') {
//     return '';
//   }
  
//   return ngayAdStr;
// };

//===== FORM TÌM KIẾM =====
export interface IFormTimKiemPhuongXaFieldsConfig {
  ma_tinh: IFormInput;
  ma_quan: IFormInput;            
  ngay_ad: IFormInput;
  ma: IFormInput;
  ten: IFormInput;
  trang_thai: IFormInput;
}

//Cấu hình form tìm kiếm phường xã ở header table
export const FormTimKiemDanhMucPhuongXa: IFormTimKiemPhuongXaFieldsConfig = {
  ma_tinh: {
    component: "select",
    name: "ma_tinh",
    label: "Tỉnh/Thành phố",
    placeholder: "Chọn tỉnh/thành phố",
    className: "!mb-0",
  },
  ma_quan: {                      
    component: "select",
    name: "ma_quan",             
    label: "Quận/Huyện",
    placeholder: "Chọn quận/huyện",
    className: "!mb-0",
  },
  ngay_ad: {
    component: "select",
    name: "ngay_ad",
    label: "Ngày áp dụng",
    placeholder: "Chọn ngày áp dụng",
    className: "!mb-0",
  },
  ma: {
    component: "input",
    name: "ma",
    label: "Mã phường/xã",
    placeholder: "Nhập mã phường/xã",
    className: "!mb-0",
  },
  ten: {
    component: "input",
    name: "ten",
    label: "Tên phường/xã",
    placeholder: "Nhập tên phường/xã",
    className: "!mb-0",
  },
  trang_thai: {
    component: "select",
    name: "trang_thai",
    label: "Trạng thái",
    placeholder: "Chọn trạng thái",
    className: "!mb-0",
  },
};

//===== FORM TẠO MỚI/CHỈNH SỬA =====
export interface IFormTaoMoiPhuongXaFieldsConfig {
  ma_tinh: IFormInput;
  ma_quan: IFormInput;            
  ngay_ad: IFormInput;
  ma: IFormInput;
  ten: IFormInput;
  postcode: IFormInput;           
  stt: IFormInput;
  trang_thai: IFormInput;
}

//Cấu hình form tạo mới/chỉnh sửa phường xã trong modal
export const FormTaoMoiPhuongXa: IFormTaoMoiPhuongXaFieldsConfig = {
  ma_tinh: {
    component: "select",
    label: "Tỉnh/Thành phố",
    name: "ma_tinh",
    placeholder: "Chọn tỉnh/thành phố",
    rules: [ruleInputMessage.required],
  },
  ma_quan: {                     
    component: "select",
    label: "Quận/Huyện",
    name: "ma_quan",             
    placeholder: "Chọn quận/huyện",
    rules: [ruleInputMessage.required],
  },
  ngay_ad: {
    component: "select",
    label: "Ngày áp dụng",
    name: "ngay_ad",
    placeholder: "Chọn ngày áp dụng",
    rules: [ruleInputMessage.required],
  },
  ma: {
    component: "input",
    label: "Mã phường/xã",
    name: "ma",
    placeholder: "Nhập mã phường/xã",
    rules: [ruleInputMessage.required],
  },
  ten: {
    component: "input",
    label: "Tên phường/xã",
    name: "ten",
    placeholder: "Nhập tên phường/xã",
    rules: [ruleInputMessage.required],
  },
  postcode: {
    component: "input",
    label: "Mã bưu điện",
    name: "postcode",
    placeholder: "Nhập mã bưu điện",
    rules: [],
  },
  stt: {
    component: "input",
    label: "Số thứ tự",
    name: "stt",
    placeholder: "Nhập số thứ tự",
    rules: [],
  },
  trang_thai: {
    component: "select",
    label: "Trạng thái",
    name: "trang_thai",
    placeholder: "Chọn trạng thái",
    rules: [ruleInputMessage.required],
  },
};

//===== RADIO ITEMS - TRẠNG THÁI =====
//Options cho dropdown trạng thái trong form tìm kiếm
export const radioItemTrangThaiPhuongXaSelect: Array<{ma: string; ten: string}> = [
  {ma: "", ten: "Tất cả"},
  {ma: "D", ten: "Đang sử dụng"},
  {ma: "K", ten: "Ngưng sử dụng"},
];

export const radioItemTrangThaiPhuongXaTable = [
  {value: "Đang sử dụng", text: "Đang sử dụng"},
  {value: "Ngưng sử dụng", text: "Ngưng sử dụng"},
];

//===== TRẠNG THÁI TẠO MỚI =====
export const TRANG_THAI_TAO_MOI_PHUONG_XA = [
  {ma: "D", ten: "Đang sử dụng"},
  {ma: "K", ten: "Ngưng sử dụng"},
];

//===== NGÀY ÁP DỤNG =====
export const radioItemNgayApDungPhuongXaSelect: Array<{ma: string; ten: string}> = [
  {ma: "", ten: "Tất cả"},
  {ma: "19000101", ten: "01/01/1900"},
  {ma: "20250701", ten: "01/07/2025"},
];

export const NGAY_AD_TAO_MOI_PHUONG_XA = [
  {ten: "01/01/1900", ma: "19000101"},
  {ten: "01/07/2025", ma: "20250701"},
];

//===== KIỂU DỮ LIỆU TABLE =====
export interface TablePhuongXaColumnDataType {
  key: string;
  sott?: number;
  ngay_ad?: number;
  ma?: string;
  ten?: string;
  ma_tinh?: string;
  ten_tinh?: string;
  ma_quan?: string;
  ten_quan?: string;
  postcode?: string;
  stt?: number;
  trang_thai?: string;
  ngay_tao?: string;
  nguoi_tao?: string;
  ngay_cap_nhat?: string;
  nguoi_cap_nhat?: string;
  trang_thai_ten?: string;
}

//===== CỘT TABLE =====
//Cấu hình các cột hiển thị trong bảng danh sách phường xã
export const tablePhuongXaColumn: TableProps<TablePhuongXaColumnDataType>["columns"] = [
  {
    title: "STT",
    dataIndex: "sott",
    key: "sott",
    align: "center",
    width: 40,
    ...defaultTableColumnsProps,
  },
  {
    title: "Ngày áp dụng",
    dataIndex: "ngay_ad",
    key: "ngay_ad",
    align: "center",
    width: 80,
    // DEPRECATED: Render function đã được move vào content để tương thích với search
    // render: (ngay_ad: number, record: TablePhuongXaColumnDataType) => {
    //   //Bỏ qua empty rows
    //   if (record.key?.toString().includes("empty")) {
    //     return "";
    //   }
    //   
    //   return convertNgayApDungToText(ngay_ad);
    // },
    ...defaultTableColumnsProps,
  },
  {
    title: "Mã phường/xã",
    dataIndex: "ma",
    key: "ma",
    align: "center",
    width: 70,
    ...defaultTableColumnsProps,
  },
  {
    title: "Tên phường/xã",
    dataIndex: "ten",
    key: "ten",
    width: 100,
    ...defaultTableColumnsProps,
  },
  {
    title: "Mã bưu điện",
    dataIndex: "postcode",
    key: "postcode",
    align: "center",
    width: 70,
    ...defaultTableColumnsProps,
  },
  {
    title: "Tên quận/huyện",
    dataIndex: "ten_quan",
    key: "ten_quan",
    width: 80,
    ...defaultTableColumnsProps,
  },
  {
    title: "Tên tỉnh/thành phố",
    dataIndex: "ten_tinh",
    key: "ten_tinh",
    width: 100,
    ...defaultTableColumnsProps,
  },
  {
    title: "Ngày tạo",
    dataIndex: "ngay_tao",
    key: "ngay_tao",
    width: 120,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Người tạo",
    dataIndex: "nguoi_tao",
    key: "nguoi_tao",
    width: 80,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Ngày cập nhật",
    dataIndex: "ngay_cap_nhat",
    key: "ngay_cap_nhat",
    width: 120,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Người cập nhật",
    dataIndex: "nguoi_cap_nhat",
    key: "nguoi_cap_nhat",
    width: 80,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Trạng thái",
    dataIndex: "trang_thai_ten",
    key: "trang_thai_ten",
    width: 80,
    align: "center",
    ...defaultTableColumnsProps,
  },
];

//keyof: return ra key của interface TablePhuongXaColumnDataType;
export type TablePhuongXaColumnDataIndex = keyof TablePhuongXaColumnDataType;

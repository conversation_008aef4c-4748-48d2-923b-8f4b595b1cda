import {ReactQuery} from "@src/@types";
import {TableDanhMucBangMaBenhColumnDataType} from "./index.configs";

export interface IQuanLyDanhMucBangMaBenhContextProps {
  listDanhMucBangMaBenh: Array<CommonExecute.Execute.IDanhMucBangMaBenh>;
  tongSoDong: number;
  loading: boolean;
  filterParams: ReactQuery.ITimKiemPhanTrangDanhMucBangMaBenhParams & ReactQuery.IPhanTrang;
  getListDanhMucBangMaBenh: (params?: ReactQuery.ITimKiemPhanTrangDanhMucBangMaBenhParams & ReactQuery.IPhanTrang) => Promise<void>;
  getChiTietDanhMucBangMaBenh: (params: TableDanhMucBangMaBenhColumnDataType) => Promise<CommonExecute.Execute.IDanhMucBangMaBenh>;
  capNhatChiTietDanhMucBangMaBenh: (params: ReactQuery.ICapNhatDanhMucBangMaBenhParams, isEditMode?: boolean) => Promise<any>;
  setFilterParams: React.Dispatch<React.SetStateAction<ReactQuery.ITimKiemPhanTrangDanhMucBangMaBenhParams & ReactQuery.IPhanTrang>>;
}

import { ReactQuery } from "@src/@types"

// khai báo interface props Context của Login
export interface HeThongChucNangContextProps {
  danhSachHeThongChucNang: Array<CommonExecute.Execute.IDanhSachHeThongChucNangPhanTrang>,
  loading: boolean,
  onUpdateHeThongChucNang: (item: ReactQuery.IUpdateChucNangParams) => void
  layDanhSachChucNangPhanTrang: (params: ReactQuery.ILayDanhSachChucNangPhanTrangParams) => void
  tongSoDong: number,
  layChiTietChucNang: (
    params: ReactQuery.IChiTietChucNangParams
  ) => Promise<CommonExecute.Execute.IChiTietChucNang | null>;
  defaultFormValue: object
  onSyncChucNang: () => void
}

import {PlusCircleOutlined, SearchOutlined} from "@ant-design/icons";
import {IFormInput, ReactQuery} from "@src/@types";
import {Button, FormInput, Highlighter, TableFilterDropdown} from "@src/components";
import {COLOR_PALETTE, ID_PAGE} from "@src/constants";
import {defaultPaginationTableProps, defaultTableProps, fillRowTableEmpty} from "@src/hooks";
import {Col, Form, InputRef, Row, Table, TableColumnType, Tag} from "antd";
import {FilterDropdownProps} from "antd/es/table/interface";
import dayjs from "dayjs";
import {isEqual} from "lodash";
import React, {memo, useCallback, useMemo, useRef, useState} from "react";
import {IModalThemHopDongRef, ModalThemHopDongConNguoi} from "./Component";
import {FormTimKiemQuanLyHopDong, listTrangThaiHopDongSelect, radioItemTrangThaiHopDongTable, tableHopDongColumn, TableHopDongColumnDataIndex, TableHopDongColumnDataType} from "./index.configs";
import {useHopDongConNguoiContext} from "./index.context";
import "./index.default.scss";

const {tu_ngay, den_ngay, ma_doi_tac_ql, ma_chi_nhanh_ql, trang_thai, nd_tim, so_hd, gcn, ten_ndbh, cmt_ndbh, dthoai_ndbh} = FormTimKiemQuanLyHopDong;

const HopDongConNguoiContent: React.FC = memo(() => {
  const {listHopDong, loading, filterHopDongParams, tongSoDongHopDong, listDoiTac, listChiNhanh, getChiTietHopDong, setFilterHopDongParams} = useHopDongConNguoiContext();

  const refModalThemHopDong = useRef<IModalThemHopDongRef>(null);

  const refSearchInputTable = useRef<InputRef>(null);
  const [searchTextTable, setSearchTextTable] = useState<string>(""); //lưu textsearch để hiển thị vào cell
  const [searchedColumn, setSearchedColumn] = useState<TableHopDongColumnDataIndex | "">(""); //key column đang được search

  const [formTimKiemHopDong] = Form.useForm();
  const watchDoiTacCapDon = Form.useWatch("ma_doi_tac_ql", formTimKiemHopDong);

  const dataTableListHopDong = useMemo<Array<TableHopDongColumnDataType>>(() => {
    try {
      const tableData = listHopDong.map(itemHopDong => {
        return {
          key: itemHopDong.so_id as number,
          ...itemHopDong,
          sott: itemHopDong.sott,
          so_id: itemHopDong.so_id as number,
          ma_doi_tac_ql: itemHopDong.ma_doi_tac_ql,
          ngay_tao: itemHopDong.ngay_tao,
          ngay_hl: itemHopDong.ngay_hl?.toString(),
          ngay_kt: itemHopDong.ngay_kt?.toString(),
          ngay_cap: itemHopDong.ngay_cap,
          trang_thai_ten: itemHopDong.trang_thai_ten,
          kieu_hd_ten: itemHopDong.kieu_hd_ten,
          so_hd: itemHopDong.so_hd,
          vip: itemHopDong.vip ?? "Không",
          ten_kh: itemHopDong.ten_kh,
          sl_dtuong: itemHopDong.sl_dtuong,
          tong_phi: itemHopDong.tong_phi,
          so_hd_g: itemHopDong.so_hd_g,
          ma_doi_tac: itemHopDong.ma_doi_tac,
        };
      });
      const arrEmptyRow: Array<TableHopDongColumnDataType> = fillRowTableEmpty(tableData.length);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      console.log("dataTableListHopDong error", error);
      return [];
    }
  }, [listHopDong]);

  const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: TableHopDongColumnDataIndex) => {
    confirm();
    setSearchTextTable(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);

  const handleReset = useCallback(
    (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: TableHopDongColumnDataIndex) => {
      clearFilters();
      setSearchTextTable("");
      handleSearch([""], confirm, dataIndex);
    },
    [handleSearch],
  );

  const onSearchApi = (values: ReactQuery.ILayDanhSachHopDongConNguoiPhanTrangParams) => {
    setFilterHopDongParams({...filterHopDongParams, ...values, trang: 1, so_dong: 20});
  };

  // RENDER
  const renderFormInputColum = (props: IFormInput, span = 4) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );

  const renderHeaderTableQuanLyHopDongConNguoi = () => {
    return (
      <Form
        form={formTimKiemHopDong}
        initialValues={{so_hd: "", ten_ndbh: "", den_ngay: dayjs(), tu_ngay: dayjs().startOf("year")}}
        layout="vertical" //Tất cả các input nằm trên 1 dòng
        onFinish={onSearchApi}
        className="[&_.ant-form-item]:mb-0" // cho margin-bottom của form = 0
      >
        <div
        // className="flex w-full flex-wrap items-end"
        >
          <Row gutter={16} align="bottom">
            {renderFormInputColum(tu_ngay)}
            {renderFormInputColum(den_ngay)}
            {renderFormInputColum(nd_tim)}
            {renderFormInputColum({
              ...ma_doi_tac_ql,
              options: listDoiTac,
              onChange: () => formTimKiemHopDong.setFieldValue("ma_chi_nhanh_ql", undefined),
            })}
            {renderFormInputColum({...ma_chi_nhanh_ql, options: listChiNhanh.filter(item => item.ma_doi_tac === watchDoiTacCapDon)})}
            {renderFormInputColum({...trang_thai, options: listTrangThaiHopDongSelect})}
          </Row>
          <Row gutter={16} align="bottom" className="mt-2">
            {renderFormInputColum(so_hd)}
            {renderFormInputColum(gcn)}
            {renderFormInputColum(ten_ndbh)}
            {renderFormInputColum(cmt_ndbh)}
            {renderFormInputColum(dthoai_ndbh)}
            <Col span={2}>
              <Form.Item>
                <Button type="primary" htmlType="submit" loading={loading} icon={<SearchOutlined />} block>
                  Tìm
                </Button>
              </Form.Item>
            </Col>
            <Col span={2}>
              <Button type="primary" icon={<PlusCircleOutlined />} onClick={() => refModalThemHopDong.current?.open()} block>
                Thêm
              </Button>
            </Col>
          </Row>
        </div>
      </Form>
    );
  };

  // dataIndex : là các key của column, title : tiêu đề của column
  const getColumnSearchProps = (dataIndex: TableHopDongColumnDataIndex, title: string): TableColumnType<TableHopDongColumnDataType> => ({
    /**
     *  filterDropdown: UI tuỳ chỉnh của ô filter (component render ra trong dropdown filter)
     * @param param0
     * @returns
     * dataIndex !== "trang_thai_ten"
     */
    filterDropdown:
      dataIndex !== "trang_thai_ten"
        ? filterDropdownParams => (
            <TableFilterDropdown
              ref={refSearchInputTable}
              title={title}
              dataIndex={dataIndex}
              handleSearch={(selectedKeys, confirm, dataIndex) => handleSearch(selectedKeys, confirm, dataIndex)}
              handleReset={(clearFilters, confirm, dataIndex) => clearFilters && handleReset(clearFilters, confirm, dataIndex)}
              {...filterDropdownParams}
            />
          )
        : undefined,
    /**
     * onFilter: hàm filter dữ liệu tại client-side (bắt buộc phải khai báo để filter hoạt động)
     * do hàm onFilter luôn phải trả về boolean, nên thêm || false vào để k bị lỗi
     * @param value : giá trị filter người dùng nhập vào
     * @param record : từng bản ghi trong dataSource
     * @returns
     */
    onFilter: (value, record) => {
      return (
        record[dataIndex]
          ?.toString()
          .toLowerCase()
          .includes((value as string).toLowerCase()) || false
      );
    },
    //filterDropdownProps: thuộc tính tuỳ chỉnh hành vi dropdown
    filterDropdownProps: {
      // onOpenChange: callback khi dropdown mở/ tắt thì sẽ focus vào input
      onOpenChange(open) {
        if (open) {
          setTimeout(() => refSearchInputTable.current?.select(), 100); // Focus/ select input khi mở filter
        }
      },
    },
    filters: dataIndex === "trang_thai_ten" ? radioItemTrangThaiHopDongTable : undefined,
    render: (text, record) => {
      if (dataIndex === "trang_thai_ten") {
        let color = COLOR_PALETTE.gray[70];
        if (text === "Đã duyệt") color = COLOR_PALETTE.green[100];
        else if (text === "Đã huỷ") color = COLOR_PALETTE.red[50];
        else if (text === "Đang trình duyệt") color = COLOR_PALETTE.yellow[50];
        if (record.key.toString().includes("empty")) return "";
        return (
          <Tag color={color} className="text-[11px]">
            {text}
          </Tag>
        );
      }
      return searchedColumn === dataIndex ? (
        <Highlighter searchWords={[searchTextTable]} textToHighlight={text ? text.toString() : ""} />
      ) : text !== undefined ? (
        text
      ) : (
        <Tag color={"transparent"} className="!text-white text-[11px]">
          {"\u00A0"}
        </Tag>
      );
    },
    onCell: () => ({
      className: `!py-1 text-[11px] ${dataIndex === "tong_phi" ? " text-right" : ""}`,
    }),
  });

  return (
    <div id={ID_PAGE.HOP_DONG_CON_NGUOI}>
      <Table<TableHopDongColumnDataType>
        {...defaultTableProps}
        dataSource={dataTableListHopDong} //mảng dữ liệu record được hiển thị
        //định nghĩa cột của table
        columns={
          tableHopDongColumn?.map(item => {
            //Nếu là cột stt thì không hiển thị search
            return {...item, ...(item.key === "sott" || item.key === "vip" ? {} : getColumnSearchProps(item.key as keyof TableHopDongColumnDataType, item.title as string))};
          }) || []
        }
        loading={loading} //hiển thị loading khi đang gọi API để loading data
        pagination={{
          ...defaultPaginationTableProps,
          total: tongSoDongHopDong,
          onChange: (page, pageSize) => {
            //được gọi khi page size change
            setFilterHopDongParams({...filterHopDongParams, trang: page, so_dong: pageSize});
          },
        }}
        title={renderHeaderTableQuanLyHopDongConNguoi}
        onRow={record => {
          return {
            style: {cursor: loading ? "progress" : "pointer"}, //  thể hiện vị trí con trỏ là button(👈)
            onClick: async () => {
              if (record.key.toString().includes("empty")) return;
              const response = await getChiTietHopDong(record as ReactQuery.IChiTietChucDanhParams);
              if (response?.so_id) refModalThemHopDong.current?.open(response);
            },
          };
        }}
      />
      <ModalThemHopDongConNguoi ref={refModalThemHopDong} />
    </div>
  );
}, isEqual);

HopDongConNguoiContent.displayName = "HopDongConNguoiContent";

export default HopDongConNguoiContent;

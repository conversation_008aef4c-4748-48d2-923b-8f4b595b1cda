import {forwardRef, memo, useEffect, useImperativeHandle, useState} from "react";
import FormChiTietNhomPhanCapDuyet, {IModalChiTietNhomPhanCapDuyetRef, Props, TRANG_THAI} from "./index.configs";
import {Col, Flex, Form, Modal, Row} from "antd";
import {useNhomPhanCapDuyetContext} from "../index.context";
import {isEqual} from "lodash";
import {Button, FormInput, HeaderModal} from "@src/components";
import {CheckOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
const {ma, ten, stt, trang_thai} = FormChiTietNhomPhanCapDuyet;

const ModalChiTietNhomPhanCapDuyetComponent = forwardRef<IModalChiTietNhomPhanCapDuyetRef, Props>(({}: Props, ref) => {
  useImperativeHandle(ref, () => ({
    open: (dataNhomPhanCapDuyet?: CommonExecute.Execute.INhomPhanCapDuyet) => {
      setIsOpen(true);
      form.resetFields();
      if (dataNhomPhanCapDuyet) setChiTietNhomPhanCapDuyet(dataNhomPhanCapDuyet);
      console.log("dataNhomPhanCapDuyet", dataNhomPhanCapDuyet);
    },

    close: () => setIsOpen(false),
  }));
  const [chiTietNhomPhanCapDuyet, setChiTietNhomPhanCapDuyet] = useState<CommonExecute.Execute.INhomPhanCapDuyet | null>(null);
  const [isOpen, setIsOpen] = useState(false);

  const {loading, onUpdateNhomPhanCapDuyet, filterParams, setFilterParams} = useNhomPhanCapDuyetContext();

  const [form] = Form.useForm();
  const formValues = Form.useWatch([], form);
  useEffect(() => {
    if (chiTietNhomPhanCapDuyet) {
      const arrFormData = [];
      for (const key in chiTietNhomPhanCapDuyet) {
        arrFormData.push({
          name: key as keyof CommonExecute.Execute.INhomPhanCapDuyet,
          value: chiTietNhomPhanCapDuyet[key as keyof CommonExecute.Execute.INhomPhanCapDuyet],
        });
      }
      form.setFields(arrFormData);
    }
  }, [chiTietNhomPhanCapDuyet, form]);
  useEffect(() => {
    form
      .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
      .then(() => {
        setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
      })
      .catch(() => {
        setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
      });
  }, [form, formValues]);
  const closeModal = () => {
    setIsOpen(false);
    setChiTietNhomPhanCapDuyet(null);
    form.resetFields();
    setFilterParams(filterParams);
  };

  const renderFormInputColum = (props?: any, span = 12) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  const onConfirm = async () => {
    try {
      const values: ReactQuery.IUpdateNhomPhanCapDuyetParams = form.getFieldsValue(); //lấy ra values của form
      const response = await onUpdateNhomPhanCapDuyet(values);
      console.log("check respon ", response);
      if (response === -1) {
        console.log("cập nhật thành công");
        setIsOpen(false);
      } else {
        console.log("cập nhật thất bại");
      }
    } catch (error) {
      console.log("onConfirm", error);
    }
  };
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  //render footer
  const renderFooter = () => {
    return (
      <Button type="primary" disabled={disableSubmit} iconPosition="end" onClick={() => onConfirm()} className="mr-2" icon={<CheckOutlined />}>
        Lưu
      </Button>
    );
  };
  const renderForm = () => (
    <Form form={form} layout="vertical" initialValues={{trang_thai: TRANG_THAI[0].ma}}>
      {/* MÃ */}
      <Row gutter={16}>
        {renderFormInputColum({...ma, disabled: chiTietNhomPhanCapDuyet ? true : false})}
        {renderFormInputColum({...ten})}
        {renderFormInputColum({...trang_thai, options: TRANG_THAI})}
        {renderFormInputColum({...stt})}
      </Row>
    </Form>
  );
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        title={
          <HeaderModal
            title={chiTietNhomPhanCapDuyet ? `Chi tiết nhóm phân cấp duyệt ${chiTietNhomPhanCapDuyet.ten}` : "Tạo mới nhóm phân cấp duyệt"}
            // trang_thai_ten={chiTietNhomPhanCapDuyet?.trang_thai_ten}
            trang_thai={chiTietNhomPhanCapDuyet?.trang_thai}
          />
        }
        maskClosable={false}
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width={{
          xs: "55%",
          sm: "55%",
          md: "55%",
          lg: "55%",
          xl: "55%",
          xxl: "55%",
        }}
        // styles={{
        //   body: {
        //     height: "60vh",
        //   },
        // }}
        footer={renderFooter}
        className="[&_.ant-space]:w-full">
        {renderForm()}
      </Modal>
    </Flex>
  );
});
ModalChiTietNhomPhanCapDuyetComponent.displayName = "ModalChiTietNhomPhanCapDuyetComponent";
export const ModalChiTietNhomPhanCapDuyet = memo(ModalChiTietNhomPhanCapDuyetComponent, isEqual);

import i18n from "@src/configs/i18n";
import "@i18n";

function strings() {
  const {t} = i18n;
  return {
    label_home: t("label.home", {defaultValue: ""}),
    label_demo: t("label.demo", {defaultValue: ""}),
    label_logout: t("label.logout", {defaultValue: ""}),
    label_dark_mode: t("label.dark_mode", {defaultValue: ""}),
    label_sign_in: t("label.sign_in", {defaultValue: ""}),
    label_sign_up: t("label.sign_up", {defaultValue: ""}),
    label_email: t("label.email", {defaultValue: ""}),
    label_password: t("label.password", {defaultValue: ""}),
    label_confirm_password: t("label.confirm_password", {defaultValue: ""}),
    label_setting: t("label.setting", {defaultValue: ""}),
    label_vietnamese: t("label.vietnamese", {defaultValue: ""}),
    label_english: t("label.english", {defaultValue: ""}),
    label_language: t("label.language", {defaultValue: ""}),
    label_search: t("label.search", {defaultValue: ""}),
    label_no_account: t("label.no_account", {defaultValue: ""}),
    label_has_account: t("label.has_account", {defaultValue: ""}),
    label_click_here_to_sign_up: t("label.click_here_to_sign_up", {defaultValue: ""}),
    label_click_here_to_sign_in: t("label.click_here_to_sign_in", {defaultValue: ""}),
    label_no_data: t("label.no_data", {defaultValue: ""}),
    label_boilerplate_logo: t("label.boilerplate_logo", {defaultValue: ""}),
    label_button: t("label.button", {defaultValue: ""}),
    label_switch: t("label.switch", {defaultValue: ""}),
    label_avatar: t("label.avatar", {defaultValue: ""}),
    label_input: t("label.input", {defaultValue: ""}),
    label_image: t("label.image", {defaultValue: ""}),
    label_paragraph: t("label.paragraph", {defaultValue: ""}),
    label_list: t("label.list", {defaultValue: ""}),
    label_version: t("label.version", {defaultValue: ""}),
    error_field_invalid: t("error.field_invalid", {defaultValue: ""}),
    placeholder_input: t("placeholder.input", {defaultValue: ""}),
  };
}
export default strings;

import React, {PropsWithChildren, useCallback, useEffect, useMemo, useState} from "react";

import {QuanLyDanhMucMaBenhICDContext} from "./index.context";
import {IQuanLyDanhMucMaBenhICDContextProps} from "./index.model";
import {ACTION_CODE} from "@src/constants";
import {ReactQuery} from "@src/@types";
import {TableDanhMucMaBenhICDColumnDataType} from "./index.configs";
import {useCommonExecute} from "@src/services/react-queries";
import {message} from "antd";

const QuanLyDanhMucMaBenhICDProvider: React.FC<PropsWithChildren> = props => {
  const {children} = props;
  const mutateUseCommonExecute = useCommonExecute();

  const [listDanhMucMaBenhICD, setListDanhMucMaBenhICD] = useState<Array<CommonExecute.Execute.IDanhMucMaBenhICD>>([]);
  const [tongSoDong, setTongSoDong] = useState<number>(0);
  const [filterParams, setFilterParams] = useState<ReactQuery.ITimKiemPhanTrangDanhMucMaBenhICDParams & ReactQuery.IPhanTrang>({ma: "", ten: "", trang_thai: "", trang: 1, so_dong: 20});
  
  //khởi tạo dữ liệu ban đàu
  useEffect(() => {//nè
    initData();
  }, []);

  const getChiTietDanhMucMaBenhICD = useCallback(
    async (data: TableDanhMucMaBenhICDColumnDataType) => {
      try {
        // console.log("[Provider] getChiTietDanhMucMaBenhICD được gọi với data:", data);
        
        if (!data.ma) {
          // console.log("[Provider] Không có mã để tìm kiếm, data.ma =", data.ma);
          return {} as CommonExecute.Execute.IDanhMucMaBenhICD;
        }
        
        console.log("[Provider] Gọi API với params:", {
          ma: data.ma,
          actionCode: ACTION_CODE.CHI_TIET_DANH_MUC_MA_BENH_ICD
        });
        
        const response = await mutateUseCommonExecute.mutateAsync({
          ma: data.ma,
          actionCode: ACTION_CODE.CHI_TIET_DANH_MUC_MA_BENH_ICD,
        } as any);
        
        console.log("[Provider] Raw response từ API:", response);
        console.log("[Provider] Response.data:", response.data);
        
        // Xử lý structure response.data.lke
       
        // Xử lý structure response.data
        const responseData = response.data as any;

        // Kiểm tra xem có data.dinh_nghia không (structure mới)
        if (responseData && responseData.dinh_nghia && typeof responseData.dinh_nghia === 'object') {
          const result = responseData.dinh_nghia as CommonExecute.Execute.IDanhMucMaBenhICD;
          // console.log("[Provider] Trả về data từ dinh_nghia:", result);
          return result;
        }
        // Fallback: kiểm tra structure cũ data.lke
        else if (responseData && responseData.lke && Array.isArray(responseData.lke) && responseData.lke.length > 0) {
          const result = responseData.lke[0] as CommonExecute.Execute.IDanhMucMaBenhICD;
          // console.log("[Provider] Trả về data từ lke[0]:", result);
          return result;
        }
        // Fallback: trả về chính responseData nếu có ma
        else if (responseData && responseData.ma) {
          // console.log("[Provider] Trả về responseData trực tiếp:", responseData);
          return responseData as CommonExecute.Execute.IDanhMucMaBenhICD;
        }
        else {
          console.log("[Provider] Không tìm thấy data trong response");
          return {} as CommonExecute.Execute.IDanhMucMaBenhICD;
        }
      
      } catch (error) {
        console.log("[Provider] getChiTietDanhMucMaBenhICD error:", error);
        return {} as CommonExecute.Execute.IDanhMucMaBenhICD;
      }
    },
    [mutateUseCommonExecute],
  );

  const getListDanhMucMaBenhICD = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        ...filterParams,
        actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_DANH_MUC_MA_BENH_ICD,
      } as any);
      if (response.data) {
        setListDanhMucMaBenhICD(response.data.data);
        setTongSoDong(response.data.tong_so_dong);
      }
    } catch (error) {
      console.log("getListDanhMucMaBenhICD error ", error);
    }
  }, [mutateUseCommonExecute]);

  useEffect(() => {
    getListDanhMucMaBenhICD();
  }, [filterParams]);

  const capNhatChiTietDanhMucMaBenhICD = useCallback(
    async (data: ReactQuery.ICapNhatDanhMucMaBenhICDParams, isEditMode: boolean = false) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...data,
          actionCode: ACTION_CODE.UPDATE_DANH_MUC_MA_BENH_ICD,
        } as any);
        message.success(isEditMode ? "Cập nhật mã bệnh ICD thành công" : "Thêm mới mã bệnh ICD thành công");
        return response.data;
      } catch (error) {
        message.error(isEditMode ? "Có lỗi xảy ra khi cập nhật mã bệnh ICD" : "Có lỗi xảy ra khi thêm mới mã bệnh ICD");
        console.log("capNhatChiTietDanhMucMaBenhICD err", error);
        // return {} as CommonExecute.Execute.IDanhMucMaBenhICD;
      }
    },
    [mutateUseCommonExecute],
  );
  

  //initData chỉ dùng trong useEffect 1 lần, không truyền đi đâu cả, nên không cần memo hoá
  const initData = () => {};

  const value = useMemo<IQuanLyDanhMucMaBenhICDContextProps>(
    () => ({
      listDanhMucMaBenhICD,
      tongSoDong,
      loading: mutateUseCommonExecute.isLoading,
      filterParams,
      getListDanhMucMaBenhICD,
      getChiTietDanhMucMaBenhICD,
      capNhatChiTietDanhMucMaBenhICD,
      setFilterParams,
    }),
    [listDanhMucMaBenhICD, tongSoDong, mutateUseCommonExecute, filterParams, getListDanhMucMaBenhICD, getChiTietDanhMucMaBenhICD, capNhatChiTietDanhMucMaBenhICD],
  );

  return <QuanLyDanhMucMaBenhICDContext.Provider value={value}>{children}</QuanLyDanhMucMaBenhICDContext.Provider>;
};

export default QuanLyDanhMucMaBenhICDProvider;

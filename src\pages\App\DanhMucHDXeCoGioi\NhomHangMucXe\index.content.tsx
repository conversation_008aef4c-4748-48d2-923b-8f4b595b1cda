import React, {memo, useCallback, useMemo, useRef, useState} from "react";
import {Col, Form, Input, InputRef, Row, Table, TableColumnType, Tag, Tooltip, Card, Flex} from "antd";
import {FilterDropdownProps} from "antd/es/table/interface";
import {PlusCircleOutlined, SearchOutlined, ClearOutlined, DeleteOutlined, EditOutlined, PlusOutlined} from "@ant-design/icons";
import {isEqual} from "lodash";
import {Button, FormInput, Highlighter, TableFilterDropdown} from "@src/components";
import {COLOR_PALETTE, ID_PAGE} from "@src/constants";
import {IFormInput, ReactQuery} from "@src/@types";
import {defaultPaginationTableProps, defaultTableProps, fillRowTableEmpty} from "@src/hooks";
import {useNhomHangMucXeContext} from "./index.context";
import {
  FormTimKiemNhomHangMucXe, 
  tableNhomHangMucXeColumn, 
  TableNhomHangMucXeColumnDataType, 
  TableNhomHangMucXeColumnDataIndex, 
  radioItemTrangThaiNhomHangMucXeSelect, 
  radioItemTrangThaiNhomHangMucXeTable, 
  DANH_SACH_NGHIEP_VU_TIM_KIEM, 
  DANH_SACH_NGHIEP_VU_XE
} from "./index.configs";
import {ModalChiTietNhomHangMucXe, IModalChiTietNhomHangMucXeRef} from "./Component";
import "./index.default.scss";

type DataIndex = keyof TableNhomHangMucXeColumnDataType;

const {
  ma,
  ten,
  nv,
  trang_thai,
} = FormTimKiemNhomHangMucXe;

/**
 * Hiển thị table danh sách và form tìm kiếm
 */
const NhomHangMucXeContent: React.FC = memo(() => {
  const {listNhomHangMucXe, loading, tongSoDong, filterParams, getChiTietNhomHangMucXe, setFilterParams} = useNhomHangMucXeContext();
  const refModalChiTietNhomHangMucXe = useRef<IModalChiTietNhomHangMucXeRef>(null);
  const refSearchInputTable = useRef<InputRef>(null);
  const [searchTextTable, setSearchTextTable] = useState<string>("");
  const [searchedColumn, setSearchedColumn] = useState<TableNhomHangMucXeColumnDataIndex | "">(""); 
  /**
   * Chuyển đổi dữ liệu từ API thành format phù hợp cho Table
   */
  const dataTableListNhomHangMucXe = useMemo<Array<TableNhomHangMucXeColumnDataType>>(() => {
    try {
      const tableData = listNhomHangMucXe.map((itemNhomHangMucXe: CommonExecute.Execute.INhomHangMucXe, index: number) => {
        return {
          key: itemNhomHangMucXe.ma || index.toString(),
          sott: itemNhomHangMucXe.sott,
          ma: itemNhomHangMucXe.ma,
          ten: itemNhomHangMucXe.ten,
          nv: itemNhomHangMucXe.nv,
          ten_nv: itemNhomHangMucXe.ten_nv,
          stt: itemNhomHangMucXe.stt,
          trang_thai: itemNhomHangMucXe.trang_thai,
          ngay_tao: itemNhomHangMucXe.ngay_tao,
          nguoi_tao: itemNhomHangMucXe.nguoi_tao,
          ngay_cap_nhat: itemNhomHangMucXe.ngay_cap_nhat,
          nguoi_cap_nhat: itemNhomHangMucXe.nguoi_cap_nhat,
          trang_thai_ten: itemNhomHangMucXe.trang_thai_ten,
        } as TableNhomHangMucXeColumnDataType;
      });
      const arrEmptyRow: Array<TableNhomHangMucXeColumnDataType> = fillRowTableEmpty(tableData.length, defaultPaginationTableProps.defaultPageSize);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      console.log("dataTableListNhomHangMucXe error", error);
      return [];
    }
  }, [listNhomHangMucXe]);

  const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: TableNhomHangMucXeColumnDataIndex) => {
    confirm();
    setSearchTextTable(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);

  const handleReset = useCallback(
    (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: TableNhomHangMucXeColumnDataIndex) => {
      clearFilters();
      setSearchTextTable("");
      handleSearch([""], confirm, dataIndex);
    },
    [handleSearch],
  );

  /**
   * Xử lý submit form tìm kiếm và cập nhật filter parameters
   */
  const onSearchApi = (values: ReactQuery.ITimKiemPhanTrangNhomHangMucXeParams) => {
    setFilterParams({...filterParams, ...values, trang: 1, so_dong: defaultPaginationTableProps.defaultPageSize});
  };

  /**
   * Xử lý click vào row để hiển thị modal chi tiết nhóm hạng mục xe
   */
  const handleRowClick = useCallback(async (record: TableNhomHangMucXeColumnDataType) => {
    if (record.key.toString().includes("empty") || loading) return;
    
    try {
      if (!record.ma) return;
      
      const response = await getChiTietNhomHangMucXe(record);
      if (response) {
        refModalChiTietNhomHangMucXe.current?.open(response);
      }
    } catch (error) {
      console.error("handleRowClick error", error);
    }
  }, [getChiTietNhomHangMucXe, loading]);

  /**
   * Render form input column với responsive layout
   */
  const renderFormInputColum = (props: IFormInput, span = 4) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );

  /**
   * Render header table với form tìm kiếm và button tạo mới
   */
  const renderHeaderTableNhomHangMucXe = () => {
    return (
      <>
        <Form initialValues={{}} layout="vertical" className="[&_.ant-form-item]:mb-0" onFinish={onSearchApi}>
          <Row gutter={16} align={"bottom"}>
            {renderFormInputColum(ma)}
            {renderFormInputColum(ten)}
            {renderFormInputColum({...nv, options: DANH_SACH_NGHIEP_VU_TIM_KIEM})}
            {renderFormInputColum({...trang_thai, options: radioItemTrangThaiNhomHangMucXeSelect})}
            <Col span={3}>
              <Button type="primary" htmlType="submit" loading={loading} icon={<SearchOutlined />} block>
                Tìm kiếm
              </Button>
            </Col>
            <Col span={3}>
              <Button
                type="primary"
                block
                icon={<PlusCircleOutlined />}
                onClick={() => {
                  refModalChiTietNhomHangMucXe.current?.open();
                }}>
                Tạo mới
              </Button>
            </Col>
          </Row>
        </Form>
      </>
    );
  };

  /**
   * Cấu hình search properties cho từng column
   * @param dataIndex - Key của column
   * @param title - Tiêu đề của column
   * @returns TableColumnType với search functionality
   */
  const getColumnSearchProps = (dataIndex: TableNhomHangMucXeColumnDataIndex, title: string): TableColumnType<TableNhomHangMucXeColumnDataType> => ({
    /**
     * filterDropdown: UI tuỳ chỉnh của ô filter (component render ra trong dropdown filter)
     */
    filterDropdown:
      dataIndex !== "trang_thai_ten"
        ? filterDropdownParams => (
            <TableFilterDropdown
              ref={refSearchInputTable}
              title={title}
              dataIndex={dataIndex}
              handleSearch={(selectedKeys, confirm, dataIndex) => handleSearch(selectedKeys, confirm, dataIndex)}
              handleReset={(clearFilters, confirm, dataIndex) => clearFilters && handleReset(clearFilters, confirm, dataIndex)}
              {...filterDropdownParams}
            />
          )
        : undefined,
    /**
     * onFilter: hàm filter dữ liệu tại client-side (bắt buộc phải khai báo để filter hoạt động)
     */
    onFilter: (value, record) => {
      return (
        record[dataIndex]
          ?.toString()
          .toLowerCase()
          .includes((value as string).toLowerCase()) || false
      );
    },
    /**
     * filterDropdownProps: thuộc tính tuì chỉnh hành vi dropdown
     */
    filterDropdownProps: {
      // onOpenChange: callback khi dropdown mở/ tắt thì sẽ focus vào input
      onOpenChange(open) {
        if (open) {
          setTimeout(() => refSearchInputTable.current?.select(), 100); // Focus/ select input khi mở filter
        }
      },
    },
    filters: dataIndex === "trang_thai_ten" ? radioItemTrangThaiNhomHangMucXeTable : undefined,
    render: (text, record) => {
      // Render trạng thái với màu sắc
      if (dataIndex === "trang_thai_ten") {
        const color = text === "Đang sử dụng" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];
        if (record.key.toString().includes("empty")) return "";
        return (
          <Tag color={color} className="text-[11px]">
            {text}
          </Tag>
        );
      }
      
      // Xử lý hiển thị cột nghiệp vụ: chuyển mã (XE, XE_MAY) thành tên
      // if (dataIndex === "nv") {
      //   if (record.key.toString().includes("empty") || !text) return "";
        
      //   const nghiepVuInfo = DANH_SACH_NGHIEP_VU_XE.find(item => item.ma === text);
      //   const displayText = nghiepVuInfo ? nghiepVuInfo.ten : text;
        
      //   return searchedColumn === dataIndex ? (
      //     <Highlighter searchWords={[searchTextTable]} textToHighlight={displayText.toString()} />
      //   ) : (
      //     displayText
      //   );
      // }
      
      // Render text thông thường với highlight
      return searchedColumn === dataIndex ? (
        <Highlighter searchWords={[searchTextTable]} textToHighlight={text ? text.toString() : ""} />
      ) : text !== undefined ? (
        text
      ) : (
        <Tag color={"transparent"} className="!text-white text-[11px]">
          {"\u00A0"}
        </Tag>
      );
    },
  });

  return (
    <div id={ID_PAGE.NHOM_HANG_MUC_XE} className="[&_.ant-space]:w-full">
      <Table<TableNhomHangMucXeColumnDataType>
        {...defaultTableProps}
        dataSource={dataTableListNhomHangMucXe} // Mảng dữ liệu record được hiển thị
        columns={
          tableNhomHangMucXeColumn?.map(item => {
            // Nếu là cột sott thì không hiển thị search
            return {...item, ...(item.key === "sott" ? {} : getColumnSearchProps(item.key as keyof TableNhomHangMucXeColumnDataType, item.title as string))};
          }) || []
        } // Định nghĩa cột của table
        loading={loading} // Hiển thị loading khi đang gọi API để loading data
        pagination={{
          ...defaultPaginationTableProps,
          total: tongSoDong,
          onChange: (page, pageSize) => {
            setFilterParams({...filterParams, trang: page, so_dong: pageSize});
          },
        }} // Cấu hình phân trang
        title={renderHeaderTableNhomHangMucXe} // Render header search form
        onRow={record => ({
          style: {cursor: loading ? "progress" : "pointer"},
          onClick: () => handleRowClick(record),
        })} // Xử lý click row
      />
      <ModalChiTietNhomHangMucXe 
        ref={refModalChiTietNhomHangMucXe} 
        onAfterSave={() => {
          // Refresh data sau khi save
          setFilterParams({...filterParams});
        }} 
      />
    </div>
  );
});

export default NhomHangMucXeContent;

.custom-input,
.custom-input-password {
  &.ant-input {
    @apply border-purple-30 text-purple-40;
    &::placeholder {
      @apply text-purple-20;
    }
    &.ant-input-status-error {
      @apply border-red-40 #{!important};
    }
  }

  &.ant-input-affix-wrapper {
    @apply border-purple-30;
    .ant-input,
    .anticon {
      @apply text-purple-40;
      &::placeholder {
        @apply text-purple-10;
      }
    }
    &.ant-input-affix-wrapper-status-error {
      @apply border-red-40 #{!important};
      .anticon {
        @apply text-red-40;
      }
    }
    &:hover {
      @apply border-purple-20 text-purple-30;
    }
  }
}

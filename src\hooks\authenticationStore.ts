import {Authen} from "@src/@types/Authentication";
import {AUTHEN_KEYS} from "@src/constants";
import {LOCAL_STORAGE_KEY} from "@src/constants/localStorage";
import Cookies from "js-cookie";
import {create} from "zustand";
import {createJSONStorage, persist} from "zustand/middleware";

export interface IProfileStore {
  profile: Authen.Profile.IMetaData;
  setProfile: (profile: Partial<Authen.Profile.IMetaData>) => void;
  setToken: (newToken: string) => void;
  setPw: (pw: string) => void;
}
//create : hàm tạo store
export const useProfile = create(
  //persist : middileware để lưu trạng thái vào localStorage
  //persist<T>() có generic type IProfileStore – kiểu dữ liệu của store gồm 1 biến profile
  persist<IProfileStore, [], [], Pick<IProfileStore, "profile">>(
    (set, get) => ({
      //khởi tạo state profile từ cookie + localStorage
      profile: {
        nsd: get()?.profile.nsd || {
          anh_dai_dien: "",
          dthoai: "",
          email: "",
          ma: "",
          ma_chi_nhanh: "",
          ma_chuc_danh: "",
          ma_doi_tac: "",
          ngay_sinh: "",
          phong: "",
          tai_khoa: "",
          ten: "",
          trang_trai: "",
        }, //nếu đã có dữ liệu trong storage (lấy bằng hàm get()?.profile), nếu không có thì lấy mặc định
        token: Cookies.get(AUTHEN_KEYS.TOKEN) ?? "",
        refresh_token: Cookies.get(AUTHEN_KEYS.REFRESH_TOKEN) ?? "",
        pw: Cookies.get(AUTHEN_KEYS.PW) ?? "",
      },
      setProfile: (profile: Partial<Authen.Profile.IMetaData>) => set(state => ({...state, profile: {...state.profile, ...profile}})),
      setToken: (newToken: string) => set(state => ({...state, profile: {...state.profile, token: newToken}})),
      setPw: (pw: string) => set(state => ({...state, profile: {...state.profile, pw}})),
    }),
    //cấu hình perstist
    {
      name: LOCAL_STORAGE_KEY.USER, //key để lưu trong localStorate
      storage: createJSONStorage(() => localStorage), //nơi lưu trữ là localStorage
      //chỉ định những phần nào trong state sẽ được lưu, tránh lưu những thứ k cần thiết như các hàm set
      partialize: state => ({
        profile: state.profile,
      }),
    },
  ),
);

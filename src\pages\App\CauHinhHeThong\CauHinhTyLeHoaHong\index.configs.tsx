import {IFormInput, ReactQuery} from "@src/@types";
import {colWidthByKey, defaultTableColumnsProps} from "@src/hooks";
import {TableProps} from "antd";

export interface TableDanhMucDaiLyDataType {
  key: string;
  stt?: number;
  ma?: string;
  ma_doi_tac_ql?: string;
  doi_tac_ql_ten_tat?: string;
  ten?: string;
  doi_tac_ql?: string;
  loai?: string;
  ten_loai?: string;
  ten_dd?: string;
  so_dien_thoai?: string;
  email?: string;
  mst?: string;
  cmt?: string;
  ngay_tao?: string;
  nguoi_tao?: string;
  ngay_cap_nhat?: string;
  nguoi_cap_nhat?: string;
  trang_thai_ten?: string;
}

// ĐỊNH NGHĨA Column RENDER RA TỪNG Ô TRONG CỘT
//đọc https://ant.design/components/table#column để biết thêm về column
export const danhMucDaiLyColumns: TableProps<TableDanhMucDaiLyDataType>["columns"] = [
  {title: "STT", dataIndex: "stt", key: "stt", width: colWidthByKey.sott, align: "center", ...defaultTableColumnsProps},
  {title: "Mã", dataIndex: "ma", key: "ma", width: 120, align: "center", ...defaultTableColumnsProps},
  {title: "Tên", dataIndex: "ten", key: "ten", width: 200, align: "left", ...defaultTableColumnsProps},
  {title: "Đối tác", dataIndex: "doi_tac_ql_ten_tat", key: "doi_tac_ql_ten_tat", width: 150, align: "center", ...defaultTableColumnsProps},
  {title: "Loại đại lý", dataIndex: "ten_loai", key: "ten_loai", width: 120, align: "center", ...defaultTableColumnsProps},
  {title: "Tên đại diện", dataIndex: "nguoi_dd", key: "nguoi_dd", width: 150, align: "center", ...defaultTableColumnsProps},
  {title: "Số điện thoại", dataIndex: "dthoai_dd", key: "dthoai_dd", width: 120, align: "center", ...defaultTableColumnsProps},
  {title: "Email", dataIndex: "email_dd", key: "email_dd", width: 150, align: "center", ...defaultTableColumnsProps},
  {title: "Mã số thuế", dataIndex: "mst_dd", key: "mst_dd", width: 120, align: "center", ...defaultTableColumnsProps},
  {title: "CMT/CCCD", dataIndex: "cmt_dd", key: "cmt_dd", width: 120, align: "center", ...defaultTableColumnsProps},
  {title: "Ngày tạo", dataIndex: "ngay_tao", key: "ngay_tao", width: colWidthByKey.ngay_tao, align: "center", ...defaultTableColumnsProps},
  {title: "Người tạo", dataIndex: "nguoi_tao", key: "nguoi_tao", width: colWidthByKey.nguoi_tao, align: "center", ...defaultTableColumnsProps},
  {title: "Ngày cập nhật", dataIndex: "ngay_cap_nhat", key: "ngay_cap_nhat", width: colWidthByKey.ngay_cap_nhat, align: "center", ...defaultTableColumnsProps},
  {title: "Người cập nhật", dataIndex: "nguoi_cap_nhat", key: "nguoi_cap_nhat", width: colWidthByKey.nguoi_cap_nhat, align: "center", ...defaultTableColumnsProps},
  {title: "Trạng Thái", dataIndex: "trang_thai_ten", key: "trang_thai_ten", width: 130, align: "center", ...defaultTableColumnsProps},
];
//Form
export interface IFormTimKiemDanhMucDaiLyFieldsConfig {
  ma_doi_tac_ql: IFormInput;
  ma: IFormInput;
  ten: IFormInput;
  loai: IFormInput;
  trang_thai: IFormInput;
}
export const FormTimKiemDanhMucDaiLy: IFormTimKiemDanhMucDaiLyFieldsConfig = {
  ma_doi_tac_ql: {
    component: "select",
    name: "ma_doi_tac_ql",
    label: "Đối tác",
    placeholder: "Chọn đối tác",
    width: "20%",
  },
  ma: {
    component: "input",
    name: "ma",
    label: "Mã đại lý",
    placeholder: "Mã đại lý",
    width: "10%",
  },
  ten: {
    component: "input",
    name: "ten",
    label: "Tên đại lý",
    placeholder: "Tên đại lý",
    width: "20%",
  },
  loai: {
    component: "select",
    name: "loai",
    label: "Loại đại lý",
    placeholder: "Chọn loại đại lý",
    width: "20%",
  },
  trang_thai: {
    component: "select",
    name: "trang_thai",
    label: "Trạng thái",
    placeholder: "Chọn trạng thái",
    width: "20%",
  },
};
// defaultFormValue tìm kiếm phân trang đại lý
export const defaultFormValue: ReactQuery.ITimKiemPhanTrangDanhSachDaiLyParams = {
  ma_doi_tac_ql: "",
  ma: "",
  ten: "",
  loai: "",
  trang_thai: "",
  // trang: 1,
  // so_dong: 20,
};
export const TRANG_THAI_CHI_TIET_DAI_LY = [
  {ten: "Đang sử dụng", ma: "D"},
  {ten: "Ngừng sử dụng", ma: "K"},
];
export const LOAI_DAI_LY = [
  {ten: "Đại lý tổ chức", ma: "T"},
  {ten: "Đại lý cá nhân", ma: "C"},
];
export const radioItemTrangThaiDaiLyTable = [
  {value: "Đang sử dụng", text: "Đang sử dụng"},
  {value: "Ngừng sử dụng", text: "Ngừng sử dụng"},
];
export const LOAI_DAI_LY_TK = [
  {ten: "Đại lý tổ chức", ma: "T"},
  {ten: "Đại lý cá nhân", ma: "C"},
];

import React, {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useState, useContext} from "react";
import {Button, Col, Flex, Form, Modal, Row} from "antd";
import {ArrowLeftOutlined, CheckOutlined} from "@ant-design/icons";
import {isEqual} from "lodash";

import {FormInput, HeaderModal, Popcomfirm} from "@src/components";
import {IFormInput, ReactQuery} from "@src/@types";

import DanhMucQuanHuyenContext from "../index.context";
import {FormTaoMoiQuanHuyen, TRANG_THAI_TAO_MOI_QUAN_HUYEN, NGAY_AD_TAO_MOI_QUAN_HUYEN} from "../index.configs";
import {IModalChiTietQuanHuyenRef} from "./index.configs";

interface IModalChiTietQuanHuyenProps {
  onAfterSave?: () => void;
  danhSachTinhThanh?: Array<{ma: string; ten: string}>; // Danh sách tỉnh thành cho dropdown
}

const ModalChiTietQuanHuyenComponent = forwardRef<IModalChiTietQuanHuyenRef, IModalChiTietQuanHuyenProps>(({onAfterSave, danhSachTinhThanh = []}, ref) => {
  // ===== CONTEXT & FORM =====
  const {capNhatChiTietQuanHuyen, listQuanHuyen, loading} = useContext(DanhMucQuanHuyenContext);
  const [form] = Form.useForm();

  // ===== STATE =====
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [chiTietQuanHuyen, setChiTietQuanHuyen] = useState<CommonExecute.Execute.IDanhMucQuanHuyen | null>(null);
  const [disableSubmit, setDisableSubmit] = useState<boolean>(true);

  // ===== FORM VALUES WATCHING =====
  const formValues = Form.useWatch([], form);

  // ===== FORM CONFIGURATIONS =====
  const {ma_tinh, ngay_ad, ma, ten, postcode, stt, trang_thai} = FormTaoMoiQuanHuyen;

  // ===== DROPDOWN OPTIONS =====
  const tinhThanhOptions = danhSachTinhThanh.filter(item => item.ma !== ""); // Bỏ "Tất cả" option
  const trangThaiOptions = TRANG_THAI_TAO_MOI_QUAN_HUYEN;

  // Debug log để kiểm tra trangThaiOptions
  console.log("[ModalChiTietQuanHuyen] trangThaiOptions:", trangThaiOptions);

  // ===== IMPERATIVE HANDLE =====
  useImperativeHandle(ref, () => ({
    open: (data?: CommonExecute.Execute.IDanhMucQuanHuyen) => {
      setChiTietQuanHuyen(data || null);
      setIsOpen(true);
    },
  }));

  // ===== EFFECTS =====

  // Set form values when modal opens
  useEffect(() => {
    if (chiTietQuanHuyen && isOpen) {
      // Chỉnh sửa - load data vào form
      const arrFormData = [];
      for (const key in chiTietQuanHuyen) {
        let value = chiTietQuanHuyen[key as keyof CommonExecute.Execute.IDanhMucQuanHuyen];

        // Convert ngay_ad to match dropdown options format
        if (key === "ngay_ad") {
          if (typeof value === "number") {
            // Convert number to string (19000101 -> "19000101")
            value = String(value);
          } else if (typeof value === "string") {
            // Convert string format ("01/01/1900" -> "19000101" hoặc "01/07/2025" -> "20250701")
            if (value === "01/01/1900") {
              value = "19000101";
            } else if (value === "01/07/2025") {
              value = "20250701";
            } else {
              // Nếu đã là format number string, giữ nguyên
              value = value;
            }
          }
        }

        arrFormData.push({
          name: key,
          value: value,
        });
      }
      form.setFields(arrFormData);
    } else if (!chiTietQuanHuyen && isOpen) {
      // Tạo mới - set giá trị mặc định
      form.setFields([
        {name: "ma_tinh", value: undefined},
        {name: "ngay_ad", value: undefined},
        {name: "ma", value: ""},
        {name: "ten", value: ""},
        {name: "postcode", value: ""},
        {name: "stt", value: ""},
        {name: "trang_thai", value: "D"}, // Mặc định là đang sử dụng
      ]);
    }
  }, [chiTietQuanHuyen, isOpen, form]);

  // Form validation
  useEffect(() => {
    if (!isOpen) return;

    form
      .validateFields({validateOnly: true})
      .then(() => {
        setDisableSubmit(false);
      })
      .catch(() => {
        setDisableSubmit(true);
      });
  }, [form, formValues, isOpen]);

  // ===== HANDLERS =====

  const closeModal = useCallback(() => {
    setIsOpen(false);
    setChiTietQuanHuyen(null);
    form.resetFields();
  }, [form]);

  const onConfirm = async () => {
    try {
      const values: ReactQuery.ICapNhatDanhMucQuanHuyenParams = form.getFieldsValue();

      // Validate mã quận huyện không trùng khi tạo mới
      // if (!chiTietQuanHuyen) {
      //   for (let i = 0; i < listQuanHuyen.length; i++) {
      //     if (listQuanHuyen[i].ma === values.ma && listQuanHuyen[i].ma_tinh === values.ma_tinh) {
      //       form.setFields([
      //         {
      //           name: "ma",
      //           errors: ["Mã quận huyện đã tồn tại trong tỉnh này!"],
      //         },
      //       ]);
      //       return;
      //     }
      //   }
      // }

      await capNhatChiTietQuanHuyen(values);
      closeModal();
      onAfterSave?.();
    } catch (error) {
      console.log("onConfirm error", error);
    }
  };

  // ===== RENDER FUNCTIONS =====

  const renderFooter = () => {
    return (
      <Form.Item>
        <Button type="default" onClick={() => closeModal()} className="mr-2" icon={<ArrowLeftOutlined />}>
          Quay lại
        </Button>
        <Popcomfirm
          title={disableSubmit ? "Vui lòng nhập đầy đủ thông tin" : ""}
          onConfirm={onConfirm}
          okText="Lưu"
          description="Bạn có chắc muốn lưu thông tin?"
          buttonTitle={"Lưu"}
          buttonDisable={disableSubmit}
          buttonClassName="w-40"
          buttonIcon={<CheckOutlined />}
          iconPosition="end"
          loading={loading}
        />
      </Form.Item>
    );
  };
  const renderFormColumn = (props: IFormInput, span = 8) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  // const renderFormColumn = (props: IFormInput) => {
  //   // Debug log để kiểm tra props của trang_thai
  //   if (props.name === "trang_thai") {
  //     console.log("[ModalChiTietQuanHuyen] trang_thai props:", props);
  //   }

  //   return (
  //     <Col span={8}>
  //       <FormInput {...props} />
  //     </Col>
  //   );
  // };

  const renderForm = () => (
    <Form form={form} layout="vertical">
      {/* TỈNH THÀNH, NGÀY ÁP DỤNG, MÃ QUẬN HUYỆN */}
      <Row gutter={16}>
        {renderFormColumn({...ma, disabled: chiTietQuanHuyen ? true : false}, 6)}
        {renderFormColumn({...ten}, 10)}
        {renderFormColumn(
          {
            ...ngay_ad,
            options: NGAY_AD_TAO_MOI_QUAN_HUYEN,
            fieldNames: {label: "ten", value: "ma"},
            disabled: chiTietQuanHuyen ? true : false,
          },
          8,
        )}
      </Row>

      {/* TÊN QUẬN HUYỆN, MÃ BƯU ĐIỆN, STT */}
      <Row gutter={16}>
        {renderFormColumn({...ma_tinh, options: tinhThanhOptions, disabled: chiTietQuanHuyen ? true : false}, 6)}
        {renderFormColumn({...postcode}, 6)}
        {renderFormColumn({...stt}, 4)}
        {renderFormColumn(
          {
            ...trang_thai,
            options: trangThaiOptions,
            fieldNames: {label: "label", value: "value"},
          },
          8,
        )}
      </Row>

      {/* TRẠNG THÁI */}
      <Row gutter={16}></Row>
    </Form>
  );

  // ===== MAIN RENDER =====
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        title={<HeaderModal title={chiTietQuanHuyen ? `${chiTietQuanHuyen.ten}` : "Tạo mới quận huyện"} trang_thai_ten={chiTietQuanHuyen?.trang_thai_ten} trang_thai={chiTietQuanHuyen?.trang_thai} />}
        maskClosable={false}
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width={700}
        styles={{
          body: {
            paddingTop: "8px",
            paddingBottom: "16px",
          },
        }}
        footer={renderFooter()}>
        {renderForm()}
      </Modal>
    </Flex>
  );
});

ModalChiTietQuanHuyenComponent.displayName = "ModalChiTietQuanHuyenComponent";
export const ModalChiTietQuanHuyen = memo(ModalChiTietQuanHuyenComponent, isEqual);

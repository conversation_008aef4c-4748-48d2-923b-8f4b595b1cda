import {createContext, useContext} from "react";

import {IQuanLyChucDanhContextProps} from "./index.model";
import {ReactQuery} from "@src/@types";

export const QuanLyChucDanhContext = createContext<IQuanLyChucDanhContextProps>({
  listDoiTac: [],
  listChucDanh: [],
  tongSoDong: 0,
  loading: false,
  filterParams: {},
  searchChucDanh: () => Promise.resolve(),
  getChiTietChucDanh: (params: ReactQuery.IChiTietChucDanhParams) => Promise.resolve({} as CommonExecute.Execute.IChiTietChucDanh),
  capNhatChiTietChucDanh: (params: ReactQuery.IUpdateChucDanhParams) => Promise.resolve(),
  setFilterParams: () => {},
});

export const useQuanLyChucDanhContext = () => useContext(QuanLyChucDanhContext);

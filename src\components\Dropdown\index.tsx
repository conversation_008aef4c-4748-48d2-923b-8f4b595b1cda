import React, {memo} from "react";
import {Dropdown as AntDropdown, DropdownProps} from "antd";
import {twMerge} from "tailwind-merge";

import {isEqual} from "lodash";

const DropdownComponent: React.FC<DropdownProps> = props => {
  const {children, menu, className = "", overlayClassName = "", ...etc} = props;
  return (
    <AntDropdown menu={menu} className={twMerge("custom-dropdown", className)} overlayClassName={twMerge("custom-dropdown-overlay", overlayClassName)} {...etc}>
      {children}
    </AntDropdown>
  );
};

const Dropdown = memo(DropdownComponent, isEqual);

export default Dropdown;

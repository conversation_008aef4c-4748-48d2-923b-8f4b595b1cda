import {ClearOutlined, PlusCircleOutlined, SearchOutlined} from "@ant-design/icons";
import {IFormInput, ReactQuery} from "@src/@types";
import {Button, FormInput, Highlighter} from "@src/components";
import {COLOR_PALETTE, ID_PAGE} from "@src/constants";
import {defaultPaginationTableProps, defaultTableProps, fillRowTableEmpty, useTableHeight} from "@src/hooks";
import {Col, Form, Input, InputRef, Row, Table, TableColumnType, Tag, Tooltip} from "antd";
import {FilterDropdownProps} from "antd/es/table/interface";
import {isEqual} from "lodash";
import React, {memo, useCallback, useMemo, useRef, useState} from "react";
import {ModalChiTietChucDanh} from "./Component";
import {IModalChiTietChucDanhRef} from "./Component/Constant";
import {
  FormTimKiemQuanLyChucDanh,
  radioItemTrangThaiChucDanhTable,
  tableChucDanhColumn,
  TableChucDanhColumnDataIndex,
  TableChucDanhColumnDataType,
  radioItemTrangThaiChucDanhSelect,
} from "./index.configs";
import {useQuanLyChucDanhContext} from "./index.context";
import "./index.default.scss";


const {ten, trang_thai, ma_doi_tac_ql} = FormTimKiemQuanLyChucDanh;

const QuanLyChucDanhContent: React.FC = memo(() => {
  const {listChucDanh, loading, filterParams, getChiTietChucDanh, setFilterParams, tongSoDong, listDoiTac} = useQuanLyChucDanhContext();
  const tableHeight = useTableHeight(["footer", "header"]);

  const refModalChiTietChucDanh = useRef<IModalChiTietChucDanhRef>(null);

  const refSearchInputTable = useRef<InputRef>(null);
  const [searchTextTable, setSearchTextTable] = useState<string>(""); //lưu textsearch để hiển thị vào cell
  const [searchedColumn, setSearchedColumn] = useState<TableChucDanhColumnDataIndex | "">(""); //key column đang được search

  const [form] = Form.useForm();

  const dataTableListChucDanh = useMemo<Array<TableChucDanhColumnDataType>>(() => {
    try {
      const tableData = listChucDanh.map(itemChucDanh => {
        return {
          key: itemChucDanh.ma,
          sott: itemChucDanh.sott,
          ma: itemChucDanh.ma,
          ma_doi_tac_ql: itemChucDanh.ma_doi_tac_ql,
          ten: itemChucDanh.ten,
          ngay_tao: itemChucDanh.ngay_tao,
          nguoi_tao: itemChucDanh.nguoi_tao,
          ngay_cap_nhat: itemChucDanh.ngay_cap_nhat,
          nguoi_cap_nhat: itemChucDanh.nguoi_cap_nhat,
          trang_thai_ten: itemChucDanh.trang_thai_ten,
          doi_tac_ql_ten_tat: itemChucDanh.doi_tac_ql_ten_tat,
        };
      });
      const arrEmptyRow: Array<TableChucDanhColumnDataType> = fillRowTableEmpty(tableData.length, defaultPaginationTableProps.defaultPageSize);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      console.log("dataTableListChucDanh error", error);
      return [];
    }
  }, [listChucDanh]);

  const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: TableChucDanhColumnDataIndex) => {
    confirm();
    setSearchTextTable(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);

  const handleReset = useCallback(
    (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: TableChucDanhColumnDataIndex) => {
      clearFilters();
      setSearchTextTable("");
      handleSearch([""], confirm, dataIndex);
    },
    [handleSearch],
  );

  const onSearchApi = (values: ReactQuery.ILayDanhSachChucDanhPhanTrangParams) => {
    setFilterParams({...filterParams, ...values, trang: 1, so_dong: defaultPaginationTableProps.defaultPageSize});
  };

  // RENDER
  const renderFormInputColum = (props: IFormInput, span = 4) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );

  const renderHeaderTableQuanLyChucDanh = () => {
    return (
      <Form
        initialValues={{}}
        layout="vertical" //Tất cả các input nằm trên 1 dòng
        onFinish={onSearchApi}
        className="[&_.ant-form-item]:mb-0">
        <Row gutter={16} align={"bottom"}>
          {renderFormInputColum(ten)}
          {renderFormInputColum({...trang_thai, options: radioItemTrangThaiChucDanhSelect})}
          {renderFormInputColum({...ma_doi_tac_ql, options: listDoiTac})}
          <Col span={3}>
            <Button type="primary" htmlType="submit" loading={loading} icon={<SearchOutlined />} block>
              Tìm kiếm
            </Button>
          </Col>
          <Col span={3}>
            <Button type="primary" icon={<PlusCircleOutlined />} onClick={() => refModalChiTietChucDanh.current?.open()} block>
              Tạo mới
            </Button>
          </Col>
        </Row>
      </Form>
    );
  };

  // dataIndex : là các key của column, title : tiêu đề của column
  const getColumnSearchProps = (dataIndex: TableChucDanhColumnDataIndex, title: string): TableColumnType<TableChucDanhColumnDataType> => ({
    /**
     *  filterDropdown: UI tuỳ chỉnh của ô filter (component render ra trong dropdown filter)
     * @param param0
     * @returns
     * dataIndex !== "trang_thai_ten"
     */
    filterDropdown:
      dataIndex !== "trang_thai_ten"
        ? ({
            setSelectedKeys,
            selectedKeys,
            confirm,
            clearFilters,
            // close
          }) => {
            return (
              <div onKeyDown={e => e.stopPropagation()} className="flex flex-row items-center p-2">
                {/* setSelectedKeys: thay đổi danh sách tìm kiếm */}
                {/* selectedKeys: danh sách giá trị hiện tại trong filter */}
                {/* confirm: hàm gọi khi áp dụng filter */}
                {/* clearFilters: hàm dùng để clear input filter */}
                {/* close: đóng dropdown filter */}
                <Input
                  ref={refSearchInputTable}
                  placeholder={`Nhập ${title}`}
                  value={selectedKeys[0]}
                  onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                  onPressEnter={() => handleSearch(selectedKeys as string[], confirm, dataIndex)}
                  className="mr-2 flex p-2"
                />
                <Tooltip title="Tìm kiếm">
                  <Button type="primary" shape="circle" icon={<SearchOutlined />} onClick={() => handleSearch(selectedKeys as string[], confirm, dataIndex)} className="mr-2" />
                </Tooltip>
                <Tooltip title="Xoá">
                  <Button type="primary" shape="circle" icon={<ClearOutlined />} onClick={() => clearFilters && handleReset(clearFilters, confirm, dataIndex)} />
                </Tooltip>
              </div>
            );
          }
        : undefined,
    /**
     * filterIcon : icon hiển thị trên header column khi filter
     * @param filtered :  // "filtered". là boolean, true nếu đang áp dụng filter,
     *  biến này thay đổi khi hàm confirm được gọi, hoặc hàm clearFilters được gọi
     * @returns
     */
    filterIcon: (filtered: boolean) => {
      return <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} size={40} />;
    },
    /**
     * onFilter: hàm filter dữ liệu tại client-side (bắt buộc phải khai báo để filter hoạt động)
     * do hàm onFilter luôn phải trả về boolean, nên thêm || false vào để k bị lỗi
     * @param value : giá trị filter người dùng nhập vào
     * @param record : từng bản ghi trong dataSource
     * @returns
     */
    onFilter: (value, record) => {
      return (
        record[dataIndex]
          ?.toString()
          .toLowerCase()
          .includes((value as string).toLowerCase()) || false
      );
    },
    /**
     * filterDropdownProps: thuộc tính tuì chỉnh hành vi dropdown
     */
    filterDropdownProps: {
      // onOpenChange: callback khi dropdown mở/ tắt thì sẽ focus vào input
      onOpenChange(open) {
        if (open) {
          setTimeout(() => refSearchInputTable.current?.select(), 100); // Focus/ select input khi mở filter
        }
      },
    },
    filterSearch: true,
    filters: dataIndex === "trang_thai_ten" ? radioItemTrangThaiChucDanhTable : undefined,
    render: (
      text,
      record,
      //  index
    ) => {
      if (dataIndex === "trang_thai_ten") {
        const color = text === "Đang sử dụng" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];
        if (record.key.toString().includes("empty")) return "";
        return (
          <Tag color={color} className="text-[11px]">
            {text}
          </Tag>
        );
      }
      return searchedColumn === dataIndex ? <Highlighter searchWords={[searchTextTable]} textToHighlight={text ? text.toString() : ""} /> : text !== undefined ? text : "\u00A0";
    },
  });

  return (
    <div id={ID_PAGE.QUAN_LY_DON_VI_CHI_NHANH}>
      <Table<TableChucDanhColumnDataType>
        {...defaultTableProps}
        dataSource={dataTableListChucDanh} //mảng dữ liệu record được hiển thị
        columns={
          tableChucDanhColumn?.map(item => {
            //Nếu là cột stt thì không hiển thị search
            return {...item, ...(item.key === "sott" ? {} : getColumnSearchProps(item.key as keyof TableChucDanhColumnDataType, item.title as string))};
          }) || []
        } //định nghĩa cột của table
        loading={loading} //hiển thị loading khi đang gọi API để loading data
        pagination={{
          ...defaultPaginationTableProps,
          total: tongSoDong,
          onChange: (page, pageSize) => setFilterParams({...filterParams, trang: page, so_dong: pageSize}),
        }}
        title={renderHeaderTableQuanLyChucDanh}
        onRow={record => {
          return {
            style: {cursor: loading ? "progress" : "pointer"}, //  thể hiện vị trí con trỏ là button(👈)
            onClick: async () => {
              if (record.key.toString().includes("empty")) return;
              const response = await getChiTietChucDanh(record as ReactQuery.IChiTietChucDanhParams);
              if (response?.ma) refModalChiTietChucDanh.current?.open(response);
            }, // click row
          };
        }}
      />
      <ModalChiTietChucDanh ref={refModalChiTietChucDanh} listChucDanh={listChucDanh} />
    </div>
  );
}, isEqual);

QuanLyChucDanhContent.displayName = "QuanLyChucDanhContent";

export default QuanLyChucDanhContent;

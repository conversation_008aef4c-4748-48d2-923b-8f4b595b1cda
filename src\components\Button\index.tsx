import {Button as AntButt<PERSON>, ButtonProps} from "antd";
import {isEqual} from "lodash";
import React, {memo, useMemo} from "react";
import {twMerge} from "tailwind-merge";

const ButtonComponent: React.FC<ButtonProps> = props => {
  const {children, className = "", type = "primary", loading, disabled, ...etc} = props;
  const isDisabled = useMemo(() => !!loading || disabled, [disabled, loading]);

  return (
    <AntButton className={twMerge("custom-button", className)} type={type} disabled={isDisabled} loading={loading} {...etc}>
      {children}
    </AntButton>
  );
};

const Button = memo(ButtonComponent, isEqual);

export default memo(Button);

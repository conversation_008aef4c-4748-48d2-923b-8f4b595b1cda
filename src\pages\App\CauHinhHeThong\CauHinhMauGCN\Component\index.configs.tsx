import {IFormInput} from "@src/@types";
import {ruleInputMessage} from "@src/hooks";

/**
 * <PERSON><PERSON><PERSON> config riêng cho component modal CauHinhMauGCN
 */

//INTERFACE ĐỊNH NGHĨA REF VÀ PROPS CHO MODAL
export interface IModalChiTietCauHinhMauGCNRef {
  open: (data?: any) => void;
}

export interface IModalChiTietCauHinhMauGCNProps {
  onAfterSave?: () => void;
}

//INTERFACE FORM MODAL CHI TIẾT - ĐỊNH NGHĨA CÁC FIELD TRONG MODAL TẠO MỚI/SỬA
export interface IFormTaoMoiCauHinhMauGCNFieldsConfig {
  ma_doi_tac_ql: IFormInput; 
  nv: IFormInput;
  ma_sp: IFormInput; 
  ten: IFormInput; 
  ngay_ad: IFormInput; 
  id_file: IFormInput;
  trang_thai: IFormInput; 
}

/**
 * Object này định nghĩa các field trong modal để tạo mới hoặc chỉnh sửa cấu hình mẫu giấy chứng nhận
 */
export const FormTaoMoiCauHinhMauGCN: IFormTaoMoiCauHinhMauGCNFieldsConfig = {
  ma_doi_tac_ql: {
    component: "select",
    label: "Đối tác quản lý",
    name: "ma_doi_tac_ql",
    placeholder: "Chọn đối tác quản lý",
    rules: [ruleInputMessage.required], 
  },
  nv: {
    component: "select",
    label: "Nghiệp vụ",
    name: "nv",
    placeholder: "Chọn nghiệp vụ",
    rules: [ruleInputMessage.required], 
  },
  ma_sp: {
    component: "select", 
    label: "Sản phẩm",
    name: "ma_sp",
    placeholder: "Chọn sản phẩm",
    rules: [ruleInputMessage.required], 
  },
  ten: {
    component: "input", 
    label: "Tên mẫu giấy chứng nhận",
    name: "ten",
    placeholder: "Nhập tên mẫu giấy chứng nhận",
    rules: [ruleInputMessage.required], 
  },
  ngay_ad: {
    component: "date-picker", 
    label: "Ngày áp dụng",
    name: "ngay_ad",
    placeholder: "Chọn ngày áp dụng",
    rules: [ruleInputMessage.required], 
  },
  id_file: {
    component: "input", 
    label: "File mẫu giấy chứng nhận",
    name: "id_file",
    placeholder: "Chọn file mẫu giấy chứng nhận", 
    rules: [ruleInputMessage.required], 
  },
  trang_thai: {
    component: "select", 
    label: "Trạng thái",
    name: "trang_thai",
    placeholder: "Chọn trạng thái",
    rules: [ruleInputMessage.required], 
  },
};

// ===== MODAL SETTINGS =====
export const MODAL_SETTINGS = {
  width: 800,
  destroyOnClose: true,
  titles: {
    create: "Tạo mới cấu hình mẫu giấy chứng nhận",
    update: "Cập nhật cấu hình mẫu giấy chứng nhận"
  },
  buttons: {
    create: "Tạo mới",
    update: "Cập nhật", 
    cancel: "Quay lại"
  }
};

import {IFormInput} from ".";

// TYPE FORM LOGIN
declare namespace Authen {
  namespace Login {
    interface DefaultResponse {
      status: string;
    }

    // là value của input field
    interface IFormFieldsValue {
      tai_khoan: string;
      mat_khau: string;
    }
    // IFormFieldsConfig : là type của config input
    interface IFormFieldsConfig {
      tai_khoan: IFormInput;
      mat_khau: IFormInput;
    }
    interface Request {
      data?: ReactQuery.Queries;
    }
    interface Response extends DefaultResponse {
      message: LooseObject;
    }
  }

  namespace Profile {
    interface IMetaData {
      nsd: {
        anh_dai_dien?: string;
        dthoai?: string;
        email?: string;
        ma?: string;
        ma_chi_nhanh?: string;
        ma_chuc_danh?: string;
        ma_doi_tac?: string;
        ngay_sinh?: string;
        phong?: string;
        tai_khoan: string;
        ten?: string;
        trang_trai?: string;
      };
      token: string;
      refresh_token: string;
      pw: string;
    }
  }

  // TYPE FORM ĐĂNG KÝ
  namespace Signup {
    interface IFormFieldsValue {
      email: string;
      password: string;
      confirmedPassword: string;
    }
    interface IFormFieldsConfig {
      email: IFormInput;
      password: IFormInput;
      confirmedPassword: IFormInput;
    }
  }
}

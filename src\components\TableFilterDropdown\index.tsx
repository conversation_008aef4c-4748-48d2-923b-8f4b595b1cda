import {ClearOutlined, SearchOutlined} from "@ant-design/icons";
import {Input, Tooltip} from "antd";
import {isEqual} from "lodash";
import {memo} from "react";
import Button from "../Button";
import "./index.default.scss";
import {FilterConfirmProps, FilterDropdownProps, FilterRestProps} from "antd/es/table/interface";

interface TableFilterDropdownProps {
  ref: any;
  title: string;
  selectedKeys?: any;
  dataIndex: string;
  setSelectedKeys: (selectedKeys: React.Key[]) => void;
  handleSearch: (selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: any) => void;
  confirm: (param?: FilterConfirmProps) => void;
  clearFilters?: (param?: FilterRestProps) => void;
  handleReset: (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: any) => void;
}

const TableFilterDropdownComponent = ({ref, title, selectedKeys, dataIndex, confirm, setSelectedKeys, handleSearch, clearFilters, handleReset}: TableFilterDropdownProps) => {
  return (
    <div onKeyDown={e => e.stopPropagation()} className="flex flex-row items-center p-2">
      {/* setSelectedKeys: thay đổi danh sách tìm kiếm */}
      {/* selectedKeys: danh sách giá trị hiện tại trong filter */}
      {/* confirm: hàm gọi khi áp dụng filter */}
      {/* clearFilters: hàm dùng để clear input filter */}
      {/* close: đóng dropdown filter */}
      <Input
        ref={ref}
        placeholder={`Nhập ${title}`}
        value={selectedKeys[0]}
        onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
        onPressEnter={() => handleSearch(selectedKeys as string[], confirm, dataIndex)}
        className="mr-2 flex p-2"
      />
      <Tooltip title="Tìm kiếm">
        <Button type="primary" shape="circle" icon={<SearchOutlined />} onClick={() => handleSearch(selectedKeys as string[], confirm, dataIndex)} className="mr-2" />
      </Tooltip>
      <Tooltip title="Xoá">
        <Button type="primary" shape="circle" icon={<ClearOutlined />} onClick={() => clearFilters && handleReset(clearFilters, confirm, dataIndex)} />
      </Tooltip>
    </div>
  );
};

export default memo(TableFilterDropdownComponent, isEqual);

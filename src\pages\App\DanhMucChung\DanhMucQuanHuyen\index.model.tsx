/**
 * Tá<PERSON> dụng: Đị<PERSON> nghĩa TypeScript interfaces cho module danh mục quận huyện
 */
import {ReactQuery} from "@src/@types";

export interface IDanhMucQuanHuyenProvider {
  listQuanHuyen: CommonExecute.Execute.IDanhMucQuanHuyen[]; //Danh sách quận huyện hiển thị trong bảng
  listTinhThanh: CommonExecute.Execute.IDanhMucTinhThanh[]; //Danh sách tỉnh thành cho dropdown filter
  tongSoDong: number; //Tổng số bản ghi để hiển thị pagination
  loading: boolean; //Trạng thái loading cho UI
  filterParams: ReactQuery.ITimKiemPhanTrangDanhMucQuanHuyenParams & ReactQuery.IPhanTrang; //Tham số filter và phân trang
  searchQuanHuyen: () => Promise<void>; //Tìm kiếm danh sách quận huyện
  getChiTietQuanHuyen: (data: {ma_tinh: string; ma: string; ngay_ad: number}) => Promise<CommonExecute.Execute.IDanhMucQuanHuyen>; //Lấy chi tiết một quận huyện
  capNhatChiTietQuanHuyen: (data: ReactQuery.ICapNhatDanhMucQuanHuyenParams) => Promise<any>; //Tạo mới/cập nhật quận huyện
  setFilterParams: React.Dispatch<React.SetStateAction<ReactQuery.ITimKiemPhanTrangDanhMucQuanHuyenParams & ReactQuery.IPhanTrang>>; //Cập nhật tham số filter
}

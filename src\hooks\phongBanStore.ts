import {create} from "zustand";

export interface IListPhongBanStore {
  listPhongBan: Array<CommonExecute.Execute.IDanhMucPhongBan>;
  setListPhongBan: (listPhongBan: Array<CommonExecute.Execute.IDanhMucPhongBan>) => void;
}

export const usePhongBan = create<IListPhongBanStore>(set => ({
  listPhongBan: [],
  setListPhongBan: listPhongBan => set({listPhongBan}),
}));

//create : hàm tạo store
// export const usePhongBan = create(
//   //persist : middileware để lưu trạng thái vào localStorage
//   persist<IListPhongBanStore, [], [], Pick<IListPhongBanStore, "listPhongBan">>(
//     //set, get phải đúng thứ tự này, nếu không sẽ báo lỗi vì sử dụng nhầm cách get/set
//     (set, get) => ({
//       //khởi tạo state menuNguoiDung từ cookie + localStorage
//       listPhongBan: get()?.listPhongBan || [],
//       setListPhongBan: (listPhongBan: Array<CommonExecute.Execute.IDanhMucPhongBan>) => set(() => ({ listPhongBan: [...listPhongBan] })),
//     }),
//     //cấu hình persist
//     {
//       name: LOCAL_STORAGE_KEY.PHONG_BAN, //key để lưu trong localStorate
//       storage: createJSONStorage(() => localStorage), //nơi lưu trữ là localStorage
//       //chỉ định những phần nào trong state sẽ được lưu, tránh lưu những thứ k cần thiết như các hàm set
//       partialize: state => ({ listPhongBan: state.listPhongBan }),
//     },
//   ),
// );

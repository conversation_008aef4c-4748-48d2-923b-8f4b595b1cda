import {IFormInput} from "@src/@types";
import {REGUlAR_EXPRESSION} from "@src/constants";
import {colWidthByKey, defaultTableColumnsProps, ruleInputMessage} from "@src/hooks";
import {TableProps} from "antd";

//ĐỊNH NGHĨA CẤU HÌNH TABLE ĐỐI TÁC GỒM NHỮNG CỘT NÀO, THỨ TỰ CỘT SẼ HIỂN THỊ THEO THỨ TỰ KHAI BÁO Ở DƯỚI
export interface TableDanhMucMaBenhICDColumnDataType {
  key: string;
  sott?: number;
  ma?: string;
  ten?: string;
  ten_e?: string;
  ma_ct?: string;
  ma_byt?: string;
  ma_nhom_benh?: string;
  bang_ma_benh?: string;
  benh_db?: string;
  benh_bs?: string;
  benh_td?: string;
  benh_cs?: string;
  nd_tim?: string;
  ghi_chu?: string;
  dinh_nghia?: string;
  trang_thai?: string;
  ngay_tao?: string;
  nguoi_tao?: string;
  ngay_cap_nhat?: string;
  nguoi_cap_nhat?: string;
  trang_thai_ten?: string;
}

// ĐỊNH NGHĨA Column RENDER RA TỪNG Ô TRONG CỘT
//đọc https://ant.design/components/table#column để biết thêm về column
export const tableDanhMucMaBenhICDColumn: TableProps<TableDanhMucMaBenhICDColumnDataType>["columns"] = [
  {
    title: "STT",
    dataIndex: "sott",
    key: "sott",
    align: "center",
    width: colWidthByKey.sott,
    ...defaultTableColumnsProps,
  },
  {
    title: "Mã bệnh ICD",
    dataIndex: "ma",
    key: "ma",
    align: "center",
    width: 180,
    ...defaultTableColumnsProps,
  },
  {
    title: "Tên mã bệnh ICD",
    dataIndex: "ten",
    key: "ten",
    width: 180,
    ...defaultTableColumnsProps,
  },
  {
    title: "Tên English",
    dataIndex: "ten_e",
    key: "ten_e",
    width: 180,
    ...defaultTableColumnsProps,
  },
  {
    title: "Mã CT",
    dataIndex: "ma_ct",
    key: "ma_ct",
    width: 100,
    ...defaultTableColumnsProps,
  },
  {
    title: "Mã BYT",
    dataIndex: "ma_byt",
    key: "ma_byt",
    width: 100,
    ...defaultTableColumnsProps,
  },
  {
    title: "Mã nhóm bệnh",
    dataIndex: "ma_nhom_benh",
    key: "ma_nhom_benh",
    width: 180,
    ...defaultTableColumnsProps,
  },
  {
    title: "Bảng mã bệnh",
    dataIndex: "bang_ma_benh",
    key: "bang_ma_benh",
    width: 180,
    ...defaultTableColumnsProps,
  },
  {
    title: "Bệnh DB",
    dataIndex: "benh_db",
    key: "benh_db",
    width: 100,
    ...defaultTableColumnsProps,
  },
  {
    title: "Bệnh BS",
    dataIndex: "benh_bs",
    key: "benh_bs",
    width: 100,
    ...defaultTableColumnsProps,
  },
  {
    title: "Bệnh TD",
    dataIndex: "benh_td",
    key: "benh_td",
    width: 100,
    ...defaultTableColumnsProps,
  },
  {
    title: "Bệnh CS",
    dataIndex: "benh_cs",
    key: "benh_cs",
    width: 100,
    ...defaultTableColumnsProps,
  },
  {
    title: "ND tim",
    dataIndex: "nd_tim",
    key: "nd_tim",
    width: 100,
    ...defaultTableColumnsProps,
  },
  {
    title: "Ghi chú",
    dataIndex: "ghi_chu",
    key: "ghi_chu",
    width: 100,
    ...defaultTableColumnsProps,
  },
  {
    title: "Định nghĩa",
    dataIndex: "dinh_nghia",
    key: "dinh_nghia",
    width: 100,
    ...defaultTableColumnsProps,
  },
  {
    title: "Ngày tạo",
    dataIndex: "ngay_tao",
    key: "ngay_tao",
    width: colWidthByKey.ngay_tao,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Người tạo",
    dataIndex: "nguoi_tao",
    key: "nguoi_tao",
    width: colWidthByKey.nguoi_tao,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Ngày cập nhật",
    dataIndex: "ngay_cap_nhat",
    key: "ngay_cap_nhat",
    width: colWidthByKey.ngay_cap_nhat,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Người cập nhật",
    dataIndex: "nguoi_cap_nhat",
    key: "nguoi_cap_nhat",
    width: colWidthByKey.nguoi_cap_nhat,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Trạng thái",
    dataIndex: "trang_thai_ten",
    key: "trang_thai_ten",
    width: 130,
    align: "center",
    ...defaultTableColumnsProps,
  },
];

//keyof: return ra key của inteface TableDanhMucMaBenhICDColumnDataType;
export type TableDanhMucMaBenhICDColumnDataIndex = keyof TableDanhMucMaBenhICDColumnDataType;

//radio trong table
export const radioItemTrangThaiDanhMucMaBenhICDTable = [
  {value: "Đang sử dụng", text: "Đang sử dụng"},
  {value: "Ngưng sử dụng", text: "Ngưng sử dụng"},
];

//form tìm kiếm
//radio trong search
export const radioItemTrangThaiDanhMucMaBenhICDSelect: Array<{ma: string; ten: string}> = [
  {ma: "D", ten: "Đang sử dụng"},
  {ma: "K", ten: "Ngưng sử dụng"},
];
export interface IFormTimKiemDanhMucMaBenhICDFieldsConfig {
  ten: IFormInput;
  ma: IFormInput;
  trang_thai: IFormInput;
}
export const FormTimKiemQuanLyDanhMucMaBenhICD: IFormTimKiemDanhMucMaBenhICDFieldsConfig = {
  ten: {
    component: "input",
    name: "ten",
    label: "Tên mã bệnh ICD",
    placeholder: "Nhập tên mã bệnh ICD",
    className: "!mb-0",
  },
  ma: {
    component: "input",
    name: "ma",
    label: "Mã bệnh ICD",
    placeholder: "Nhập mã bệnh ICD",
    className: "!mb-0",
  },
  trang_thai: {
    component: "select",
    name: "trang_thai",
    label: "Trạng thái",
    placeholder: "Chọn trạng thái",
    className: "!mb-0",
  },
};

//form update / create
export interface IFormTaoMoiDanhMucMaBenhICDFieldsConfig {
  ma: IFormInput;
  ten: IFormInput;
  ten_e: IFormInput;
  ma_ct: IFormInput;
  ma_byt: IFormInput;
  stt: IFormInput;
  trang_thai: IFormInput;
}

// const ruleRequired = {
//   required: true,
//   message: "Thông tin bắt buộc",
// };

export const FormTaoMoiDanhMucMaBenhICD: IFormTaoMoiDanhMucMaBenhICDFieldsConfig = {
  ma: {
    component: "input",
    label: "Mã",
    name: "ma",
    placeholder: "Nhập mã Nhóm mã bệnh",
    rules: [ruleInputMessage.required],
  },
  ten: {
    component: "input",
    label: "Tên",
    name: "ten",
    placeholder: "Nhập tên Nhóm mã bệnh",
    rules: [ruleInputMessage.required],
  },
  ten_e: {
    component: "input",
    label: "Tên English",
    name: "ten_e",
    placeholder: "Nhập tên English",
  },
  ma_ct: {
    component: "input",
    label: "Mã CT",
    name: "ma_ct",
    placeholder: "Nhập mã CT",
  },
  ma_byt: {
    component: "input",
    label: "Mã BYT",
    name: "ma_byt",
    placeholder: "Nhập mã BYT",
  },
  stt: {
    component: "input",
    label: "Số thứ tự",
    placeholder: "Nhập số thứ tự",
    name: "stt",
    rules: [ruleInputMessage.required],
  },
  trang_thai: {
    component: "select",
    label: "Trạng thái",
    name: "trang_thai",
    placeholder: "Chọn trạng thái",
    rules: [ruleInputMessage.required],
  }
};

export const TRANG_THAI_TAO_MOI_DANH_MUC_MA_BENH_ICD = [
  {ten: "Đang sử dụng", ma: "D"},
  {ten: "Ngừng sử dụng", ma: "K"},
];

import React, {memo, PropsWithChildren} from "react";
import {Skeleton as AntSkeleton} from "antd";
import {SkeletonButtonProps} from "antd/es/skeleton/Button";
import {twMerge} from "tailwind-merge";

import {isEqual} from "lodash";

interface ButtonProps extends SkeletonButtonProps {
  loading?: boolean;
}

const ButtonSkeletonComponent: React.FC<PropsWithChildren<ButtonProps>> = props => {
  const {className = "", loading = true, children, active = true, ...etc} = props;

  return <React.Fragment>{loading ? <AntSkeleton.Button className={twMerge("custom-button-skeleton", className)} active={active} {...etc} /> : children}</React.Fragment>;
};

const ButtonSkeleton = memo(ButtonSkeletonComponent, isEqual);

export default ButtonSkeleton;

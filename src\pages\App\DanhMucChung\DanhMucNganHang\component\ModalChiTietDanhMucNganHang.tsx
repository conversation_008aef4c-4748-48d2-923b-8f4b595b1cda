import {forwardRef, memo, useEffect, useImperativeHandle, useState} from "react";
import FormChiTietDanhMucNganHang, {IModalChiTietDanhMucNganHangRef, Props, TRANG_THAI_CHI_TIET_NGAN_HANG} from "./index.configs";
import {Col, Flex, Form, Modal, Row} from "antd";
import {useDanhMucNganHangContext} from "../index.context";
import {isEqual} from "lodash";
import {Button, FormInput, HeaderModal} from "@src/components";
import {ArrowLeftOutlined, CheckOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";

const {ma, ten, stt, trang_thai} = FormChiTietDanhMucNganHang;

const ModalChiTietDanhMucNganHangComponent = forwardRef<IModalChiTietDanhMucNganHangRef, Props>(({listDoiTac}: Props, ref) => {
  useImperativeHandle(ref, () => ({
    open: (dataDanhMucNganHang?: CommonExecute.Execute.IDanhMucNganHang) => {
      setIsOpen(true);
      if (dataDanhMucNganHang) setChiTietDanhMucNganHang(dataDanhMucNganHang);
    },
    close: () => setIsOpen(false),
  }));
  const [chiTietDanhMucNganHang, setChiTietDanhMucNganHang] = useState<CommonExecute.Execute.IDanhMucNganHang | null>(null);
  const [isOpen, setIsOpen] = useState(false);

  const {loading, onUpdateDanhMucNganHang, filterParams, setFilterParams} = useDanhMucNganHangContext();

  const [form] = Form.useForm();
  const formValues = Form.useWatch([], form);
  useEffect(() => {
    if (chiTietDanhMucNganHang) {
      const arrFormData = [];
      for (const key in chiTietDanhMucNganHang) {
        arrFormData.push({
          name: key as keyof CommonExecute.Execute.IDanhMucNganHang,
          value: chiTietDanhMucNganHang[key as keyof CommonExecute.Execute.IDanhMucNganHang],
        });
      }
      form.setFields(arrFormData);
    }
  }, [chiTietDanhMucNganHang, form]);
  useEffect(() => {
    form
      .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
      .then(() => {
        setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
      })
      .catch(() => {
        setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
      });
  }, [form, formValues]);
  const closeModal = () => {
    setIsOpen(false);
    setChiTietDanhMucNganHang(null);
    form.resetFields();
    setFilterParams(filterParams);
  };

  const renderFormInputColum = (props?: any, span = 12) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  const onConfirm = async () => {
    try {
      const values: ReactQuery.ICapNhatDanhMucNganHangParams = form.getFieldsValue(); //lấy ra values của form
      console.log("values", values);
      await onUpdateDanhMucNganHang(values);
      setIsOpen(false);
    } catch (error) {
      console.log("onConfirm", error);
    }
  };
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);

  //render footer
  const renderFooter = () => {
    return (
      <Form.Item>
        <Button type="default" onClick={() => setIsOpen(false)} className="mr-2" icon={<ArrowLeftOutlined />}>
          Quay lại
        </Button>
        <Button type="primary" onClick={onConfirm} className="mr-2" icon={<CheckOutlined />} disabled={disableSubmit} iconPosition="end">
          Lưu
        </Button>
      </Form.Item>
    );
  };
  const renderForm = () => (
    <Form form={form} layout="vertical" initialValues={{trang_thai: TRANG_THAI_CHI_TIET_NGAN_HANG[0].ma}}>
      {/* MÃ */}
      <Row gutter={16}>
        {/* {renderFormInputColum({...ma_doi_tac_ql, options: listDoiTac, disabled: chiTietDanhMucNganHang ? true : false})} */}
        {/* {renderFormInputColum({...nv, options: NGHIEP_VU_NGAN_HANG, disabled: chiTietDanhMucNganHang ? true : false})} */}
        {renderFormInputColum({...ma, disabled: chiTietDanhMucNganHang ? true : false})}
        {renderFormInputColum({...ten})}
      </Row>
      <Row gutter={16}>
        {renderFormInputColum({...stt})}
        {renderFormInputColum({...trang_thai, options: TRANG_THAI_CHI_TIET_NGAN_HANG})}
      </Row>
    </Form>
  );
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        maskClosable={false}
        title={
          <HeaderModal
            title={chiTietDanhMucNganHang ? `Chi tiết  ${chiTietDanhMucNganHang.ten}` : "Tạo mới ngân hàng"}
            trang_thai_ten={chiTietDanhMucNganHang?.trang_thai_ten}
            trang_thai={chiTietDanhMucNganHang?.trang_thai}
          />
        }
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width={{
          xs: "50%",
          sm: "50%",
          md: "50%",
          lg: "50%",
          xl: "50%",
          xxl: "50%",
        }}
        footer={renderFooter}
        className="[&_.ant-space]:w-full">
        {renderForm()}
      </Modal>
    </Flex>
  );
});
ModalChiTietDanhMucNganHangComponent.displayName = "ModalChiTietDanhMucNganHangComponent";
export const ModalChiTietDanhMucNganHang = memo(ModalChiTietDanhMucNganHangComponent, isEqual);

import React, {PropsWithChildren, useCallback, useEffect, useMemo, useState} from "react";

import {QuanLyDanhMucChauLucContext} from "./index.context";
import {IQuanLyDanhMucChauLucContextProps} from "./index.model";
import {ACTION_CODE} from "@src/constants";
import {ReactQuery} from "@src/@types";
import {TableDanhMucChauLucColumnDataType} from "./index.configs";
import {useCommonExecute} from "@src/services/react-queries";

const QuanLyDanhMucChauLucProvider: React.FC<PropsWithChildren> = props => {
  const {children} = props;
  const mutateUseCommonExecute = useCommonExecute();

  const [listDanhMucChauLuc, setListDanhMucChauLuc] = useState<Array<CommonExecute.Execute.IDanhMucChauLuc>>([]);
  const [tongSoDong, setTongSoDong] = useState<number>(0);
  const [filterParams, setFilterParams] = useState<ReactQuery.ITimKiemPhanTrangDanhMucChauLucParams & ReactQuery.IPhanTrang>({ma: "", ten: "", trang_thai: "", trang: 1, so_dong: 20});
  
  //khởi tạo dữ liệu ban đàu
  useEffect(() => {//nè
    initData();
  }, []);

  const getChiTietDanhMucChauLuc = useCallback(
    async (data: TableDanhMucChauLucColumnDataType) => {
      try {
        console.log("[Provider] getChiTietDanhMucChauLuc được gọi với data:", data);
        
        if (!data.ma) {
          console.log("[Provider] Không có mã để tìm kiếm, data.ma =", data.ma);
          return {} as CommonExecute.Execute.IDanhMucChauLuc;
        }
        
        console.log("[Provider] Gọi API với params:", {
          ma: data.ma,
          actionCode: ACTION_CODE.CHI_TIET_DANH_MUC_CHAU_LUC
        });
        
        const response = await mutateUseCommonExecute.mutateAsync({
          ma: data.ma,
          actionCode: ACTION_CODE.CHI_TIET_DANH_MUC_CHAU_LUC,
        } as any);
        
        console.log("[Provider] Raw response từ API:", response);
        console.log("[Provider] Response.data:", response.data);
        
        // Xử lý structure response.data.lke
        const responseData = response.data as any; // Type casting để truy cập lke
        if (responseData && responseData.lke && Array.isArray(responseData.lke) && responseData.lke.length > 0) {
          const result = responseData.lke[0] as CommonExecute.Execute.IDanhMucChauLuc;
          console.log("[Provider] Trả về data từ lke[0]:", result);
          return result;
        } else {
          console.log("[Provider] Không tìm thấy data trong response.data.lke");
          return {} as CommonExecute.Execute.IDanhMucChauLuc;
        }
      } catch (error) {
        console.log("[Provider] getChiTietDanhMucChauLuc error:", error);
        return {} as CommonExecute.Execute.IDanhMucChauLuc;
      }
    },
    [mutateUseCommonExecute],
  );

  const getListDanhMucChauLuc = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        ...filterParams,
        actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_DANH_MUC_CHAU_LUC,
      } as any);
      if (response.data) {
        setListDanhMucChauLuc(response.data.data);
        setTongSoDong(response.data.tong_so_dong);
      }
    } catch (error) {
      console.log("getListDanhMucChauLuc error ", error);
    }
  }, [mutateUseCommonExecute]);

  useEffect(() => {
    getListDanhMucChauLuc();
  }, [filterParams]);

  const capNhatChiTietDanhMucChauLuc = useCallback(
    async (data: ReactQuery.ICapNhatDanhMucChauLucParams) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...data,
          actionCode: ACTION_CODE.UPDATE_DANH_MUC_CHAU_LUC,
        } as any);
        return response.data;
      } catch (error) {
        console.log("capNhatChiTietDanhMucChauLuc err", error);
        // return {} as CommonExecute.Execute.IDanhMucChauLuc;
      }
    },
    [mutateUseCommonExecute],
  );

  //initData chỉ dùng trong useEffect 1 lần, không truyền đi đâu cả, nên không cần memo hoá
  const initData = () => {};

  const value = useMemo<IQuanLyDanhMucChauLucContextProps>(
    () => ({
      listDanhMucChauLuc,
      tongSoDong,
      loading: mutateUseCommonExecute.isLoading,
      filterParams,
      getListDanhMucChauLuc,
      getChiTietDanhMucChauLuc,
      capNhatChiTietDanhMucChauLuc,
      setFilterParams,
    }),
    [listDanhMucChauLuc, tongSoDong, mutateUseCommonExecute, filterParams, getListDanhMucChauLuc, getChiTietDanhMucChauLuc, capNhatChiTietDanhMucChauLuc],
  );

  return <QuanLyDanhMucChauLucContext.Provider value={value}>{children}</QuanLyDanhMucChauLucContext.Provider>;
};

export default QuanLyDanhMucChauLucProvider;

import {ArrowLeftOutlined, CheckOutlined} from "@ant-design/icons";
import {IFormInput, ReactQuery} from "@src/@types";
import {Button, FormInput, HeaderModal, Popcomfirm} from "@src/components";
import {Col, Flex, Form, Modal, Row} from "antd";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useState} from "react";
import {FormTaoMoiDanhMucBangMaBenh, TRANG_THAI_TAO_MOI_DANH_MUC_BANG_MA_BENH} from "../index.configs";
import {useQuanLyDanhMucBangMaBenhContext} from "../index.context";
import {ChiTietDanhMucBangMaBenhProps, IModalChiTietDanhMucBangMaBenhRef} from "./Constant";
const {ma, ten, stt, trang_thai} = FormTaoMoiDanhMucBangMaBenh;

const ModalChiTietDanhMucBangMaBenhComponent = forwardRef<IModalChiTietDanhMucBangMaBenhRef, ChiTietDanhMucBangMaBenhProps>(({listDanhMucBangMaBenh}: ChiTietDanhMucBangMaBenhProps, ref) => {
  useImperativeHandle(ref, () => ({
    open: (dataDanhMucBangMaBenh?: CommonExecute.Execute.IDanhMucBangMaBenh) => {
      setIsOpen(true);
      form.resetFields();
      if (dataDanhMucBangMaBenh) setChiTietDanhMucBangMaBenh(dataDanhMucBangMaBenh); // nếu có dữ liệu -> set chi tiết DanhMucBangMaBenh -> là sửa
    },
    close: () => setIsOpen(false),
  }));
  const [chiTietDanhMucBangMaBenh, setChiTietDanhMucBangMaBenh] = useState<CommonExecute.Execute.IDanhMucBangMaBenh | null>(null);
  const [isOpen, setIsOpen] = useState(false);

  const {capNhatChiTietDanhMucBangMaBenh, getListDanhMucBangMaBenh, loading} = useQuanLyDanhMucBangMaBenhContext();
  const [form] = Form.useForm();
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const formValues = Form.useWatch([], form);
  // init form data - load dữ liệu vào form khi sửa
  useEffect(() => {
    if (chiTietDanhMucBangMaBenh) {
      const arrFormData = [];
      for (const key in chiTietDanhMucBangMaBenh) {
        arrFormData.push({
          name: key,
          value: chiTietDanhMucBangMaBenh[key as keyof CommonExecute.Execute.IDanhMucBangMaBenh],
        });
      }
      form.setFields(arrFormData);
    }
  }, [chiTietDanhMucBangMaBenh]);

  //xử lý validate form
  useEffect(() => {
    form
      .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
      .then(() => {
        setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
      })
      .catch(() => {
        setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
      });
  }, [form, formValues]);

  const closeModal = useCallback(() => {
    setIsOpen(false);
    setChiTietDanhMucBangMaBenh(null);
    form.resetFields();
  }, []);

  //Bấm Update
  const onConfirm = async () => {
    try {
      const values: ReactQuery.ICapNhatDanhMucBangMaBenhParams = form.getFieldsValue(); //lấy ra values của form

      // Xác định đây là EDIT hay CREATE mode
      const isEditMode = !!chiTietDanhMucBangMaBenh;

      await capNhatChiTietDanhMucBangMaBenh(values, isEditMode); //cập nhật lại DanhMucBangMaBenh
      await getListDanhMucBangMaBenh(); //lấy lại danh sách DanhMucBangMaBenh
      closeModal();
    } catch (error) {
      console.log("onConfirm", error);
    }
  };

  // RENDER
  //FOOTER
  const renderFooter = () => {
    return (
      <Form.Item>
        <Button type="default" onClick={() => closeModal()} className="mr-2" icon={<ArrowLeftOutlined />}>
          Quay lại
        </Button>
        <Popcomfirm
          title={disableSubmit ? "Vui lòng nhập đầy đủ thông tin" : ""}
          onConfirm={onConfirm}
          okText="Lưu"
          description="Bạn có chắc muốn lưu thông tin?"
          buttonTitle={"Lưu"}
          buttonDisable={disableSubmit}
          buttonClassName="w-40"
          buttonIcon={<CheckOutlined />}
          iconPosition="end"
          loading={loading}
        />
      </Form.Item>
    );
  };
  const renderFormColum = (props: IFormInput, span = 12) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  const renderForm = () => (
    <Form form={form} layout="vertical">
      <Row gutter={16}>
        {renderFormColum({...ma, disabled: chiTietDanhMucBangMaBenh ? true : false}, 6)}
        {renderFormColum({...ten}, 18)}
      </Row>

      <Row gutter={16}>
        {renderFormColum({...stt}, 6)}
        {renderFormColum({...trang_thai, options: TRANG_THAI_TAO_MOI_DANH_MUC_BANG_MA_BENH}, 18)}
      </Row>
    </Form>
  );
  //Render
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        title={
          <HeaderModal
            title={chiTietDanhMucBangMaBenh ? `${chiTietDanhMucBangMaBenh.ten}` : "Tạo mới danh mục Bảng mã bệnh"}
            trang_thai_ten={chiTietDanhMucBangMaBenh?.trang_thai_ten}
            trang_thai={chiTietDanhMucBangMaBenh?.trang_thai}
          />
        }
        // centered
        maskClosable={false}
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width={600}
        styles={{
          body: {
            paddingTop: "8px",
            paddingBottom: "16px",
          },
        }}
        footer={renderFooter}>
        {renderForm()}
      </Modal>
    </Flex>
  );
});

ModalChiTietDanhMucBangMaBenhComponent.displayName = "ModalChiTietDanhMucBangMaBenhComponent";
export const ModalChiTietDanhMucBangMaBenh = memo(ModalChiTietDanhMucBangMaBenhComponent, isEqual);

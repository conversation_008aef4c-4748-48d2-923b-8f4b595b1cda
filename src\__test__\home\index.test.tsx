// import {describe, expect, jest, test} from "@jest/globals";
// // import Home from "@src/pages/home";
// import {act, fireEvent, render, screen, waitFor} from "@testing-library/react";
// import "@src/__mocks__";

// jest.mock("@utils/env", () => ({
//   env: {
//     MODE: "development",
//     VITE_BASE_URL: "test_base_url",
//   },
// }));

// describe("Homepage test", () => {
//   test("render home page", async () => {
//     const renderedComponent = render(<Home />);
//     renderedComponent.getByText("Dark mode");

//     // test input
//     const input = renderedComponent.getByLabelText("Input");
//     await act(() => {
//       fireEvent.change(input, {target: {value: "test123123"}});
//     });
//     // expect(screen.getByDisplayValue("test123123") === input).toBe(true);
//     expect(input).toHaveValue("test123123");

//     // test switch
//     const switchComp = renderedComponent.getByLabelText("Switch");
//     expect(switchComp.getAttribute("aria-checked")).toBe("false");
//     await act(() => {
//       fireEvent.click(switchComp);
//     });
//     expect(switchComp.getAttribute("aria-checked")).toBe("true");

//     // test checkbox
//     const checkboxComp = renderedComponent.getByLabelText("Checkbox");
//     expect(checkboxComp).not.toBeChecked();
//     await act(() => {
//       fireEvent.click(checkboxComp);
//     });
//     expect(checkboxComp).toBeChecked();

//     // test select
//     const selectComp = renderedComponent.getByLabelText("Select");
//     await act(() => {
//       fireEvent.mouseDown(selectComp);
//     });
//     await waitFor(() => {
//       ["Option 1", "Option 2"].map(item => {
//         const optionText = screen.getByText(item);
//         expect(optionText).toBeInTheDocument();
//       });
//     });
//   });
// });

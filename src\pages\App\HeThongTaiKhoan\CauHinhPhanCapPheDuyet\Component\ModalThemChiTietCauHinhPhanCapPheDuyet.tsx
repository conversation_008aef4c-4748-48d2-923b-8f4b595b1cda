import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useMemo, useState} from "react";
import {IModalThemCauHinhPhanCapPheDuyetRef, Props, FormThemPhanCapPheDuyetCT, TRANG_THAI_PHAN_CAP_PHE_DUYET_CT, NGHIEP_VU_PHAN_CAP_PHE_DUYET_CT} from "./index.configs";
import {Col, Flex, Form, Modal, Row} from "antd";
import {useCauHinhPhanCapPheDuyetContext} from "../index.context";
import {isEqual} from "lodash";
import {Button, FormInput, HeaderModal} from "@src/components";
import {CheckOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
const {ma_doi_tac_ql, ma_chi_nhanh_ql, ma_sp, nv, nhom_duyet, tien_toi, tien_tu, trang_thai} = FormThemPhanCapPheDuyetCT;
// const interface Props:{}
const ModalThemCauHinhPhanCapPheDuyetComponent = forwardRef<IModalThemCauHinhPhanCapPheDuyetRef, Props>(({PhanCapPheDuyet}: Props, ref) => {
  useImperativeHandle(ref, () => ({
    open: (dataChiTietCauHinhPhanCapPheDuyetCT?: CommonExecute.Execute.ICauHinhPhanCapPheDuyetCT) => {
      setIsOpen(true);
      setDisableSubmit(true);
      if (dataChiTietCauHinhPhanCapPheDuyetCT) setThemCauHinhPhanCapPheDuyetCT(dataChiTietCauHinhPhanCapPheDuyetCT);
      console.log("dataChiTietCauHinhPhanCapPheDuyetCT", dataChiTietCauHinhPhanCapPheDuyetCT);
    },
    close: () => setIsOpen(false),
  }));

  const [themCauHinhPhanCapPheDuyetCT, setThemCauHinhPhanCapPheDuyetCT] = useState<CommonExecute.Execute.ICauHinhPhanCapPheDuyetCT | null>(null);
  const [isOpen, setIsOpen] = useState(false);

  const {
    loading,
    onUpdateCauHinhPhanCapPheDuyetCT,
    listChiNhanh,
    listDoiTac,
    getListChiNhanhTheoDoiTac,
    getListSanPhamTheoDoiTac,
    listSanPham,
    layDanhSachCauHinhPhanCapPheDuyetCT,
    listPhanCapNhom,
    danhSachCauHinhPhanCapPheDuyetCT,
  } = useCauHinhPhanCapPheDuyetContext();

  const [formThemPhanCapPheDuyetCT] = Form.useForm();
  const formValues = Form.useWatch([], formThemPhanCapPheDuyetCT);
  const [filteredChiNhanh, setFilteredChiNhanh] = useState<Array<CommonExecute.Execute.IChiNhanh>>([]);
  const [filteredSanPham, setFilteredSanPham] = useState<Array<CommonExecute.Execute.IDanhMucSanPham>>([]);
  const watchDoiTac = Form.useWatch("ma_doi_tac_ql", formThemPhanCapPheDuyetCT);
  const watchNV = Form.useWatch("nv", formThemPhanCapPheDuyetCT);

  useEffect(() => {
    if (themCauHinhPhanCapPheDuyetCT) {
      const arrFormData = [];
      for (const key in themCauHinhPhanCapPheDuyetCT) {
        arrFormData.push({
          name: key as keyof CommonExecute.Execute.ICauHinhPhanCapPheDuyetCT,
          value: themCauHinhPhanCapPheDuyetCT[key as keyof CommonExecute.Execute.ICauHinhPhanCapPheDuyetCT],
        });
      }
      formThemPhanCapPheDuyetCT.setFields(arrFormData);
    }
  }, [themCauHinhPhanCapPheDuyetCT, formThemPhanCapPheDuyetCT]);

  useEffect(() => {
    if (PhanCapPheDuyet) {
      setDisableSubmit(false);
    } else {
      setDisableSubmit(true);
    }
  }, [PhanCapPheDuyet]);
  //xử lý validate form
  useEffect(() => {
    formThemPhanCapPheDuyetCT
      .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
      .then(() => {
        setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
      })
      .catch(() => {
        setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
      });
  }, [formThemPhanCapPheDuyetCT, formValues]);
  useEffect(() => {
    if (themCauHinhPhanCapPheDuyetCT) {
      setFilteredChiNhanh(listChiNhanh.filter(item => item.ma_doi_tac === themCauHinhPhanCapPheDuyetCT?.ma_doi_tac_ql));
    }
  }, [themCauHinhPhanCapPheDuyetCT]);
  // Sửa: Gọi API lấy sản phẩm theo đối tác và nghiệp vụ - LOẠI BỎ getListSanPhamTheoDoiTac khỏi dependency
  useEffect(() => {
    console.log("watchDoiTac", watchDoiTac);
    console.log("watchNV", watchNV);
    if (watchDoiTac && watchNV) {
      // Gọi API để lấy danh sách sản phẩm theo đối tác và nghiệp vụ
      getListSanPhamTheoDoiTac({ma_doi_tac_ql: watchDoiTac, nv: watchNV});
      console.log("list sản phẩm theo đối tác và nghiệp vụ", listSanPham);
    } else {
      // Reset danh sách sản phẩm khi không có đối tác hoặc nghiệp vụ
      setFilteredSanPham([]);
    }
    // if (watchNV) {
    //   //khi thay đổi nghiệp vụ thì input ma_sp phải được = null
    //   formThemPhanCapPheDuyetCT.setFieldsValue({ma_sp: null});
    // }
  }, [watchDoiTac, watchNV]); // Loại bỏ getListSanPhamTheoDoiTac khỏi dependency

  const closeModal = useCallback(() => {
    setIsOpen(false);
    // setThemCauHinhPhanCapPheDuyet(null);
    formThemPhanCapPheDuyetCT.resetFields();
    console.log("bt phân cấp phê duyệt", PhanCapPheDuyet);
    // layDanhSachCauHinhPhanCapPheDuyetCT({bt_phan_cap: PhanCapPheDuyet});
    // Reset các state lọc
    setFilteredChiNhanh([]);
    setFilteredSanPham([]);
    setThemCauHinhPhanCapPheDuyetCT(null);
  }, [formThemPhanCapPheDuyetCT]);
  useEffect(() => {
    console.log("themCauHinhPhanCapPheDuyetCT", themCauHinhPhanCapPheDuyetCT);
  }, [themCauHinhPhanCapPheDuyetCT]);
  // Sửa: Lọc sản phẩm từ danh sách đã được API trả về
  const listSanPhamSelect = useMemo(() => {
    if (!watchDoiTac || !watchNV) return [];
    // Lọc từ danh sách sản phẩm đã được API trả về theo đối tác và nghiệp vụ
    return listSanPham.filter(item => item.ma_doi_tac_ql === watchDoiTac && item.nv === watchNV);
  }, [listSanPham, watchDoiTac, watchNV]);

  // Sửa: Sử dụng useCallback để tránh tạo function mới mỗi lần render
  const handleChangeDoiTac = useCallback(
    (value: any) => {
      // Lọc danh sách chi nhánh theo mã đối tác
      const maDoiTac = typeof value === "string" ? value : "";

      // Gọi API để lấy danh sách chi nhánh theo đối tác
      getListChiNhanhTheoDoiTac();

      // Lọc local danh sách chi nhánh
      const filtered = listChiNhanh.filter(pb => pb.ma_doi_tac === maDoiTac);
      console.log(
        "filtered",
        listChiNhanh.filter(item => item.ma_doi_tac === themCauHinhPhanCapPheDuyetCT?.ma_doi_tac_ql),
      );

      setFilteredChiNhanh(filtered);

      // Reset danh sách sản phẩm khi thay đổi đối tác
      setFilteredSanPham([]);

      return filtered;
    },
    [getListChiNhanhTheoDoiTac, listChiNhanh],
  );
  const handleChangeNV = (value: any) => {
    formThemPhanCapPheDuyetCT.setFieldsValue({ma_sp: null});
  };
  const setErrorFormFields = useCallback(
    (name: string, errors: string[]) => {
      formThemPhanCapPheDuyetCT.setFields([
        {
          name,
          errors,
        },
      ]);
    },
    [formThemPhanCapPheDuyetCT],
  );
  //XỬ LÝ ĐIỀU KIỆN ĐỂ MỞ SELECT
  const checkDieuKienMoSelect = (inputName: string, isOpen: boolean) => {
    //NẾU CHƯA CÓ DATA ĐỐI TÁC MÀ CHỌN CÁC INPUT TƯƠNG ỨNG -> HIỂN THỊ LỖI
    const arrInputNameTheoDoiTacNV: Array<string> = ["ma_sp"];

    if (arrInputNameTheoDoiTacNV.includes(inputName) && isOpen)
      if (!watchDoiTac) {
        setErrorFormFields("ma_doi_tac_ql", ["Vui lòng chọn đối tác "]);
      }
    if (!watchNV) {
      setErrorFormFields("nv", ["Vui lòng chọn nghiệp vụ "]);
    }
  };
  const renderFormInputColum = (props?: any, span = 8) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );

  const onConfirm = useCallback(async () => {
    try {
      const values: ReactQuery.IUpdateCauHinhPhanCapPheDuyetCTParams = formThemPhanCapPheDuyetCT.getFieldsValue(); //lấy ra values của form
      console.log("PhanCapPheDuyet", PhanCapPheDuyet);

      const finalValues = {
        ...values,
        bt_phan_cap: Number(PhanCapPheDuyet),
        bt: themCauHinhPhanCapPheDuyetCT?.bt,
      };
      console.log("check list dai ly", danhSachCauHinhPhanCapPheDuyetCT);

      console.log("check list dai ly", danhSachCauHinhPhanCapPheDuyetCT);
      // for (let i = 0; i < danhSachCauHinhPhanCapPheDuyetCT.length; i++) {
      //   if (
      //     danhSachCauHinhPhanCapPheDuyetCT[i].ma_doi_tac_ql === values.ma_doi_tac_ql &&
      //     danhSachCauHinhPhanCapPheDuyetCT[i].ma_sp === values.ma_sp &&
      //     danhSachCauHinhPhanCapPheDuyetCT[i].nhom_duyet === values.nhom_duyet
      //   ) {
      //     console.log("có giá trị thoa mãn ");
      //     // formThemPhanCapPheDuyetCT.setFields([
      //     //   {
      //     //     name: "ma_sp",
      //     //     errors: ["Mã sản phẩm đã tổn tại!"],
      //     //   },
      //     // ]);
      //     return;
      //   }
      // }

      console.log("update cấu hình pahan capasnhoms duyệt chi tiết params", finalValues);
      const response = await onUpdateCauHinhPhanCapPheDuyetCT(finalValues);
      console.log("check respon ", response);
      await layDanhSachCauHinhPhanCapPheDuyetCT({bt_phan_cap: Number(PhanCapPheDuyet)});

      // Sửa: Kiểm tra response đúng cách
      if (response !== undefined && response !== null) {
        console.log("cập nhật thành công");
        setIsOpen(false);
        closeModal();
      } else {
        console.log("cập nhật thất bại");
      }
    } catch (error) {
      console.log("onConfirm", error);
    }
  }, [formThemPhanCapPheDuyetCT, onUpdateCauHinhPhanCapPheDuyetCT, closeModal]);

  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  //render footer
  const renderFooter = () => {
    return (
      // <Form.Item>
      <Button type="primary" disabled={disableSubmit} onClick={onConfirm} icon={<CheckOutlined />}>
        Lưu
      </Button>
      // </Form.Item>
    );
  };
  const renderForm = () => (
    <Form form={formThemPhanCapPheDuyetCT} layout="vertical">
      {/* MÃ */}
      <Row gutter={16}>
        {renderFormInputColum({...nhom_duyet, options: listPhanCapNhom, disabled: themCauHinhPhanCapPheDuyetCT ? true : false})}
        {renderFormInputColum({...ma_doi_tac_ql, options: listDoiTac, onChange: handleChangeDoiTac, disabled: themCauHinhPhanCapPheDuyetCT ? true : false})}
        {renderFormInputColum({
          ...ma_chi_nhanh_ql,
          options: filteredChiNhanh,
          onDropdownVisibleChange: (open: boolean) => checkDieuKienMoSelect(ma_chi_nhanh_ql?.name, open),
          // disabled: themCauHinhPhanCapPheDuyetCT ? true : false,
        })}
        {renderFormInputColum({
          ...nv,
          options: NGHIEP_VU_PHAN_CAP_PHE_DUYET_CT,
          onChange: handleChangeNV,
        })}
        {renderFormInputColum({
          ...ma_sp,
          options: listSanPhamSelect,
          onDropdownVisibleChange: (open: boolean) => checkDieuKienMoSelect(ma_sp?.name, open),
          // disabled: themCauHinhPhanCapPheDuyetCT ? true : false,
        })}
        {renderFormInputColum({...trang_thai, options: TRANG_THAI_PHAN_CAP_PHE_DUYET_CT})}
        {renderFormInputColum({...tien_tu})}
        {renderFormInputColum({...tien_toi})}
      </Row>
    </Form>
  );
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        title={
          <HeaderModal
            title={"Thêm mới chi tiết phân cấp phê duyệt "}
            // trang_thai_ten={themCauHinhPhanCapPheDuyet?.trang_thai_ten}
            // trang_thai={themCauHinhPhanCapPheDuyet?.trang_thai}
          />
        }
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width={{
          xs: "55%",
          sm: "55%",
          md: "55%",
          lg: "55%",
          xl: "55%",
          xxl: "55%",
        }}
        style={{top: 180}}
        footer={renderFooter}
        className="[&_.ant-space]:w-full">
        {renderForm()}
      </Modal>
    </Flex>
  );
});
ModalThemCauHinhPhanCapPheDuyetComponent.displayName = "ModalThemCauHinhPhanCapPheDuyetComponent";
export const ModalThemCauHinhPhanCapPheDuyet = memo(ModalThemCauHinhPhanCapPheDuyetComponent, isEqual);

import { Gutter } from "antd/es/grid/row";

export const DEBOUNCE_TIME = 1000; // unit: second
export const DEFAULT_GUTTER: {
  GRID: [<PERSON><PERSON>, Gutter];
  HORIZONTAL: Gutter;
  VERTICAL: Gutter;
} = {
  GRID: [24, 24],
  HORIZONTAL: 24,
  VERTICAL: 24,
};

export const REGUlAR_EXPRESSION = {
  REG_PHONE: /^\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})$/,
  REG_PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&.#^])[A-Za-z\d@$!%*?&.#^]{8,}$/,
  // eslint-disable-next-line max-len
  REG_EMAIL: /^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/,
};

/**
 * Tiêu đề cho các trang trong ứng dụng
 */
// export const TITLE_PAGE = {
//   // Quản lý tỉnh thành
//   QUAN_LY_TINH_THANH: "Quản lý tỉnh thành",
//   DANH_MUC_TINH_THANH: "Danh mục tỉnh thành",
//   DANH_MUC_QUAN_HUYEN: "Danh mục quận huyện",
//   DANH_MUC_PHUONG_XA: "Danh mục phường xã",
  
//   // Quản lý xe cơ giới
//   NHOM_HANG_MUC_XE: "Nhóm hạng mục xe",
//   HANG_MUC_XE: "Hạng mục xe",
//   DANH_MUC_HANG_XE: "Danh mục hãng xe", 
//   DANH_MUC_HIEU_XE: "Danh mục hiệu xe",
//   DANH_MUC_LOAI_XE: "Danh mục loại xe",
// };
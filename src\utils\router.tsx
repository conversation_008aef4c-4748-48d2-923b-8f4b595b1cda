import React, {PropsWith<PERSON>hildren, ReactNode} from "react";
import {Navigate, Route} from "react-router-dom";
import {PathProps} from "@src/@types";
import {ROUTER_PATHS} from "@src/constants";

// chỗ này xử lý logic route khi chưa login, đã loign
const ProtectedRoute: React.FC<PropsWithChildren<{isAuthenticated: boolean; id: string | undefined}>> = props => {
  const {isAuthenticated, children, id} = props;
  //nếu đã login thì render ra screen
  if (isAuthenticated) {
    return <React.Fragment key={id}>{children}</React.Fragment>;
  }
  //còn chưa login thì render ra màn LOGIN
  return <Navigate to={ROUTER_PATHS.DANG_NHAP} replace key={id} />;
};

export const generateRouter = (paths: PathProps[], isAuthenticated: boolean, basePath?: string, routerProtected?: boolean): ReactNode[] => {
  return paths
    .map(item => {
      const {path, element, children, index, isProtected} = item;
      const indexPath = index ? "" : path;
      const _routerProtected = routerProtected || isProtected;
      const _path = basePath ? basePath + "/" + indexPath : indexPath;
      if (element) {
        return _routerProtected ? (
          <React.Fragment key={_path}>
            <Route
              path={_path}
              element={
                <ProtectedRoute isAuthenticated={isAuthenticated} id={_path}>
                  {element}
                </ProtectedRoute>
              }
              index={index}
            />
          </React.Fragment>
        ) : (
          <React.Fragment key={_path}>
            <Route path={_path} element={element} index={index} />
          </React.Fragment>
        );
      } else {
        // if (children) {
        //   return <React.Fragment key={_path}>{generateRouter(children, isAuthenticated, _path, _routerProtected)}</React.Fragment>;
        // } else {
        //   return <Navigate key={_path} to="/" />;
        // }
      }
    })
    .flat(4);
};

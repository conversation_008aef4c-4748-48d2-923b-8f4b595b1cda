import {ReactQuery} from "@src/@types";
import {colWidthByKey, defaultTableColumnsProps} from "@src/hooks";
import {FormRule} from "antd";
import {ColumnsType} from "antd/es/table";

/**
 * INTERFACE CHO KIỂU DỮ LIỆU CỦA BẢNG
 */
export interface TableDanhMucLoaiXeDataType {
  /** Key duy nhất cho mỗi dòng (bắt buộc của Ant Design Table) */
  key: React.Key;
  sott: number;
  ma: string;
  ten: string;
  nv: string;
  ten_nv: string;
  trang_thai_ten: string;
  ngay_tao: string;
  nguoi_tao: string;
  ngay_cap_nhat: string;
  nguoi_cap_nhat: string;
  trang_thai: string;
}

/** Giá trị mặc định cho form tìm kiếm và phân trang */
export const defaultFormValue: ReactQuery.ITimKiemPhanTrangDanhMucLoaiXeParams & ReactQuery.IPhanTrang = {
  nv: "",
  ma: "",
  ten: "",
  trang_thai: "",
  trang: 1,
  so_dong: 13,
};

/** Danh sách trạng thái loại xe */
export const TRANG_THAI_LOAI_XE = [
  // {ten: "Tất cả", ma: ""},
  {ten: "Đang sử dụng", ma: "D"},
  {ten: "Ngừng sử dụng", ma: "K"},
];

/** Danh sách nghiệp vụ cho form tìm kiếm (có thêm "Tất cả") */
export const NGHIEP_VU_TIM_KIEM = [
  {ten: "Tất cả", ma: ""},
  {ten: "Bảo hiểm ô tô", ma: "XE"},
  {ten: "Bảo hiểm xe máy", ma: "XE_MAY"},
];

/** Options cho filter trạng thái trong table */
export const trangThaiOptions = [
  {label: "Đang sử dụng", value: "Đang sử dụng"},
  {label: "Ngừng sử dụng", value: "Ngừng sử dụng"},
];

/** Định nghĩa các cột của table */
export const getColumns = (): ColumnsType<TableDanhMucLoaiXeDataType> => [
  {
    title: "STT",
    dataIndex: "sott",
    key: "stt",
    width: colWidthByKey.sott,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Mã loại xe",
    dataIndex: "ma",
    key: "ma",
    width: 120,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Tên loại xe",
    dataIndex: "ten",
    key: "ten",
    align: "left",
    ...defaultTableColumnsProps,
  },
  {
    title: "Nghiệp vụ",
    dataIndex: "nv",
    key: "nv",
    width: 100,
    align: "left",
    ...defaultTableColumnsProps,
    render: (value: string, record: TableDanhMucLoaiXeDataType) => {
      // Xử lý empty rows - return &nbsp; để giữ chiều cao đồng nhất
      if (record.key.toString().includes("empty")) return <span>&nbsp;</span>;

      // Mapping cố định theo thông tin từ BE
      const nghiepVuMapping: {[key: string]: string} = {
        XE: "Bảo hiểm ô tô",
        XE_MAY: "Bảo hiểm xe máy",
      };

      // Hiển thị theo mapping, fallback về ten_nv từ API, hoặc value gốc
      return nghiepVuMapping[value] || (record as any).ten_nv || value || "Chưa xác định";
    },
  },
  {
    title: "Ngày tạo",
    dataIndex: "ngay_tao",
    key: "ngay_tao",
    width: colWidthByKey.ngay_tao,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Người tạo",
    dataIndex: "nguoi_tao",
    key: "nguoi_tao",
    width: colWidthByKey.nguoi_tao,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Ngày cập nhật",
    dataIndex: "ngay_cap_nhat",
    key: "ngay_cap_nhat",
    width: colWidthByKey.ngay_cap_nhat,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Người cập nhật",
    dataIndex: "nguoi_cap_nhat",
    key: "nguoi_cap_nhat",
    width: colWidthByKey.nguoi_cap_nhat,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Trạng thái",
    dataIndex: "trang_thai_ten",
    key: "trang_thai_ten",
    width: 130,
    align: "center",
    ...defaultTableColumnsProps,
  },
];

/** Cấu hình các field trong form tìm kiếm */
export const FormTimKiemDanhMucLoaiXe = {
  ma: {
    component: "input" as const,
    name: "ma",
    label: "Mã loại xe",
    placeholder: "Nhập mã loại xe",
    className: "!mb-0",
  },
  ten: {
    component: "input" as const,
    name: "ten",
    label: "Tên loại xe",
    placeholder: "Nhập tên loại xe",
    className: "!mb-0",
  },
  /** Field tìm kiếm theo nghiệp vụ: "XE" | "XE_MAY" */
  // nv: {
  //   component: "select" as const,
  //   name: "nv",
  //   label: "Nghiệp vụ",
  //   placeholder: "Chọn nghiệp vụ",
  //   className: "!mb-0",
  //   options: NGHIEP_VU_TIM_KIEM,
  // },
  trang_thai: {
    component: "select" as const,
    name: "trang_thai",
    label: "Trạng thái",
    placeholder: "Chọn trạng thái",
    className: "!mb-0",
    options: TRANG_THAI_LOAI_XE,
  },
};

/**
 * CÁC QUY TẮC VALIDATION CHO FORM
 */
export const validationRules = {
  ma: [
    {required: true, message: "Vui lòng nhập mã loại xe!"},
    {max: 50, message: "Mã loại xe không được vượt quá 50 ký tự!"},
  ] as FormRule[],

  ten: [
    {required: true, message: "Vui lòng nhập tên loại xe!"},
    {max: 200, message: "Tên loại xe không được vượt quá 200 ký tự!"},
  ] as FormRule[],

  nv: [
    {required: true, message: "Vui lòng chọn nghiệp vụ!"},
    {
      validator: (_: any, value: string) => {
        if (!value) return Promise.resolve();
        if (!["XE", "XE_MAY"].includes(value)) {
          return Promise.reject(new Error("Nghiệp vụ phải là 'XE' hoặc 'XE_MAY'!"));
        }
        return Promise.resolve();
      },
    },
  ] as FormRule[],

  trang_thai: [{required: true, message: "Vui lòng chọn trạng thái!"}] as FormRule[],

  /** Rules cho số thứ tự */
  stt: [
    {required: true, message: "Vui lòng nhập số thứ tự!"},
    {type: "number" as const, min: 1, message: "Số thứ tự phải lớn hơn 0!"},
  ] as FormRule[],
};

/** Cấu hình form fields cho modal thêm/sửa */
export const detailFormFields = [
  {
    name: "ma",
    label: "Mã loại xe",
    component: "input" as const,
    placeholder: "Nhập mã loại xe",
    rules: validationRules.ma,
  },
  {
    name: "ten",
    label: "Tên loại xe",
    component: "input" as const,
    placeholder: "Nhập tên loại xe",
    rules: validationRules.ten,
  },
  {
    name: "nv",
    label: "Nghiệp vụ",
    component: "select" as const,
    placeholder: "Chọn nghiệp vụ",
    options: NGHIEP_VU_TIM_KIEM.filter(item => item.ma !== ""),
    rules: validationRules.nv,
  },
  {
    name: "trang_thai",
    label: "Trạng thái",
    component: "select" as const,
    placeholder: "Chọn trạng thái",
    options: TRANG_THAI_LOAI_XE.filter(item => item.ma !== ""),
    rules: validationRules.trang_thai,
  },
  {
    name: "stt",
    label: "Số thứ tự",
    component: "inputNumber" as const,
    placeholder: "Nhập số thứ tự",
    rules: validationRules.stt,
  },
];

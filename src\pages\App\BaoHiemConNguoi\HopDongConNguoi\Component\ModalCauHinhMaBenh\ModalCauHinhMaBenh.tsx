import {forwardRef, memo, useCallback, useImperativeHandle, useRef, useState} from "react";
import {CheckOutlined, CloseOutlined} from "@ant-design/icons";
import {Flex, Modal, Tabs} from "antd";
import {debounce, isEqual} from "lodash";

import {Button, HeaderModal} from "@src/components";
import {useAsyncAction} from "@src/hooks";
import {useHopDongConNguoiContext} from "../../index.context";
import {TableMaBenhBlacklist, TableMaBenhWhitelist} from ".";
import {IModalCauHinhMaBenhRef, ITableMaBenhBlacklistRef, ITableMaBenhWhitelistRef, LOAI_AP_DUNG, ModalCauHinhMaBenhProps} from "./Constant";

import "./ModalCauHinhMaBenh.scss";

const ModalCauHinhMaBenhComponent = forwardRef<IModalCauHinhMaBenhRef, ModalCauHinhMaBenhProps>(({}: ModalCauHinhMaBenhProps, ref) => {
  // Context
  const {luuCauHinhMaBenhHopDongConNguoi} = useHopDongConNguoiContext();

  // State
  const [isOpen, setIsOpen] = useState(false);
  const [soId, setSoId] = useState<number | null>(null);
  const [soIdDt, setSoIdDt] = useState<number | null>(null);
  const [activeTab, setActiveTab] = useState("1");

  // Refs
  const refTableWhitelist = useRef<ITableMaBenhWhitelistRef>(null);
  const refTableBlacklist = useRef<ITableMaBenhBlacklistRef>(null);

  // Save handler
  const saveAsyncFn = useCallback(async () => {
    if (!soId || !soIdDt) return;

    const isWhitelistTab = activeTab === "1";
    const currentTableRef = isWhitelistTab ? refTableWhitelist : refTableBlacklist;
    const tableData = currentTableRef.current?.getData();

    if (!tableData) return;

    const selectedMaBenh = tableData
      .filter(item => !item.key.includes("empty") && item.is_selected)
      .map(item => ({
        ma: item.ma || "",
        hinh_thuc_ad: item.hinh_thuc_ap_dung || "",
      }));

    const success = await luuCauHinhMaBenhHopDongConNguoi({
      so_id: soId,
      so_id_dt: soIdDt,
      loai_ad: isWhitelistTab ? "WL" : "BL",
      benh: selectedMaBenh,
    });

    if (success) {
      currentTableRef.current?.refreshData();
    }
  }, [soId, soIdDt, activeTab, luuCauHinhMaBenhHopDongConNguoi]);

  const [handleSave, isSaving] = useAsyncAction(saveAsyncFn);

  // Debounced tab change
  const debouncedTabChange = useCallback(
    debounce((key: string) => setActiveTab(key), 150),
    [],
  );

  // Reset modal state
  const resetModal = useCallback(() => {
    setIsOpen(false);
    setSoId(null);
    setSoIdDt(null);
    setActiveTab("1");
  }, []);

  // Expose methods via ref
  useImperativeHandle(ref, () => ({
    open: (hopDongId?: number, doiTuongId?: number) => {
      setIsOpen(true);
      setSoId(hopDongId || null);
      setSoIdDt(doiTuongId || null);
      setActiveTab("1");

      setTimeout(() => {
        refTableWhitelist.current?.resetSelections();
        refTableBlacklist.current?.resetSelections();
      }, 100);
    },
    close: resetModal,
  }));

  // Tab change handler
  const handleTabChange = useCallback(
    (activeKey: string) => {
      if (activeKey === activeTab) return;

      debouncedTabChange(activeKey);

      setTimeout(() => {
        const tableRef = activeKey === "1" ? refTableWhitelist : refTableBlacklist;
        tableRef.current?.refreshData();
      }, 200);
    },
    [activeTab, debouncedTabChange],
  );

  // Tab items
  const tabItems = [
    {
      key: "1",
      label: "Mã bệnh white list",
      children: <TableMaBenhWhitelist ref={refTableWhitelist} soId={soId || undefined} soIdDt={soIdDt || undefined} loaiApDung={LOAI_AP_DUNG.WHITELIST} />,
    },
    {
      key: "2",
      label: "Mã bệnh black list",
      children: <TableMaBenhBlacklist ref={refTableBlacklist} soId={soId || undefined} soIdDt={soIdDt || undefined} loaiApDung={LOAI_AP_DUNG.BLACKLIST} />,
    },
  ];

  // Footer
  const footer = (
    <Flex justify="flex-end" gap="small">
      <Button type="default" icon={<CloseOutlined />} onClick={resetModal}>
        Đóng
      </Button>
      <Button type="primary" icon={<CheckOutlined />} loading={isSaving} onClick={handleSave}>
        Lưu
      </Button>
    </Flex>
  );

  return (
    <Modal
      centered
      maskClosable={false}
      title={<HeaderModal title="Cấu hình mã bệnh" trang_thai_ten="" trang_thai="" />}
      open={isOpen}
      onCancel={resetModal}
      width="80%"
      styles={{
        body: {
          height: "80vh",
        },
      }}
      footer={footer}
      className="modal-cau-hinh-ma-benh [&_.ant-space]:w-full">
      <Tabs animated={false} size="small" defaultActiveKey="1" activeKey={activeTab} onChange={handleTabChange} items={tabItems} />
    </Modal>
  );
});

ModalCauHinhMaBenhComponent.displayName = "ModalCauHinhMaBenhComponent";
export const ModalCauHinhMaBenh = memo(ModalCauHinhMaBenhComponent, isEqual);

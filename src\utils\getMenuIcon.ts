import {
  UserOutlined,
  SafetyOutlined,
  FileTextOutlined,
  BuildOutlined,
  CarOutlined,
  CheckCircleOutlined,
  Bar<PERSON>hartOutlined,
  ControlOutlined,
  MenuOutlined,
  AppstoreOutlined,
  SettingOutlined,
  TeamOutlined,
  DatabaseOutlined,
  DashboardOutlined,
} from "@ant-design/icons";
import React from "react";

/**
 * Enum cho các menu thực tế từ hệ thống (14 mục)
 * Dựa trên dữ liệu menuNguoiDung đã ghi log
 */
enum MenuItems {
  QUAN_LY_KHACH_HANG = "quan ly khach hang", // Quản lý khách hàng
  BAO_HIEM_CON_NGUOI = "bao hiem con nguoi", // Bảo hiểm con người
  HOP_DONG_CON_NGUOI = "hop dong con nguoi", // Hợp đồng con người
  XAY_DUNG_GOI_BAO_HIEM = "xay dung goi bao hiem", // Xây dựng gói bảo hiểm
  CHUONG_TRINH_BAO_HIEM = "chuong trinh bao hiem", // Chương trình bảo hiểm
  BAO_HIEM_XE_CO_GIOI = "bao hiem xe co gioi", // Bảo hiểm xe cơ giới
  PHE_DUYET_HOP_DONG = "phe duyet hop dong", // Phê duyệt hợp đồng
  PHE_DUYET_HOP_DONG_CON_NGUOI = "phe duyet hop dong con nguoi", // Phê duyệt hợp đồng con người
  HOP_DONG_XE_CO_GIOI = "hop dong xe co gioi", // Hợp đồng xe cơ giới
  HE_THONG_BAO_CAO = "he thong bao cao", // Hệ thống báo cáo
  BAO_CAO_HO_SO_PHAT_SINH = "bao cao ho so phat sinh", // Báo cáo hồ sơ phát sinh
  BAO_CAO_HSBT_CHUA_GIAI_QUYET = "bao cao hsbt chua giai quyet", // Báo cáo HSBT chưa giải quyết
  BAO_CAO_HSBT_DA_GIAI_QUYET = "bao cao hsbt da giai quyet", // Báo cáo HSBT đã giải quyết
  BAO_CAO_DU_PHONG = "bao cao du phong", // Báo cáo dự phòng
  QUAN_TRI_HE_THONG = "quan tri he thong", // Quản trị hệ thống
  DANH_MUC_CHUNG = "danh muc chung", // Danh mục chung
  DANH_MUC_HD_CHUNG = "danh muc hd chung", // Danh mục HĐ chung
  DANH_MUC_HD_XE_CO_GIOI = "danh muc hd xe co gioi", // Danh mục HĐ xe cơ giới
  CAU_HINH_HE_THONG = "cau hinh he thong", // Cấu hình hệ thống
  HE_THONG_TAI_KHOAN = "he thong tai khoan", // Hệ thống tài khoản
}

/**
 * Ánh xạ icon đơn giản và rõ ràng cho các menu thực tế
 */
const MENU_ICON_MAPPING: Record<string, React.ComponentType<any>> = {
  [MenuItems.QUAN_LY_KHACH_HANG]: UserOutlined,
  [MenuItems.BAO_HIEM_CON_NGUOI]: SafetyOutlined,
  [MenuItems.HOP_DONG_CON_NGUOI]: FileTextOutlined,
  [MenuItems.XAY_DUNG_GOI_BAO_HIEM]: BuildOutlined,
  [MenuItems.CHUONG_TRINH_BAO_HIEM]: SafetyOutlined,
  [MenuItems.BAO_HIEM_XE_CO_GIOI]: CarOutlined,
  [MenuItems.PHE_DUYET_HOP_DONG]: CheckCircleOutlined,
  [MenuItems.PHE_DUYET_HOP_DONG_CON_NGUOI]: CheckCircleOutlined,
  [MenuItems.HOP_DONG_XE_CO_GIOI]: CarOutlined,
  [MenuItems.HE_THONG_BAO_CAO]: BarChartOutlined,
  [MenuItems.BAO_CAO_HO_SO_PHAT_SINH]: FileTextOutlined,
  [MenuItems.BAO_CAO_HSBT_CHUA_GIAI_QUYET]: FileTextOutlined,
  [MenuItems.BAO_CAO_HSBT_DA_GIAI_QUYET]: FileTextOutlined,
  [MenuItems.BAO_CAO_DU_PHONG]: SafetyOutlined,
  [MenuItems.QUAN_TRI_HE_THONG]: ControlOutlined,
  [MenuItems.DANH_MUC_CHUNG]: AppstoreOutlined,
  [MenuItems.DANH_MUC_HD_CHUNG]: DatabaseOutlined,
  [MenuItems.DANH_MUC_HD_XE_CO_GIOI]: CarOutlined,
  [MenuItems.CAU_HINH_HE_THONG]: SettingOutlined,
  [MenuItems.HE_THONG_TAI_KHOAN]: TeamOutlined,
};

/**
 * Chuẩn hóa văn bản để so khớp nhất quán
 */
const normalizeText = (text: string): string => {
  return text
    .toLowerCase()
    .trim()
    .replace(/[àáạảãâầấậẩẫăằắặẳẵ]/g, "a")
    .replace(/[èéẹẻẽêềếệểễ]/g, "e")
    .replace(/[ìíịỉĩ]/g, "i")
    .replace(/[òóọỏõôồốộổỗơờớợởỡ]/g, "o")
    .replace(/[ùúụủũưừứựửữ]/g, "u")
    .replace(/[ỳýỵỷỹ]/g, "y")
    .replace(/đ/g, "d")
    .replace(/[^a-z0-9\s]/g, " ")
    .replace(/\s+/g, " ")
    .trim();
};

/**
 * Tạo ánh xạ icon dựa trên dữ liệu menuNguoiDung thực tế
 * Hàm này nên được gọi một lần để tạo ánh xạ chính xác
 */
export const createIconMappingFromMenuData = (menuData: any[]): void => {
  console.log("=== Tạo ánh xạ Icon từ dữ liệu Menu ===");
  const uniqueMenuNames = new Set<string>();

  menuData.forEach(item => {
    if (item.ten) {
      const normalizedName = normalizeText(item.ten);
      uniqueMenuNames.add(normalizedName);
      const iconComponent = getIconComponent(normalizedName);
      // console.log(`Main Menu: "${item.ten}" -> "${normalizedName}" -> ${iconComponent.name}`);
    }

    // Check subitems
    const itemWithItems = item as any;
    if (itemWithItems.items && itemWithItems.items.length > 0) {
      itemWithItems.items.forEach((subItem: any) => {
        if (subItem.ten || subItem.label) {
          const subMenuName = subItem.ten || subItem.label;
          const normalizedName = normalizeText(subMenuName);
          uniqueMenuNames.add(normalizedName);
          const iconComponent = getIconComponent(normalizedName);
          // console.log(`  Sub Menu: "${subMenuName}" -> "${normalizedName}" -> ${iconComponent.name}`);
        }
      });
    }
  });
};

/**
 * Lấy component icon cho menu dựa trên tên đã chuẩn hóa
 */
const getIconComponent = (normalizedName: string): React.ComponentType<any> => {
  // Kiểm tra xem có khớp với menu đã định nghĩa không
  const iconComponent = MENU_ICON_MAPPING[normalizedName];
  if (iconComponent) {
    return iconComponent;
  }

  // Khớp mẫu cho các mục tương tự
  if (normalizedName.includes("khach hang") || normalizedName.includes("customer")) return UserOutlined;
  if (normalizedName.includes("bao hiem") && normalizedName.includes("con nguoi")) return SafetyOutlined;
  if (normalizedName.includes("bao hiem") && normalizedName.includes("xe")) return CarOutlined;
  if (normalizedName.includes("bao hiem")) return SafetyOutlined;
  if (normalizedName.includes("hop dong") && normalizedName.includes("xe")) return CarOutlined;
  if (normalizedName.includes("hop dong")) return FileTextOutlined;
  if (normalizedName.includes("phe duyet")) return CheckCircleOutlined;
  if (normalizedName.includes("xay dung") || normalizedName.includes("goi")) return BuildOutlined;
  if (normalizedName.includes("chuong trinh")) return SafetyOutlined;
  if (normalizedName.includes("xe") && !normalizedName.includes("bao hiem")) return CarOutlined;
  if (normalizedName.includes("bao cao") && normalizedName.includes("he thong")) return BarChartOutlined;
  if (normalizedName.includes("bao cao")) return FileTextOutlined;
  if (normalizedName.includes("danh muc") && normalizedName.includes("hd") && normalizedName.includes("xe")) return CarOutlined;
  if (normalizedName.includes("danh muc") && normalizedName.includes("hd")) return DatabaseOutlined;
  if (normalizedName.includes("danh muc")) return AppstoreOutlined;
  if (normalizedName.includes("cau hinh")) return SettingOutlined;
  if (normalizedName.includes("tai khoan")) return TeamOutlined;
  if (normalizedName.includes("quan tri") || normalizedName.includes("he thong")) return ControlOutlined;

  // Dashboard
  if (normalizedName.includes("dashboard") || normalizedName === "dashboard") return DashboardOutlined;

  // Icon mặc định
  return MenuOutlined;
};

/**
 * Lấy icon phù hợp cho menu dựa trên tên menu hoặc URL
 * @param menuName - Tên của menu
 * @param menuUrl - URL tùy chọn của menu
 * @returns React component cho icon
 */
export const getMenuIcon = (menuName?: string, menuUrl?: string): React.ReactElement => {
  try {
    if (!menuName && !menuUrl) {
      return React.createElement(MenuOutlined, {className: "icon"});
    }

    if (menuName) {
      const normalizedMenuName = normalizeText(menuName);
      const IconComponent = getIconComponent(normalizedMenuName);
      return React.createElement(IconComponent, {className: "icon"});
    }

    // Thử khớp URL nếu tên menu không hoạt động
    if (menuUrl && menuUrl !== "#") {
      const normalizedUrl = normalizeText(menuUrl);
      const IconComponent = getIconComponent(normalizedUrl);
      return React.createElement(IconComponent, {className: "icon"});
    }

    // Icon mặc định
    return React.createElement(MenuOutlined, {className: "icon"});
  } catch (error) {
    // Xử lý lỗi - trả về icon mặc định nếu có lỗi xảy ra
    console.warn("Lỗi trong getMenuIcon:", error);
    return React.createElement(MenuOutlined, {className: "icon"});
  }
};

export default getMenuIcon;

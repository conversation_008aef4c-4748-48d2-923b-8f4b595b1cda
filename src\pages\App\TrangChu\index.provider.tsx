import React, {<PERSON>psWithChildren, useMemo, useState} from "react";
import {TrangChuContext} from "./index.context";
import {ITrangChuContextProps} from "./index.model";

const TrangChuProvider: React.FC<PropsWithChildren> = props => {
  const {children} = props;
  const [loading, setLoading] = useState<boolean>(false);

  const value = useMemo<ITrangChuContextProps>(
    () => ({
      loading,
      setLoading,
    }),
    [loading],
  );

  return <TrangChuContext.Provider value={value}>{children}</TrangChuContext.Provider>;
};

export default TrangChuProvider;

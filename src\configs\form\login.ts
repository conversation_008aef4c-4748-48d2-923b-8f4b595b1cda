import {Authen} from "@src/@types/Authentication";

export const LoginFormConfigs: Authen.Login.IFormFieldsConfig = {
  tai_khoan: {
    name: "tai_khoan",
    // label: strings().label_email,
    component: "input",
    rules: [
      {
        type: "email",
      },
    ],
  },
  mat_khau: {
    name: "mat_khau",
    // label: strings().label_password,
    // errorMessage: strings().error_field_invalid,
    component: "input-password",
    rules: [
      {
        min: 8,
      },
    ],
  },
};

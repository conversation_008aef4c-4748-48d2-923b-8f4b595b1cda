import {ReactQuery} from "@src/@types";
import {ACTION_CODE} from "@src/constants";
import {CommonExecuteResponse, useCommonExecute} from "@src/services/react-queries";
import {message} from "antd";
import React, {PropsWithChildren, useCallback, useEffect, useMemo, useState} from "react";
import {HeThongChucNangContext} from "./index.context";
import {HeThongChucNangContextProps} from "./index.model";

/* 
  file Provider : Chứa logic quản lý state, gọi API, wrap context
*/
const HeThongChucNangProvider: React.FC<PropsWithChildren> = props => {
  const {children} = props;
  const mutateUseCommonExecute = useCommonExecute();
  const [danhSachHeThongChucNang, setDanhSachHeThongChucNang] = useState<Array<CommonExecute.Execute.IDanhSachHeThongChucNangPhanTrang>>([]);
  const [chiTietHeThongChucNang, setChiTietHeThongChucNang] = useState<CommonExecute.Execute.IChiTietChucNang>({});
  const [tongSoDong, setTongSoDong] = useState<number>(0);
  const defaultFormValue: ReactQuery.ILayDanhSachChucNangPhanTrangParams = {
    ten: "",
    loai: "",
    kieu_ad: "",
    trang_thai: "",
    trang: 1,
    so_dong: 13,
  };

  useEffect(() => {
    // layDanhSachPhongBan()
    initData();
  }, []);

  const initData = () => {
    layDanhSachChucNangPhanTrang(defaultFormValue);
  };

  //DS phòng ban phân trang - Danh sách người dùng
  const layDanhSachChucNangPhanTrang = useCallback(
    async (body: ReactQuery.ILayDanhSachChucNangPhanTrangParams & ReactQuery.IPhanTrang) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.GET_DANH_SACH_CHUC_NANG_PHAN_TRANG,
        };
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        const data = response.data.data;
        setDanhSachHeThongChucNang(data);
        setTongSoDong(response.data.tong_so_dong);
      } catch (error: any) {
        console.log("layDanhSachChucNangPhanTrang error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );

  //Lấy chi tiết 1 chức năng
  const layChiTietChucNang = useCallback(
    async (item: ReactQuery.IChiTietChucNangParams): Promise<CommonExecute.Execute.IChiTietChucNang | null> => {
      try {
        const params = {
          ma: item.ma,
          actionCode: ACTION_CODE.GET_CHI_TIET_CHUC_NANG,
        };
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        setChiTietHeThongChucNang(responseData.data);
        return responseData.data as CommonExecute.Execute.IChiTietChucNang;
      } catch (error: any) {
        console.log("layChiTietChucNang error ", error.message || error);
        return null;
      }
    },
    [mutateUseCommonExecute],
  );

  //Đồng bộ chức năng
  const onSyncChucNang = useCallback(async () => {
    try {
      const params = {
        actionCode: ACTION_CODE.SYNC_CHUC_NANG,
      };
      const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
      if (responseData && (responseData.data as unknown as number) === -1) {
        message.success("Đồng bộ chức năng thành công!");
        initData();
      }
    } catch (error: any) {
      console.log("onSyncChucNang error ", error.message || error);
    }
  }, [mutateUseCommonExecute]);

  //Cập nhật hoặc tạo mới 1 phòng ban
  const onUpdateHeThongChucNang = useCallback(
    async (body: ReactQuery.IUpdateChucNangParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.LUU_CAP_NHAT_CHUC_NANG,
        };
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        if (responseData && (responseData.data as unknown as number) === -1) {
          message.success("Cập nhật thông tin thành công!");
          initData();
        }
      } catch (error: any) {
        console.log("onUpdateHeThongChucNang error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );

  //khai báo giá trị của context để truyền vào Component
  const value = useMemo<HeThongChucNangContextProps>(
    () => ({
      danhSachHeThongChucNang: danhSachHeThongChucNang,
      loading: mutateUseCommonExecute.isLoading,
      onUpdateHeThongChucNang: onUpdateHeThongChucNang,
      tongSoDong,
      layDanhSachChucNangPhanTrang: layDanhSachChucNangPhanTrang,
      layChiTietChucNang: layChiTietChucNang,
      defaultFormValue,
      onSyncChucNang: onSyncChucNang,
    }),
    [danhSachHeThongChucNang, mutateUseCommonExecute, onUpdateHeThongChucNang, tongSoDong, layDanhSachChucNangPhanTrang, layChiTietChucNang, onSyncChucNang],
  );

  return <HeThongChucNangContext.Provider value={value}>{children}</HeThongChucNangContext.Provider>;
};

export default HeThongChucNangProvider;

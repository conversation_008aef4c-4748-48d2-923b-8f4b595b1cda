import React from "react";
import {<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, Table, Avatar} from "antd";
import {DollarOutlined, TrophyOutlined, TeamOutlined, AimOutlined} from "@ant-design/icons";
import {useDashboardContext} from "./index.context";
import {<PERSON><PERSON><PERSON>, Pie<PERSON>hart} from "./components";
import TrendIndicator from "./components/TrendIndicator";
import {ITopClient, ITopSeller} from "./index.model";

const DashboardContent: React.FC = () => {
  const {data, selectedPeriod, setSelectedPeriod} = useDashboardContext();

  // Format number to Vietnamese currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    }).format(value);
  };

  // Format number with thousand separators (Vietnamese format)
  const formatNumber = (value: number) => {
    return new Intl.NumberFormat("vi-VN").format(value);
  };

  // Prepare chart data
  const monthlyChartData = {
    labels: data.monthlyStats.map(stat => stat.month),
    currentYear: data.monthlyStats.map(stat => stat.currentYear),
    previousYear: data.monthlyStats.map(stat => stat.previousYear),
    target: data.monthlyStats.map(stat => stat.target), // Add target data, use a default if not available
  };

  const revenueByChannelData = {
    labels: data.revenueByChannel.map(item => item.channel),
    values: data.revenueByChannel.map(item => item.amount),
  };

  // Table columns for top clients
  const clientColumns = [
    {
      title: "",
      dataIndex: "id",
      key: "id",
      width: 30,
      render: (id: number) => <span style={{color: "#8c8c8c", fontSize: "14px", fontWeight: 500}}>{id}</span>,
    },
    {
      title: "Tên",
      dataIndex: "name",
      key: "name",
      render: (text: string, record: ITopClient) => (
        <div style={{display: "flex", alignItems: "center", justifyContent: "space-between"}}>
          <span style={{fontSize: "14px", color: "#262626"}}>{text}</span>
          <TrendIndicator trend={record.trend} value={record.trendValue} size="small" />
        </div>
      ),
    },
    {
      title: "Doanh thu",
      dataIndex: "revenue",
      key: "revenue",
      render: (value: number) => <span style={{fontSize: "14px", color: "#262626", fontWeight: 500}}>{formatNumber(value)}</span>,
      align: "right" as const,
    },
  ];

  // Table columns for top sellers
  const sellerColumns = [
    {
      title: "",
      dataIndex: "id",
      key: "id",
      width: 30,
      render: (id: number) => <span style={{color: "#8c8c8c", fontSize: "14px", fontWeight: 500}}>{id}</span>,
    },
    {
      title: "Tên",
      dataIndex: "name",
      key: "name",
      render: (text: string, record: ITopSeller) => (
        <div style={{display: "flex", alignItems: "center", justifyContent: "space-between"}}>
          <div style={{display: "flex", alignItems: "center"}}>
            <Avatar size="small" style={{marginRight: 8, backgroundColor: "#52c41a"}}>
              {text.charAt(0)}
            </Avatar>
            <span style={{fontSize: "14px", color: "#262626"}}>{text}</span>
          </div>
          <TrendIndicator trend={record.trend} value={record.trendValue} size="small" />
        </div>
      ),
    },
    {
      title: "Doanh thu",
      dataIndex: "revenue",
      key: "revenue",
      render: (value: number) => <span style={{fontSize: "14px", color: "#262626", fontWeight: 500}}>{formatNumber(value)}</span>,
      align: "right" as const,
    },
  ];

  return (
    <div className="dashboard-content">
      {/* Controls */}
      {/* <div className="dashboard-header-controls" style={{marginBottom: "16px", display: "flex", justifyContent: "flex-end", alignItems: "center", gap: "12px"}}>
        <Select value={selectedPeriod} onChange={setSelectedPeriod} style={{width: 120}}>
          {PERIOD_OPTIONS.map(option => (
            <Option key={option.value} value={option.value}>
              {option.label}
            </Option>
          ))}
        </Select>
        <Button icon={<ReloadOutlined />} onClick={refreshData} loading={loading}>
          Làm mới
        </Button>
      </div> */}

      {/* Stats Cards */}
      <Row gutter={[8, 8]} style={{marginBottom: "8px"}}>
        <Col xs={24} sm={12} lg={6}>
          <Card className="stats-card stats-card-revenue">
            <div className="stats-content">
              <div className="stats-title">
                <div className="stats-icon">
                  <DollarOutlined />
                </div>
                Tổng Doanh Thu
              </div>
              <div className="stats-value">{formatCurrency(data.stats.totalRevenue)}</div>
              <div className="stats-subtitle">+15% so với cùng kỳ</div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card className="stats-card stats-card-retained">
            <div className="stats-content">
              <div className="stats-title">
                <div className="stats-icon">
                  <TrophyOutlined />
                </div>
                Doanh Thu Giữ lại
              </div>
              <div className="stats-value">{formatCurrency(data.stats.retainedRevenue)}</div>
              <div className="stats-subtitle">+8% so với cùng kỳ</div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card className="stats-card stats-card-sales">
            <div className="stats-content">
              <div className="stats-title">
                <div className="stats-icon">
                  <TeamOutlined />
                </div>
                Tổng Doanh Số
              </div>
              <div className="stats-value">{formatCurrency(data.stats.totalCompanies).toLocaleString()}</div>
              <div className="stats-subtitle">-3% so với cùng kỳ</div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card className="stats-card stats-card-target">
            <div className="stats-content">
              <div className="stats-title">
                <div className="stats-icon">
                  <AimOutlined />
                </div>
                Mục tiêu
              </div>
              <div className="stats-value">{data.stats.targetAmount.toLocaleString()}</div>
              <div className="stats-subtitle">để hoàn thành mục tiêu</div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Charts Row */}
      <Row gutter={[8, 8]} style={{marginBottom: "8px"}}>
        {/* Chart Section */}

        <Col xs={24} sm={24} md={12} lg={18}>
          <Card title="Thống kê từng kỳ" style={{height: "400px", display: ""}} className="stats-chart-card">
            <div className="chart-container" style={{width: "70%"}}>
              <LineChart data={monthlyChartData} height={320} />
            </div>
            <div className="time-statistics-wrapper" style={{width: "30%"}}>
              {/* Period Tabs */}
              <div className="period-tabs">
                <Button type={selectedPeriod === "week" ? "primary" : "default"} size="small" onClick={() => setSelectedPeriod("week")}>
                  Tuần
                </Button>
                <Button type={selectedPeriod === "month" ? "primary" : "default"} size="small" onClick={() => setSelectedPeriod("month")}>
                  Tháng
                </Button>
                <Button type={selectedPeriod === "year" ? "primary" : "default"} size="small" onClick={() => setSelectedPeriod("year")}>
                  Năm
                </Button>
              </div>
              {/* Current Period Stats */}

              {/* className="period-comparison" */}
              <div className="period-comparison">
                <Row>
                  <Col span={12} className="comparison-item text-center">
                    <Button type="primary" color="green" className="period-btn week" size="small">
                      {/* <div className="period-label"> */}
                      {selectedPeriod === "week" ? "Tuần này" : selectedPeriod === "month" ? "Tháng này" : "Năm này"}

                      {/* </div> */}
                    </Button>
                    <div className="period-date">{selectedPeriod === "week" ? "Tuần 42/2024" : selectedPeriod === "month" ? "Tháng 10/2024" : "Năm 2024"}</div>
                  </Col>
                  <Col span={12} className="comparison-item text-center">
                    <Button className="period-btn target" size="small">
                      Mục tiêu
                    </Button>
                    <div className="comparison-value">{formatNumber(selectedPeriod === "week" ? 38000000 : selectedPeriod === "month" ? 190000000 : 2100000000)}</div>
                  </Col>
                </Row>
                <Row>
                  <Col span={12} className="comparison-item text-center">
                    <Button className="period-btn current" size="small">
                      Năm nay
                    </Button>
                    <div className="comparison-value">{formatNumber(selectedPeriod === "week" ? 42000000 : selectedPeriod === "month" ? 170000000 : 2340000000)}</div>
                  </Col>
                  <Col span={12} className="comparison-item text-center">
                    <Button className="period-btn previous" size="small">
                      Năm trước
                    </Button>
                    <div className="comparison-value">{formatNumber(selectedPeriod === "week" ? 38000000 : selectedPeriod === "month" ? 190000000 : 2100000000)}</div>
                  </Col>
                </Row>
              </div>
            </div>
          </Card>
        </Col>

        <Col xs={24} sm={12} md={6} lg={6}>
          <Card title="Doanh thu theo kênh bán" style={{height: "400px"}}>
            <PieChart data={revenueByChannelData} height={300} />
          </Card>
        </Col>
      </Row>

      {/* Insurance Categories Table */}
      <Row gutter={[8, 8]} style={{marginBottom: "8px"}}>
        <Col xs={24} lg={12}>
          <Card title="Tỷ lệ bồi thường" className="insurance-table-card">
            <div className="insurance-grid">
              {data.insuranceCategories.map((category, index) => (
                <div key={index} className="insurance-item">
                  {/* <div className="insurance-icon">
                    <div className={`icon-bg icon-${index + 1}`}>{category.name.charAt(0)}</div>
                  </div> */}
                  <div className="insurance-content">
                    <div className="insurance-name">
                      <div className="insurance-icon">
                        <div className={`icon-bg icon-${index + 1}`}>{category.name.charAt(0)}</div>
                      </div>
                      {category.name}
                    </div>
                    <div className="insurance-stats">
                      <div className="stat-item">
                        <span className="stat-label">Doanh thu:</span>
                        <span className="stat-value">{formatCurrency(category.revenue)}</span>
                      </div>
                      <div className="stat-item">
                        <span className="stat-label">Bồi thường:</span>
                        <span className="stat-value">{formatCurrency(category.claims)}</span>
                      </div>
                      <div className="stat-item">
                        <span className="stat-label">Tỷ lệ bồi thường:</span>
                        <span className="stat-value rate">{category.claimsRate}%</span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </Col>
        <Col xs={24} lg={6}>
          <Card title="Top đơn vị" className="top-clients-card" style={{height: "100%"}}>
            <Table columns={clientColumns} dataSource={data.topClients} pagination={false} size="small" rowKey="id" showHeader={true} />
          </Card>
        </Col>
        <Col xs={24} lg={6}>
          <Card title="Top seller" className="top-sellers-card" style={{height: "100%"}}>
            <Table columns={sellerColumns} dataSource={data.topSellers} pagination={false} size="small" rowKey="id" showHeader={true} />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

DashboardContent.displayName = "DashboardContent";

export default DashboardContent;

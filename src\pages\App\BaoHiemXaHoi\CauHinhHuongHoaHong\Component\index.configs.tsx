import {IFormInput} from "@src/@types";
import {colWidthByKey, defaultTableColumnsProps, ruleInputMessage} from "@src/hooks";
import {TableProps} from "antd";

export interface IModalChiTietCauHinhHuongHoaHongRef {
  open: (data?: any) => void;
  close: () => void;
}

export interface PropsThem {
  selectedNgayApDung: number | null;
}
export interface Props {}
//cấu hình bảng ngày áp dụng
export interface TableNgayApDungDataType {
  key: string;
  stt: number;
  ngay_ad: string;
  bt: number;
  hanh_dong?: () => JSX.Element | null; // Định nghĩa hanh_dong là hàm trả về JSX.Element;
}
export const ngayApDungColumns: TableProps<TableNgayApDungDataType>["columns"] = [
  {
    ...defaultTableColumnsProps,
    title: "Ngày áp dụng",
    dataIndex: "ngay_ad",
    key: "ngay_ad",
  },

  {
    title: "Xóa",
    dataIndex: "hanh_dong",
    // key: "hanh_dong",
    width: 50,

    render: (_, record) => record.hanh_dong?.(),
    ...defaultTableColumnsProps,
  },
];
export type DataIndexNgayApDung = keyof TableNgayApDungDataType;
//FormThemNgayApDung
export interface IFormThemNgayApDungFieldsConfig {
  ngay_ad?: IFormInput;
}
export const FormThemNgayApDung: IFormThemNgayApDungFieldsConfig = {
  ngay_ad: {
    component: "date-picker",
    name: "ngay_ad",
    placeholder: "Ngày áp dụng",
    label: "Ngày áp dụng", // cho label vào thì sẽ thành input with label
    rules: [ruleInputMessage.required],
  },
};
export interface TableCauHinhHuongHoaHongDataType {
  key: string;
  stt: number;
  bt_ad: number;
  bt: number;
  ma_dvi: string;
  ma_sp: string;
  loai_hd: string;
  loai_ho_gia_dinh: string;
  tlhh: number;
  so_thang_tu: number;
  so_thang_toi: number;
  loai_hd_ten: string;
  loai_ho_gia_dinh_ten: string;
  hanh_dong?: () => JSX.Element | null; // Định nghĩa hanh_dong là hàm trả về JSX.Element;
}
export const cauHinhHuongHoaHongColumns: TableProps<TableCauHinhHuongHoaHongDataType>["columns"] = [
  {
    ...defaultTableColumnsProps,
    title: "STT",
    dataIndex: "stt",
    width: colWidthByKey.sott,
    key: "stt",
    // onHeaderCell: () => ({
    //   style: {borderRadius: 0},
    // }),
  },
  {
    ...defaultTableColumnsProps,
    title: "Đơn vị",
    dataIndex: "ma_dvi",
    width: 100,
    key: "ma_dvi",
  },
  {
    ...defaultTableColumnsProps,
    title: "Mã sản phẩm",
    dataIndex: "ma_sp",
    width: 120,
    key: "ma_sp",
  },
  {
    ...defaultTableColumnsProps,
    title: "Loại hợp đồng",
    dataIndex: "loai_hd_ten",
    // width: 100,
    key: "loai_hd_ten",
  },
  {
    ...defaultTableColumnsProps,
    title: "Loại hộ gia đình",
    dataIndex: "loai_ho_gia_dinh_ten",
    // width: 100,
    key: "loai_ho_gia_dinh_ten",
  },

  {
    ...defaultTableColumnsProps,
    title: "Số tháng từ",
    dataIndex: "so_thang_tu",
    width: 100,
    key: "so_thang_tu",
  },
  {
    ...defaultTableColumnsProps,
    title: "Số tháng tới",
    dataIndex: "so_thang_toi",
    width: 100,
    key: "so_thang_toi",
  },
  {
    ...defaultTableColumnsProps,
    title: "TLHH (%)",
    dataIndex: "tlhh",
    width: 100,
    key: "tlhh",
  },
  {
    title: "Xóa",
    dataIndex: "hanh_dong",
    // key: "hanh_dong",
    width: 50,
    // onHeaderCell: () => ({
    //   style: {borderRadius: 0},
    // }),
    render: (_, record) => record.hanh_dong?.(),
    ...defaultTableColumnsProps,
  },
];
export type DataIndexCauHinhHuongHoaHong = keyof TableCauHinhHuongHoaHongDataType;

export interface IFormThemCauHinhHuongHoaHongFieldsConfig {
  ma_sp?: IFormInput;
  ma_dvi?: IFormInput;
  loai_hd?: IFormInput;
  loai_ho_gia_dinh?: IFormInput;
  tlhh?: IFormInput;
  so_thang_tu?: IFormInput;
  so_thang_toi?: IFormInput;
}
export const FormThemCauHinhHuongHoaHong: IFormThemCauHinhHuongHoaHongFieldsConfig = {
  ma_sp: {
    component: "select",
    name: "ma_sp",
    placeholder: "Mã sản phẩm",
    label: "Mã sản phẩm", // cho label vào thì sẽ thành input with label
    rules: [ruleInputMessage.required],
  },

  loai_hd: {
    component: "select",
    name: "loai_hd",
    placeholder: "Loại hợp đồng",
    label: "Loại hợp đồng", // cho label vào thì sẽ thành input with label
    rules: [ruleInputMessage.required],
  },
  loai_ho_gia_dinh: {
    component: "select",
    name: "loai_ho_gia_dinh",
    placeholder: "Loại hộ gia đình",
    label: "Loại hộ gia đình", // cho label vào thì sẽ thành input with label
    rules: [ruleInputMessage.required],
  },
  tlhh: {
    component: "input",
    name: "tlhh",
    placeholder: "Tỷ lệ hoa hồng (%)",
    label: "Tỷ lệ hoa hồng (%)", // cho label vào thì sẽ thành input with label
    rules: [ruleInputMessage.required],
  },
  so_thang_tu: {
    component: "input",
    name: "so_thang_tu",
    placeholder: "Số tháng từ",
    label: "Số tháng từ", // cho label vào thì sẽ thành input with label
    rules: [ruleInputMessage.required],
  },
  so_thang_toi: {
    component: "input",
    name: "so_thang_toi",
    placeholder: "Số tháng tới",
    label: "Số tháng tới", // cho label vào thì sẽ thành input with label
    rules: [ruleInputMessage.required],
  },
};
export const LOAI_HOP_DONG = [
  {ten: "Hợp đồng gốc", ma: "G"},
  {ten: "Hợp đồng tái tục", ma: "T"},
];
export const SAN_PHAM = [
  {ten: "Bảo hiểm xã hội", ma: "BHXH"},
  {ten: "Bảo hiểm y tế", ma: "BHYT"},
];
export interface IModalThemCauHinhHoaHongRef {
  open: (data?: any) => void;
  close: () => void;
}
export interface IModalThemNgayADRef {
  open: (data?: any) => void;
  close: () => void;
}
export interface PropThems {}

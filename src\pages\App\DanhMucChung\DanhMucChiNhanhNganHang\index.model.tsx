import {ReactQuery} from "@src/@types";

export interface DanhMucChiNhanhNganHangContextProps {
  /** Danh sách chi nhánh ngân hàng hiển thị trên bảng */
  danhSachChiNhanhNganHang: CommonExecute.Execute.IDanhSachChiNhanhNganHangPhanTrang[];
  loading: boolean;
  tongSoDong: number;
  /** Danh sách ngân hàng để hiển thị trong dropdown */
  listNganHang: CommonExecute.Execute.IDanhMucNganHang[];
  /** Giá trị mặc định cho form tìm kiếm */
  defaultFormValue: ReactQuery.ITimKiemPhanTrangChiNhanhNganHangParams & ReactQuery.IPhanTrang;
  layDanhSachChiNhanhNganHang: (params: ReactQuery.ITimKiemPhanTrangChiNhanhNganHangParams & ReactQuery.IPhanTrang) => Promise<void>;
  layChiTietChiNhanhNganHang: (params: ReactQuery.IChiTietChiNhanhNganHangParams) => Promise<CommonExecute.Execute.IChiTietChiNhanhNganHang | null>;
  capNhatChiNhanhNganHang: (data: ReactQuery.ICapNhatChiNhanhNganHangParams) => Promise<boolean>;
  getListNganHang: () => Promise<void>;
}

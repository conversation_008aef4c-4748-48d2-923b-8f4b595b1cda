import { Authen } from "@src/@types/Authentication";
import strings from "@src/assets/strings";

export const SignupFormConfigs: Authen.Signup.IFormFieldsConfig = {
  email: {
    name: "email",
    label: strings().label_email,
    component: "input",
    rules: [
      {
        type: "email",
      },
    ],
  },
  password: {
    name: "password",
    label: strings().label_password,
    errorMessage: strings().error_field_invalid,
    component: "input-password",
    rules: [
      {
        min: 8,
      },
    ],
  },
  confirmedPassword: {
    name: "confirmed_password",
    label: strings().label_confirm_password,
    errorMessage: strings().error_field_invalid,
    component: "input-password",
    rules: [
      {
        min: 8,
      },
    ],
  },
};

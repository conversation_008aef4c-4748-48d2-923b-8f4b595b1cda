import React from "react";
import React<PERSON>pex<PERSON><PERSON> from "react-apexcharts";
import {ApexOptions} from "apexcharts";

interface IBarChartProps {
  data: {
    categories: string[];
    series: {
      name: string;
      data: number[];
      color?: string;
    }[];
  };
  height?: number;
  title?: string;
  horizontal?: boolean;
}

const BarChart: React.FC<IBarChartProps> = ({data, height = 300, title, horizontal = false}) => {
  const options: ApexOptions = {
    chart: {
      type: "bar",
      height: height,
      toolbar: {
        show: false,
      },
    },
    title: {
      text: title,
      align: "left",
      style: {
        fontSize: "16px",
        fontWeight: "600",
        color: "#262626",
      },
    },
    plotOptions: {
      bar: {
        horizontal: horizontal,
        columnWidth: "60%",
        borderRadius: 4,
        dataLabels: {
          position: "top",
        },
      },
    },
    dataLabels: {
      enabled: true,
      formatter: function (val: number) {
        // Check if this is percentage data (values between 0-100)
        if (val <= 100 && data.series[0]?.name?.includes("%")) {
          return val.toFixed(1) + "%";
        }
        return new Intl.NumberFormat("vi-VN", {
          notation: "compact",
          compactDisplay: "short",
        }).format(val);
      },
      offsetY: -20,
      style: {
        fontSize: "10px",
        colors: ["#304758"],
      },
    },
    colors: data.series.map(s => s.color || "#1890ff"),
    xaxis: {
      categories: data.categories,
      labels: {
        style: {
          colors: "#8c8c8c",
          fontSize: "12px",
        },
      },
    },
    yaxis: {
      labels: {
        style: {
          colors: "#8c8c8c",
          fontSize: "12px",
        },
        formatter: function (value) {
          return new Intl.NumberFormat("vi-VN", {
            notation: "compact",
            compactDisplay: "short",
          }).format(value);
        },
      },
    },
    grid: {
      borderColor: "#f0f0f0",
      strokeDashArray: 4,
    },
    legend: {
      position: "top",
      horizontalAlign: "right",
      fontSize: "12px",
      markers: {
        width: 8,
        height: 8,
        radius: 4,
      },
    },
    tooltip: {
      y: {
        formatter: function (value) {
          // Check if this is percentage data
          if (value <= 100 && data.series[0]?.name?.includes("%")) {
            return value.toFixed(1) + "%";
          }
          return new Intl.NumberFormat("vi-VN", {
            style: "currency",
            currency: "VND",
          }).format(value);
        },
      },
    },
    responsive: [
      {
        breakpoint: 768,
        options: {
          chart: {
            height: 250,
          },
          legend: {
            position: "bottom",
          },
          plotOptions: {
            bar: {
              horizontal: false,
            },
          },
        },
      },
    ],
  };

  const series = data.series.map(s => ({
    name: s.name,
    data: s.data,
  }));

  return (
    <div className="bar-chart-container">
      <ReactApexChart options={options} series={series} type="bar" height={height} />
    </div>
  );
};

export default BarChart;

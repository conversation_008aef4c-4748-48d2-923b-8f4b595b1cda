import {createContext, useContext} from "react";

import {IDanhMucPhuongXaProvider} from "./index.model";

// Tạo React Context để chia sẻ state và methods giữa các component
export const DanhMucPhuongXaContext = createContext<IDanhMucPhuongXaProvider>({
  // Dữ liệu hiển thị
  listPhuongXa: [],
  listTinhThanh: [],
  listQuanHuyen: [],
  tongSoDong: 0,
  loading: false,
  
  // Tham số filter và phân trang
  filterParams: {
    ma_tinh: "",
    ma_quan: "",           
    ngay_ad: "",
    ma: "",
    ten: "",
    trang_thai: "",
    trang: 1,
    so_dong: 13,
  },
  
  // Actions
  getListPhuongXa: async () => {},
  getListTinhThanh: async () => {},
  getListQuanHuyen: async () => {},
  getChiTietPhuongXa: async () => ({} as CommonExecute.Execute.IDanhMucPhuongXa),
  capNhatChiTietPhuongXa: async () => {},
  setFilterParams: () => {},
  getTotalCount: async () => 0, // Workaround function cho trường hợp server trả về tong_so_dong sai
});

// Chức năng: Custom hook để các component con có thể dễ dàng access context
// Tự động handle việc kiểm tra context existence và type safety
export const useDanhMucPhuongXaContext = () => useContext(DanhMucPhuongXaContext);

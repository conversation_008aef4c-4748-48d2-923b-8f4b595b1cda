import {ReactQuery} from "@src/@types";
import {ACTION_CODE} from "@src/constants";
import {CommonExecuteResponse, useCommonExecute} from "@src/services/react-queries";
import React, {PropsWithChildren, useCallback, useEffect, useMemo, useState} from "react";
import {defaultFormValue} from "./index.configs";
import {BoMaNguyenteContext} from "./index.context";
import {BoMaNguyenteProps} from "./index.model";
import {message} from "antd";

/* 
  file Provider : Chứa logic quản lý state, gọi API, wrap context
*/
const BoMaNguyenTeProvider: React.FC<PropsWithChildren> = props => {
  const mutateUseCommonExecute = useCommonExecute();
  const [danhSachBoMaNguyenTe, setDanhSachBoMaNguyenTe] = useState<Array<CommonExecute.Execute.IDanhSachBoMaNguyenTe>>([]);
  const [tongSoDong, setTongSoDong] = useState<number>(0);
  const [listDoiTac, setListDoiTac] = useState<Array<CommonExecute.Execute.IDoiTac>>([]);

  useEffect(() => {
    initData();
  }, []);
  const initData = () => {
    getListDoiTac();
    layDanhSachBoMaNguyenTe(defaultFormValue);
  };
  const {children} = props;

  //TÌM KIẾM PHÂN TRANG BỘ MÃ NGUYÊN TỆ
  const layDanhSachBoMaNguyenTe = useCallback(
    async (body: ReactQuery.ITimKiemPhanTrangBoMaNguyenTeParams & ReactQuery.IPhanTrang) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_BO_MA_NGUYEN_TE,
        };
        console.log("params lấy ptkt", params);
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        console.log("response lấy response ptkt", response);
        const data = response.data.data;
        setDanhSachBoMaNguyenTe(data);
        setTongSoDong(response.data.tong_so_dong);
      } catch (error: any) {
        console.log("layDanhSachTimKiemPhanTrangBoMaNguyenTe error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );
  /* ĐỐI TÁC */
  const getListDoiTac = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        actionCode: ACTION_CODE.LIET_KE_DANH_SACH_DOI_TAC,
      });

      setListDoiTac(response?.data?.map(item => ({...item, ten: item.ma + " - " + item.ten})));
    } catch (error) {
      console.log("getListDoiTac error ", error);
    }
  }, [mutateUseCommonExecute]);
  //LẤY CHI TIẾT BỘ MÃ NGUYÊN TỆ
  const layChiTietBoMaNguyenTe = useCallback(
    async (item: ReactQuery.IlayChiTietBoMaNguyenTeParams): Promise<CommonExecute.Execute.IChiTietBoMaNguyenTe | null> => {
      try {
        const params = {
          ma: item.ma,
          ma_doi_tac_ql: item.ma_doi_tac_ql,
          actionCode: ACTION_CODE.CHI_TIET_BO_MA_NGUYEN_TE,
        };
        console.log("params sua ham chi tiet", params);
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        console.log("responseData", responseData);
        // setChiTietHopDongBaoHiemXe(responseData.data)
        return responseData.data as CommonExecute.Execute.IChiTietChucNang;
      } catch (error: any) {
        console.log("layChiTietChucNang error ", error.message || error);
        return null;
      }
    },
    [mutateUseCommonExecute],
  );
  //UPDATE BỘ MÃ NGUYÊN TỆ
  const updateBoMaNguyenTe = useCallback(async (item: ReactQuery.IUpdateBoMaNguyenTeParams) => {
    try {
      const params = {
        ma_doi_tac_ql: item.ma_doi_tac_ql,
        ma: item.ma,
        ten: item.ten,
        stt: item.stt,
        trang_thai: item.trang_thai,
        actionCode: ACTION_CODE.UPDATE_BO_MA_NGUYEN_TE,
      };
      console.log("params update bmnt", params);
      const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
      if (responseData && (responseData.data as unknown as number) === -1) {
        message.success("Cập nhật thông tin thành công!");
        initData();
        //chuyển ddoooit responseData thành number
        return responseData.data as unknown as number; //chuyển đổi responseData thành number
      }
      console.log("responseData", responseData);
      //set updateBoMaNguyenTe(responseData.data)
      return responseData.data;
    } catch (error: any) {
      console.log("updateBoMaNguyenTe", error.message || error);
      return null;
    }
  }, []);
  const value = useMemo<BoMaNguyenteProps>(
    () => ({
      listDoiTac,

      getListDoiTac,

      danhSachBoMaNguyenTe,
      loading: mutateUseCommonExecute.isLoading,
      tongSoDong,
      layDanhSachBoMaNguyenTe,
      layChiTietBoMaNguyenTe,
      defaultFormValue,
      updateBoMaNguyenTe,
    }),
    [danhSachBoMaNguyenTe, mutateUseCommonExecute, tongSoDong, listDoiTac, layDanhSachBoMaNguyenTe, updateBoMaNguyenTe, getListDoiTac],
  );
  return <BoMaNguyenteContext.Provider value={value}>{children}</BoMaNguyenteContext.Provider>;
};
export default BoMaNguyenTeProvider;

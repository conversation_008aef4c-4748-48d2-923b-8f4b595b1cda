import {CheckOutlined, CloseOutlined, PlusCircleOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {Button, FormInput, HeaderModal, InputCellTable, Popcomfirm} from "@src/components";
import {formatCurrencyUS, formatDateTimeToNumber, parseDateTime} from "@src/utils";
import {Col, Flex, Form, Modal, Row, Table, TableColumnType} from "antd";
import dayjs from "dayjs";
import {isEqual} from "lodash";
import {forwardRef, memo, useEffect, useImperativeHandle, useState} from "react";
import {useHopDongConNguoiContext} from "@src/pages/App/BaoHiemConNguoi/HopDongConNguoi/index.context";
import {formNhapKyThanhToanInputConfigs, parseNumber, tableNgayThanhToanColumns, TableNgayThanhToanDataType, IModalKyThanhToanRef} from "./Constant";

const {ky_tt, so_tien} = formNhapKyThanhToanInputConfigs;

const ModalKyThanhToanComponent = forwardRef<IModalKyThanhToanRef, {disabled?: boolean}>(({disabled = false}, ref) => {
  useImperativeHandle(ref, () => ({
    open: (dataKyThanhToan?: CommonExecute.Execute.IKyThanhToan, ky_ttoan_ct?: CommonExecute.Execute.IKyThanhToan) => {
      setIsOpen(true);
      initFormData(dataKyThanhToan ?? ({} as CommonExecute.Execute.IKyThanhToan));
      if (dataKyThanhToan) setChiTietKyThanhToan(dataKyThanhToan);
      if (Array.isArray(ky_ttoan_ct)) initFormTableData(ky_ttoan_ct);
    },
    close: () => setIsOpen(false),
  }));

  const {loading, chiTietHopDong, updateKyThanhToan} = useHopDongConNguoiContext();

  const [isOpen, setIsOpen] = useState<boolean>(false);

  const [formNhapKyThanhToan] = Form.useForm();
  const formValues = Form.useWatch([], formNhapKyThanhToan);
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);

  const [chiTietKyThanhToan, setChiTietKyThanhToan] = useState<CommonExecute.Execute.IKyThanhToan | null>(null);
  const [dataChiTietKyThanhToan, setDataChiTietKyThanhToan] = useState<Array<TableNgayThanhToanDataType>>([]);

  // init modal data kỳ thanh toán
  const initFormData = (chiTiet: CommonExecute.Execute.IKyThanhToan) => {
    const arrFormData = [];
    if (chiTiet) {
      for (const key in chiTiet) {
        let value: any = chiTiet[key as keyof CommonExecute.Execute.IKyThanhToan];

        // Convert ky_tt to dayjs object if it exists and is not null/undefined
        if (key === "ky_tt" && value) {
          value = parseDateTime(value);
        }
        arrFormData.push({
          name: key,
          value: value,
        });
      }
    }
    arrFormData.push({
      name: "so_hd_d",
      value: chiTietHopDong?.so_hd_d ?? "",
    });
    formNhapKyThanhToan.setFields(arrFormData);
  };

  //init modaldata kỳ thanh toán (bảng ngày thanh toán)
  const initFormTableData = (chiTietKyThanhToan: CommonExecute.Execute.IKyThanhToan) => {
    if (chiTietKyThanhToan && Array.isArray(chiTietKyThanhToan)) {
      const tableData = chiTietKyThanhToan.map((item, idx) => {
        const parsedDate = item.ngay_tt ? parseDateTime(item.ngay_tt) : null;
        return {
          ...item,
          key: item.key || item.bt,
          stt: item.stt ?? idx + 1,
          ngay_tt: parsedDate ? dayjs(parsedDate).format("YYYY-MM-DD") : "",
          so_tien_da_tt: item.so_tien_da_tt ?? 0,
          bt: item.bt ?? 0,
        };
      });
      setDataChiTietKyThanhToan(tableData);
    } else {
      setDataChiTietKyThanhToan([]);
    }
  };

  //xử lý validate form
  useEffect(() => {
    formNhapKyThanhToan
      .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
      .then(() => {
        setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
      })
      .catch(() => {
        setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
      });
  }, [formNhapKyThanhToan, formValues]);

  // Hàm xử lý thay đổi giá trị input của bảng
  const handleTableInputChange = (index: number, dataIndex: string, value: any) => {
    setDataChiTietKyThanhToan(prev => {
      const newData = [...prev];
      newData[index] = {
        ...newData[index],
        [dataIndex]: dataIndex === "so_tien_da_tt" ? parseNumber(value) : value,
      };
      return newData;
    });
  };

  //Bấm Update
  const onPressUpdateKyThanhToan = async () => {
    try {
      const values: ReactQuery.IUpdateKyThanhToanParams = formNhapKyThanhToan.getFieldsValue(); //lấy ra values của form
      const cleanedValues = {
        ...values,
        ky_tt: formatDateTimeToNumber(values?.ky_tt),
        so_id: chiTietHopDong?.so_id ?? 0,
        so_tien: parseNumber(values?.so_tien) ?? 0,
        so_id_ky_ttoan: chiTietKyThanhToan?.so_id_ky_ttoan ?? 0,
        ttoan: dataChiTietKyThanhToan.map(item => ({
          bt: item.bt ?? 0,
          so_tien_da_tt: parseNumber(item.so_tien_da_tt),
          ngay_tt: item.ngay_tt ? formatDateTimeToNumber(item.ngay_tt) : 0,
          so_ct: item.so_ct || "",
        })),
      };

      const response = await updateKyThanhToan(cleanedValues);
      if (response) {
        onCloseModal();
        return;
      }
    } catch (error) {
      console.log("onConfirm", error);
    }
  };

  //bấm huỷ / để sau xoá dữ liệu của modal khi nào mở thì gọi lại
  const onCloseModal = () => {
    setIsOpen(false);
    setChiTietKyThanhToan(null);
    formNhapKyThanhToan.resetFields();
    setDataChiTietKyThanhToan([]);
  };

  const renderFormInputColum = (props?: any, span = 8) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );

  //bấm xoá 1 dòng trong bảng ngày thanh toán
  const handleDeleteRow = (key: string) => {
    if (disabled) return;
    setDataChiTietKyThanhToan(prev => {
      // Xoá phần tử có key tương ứng
      const filtered = prev.filter(item => item.key !== key);
      // Gen lại stt từ 1
      return filtered.map((item, idx) => ({
        ...item,
        stt: idx + 1,
      }));
    });
  };

  //render cột
  const renderColumn = (column: TableColumnType<TableNgayThanhToanDataType>) => {
    if (!("dataIndex" in column)) return column;
    if (["so_tien_da_tt"].includes(column.dataIndex as string)) {
      return {
        ...column,
        render: (value: any, record: TableNgayThanhToanDataType, index: number) => (
          <InputCellTable
            allowNegative={true}
            component="input-price"
            key={`${record.key}-${column.dataIndex}`}
            value={value}
            index={index}
            dataIndex={column.dataIndex as string}
            onChange={handleTableInputChange}
          />
        ),
      };
    }
    // date
    if (column.dataIndex === "ngay_tt") {
      return {
        ...column,
        render: (text: any, record: TableNgayThanhToanDataType, index: number) => (
          <FormInput
            className="!mb-0"
            component="date-picker"
            value={text && dayjs(text).isValid() ? (dayjs(text) as any) : undefined}
            onChange={(value: any) => {
              // value là dayjs object hoặc null
              const dateString = value ? dayjs(value).format("YYYY-MM-DD") : "";
              handleTableInputChange(index, "ngay_tt", dateString);
            }}
          />
        ),
      };
    }
    if (column.dataIndex === "so_ct") {
      return {
        ...column,
        render: (text: any, record: TableNgayThanhToanDataType, index: number) => (
          <FormInput
            className={"!mb-0 rounded-none !border-l-0 !border-r-0 !border-t-0 !pb-0"}
            component="input"
            value={text}
            onChange={(value: any) => {
              // Nếu value là event, lấy value.target.value, còn lại lấy trực tiếp
              const newValue = value && value.target ? value.target.value : value;
              handleTableInputChange(index, column.dataIndex as string, newValue);
            }}
          />
        ),
      };
    }
    // custom color for ten_loai
    if (column.dataIndex === "ten_loai") {
      return {
        ...column,
        render: (text: string) => {
          let color = undefined;
          if (text === "Tăng phí") color = "#52c41a"; // green
          if (text === "Hoàn phí") color = "#ff4d4f"; // red
          return <span style={color ? {color, fontWeight: 600} : {}}>{text}</span>;
        },
      };
    }
    if (column.dataIndex === "action") {
      return {
        ...column,
        render: (_: any, record: TableNgayThanhToanDataType, index: number) => (
          <Button disabled={disabled} icon={<CloseOutlined />} type="link" onClick={() => handleDeleteRow(record.key)} className="custom-link-button button_xoa"></Button>
        ),
      };
    }

    return column;
  };

  //render tổng cộng của cột
  const renderSummaryCell = (index: number, value: string) => {
    return (
      <Table.Summary.Cell index={index} className="!p-[8px] !pr-[18px]">
        <div className="text-right font-medium">{value}</div>
      </Table.Summary.Cell>
    );
  };

  // Calculate totals
  const totals = dataChiTietKyThanhToan.reduce(
    (acc, curr) => ({
      so_tien_da_tt: acc.so_tien_da_tt + parseNumber(curr.so_tien_da_tt),
    }),
    {so_tien_da_tt: 0},
  );
  //renderFooter Modal
  const renderFooter = () => {
    return (
      <Form.Item className="mb-0">
        <Button type="default" htmlType="reset" form="formNhapKyThanhToan" onClick={onCloseModal} className="mr-2" icon={<CloseOutlined />}>
          Đóng
        </Button>
        <Popcomfirm
          title="Thông báo"
          onConfirm={onPressUpdateKyThanhToan}
          htmlType="submit"
          okText="Lưu"
          description="Bạn có chắc chắn muốn lưu thông tin kỳ thanh toán không?"
          buttonTitle={"Lưu"}
          buttonDisable={disableSubmit || disabled}
          buttonIcon={<CheckOutlined />}
          loading={loading}
        />
      </Form.Item>
    );
  };
  //Render
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        className="modal-ky-thanh-toan"
        title={<HeaderModal title={"Kỳ thanh toán"} />}
        // centered
        open={isOpen}
        onOk={() => setIsOpen(false)}
        onCancel={onCloseModal}
        width={"58vw"}
        maskClosable={false}
        footer={renderFooter}>
        <Form id="formNhapKyThanhToan" onFinish={onPressUpdateKyThanhToan} form={formNhapKyThanhToan} layout="vertical">
          <Row gutter={16} className="mt-4" align="bottom">
            {renderFormInputColum({...ky_tt})}
            {renderFormInputColum({...so_tien})}
            {/* {renderFormInputColum({...so_hd_d})} */}
            <Col>
              <Button
                className="!mb-2"
                disabled={disabled}
                icon={<PlusCircleOutlined />}
                onClick={() => {
                  setDataChiTietKyThanhToan(prev => {
                    const nextStt = prev.length > 0 ? Math.max(...prev.map(item => (item as any).stt || 0)) + 1 : 1;
                    return [
                      ...prev,
                      {
                        key: `${Date.now()}-${Math.random()}`,
                        stt: nextStt,
                        ngay_tt: "",
                        so_tien_da_tt: 0,
                        bt: 0,
                        ten_loai: "",
                        so_ct: "",
                      },
                    ];
                  });
                }}>
                Thêm ngày thanh toán
              </Button>
            </Col>
          </Row>
          <div style={{height: 250}}>
            <Table<TableNgayThanhToanDataType>
              className={`no-header-border-radius no-hover-table`}
              columns={(tableNgayThanhToanColumns || []).map(renderColumn)}
              dataSource={dataChiTietKyThanhToan as TableNgayThanhToanDataType[]}
              pagination={false}
              scroll={dataChiTietKyThanhToan.length > 4 ? {y: 150} : {x: "100%"}}
              bordered
              summary={() => (
                <Table.Summary fixed="bottom">
                  <Table.Summary.Row>
                    <Table.Summary.Cell className="!p-[8px]" index={0} colSpan={(tableNgayThanhToanColumns || []).findIndex(col => "dataIndex" in col && col.dataIndex === "so_tien_da_tt")}>
                      <div className="text-center font-medium">Tổng cộng</div>
                    </Table.Summary.Cell>
                    {renderSummaryCell(1, formatCurrencyUS(totals.so_tien_da_tt))}
                    {renderSummaryCell(2, "")}
                    {renderSummaryCell(3, "")}
                  </Table.Summary.Row>
                </Table.Summary>
              )}
            />
          </div>
        </Form>
      </Modal>
    </Flex>
  );
});

ModalKyThanhToanComponent.displayName = "ModalKyThanhToanComponent";
export const ModalKyThanhToan = memo(ModalKyThanhToanComponent, isEqual);

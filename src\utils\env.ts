import {deepParse} from ".";

export const env: ImportMetaEnv = deepParse(import.meta.env);

// Utility functions để kiểm tra môi trường
export const isProduction = (): boolean => {
  return env.VITE_IS_PRODUCTION === "true" || env.VITE_APP_ENV === "production";
};

export const isDevelopment = (): boolean => {
  return env.VITE_IS_PRODUCTION === "false" || env.VITE_APP_ENV === "development";
};

export const getAppEnvironment = (): "development" | "production" => {
  return isProduction() ? "production" : "development";
};

// Helper function để log chỉ trong development
export const devLog = (...args: any[]) => {
  if (isDevelopment()) {
    console.log("[DEV]", ...args);
  }
};

// Helper function để log chỉ trong production
export const prodLog = (...args: any[]) => {
  if (isProduction()) {
    console.log("[PROD]", ...args);
  }
};

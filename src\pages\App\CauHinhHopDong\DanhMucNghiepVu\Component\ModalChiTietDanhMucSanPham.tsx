import {forwardRef, memo, useEffect, useImperativeHandle, useState} from "react";
import FormChiTietDanhMucNghiepVu, {IModalChiTietDanhMucNghiepVuRef, Props, TRANG_THAI} from "./index.configs";
import {Col, Flex, Form, Modal, Row} from "antd";
import {useDanhMucNghiepVuContext} from "../index.context";
import {isEqual} from "lodash";
import {Button, FormInput, HeaderModal} from "@src/components";
import {ArrowLeftOutlined, CheckOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
const {ma_doi_tac_ql, ma, ten, nv, stt, trang_thai} = FormChiTietDanhMucNghiepVu;

const ModalChiTietDanhMucNghiepVuComponent = forwardRef<IModalChiTietDanhMucNghiepVuRef, Props>(({listDoiTac}: Props, ref) => {
  useImperativeHandle(ref, () => ({
    open: (dataDanhMucNghiepVu?: CommonExecute.Execute.IDanhMucNghiepVu) => {
      setIsOpen(true);
      if (dataDanhMucNghiepVu) setChiTietDanhMucNghiepVu(dataDanhMucNghiepVu);
    },
    close: () => setIsOpen(false),
  }));
  const [chiTietDanhMucNghiepVu, setChiTietDanhMucNghiepVu] = useState<CommonExecute.Execute.IDanhMucNghiepVu | null>(null);
  const [isOpen, setIsOpen] = useState(false);

  const {loading, onUpdateDanhMucNghiepVu, filterParams, setFilterParams, danhSachNghiepVuPhanTrang} = useDanhMucNghiepVuContext();

  const [form] = Form.useForm();
  const formValues = Form.useWatch([], form);
  useEffect(() => {
    if (chiTietDanhMucNghiepVu) {
      const arrFormData = [];
      for (const key in chiTietDanhMucNghiepVu) {
        arrFormData.push({
          name: key as keyof CommonExecute.Execute.IDanhMucNghiepVu,
          value: chiTietDanhMucNghiepVu[key as keyof CommonExecute.Execute.IDanhMucNghiepVu],
        });
      }
      form.setFields(arrFormData);
    }
  }, [chiTietDanhMucNghiepVu, form]);
  useEffect(() => {
    form
      .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
      .then(() => {
        setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
      })
      .catch(() => {
        setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
      });
  }, [form, formValues]);
  const closeModal = () => {
    setIsOpen(false);
    setChiTietDanhMucNghiepVu(null);
    form.resetFields();
    setFilterParams(filterParams);
  };

  const renderFormInputColum = (props?: any, span = 12) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  const onConfirm = async () => {
    try {
      const values: ReactQuery.IUpdateDanhMucNghiepVuParams = form.getFieldsValue(); //lấy ra values của form

      const response = await onUpdateDanhMucNghiepVu(values);
      console.log("check respon ", response);
      if (response === -1) {
        console.log("cập nhật thành công");
        setIsOpen(false);
      } else {
        console.log("cập nhật thất bại");
      }
    } catch (error) {
      console.log("onConfirm", error);
    }
  };
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  //render footer
  const renderFooter = () => {
    return (
      <Form.Item>
        <Button type="default" onClick={() => setIsOpen(false)} className="mr-2" icon={<ArrowLeftOutlined />}>
          Quay lại
        </Button>
        <Button type="primary" disabled={disableSubmit} iconPosition="end" onClick={() => onConfirm()} className="mr-2" icon={<CheckOutlined />}>
          Lưu
        </Button>
        {/* <Popcomfirm
          title="Thông báo"
          onConfirm={onConfirm}
          okText="Lưu"
          description="Bạn có chắc muốn lưu thông tin?"
          buttonTitle="Lưu"
          buttonDisable={disableSubmit}
          buttonIcon={<CheckOutlined />}
          iconPosition="end"
          loading={loading}
        /> */}
      </Form.Item>
    );
  };
  const renderForm = () => (
    <Form form={form} layout="vertical" initialValues={{trang_thai: TRANG_THAI[0].ma}}>
      {/* MÃ */}
      <Row gutter={16}>
        {renderFormInputColum({...ma, disabled: chiTietDanhMucNghiepVu ? true : false})}
        {renderFormInputColum({...ten})}
        {renderFormInputColum({...trang_thai, options: TRANG_THAI})}
        {renderFormInputColum({...stt})}
      </Row>
    </Form>
  );
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        title={
          <HeaderModal
            title={chiTietDanhMucNghiepVu ? `Chi tiết ngiệp vụ ${chiTietDanhMucNghiepVu.ten}` : "Tạo mới nghiệp vụ"}
            trang_thai_ten={chiTietDanhMucNghiepVu?.trang_thai_ten}
            trang_thai={chiTietDanhMucNghiepVu?.trang_thai}
          />
        }
        maskClosable={false}
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width={{
          xs: "50%",
          sm: "50%",
          md: "50%",
          lg: "50%",
          xl: "50%",
          xxl: "50%",
        }}
        footer={renderFooter}
        className="[&_.ant-space]:w-full">
        {renderForm()}
      </Modal>
    </Flex>
  );
});
ModalChiTietDanhMucNghiepVuComponent.displayName = "ModalChiTietDanhMucNghiepVuComponent";
export const ModalChiTietDanhMucNghiepVu = memo(ModalChiTietDanhMucNghiepVuComponent, isEqual);

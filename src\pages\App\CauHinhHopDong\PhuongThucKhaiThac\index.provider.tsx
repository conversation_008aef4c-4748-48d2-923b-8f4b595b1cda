import {ReactQuery} from "@src/@types";
import {ACTION_CODE} from "@src/constants";
import {CommonExecuteResponse, useCommonExecute} from "@src/services/react-queries";

import React, {PropsWithChildren, useCallback, useEffect, useMemo, useState} from "react";
import {defaultFormValue} from "./index.configs";
import {PhuongThucKhaiThacContext} from "./index.context";
import {PhuongThucKhaiThacProps} from "./index.model";
import {message} from "antd";

/* 
  file Provider : Chứa logic quản lý state, gọi API, wrap context
*/

const PhuongthuckhaithacProvider: React.FC<PropsWithChildren> = props => {
  const {children} = props;
  const mutateUseCommonExecute = useCommonExecute();
  const [danhSachPhuongThucKhaiThac, setDanhSachPhuongThucKhaiThac] = useState<Array<CommonExecute.Execute.IDanhSachPhuongThucKhaiThac>>([]);
  const [tongSoDong, setTongSoDong] = useState<number>(0);
  const [listDoiTac, setListDoiTac] = useState<Array<CommonExecute.Execute.IDoiTac>>([]);

  useEffect(() => {
    initData();
  }, []);

  const initData = () => {
    getListDoiTac();
    LayDanhSachPhuongThuckhaiThac(defaultFormValue);
  };

  //TÌM KIẾM PHÂN TRANG PHƯƠNG THỨC KHAI THÁC
  const LayDanhSachPhuongThuckhaiThac = useCallback(
    async (body: ReactQuery.ITimKiemPhanTrangPhuongThucKhaiThacParams & ReactQuery.IPhanTrang) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_PHUONG_THUC_KHAI_THAC,
        };
        console.log("params lấy ptkt", params);
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        console.log("response lấy response ptkt", response);
        const data = response.data.data;
        setDanhSachPhuongThucKhaiThac(data);
        setTongSoDong(response.data.tong_so_dong);
      } catch (error: any) {
        console.log("LayDanhSachPhuongThuckhaiThac error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );
  /* ĐỐI TÁC */
  const getListDoiTac = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        actionCode: ACTION_CODE.LIET_KE_DANH_SACH_DOI_TAC,
      });

      setListDoiTac(response?.data?.map(item => ({...item, ten: item.ma + " - " + item.ten})));
    } catch (error) {
      console.log("getListDoiTac error ", error);
    }
  }, [mutateUseCommonExecute]);
  //LẤY CHI TIẾT PHƯƠNG THỨC KHAI THÁC
  const layChiTietPhuongThucKhaiThac = useCallback(
    async (item: ReactQuery.IlayChiTietPhuongThucKhaiThacParams): Promise<CommonExecute.Execute.IChiTietPhuongThucKhaiThac | null> => {
      try {
        const params = {
          ma: item.ma,
          ma_doi_tac_ql: item.ma_doi_tac_ql,
          actionCode: ACTION_CODE.CHI_TIET_PHUONG_THUC_KHAI_THAC,
        };
        console.log("params sua ham chi tiet", params);
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        console.log("responseData", responseData);
        // setChiTietHopDongBaoHiemXe(responseData.data)
        return responseData.data as CommonExecute.Execute.IChiTietChucNang;
      } catch (error: any) {
        console.log("layChiTietChucNang error ", error.message || error);
        return null;
      }
    },
    [mutateUseCommonExecute],
  );
  // UPDATE PHƯƠNG THỨC KHAI THÁC
  const updatePhuongThucKhaiThac = useCallback(async (item: ReactQuery.IUpdatePhuongThucKhaiThacParams) => {
    try {
      const params = {
        ma_doi_tac_ql: item.ma_doi_tac_ql,
        ma: item.ma,
        ten: item.ten,
        stt: item.stt,
        trang_thai: item.trang_thai,
        actionCode: ACTION_CODE.UPDATE_PHƯƠNG_THUC_KHAI_THAC,
      };
      console.log("params update ptkt", params);
      const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
      if (responseData && (responseData.data as unknown as number) === -1) {
        message.success("Cập nhật thông tin thành công!");
        initData();
        //chuyển đổi responseData thành number
        return responseData as unknown as number;
      }
      console.log("responseData", responseData);
      //set updatephuongthuckhaithac(responseData.data)
      return responseData.data;
    } catch (error: any) {
      console.log("updatephuongThucKhaiThac", error.message || error);
      return null;
    }
  }, []);

  //khai báo giá trị của context để truyền vào Component
  const value = useMemo<PhuongThucKhaiThacProps>(
    () => ({
      listDoiTac,

      getListDoiTac,

      danhSachPhuongThucKhaiThac,
      loading: mutateUseCommonExecute.isLoading,
      tongSoDong,
      LayDanhSachPhuongThuckhaiThac,
      layChiTietPhuongThucKhaiThac,
      defaultFormValue,
      updatePhuongThucKhaiThac,
    }),
    [danhSachPhuongThucKhaiThac, mutateUseCommonExecute, tongSoDong, listDoiTac, LayDanhSachPhuongThuckhaiThac, layChiTietPhuongThucKhaiThac, updatePhuongThucKhaiThac, getListDoiTac],
  );

  return <PhuongThucKhaiThacContext.Provider value={value}>{children}</PhuongThucKhaiThacContext.Provider>;
};

export default PhuongthuckhaithacProvider;

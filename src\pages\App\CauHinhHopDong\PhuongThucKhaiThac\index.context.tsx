import {createContext, useContext} from "react";
import {PhuongThucKhaiThacProps} from "./index.model";
//createContext : khai báo 1 context mà component có thể cung cấp hoặc đọc
export const PhuongThucKhaiThacContext = createContext<PhuongThucKhaiThacProps>({
  listDoiTac: [],
  //khởi tạo các giá trị mặc định, trong context này có giá trị là onSubmit
  danhSachPhuongThucKhaiThac: [],
  loading: false,
  LayDanhSachPhuongThuckhaiThac: () => Promise.resolve(),
  tongSoDong: 0,
  layChiTietPhuongThucKhaiThac: () => Promise.resolve(null),
  defaultFormValue: {},
  updatePhuongThucKhaiThac: () => Promise.resolve(null),
  getListDoiTac: () => Promise.resolve(),
});

//useContext(someContext): là React Hook cho phép bạn đọc và subscribe context từ component của mình.
// file .context muốn truy câp context thì sử dụng useLoginContext : const {onSubmit} = useLoginContext();
export const useChiTietPhuongThucKTContext = () => useContext(PhuongThucKhaiThacContext);

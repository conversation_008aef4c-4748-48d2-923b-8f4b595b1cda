import {create} from "zustand";

export interface IListDaiLyStore {
  listDaiLy: Array<CommonExecute.Execute.IDanhMucDaiLy>;
  setListDaiLy: (listDaiLy: Array<CommonExecute.Execute.IDanhMucDaiLy>) => void;
}

export const useDaiLy = create<IListDaiLyStore>(set => ({
  listDaiLy: [],
  setListDaiLy: listDaiLy => set({listDaiLy}),
}));

//create : hàm tạo store
// export const useDaiLy = create(
//   //persist : middileware để lưu trạng thái vào localStorage
//   persist<IListDaiLyStore, [], [], Pick<IListDaiLyStore, "listDaiLy">>(
//     //set, get phải đúng thứ tự này, nếu không sẽ báo lỗi vì sử dụng nhầm cách get/set
//     (set, get) => ({
//       //khởi tạo state menuNguoiDung từ cookie + localStorage
//       listDaiLy: get()?.listDaiLy || [],
//       setListDaiLy: (listDaiLy: Array<CommonExecute.Execute.IDanhMucDaiLy>) => set(state => ({listDaiLy: [...listDaiLy]})),
//     }),
//     //cấu hình persist
//     {
//       name: LOCAL_STORAGE_KEY.DOI_TAC, //key để lưu trong localStorate
//       storage: createJSONStorage(() => localStorage), //nơi lưu trữ là localStorage
//       //chỉ định những phần nào trong state sẽ được lưu, tránh lưu những thứ k cần thiết như các hàm set
//       partialize: state => ({listDaiLy: state.listDaiLy}),
//     },
//   ),
// );

import {createContext, useContext} from "react";

import {IQuanLyDanhMucNhomMaBenhContextProps} from "./index.model";

//khởi tạo giá trị mặc định
export const QuanLyDanhMucNhomMaBenhContext = createContext<IQuanLyDanhMucNhomMaBenhContextProps>({
  listDanhMucNhomMaBenh: [],
  tongSoDong: 0,
  loading: false,
  filterParams: {},
  getListDanhMucNhomMaBenh: async () => Promise.resolve(),
  getChiTietDanhMucNhomMaBenh: async () => Promise.resolve({} as CommonExecute.Execute.IDanhMucNhomMaBenh),
  capNhatChiTietDanhMucNhomMaBenh: async () => Promise.resolve(),
  setFilterParams: () => {},
});

export const useQuanLyDanhMucNhomMaBenhContext = () => useContext(QuanLyDanhMucNhomMaBenhContext);
import {IFormInput, ReactQuery} from "@src/@types";
import {defaultTableColumnsProps} from "@src/hooks";
import {TableProps} from "antd";

//form tìm kiếm
export interface IFormTimKiemDanhMucNganHangFieldsConfig {
  // ma_doi_tac_ql: IFormInput;
  ma: IFormInput;
  ten: IFormInput;
  // nv: IFormInput;
  trang_thai: IFormInput;
}
export const FormTimKiemDanhMucNganHang: IFormTimKiemDanhMucNganHangFieldsConfig = {
  // ma_doi_tac_ql: {
  //   component: "select",
  //   name: "ma_doi_tac_ql",
  //   label: "Đối tác",
  //   placeholder: "Chọn đối tác",
  // },
  ma: {
    component: "input",
    name: "ma",
    label: "Mã ngân hàng",
    placeholder: "Mã ngân hàng",
  },
  ten: {
    component: "input",
    name: "ten",
    label: "Tên ngân hàng",
    placeholder: "Tên ngân hàng",
  },
  // nv: {
  //   component: "select",
  //   name: "nv",
  //   label: "Nghiệp vụ",
  //   placeholder: "Chọn nghiệp vụ",
  // },
  trang_thai: {
    component: "select",
    name: "trang_thai",
    label: "Trạng thái",
    placeholder: "Chọn trạng thái",
  },
};

export interface TableDanhMucNganHangDataType {
  key: string;
  stt?: number;
  ma?: string;
  ten?: string;
  ngay_tao?: string;
  nguoi_tao?: string;
  ngay_cap_nhat?: string;
  nguoi_cap_nhat?: string;
  trang_thai_ten?: string;
  trang_thai?: string;
}

export const danhMucNganHangColumns: TableProps<TableDanhMucNganHangDataType>["columns"] = [
  {title: "STT", dataIndex: "stt", key: "stt", width: 70, align: "center", ...defaultTableColumnsProps},
  {title: "Mã", dataIndex: "ma", key: "ma", width: 100, align: "center", ...defaultTableColumnsProps},
  {title: "Tên", dataIndex: "ten", key: "ten", width: 300, align: "left", ...defaultTableColumnsProps},
  {title: "Ngày tạo", dataIndex: "ngay_tao", key: "ngay_tao", width: 110, align: "center", ...defaultTableColumnsProps},
  {title: "Người tạo", dataIndex: "nguoi_tao", key: "nguoi_tao", width: 110, align: "center", ...defaultTableColumnsProps},
  {title: "Ngày cập nhật", dataIndex: "ngay_cap_nhat", key: "ngay_cap_nhat", width: 120, align: "center", ...defaultTableColumnsProps},
  {title: "Người cập nhật", dataIndex: "nguoi_cap_nhat", key: "nguoi_cap_nhat", width: 120, align: "center", ...defaultTableColumnsProps},
  {title: "Trạng thái", dataIndex: "trang_thai_ten", key: "trang_thai_ten", width: 130, align: "center", ...defaultTableColumnsProps},
];
export const defaultFormValue: ReactQuery.ITimKiemPhanTrangDanhMucNganHangParams = {
  ma: "",
  ten: "",
  trang_thai: "",
  // trang: 1,
  // so_dong: 20,
  actionCode: "",
};
export const radioItemTrangThaiNganHangTable = [
  {value: "Đang sử dụng", text: "Đang sử dụng"},
  {value: "Ngừng sử dụng", text: "Ngừng sử dụng"},
];
export const TRANG_THAI_TK_NGAN_HANG = [
  {ten: "Đang sử dụng", ma: "D"},
  {ten: "Ngừng sử dụng", ma: "K"},
];
export type DataIndex = keyof TableDanhMucNganHangDataType;

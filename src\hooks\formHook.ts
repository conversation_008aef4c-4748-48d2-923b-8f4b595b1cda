import {REGUlAR_EXPRESSION} from "@src/constants";
import {FormRule} from "antd";

export const ruleInputMessage = {
  required: {
    required: true,
    message: "Thông tin bắt buộc",
  },
  email: {
    pattern: REGUlAR_EXPRESSION.REG_EMAIL,
    message: "Email sai định dạng",
  },
  phone: {
    pattern: REGUlAR_EXPRESSION.REG_PHONE,
    message: "Số điện thoại sai định dạng",
  },
};

/**
 * VALIDATION RULES CHO DANH MỤC CHI NHÁNH NGÂN HÀNG
 */
export const validationRules = {
  /** Rules cho mã chi nhánh */
  ma: [
    {required: true, message: "Vui lòng nhập mã chi nhánh!"},
    {max: 50, message: "Mã chi nhánh không được vượt quá 50 ký tự!"},
  ] as FormRule[],

  /** Rules cho tên chi nhánh */
  ten: [
    {required: true, message: "Vui lòng nhập tên chi nhánh!"},
    {max: 200, message: "Tên chi nhánh không được vượt quá 200 ký tự!"},
  ] as FormRule[],

  /** Rules cho mã ngân hàng */
  ma_ngan_hang: [{required: true, message: "Vui lòng chọn ngân hàng!"}] as FormRule[],

  /** Rules cho trạng thái */
  trang_thai: [{required: true, message: "Vui lòng chọn trạng thái!"}] as FormRule[],

  /** Rules cho số thứ tự */
  stt: [
    {required: true, message: "Vui lòng nhập số thứ tự!"},
    {
      validator: (_: any, value: string) => {
        if (!value) return Promise.resolve();
        const num = Number(value);
        if (isNaN(num)) {
          return Promise.reject(new Error("Số thứ tự phải là một số!"));
        }
        if (num <= 0) {
          return Promise.reject(new Error("Số thứ tự phải lớn hơn 0!"));
        }
        // if (num > 999) {
        //   return Promise.reject(new Error("Số thứ tự không được vượt quá 999!"));
        // }
        if (!Number.isInteger(num)) {
          return Promise.reject(new Error("Số thứ tự phải là số nguyên!"));
        }
        return Promise.resolve();
      },
    },
  ] as FormRule[],
};

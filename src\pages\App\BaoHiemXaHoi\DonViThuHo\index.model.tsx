import {ReactQuery} from "@src/@types";
import {TableDonViThuHoColumnDataType} from "./index.configs";

export interface IQuanLyDonViThuHoContextProps {
  listDonViThuHo: Array<CommonExecute.Execute.IDonViThuHo>;
  tongSoDong: number;
  loading: boolean;
  filterParams: ReactQuery.ITimKiemPhanTrangDonViThuHoParams & ReactQuery.IPhanTrang;
  listNganHang: Array<CommonExecute.Execute.IDanhMucNganHang>;
  getListDonViThuHo: (params?: ReactQuery.ITimKiemPhanTrangDonViThuHoParams & ReactQuery.IPhanTrang) => Promise<void>;
  getChiTietDonViThuHo: (params: TableDonViThuHoColumnDataType) => Promise<CommonExecute.Execute.IDonViThuHo>;
  capNhatChiTietDonViThuHo: (params: ReactQuery.ICapNhatDonViThuHoParams, isEditMode?: boolean) => Promise<any>;
  setFilterParams: React.Dispatch<React.SetStateAction<ReactQuery.ITimKiemPhanTrangDonViThuHoParams & ReactQuery.IPhanTrang>>;
}

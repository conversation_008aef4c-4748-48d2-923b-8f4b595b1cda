import {DanhMucHieuXeProps} from "./index.model";
import {useContext, createContext} from "react";

export const DanhMucHieuXeContext = createContext<DanhMucHieuXeProps>({
  listHangXe: [],
  danhSachDanhMucHieuXe: [],
  loading: false,
  layDanhSachDanhMucHieuXe: params => {},
  tongSoDong: 0,
  layChiTietDanhMucHieuXe: () => Promise.resolve(null),
  defaultFormValue: {},
  onUpdateDanhMucHieuXe: () => Promise.resolve(null),
  getListHangXe: () => Promise.resolve(),
});
export const useChiTietDanhMucHieuXeContext = () => useContext(DanhMucHieuXeContext);

import {StarFilled, StarOutlined} from "@ant-design/icons";
import {IFormInput, ReactQuery} from "@src/@types";
import {colWidthByKey, defaultTableColumnsProps} from "@src/hooks";
import {TableProps} from "antd";
import dayjs from "dayjs";
//ĐỊNH NGHĨA CẤU HÌNH TABLE ĐỐI TÁC GỒM NHỮNG CỘT NÀO, THỨ TỰ CỘT SẼ HIỂN THỊ THEO THỨ TỰ KHAI BÁO Ở DƯỚI
export interface TableHopDongColumnDataType {
  key: number;
  sott?: number; //CỘT 1
  so_hd_d?: string; // số hd đầu,
  ten_doi_tac_ql?: string;
  ten_chi_nhanh_ql?: string;
  ngay_cap?: string;
  vip?: string;
  kieu_hd_ten?: string;
  ngay_hl?: string;
  ngay_kt?: string;
  sl_dtuong?: number;
  trang_thai_ten?: string;
  tong_phi?: string | number;
  ten_kh?: string;
  ngay_tao?: string;
  nguoi_tao?: string;
  ngay_cap_nhat?: string;
  nguoi_cap_nhat?: string;
  // tham số bổ sung để gọi API
  so_hd?: string;
  so_hd_g?: string;
  ma_doi_tac?: string;
  so_id: number;
  ma_doi_tac_ql?: string;
}

// ĐỊNH NGHĨA Column RENDER RA TỪNG Ô TRONG CỘT
//đọc https://ant.design/components/table#column để biết thêm về column
export const tableHopDongColumn: TableProps<TableHopDongColumnDataType>["columns"] = [
  {...defaultTableColumnsProps, title: "STT", dataIndex: "sott", key: "sott", width: colWidthByKey.sott, align: "center"},
  {
    ...defaultTableColumnsProps,
    title: "VIP",
    dataIndex: "vip",
    key: "vip",
    width: 50,
    align: "center",
    render: (text: any, record: any) => {
      if (record.key.toString().includes("empty")) return null;
      if (text) return <StarFilled style={{color: "orange"}} />;
      return <StarOutlined style={{color: "#bfbfbf"}} />;
    },
  },
  {...defaultTableColumnsProps, title: "Ngày cấp", dataIndex: "ngay_cap", key: "ngay_cap", width: 90, align: "center"},
  {...defaultTableColumnsProps, title: "Số hợp đồng", dataIndex: "so_hd", key: "so_hd", width: 220, align: "center"},
  {...defaultTableColumnsProps, title: "Kiểu HĐ", dataIndex: "kieu_hd_ten", key: "kieu_hd_ten", width: 90, align: "center"},
  {...defaultTableColumnsProps, title: "Đối tác cấp đơn", dataIndex: "ten_doi_tac_ql", key: "ten_doi_tac_ql", width: 150, align: "center"},
  {...defaultTableColumnsProps, title: "Đơn vị cấp", dataIndex: "ten_chi_nhanh_ql", key: "ten_chi_nhanh_ql", width: 150, align: "center"},
  {...defaultTableColumnsProps, title: "Trạng thái", dataIndex: "trang_thai_ten", key: "trang_thai_ten", width: 130, align: "center"},
  {...defaultTableColumnsProps, title: "Tên khách", dataIndex: "ten_kh", key: "ten_kh", align: "center", width: 250},
  {...defaultTableColumnsProps, title: "Ngày HL", dataIndex: "ngay_hl", key: "ngay_hl", width: 90, align: "center"},
  {...defaultTableColumnsProps, title: "Ngày KT", dataIndex: "ngay_kt", key: "ngay_kt", width: 90, align: "center"},
  {...defaultTableColumnsProps, title: "SL đối tượng", dataIndex: "sl_dtuong", key: "sl_dtuong", align: "center", width: 140},
  {...defaultTableColumnsProps, title: "Tổng phí", dataIndex: "tong_phi", key: "tong_phi", width: 100, align: "right"},
  {...defaultTableColumnsProps, title: "Ngày tạo", dataIndex: "ngay_tao", key: "ngay_tao", width: colWidthByKey.ngay_tao, align: "center"},
  {...defaultTableColumnsProps, title: "Người tạo", dataIndex: "nguoi_tao", key: "nguoi_tao", width: colWidthByKey.nguoi_tao, align: "center"},
  {...defaultTableColumnsProps, title: "Ngày cập nhật", dataIndex: "ngay_cap_nhat", key: "ngay_cap_nhat", width: colWidthByKey.ngay_cap_nhat, align: "center"},
  {...defaultTableColumnsProps, title: "Người cập nhật", dataIndex: "nguoi_cap_nhat", key: "nguoi_cap_nhat", width: colWidthByKey.nguoi_cap_nhat, align: "center"},
  // {title: "Số hợp đồng đầu", dataIndex: "so_hd_d", key: "so_hd_d", width: 150, align: "center"},
  // {title: "Số hợp đồng SĐBS", dataIndex: "so_hd_g", key: "so_hd_g", width: 160, align: "center"},
];

//keyof: return ra key của inteface TableHopDongColumnDataType;
export type TableHopDongColumnDataIndex = keyof TableHopDongColumnDataType;

//radio trong table
export const radioItemTrangThaiHopDongTable = [
  {value: "Chưa duyệt", text: "Chưa duyệt"},
  {value: "Đã duyệt", text: "Đã duyệt"},
];

//form tìm kiếm
//radio trong search
export const listTrangThaiHopDongSelect: Array<{ma: string; ten: string}> = [
  {ma: "C", ten: "Chưa duyệt"},
  {ma: "D", ten: "Đã duyệt"},
];
export interface IFormTimKiemHopDongFieldsConfig {
  tu_ngay: IFormInput;
  den_ngay: IFormInput;
  ma_doi_tac_ql: IFormInput;
  ma_chi_nhanh_ql: IFormInput;
  trang_thai: IFormInput;
  nd_tim: IFormInput;
  so_hd: IFormInput;
  gcn: IFormInput;
  ten_ndbh: IFormInput;
  cmt_ndbh: IFormInput;
  dthoai_ndbh: IFormInput;
}
export const FormTimKiemQuanLyHopDong: IFormTimKiemHopDongFieldsConfig = {
  tu_ngay: {component: "date-picker", name: "tu_ngay", label: "Từ ngày", className: "block", allowClear: false},
  den_ngay: {component: "date-picker", name: "den_ngay", label: "Đến ngày", className: "block", allowClear: false},
  ma_doi_tac_ql: {component: "select", name: "ma_doi_tac_ql", label: "Đối tác", placeholder: "Chọn đối tác"},
  ma_chi_nhanh_ql: {component: "select", name: "ma_chi_nhanh_ql", label: "Đơn vị", placeholder: "Chọn đơn vị"},
  trang_thai: {component: "select", name: "trang_thai", label: "Trạng thái", placeholder: "Chọn trạng thái"},
  nd_tim: {component: "input", name: "nd_tim", label: "Nội dung tìm", placeholder: "Nhập nội dung tìm"},
  so_hd: {component: "input", name: "so_hd", placeholder: "Nhập số hợp đồng"},
  gcn: {component: "input", name: "gcn", placeholder: "Nhập giấy chứng nhận"},
  ten_ndbh: {component: "input", name: "ten_ndbh", placeholder: "Nhập tên NĐBH"},
  cmt_ndbh: {component: "input", name: "cmt_ndbh", placeholder: "Nhập CMT NĐBH"},
  dthoai_ndbh: {component: "input", name: "dthoai_ndbh", placeholder: "Nhập Điện thoại NĐBH"},
};

export const defaultParamsTimKiemPhanTrangHopDong: ReactQuery.ILayDanhSachHopDongConNguoiPhanTrangParams = {
  so_hd: "",
  ten_ndbh: "",
  tu_ngay: +dayjs().startOf("year"),
  den_ngay: +dayjs(),
  ma_doi_tac_ql: "",
  ma_chi_nhanh_ql: "",
  nv: "NG",
  trang: 1,
  so_dong: 20,
};

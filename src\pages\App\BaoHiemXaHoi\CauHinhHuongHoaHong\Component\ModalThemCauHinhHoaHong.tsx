import {forwardRef, memo, useEffect, useImperativeHandle, useState} from "react";
import {FormThemCauHinhHuongHoaHong, IModalThemCauHinhHoaHongRef, LOAI_HOP_DONG, PropsThem, SAN_PHAM} from "./index.configs";
import {Col, Flex, Form, Modal, Row} from "antd";
import {useCauHinhHuongHoaHongContext} from "../index.context";
import {isEqual} from "lodash";
import {Button, FormInput, HeaderModal} from "@src/components";
import {ArrowLeftOutlined, CheckOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
const {ma_sp, loai_hd, loai_ho_gia_dinh, tlhh, so_thang_tu, so_thang_toi} = FormThemCauHinhHuongHoaHong;

const ModalThemCauHinhHoaHongComponent = forwardRef<IModalThemCauHinhHoaHongRef, PropsThem>(({selectedNgayApDung}: PropsThem, ref) => {
  useImperativeHandle(ref, () => ({
    open: (dataCauHinhHuongHoaHong?: CommonExecute.Execute.ICauHinhHuongHoaHong) => {
      setIsOpen(true);
      if (dataCauHinhHuongHoaHong) setThemCauHinhHoaHong(dataCauHinhHuongHoaHong);
    },
    close: () => setIsOpen(false),
  }));
  const [ThemCauHinhHoaHong, setThemCauHinhHoaHong] = useState<CommonExecute.Execute.ICauHinhHuongHoaHong | null>(null);
  const [isOpen, setIsOpen] = useState(false);

  const {loading, capNhatCauHinhHuongHoaHong, filterParams, setFilterParams, listLoaiHoGiaDinh, chiTietTaiKhoanDonViThuHo, layChiTietCauHinhHuongHoaHong, layDanhSachCauHinhHuongHoaHong} =
    useCauHinhHuongHoaHongContext();

  const [form] = Form.useForm();
  const formValues = Form.useWatch([], form);
  useEffect(() => {
    if (ThemCauHinhHoaHong) {
      const arrFormData = [];
      for (const key in ThemCauHinhHoaHong) {
        arrFormData.push({
          name: key as keyof CommonExecute.Execute.ICauHinhHuongHoaHong,
          value: ThemCauHinhHoaHong[key as keyof CommonExecute.Execute.ICauHinhHuongHoaHong],
        });
      }
      form.setFields(arrFormData);
    }
  }, [ThemCauHinhHoaHong, form]);
  useEffect(() => {
    form
      .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
      .then(() => {
        setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
      })
      .catch(() => {
        setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
      });
  }, [form, formValues]);
  const closeModal = () => {
    setIsOpen(false);
    setThemCauHinhHoaHong(null);
    form.resetFields();
    setFilterParams(filterParams);
    layDanhSachCauHinhHuongHoaHong({bt_ad: Number(selectedNgayApDung)});
  };

  const renderFormInputColum = (props?: any, span = 8) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  const onConfirm = async () => {
    try {
      const values: ReactQuery.IUpdateCauHinhHuongHoaHongParams = form.getFieldsValue(); //lấy ra values của form

      const params = {
        ...values,
        ma_dvi: chiTietTaiKhoanDonViThuHo?.ma_dvi,
        bt: ThemCauHinhHoaHong?.bt || 0,
        so_thang_tu: Number(values.so_thang_tu) || 0,
        so_thang_toi: Number(values.so_thang_toi) || 0,
        tlhh: Number(values.tlhh) || 0,
        bt_ad: selectedNgayApDung || 0,
      };
      const response = await capNhatCauHinhHuongHoaHong(params);
      console.log("check respon ", response);
      if (response == true) {
        console.log("cập nhật thành công");
        closeModal();
      } else {
        console.log("cập nhật thất bại");
      }
    } catch (error) {
      console.log("onConfirm", error);
    }
  };
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  //render footer
  const renderFooter = () => {
    return (
      <Form.Item>
        <Button type="default" onClick={() => setIsOpen(false)} className="mr-2" icon={<ArrowLeftOutlined />}>
          Quay lại
        </Button>
        <Button type="primary" disabled={disableSubmit} iconPosition="end" onClick={() => onConfirm()} className="mr-2" icon={<CheckOutlined />}>
          Lưu
        </Button>
      </Form.Item>
    );
  };
  const renderForm = () => (
    <Form form={form} layout="vertical">
      {/* MÃ */}
      <Row gutter={16}>
        {renderFormInputColum({...ma_sp, options: SAN_PHAM, disabled: ThemCauHinhHoaHong ? true : false})}
        {renderFormInputColum({...loai_hd, options: LOAI_HOP_DONG, disabled: ThemCauHinhHoaHong ? true : false})}
        {renderFormInputColum({...loai_ho_gia_dinh, options: listLoaiHoGiaDinh, disabled: ThemCauHinhHoaHong ? true : false})}
      </Row>
      <Row gutter={16}>
        {renderFormInputColum(so_thang_tu)}
        {renderFormInputColum(so_thang_toi)}
        {renderFormInputColum(tlhh)}
      </Row>
    </Form>
  );
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        title={<HeaderModal title={ThemCauHinhHoaHong ? "Chi tiết cấu hình" : "Thêm mới cấu hình"} trang_thai={ThemCauHinhHoaHong?.trang_thai} />}
        maskClosable={false}
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width={{
          xs: "55%",
          sm: "55%",
          md: "55%",
          lg: "55%",
          xl: "55%",
          xxl: "55%",
        }}
        style={{top: 130}}
        footer={renderFooter}
        className="[&_.ant-space]:w-full">
        {renderForm()}
      </Modal>
    </Flex>
  );
});
ModalThemCauHinhHoaHongComponent.displayName = "ModalThemCauHinhHoaHongComponent";
export const ModalThemCauHinhHoaHong = memo(ModalThemCauHinhHoaHongComponent, isEqual);

import React from "react";
import {CaretUpOutlined, CaretDownOutlined, MinusOutlined} from "@ant-design/icons";

interface TrendIndicatorProps {
  trend?: "up" | "down" | "neutral";
  value?: number;
  size?: "small" | "default";
}

const TrendIndicator: React.FC<TrendIndicatorProps> = ({trend = "neutral", value, size = "default"}) => {
  const getIcon = () => {
    switch (trend) {
      case "up":
        return <CaretUpOutlined />;
      case "down":
        return <CaretDownOutlined />;
      default:
        return <MinusOutlined />;
    }
  };

  const getColor = () => {
    switch (trend) {
      case "up":
        return "#52c41a";
      case "down":
        return "#ff4d4f";
      default:
        return "#d9d9d9";
    }
  };

  const getBackgroundColor = () => {
    switch (trend) {
      case "up":
        return "#f6ffed";
      case "down":
        return "#fff2f0";
      default:
        return "#fafafa";
    }
  };

  const iconSize = size === "small" ? 10 : 12;
  const containerSize = size === "small" ? 20 : 24;

  return (
    <div
      style={{
        display: "inline-flex",
        alignItems: "center",
        justifyContent: "center",
        gap: 2,
        padding: "2px 6px",
        borderRadius: "12px",
        backgroundColor: getBackgroundColor(),
        color: getColor(),
        fontSize: iconSize,
        marginLeft: 8,
      }}>
      {getIcon()}
      {value && <span style={{fontSize: 10, fontWeight: 500}}>{value}</span>}
    </div>
  );
};

export default TrendIndicator;

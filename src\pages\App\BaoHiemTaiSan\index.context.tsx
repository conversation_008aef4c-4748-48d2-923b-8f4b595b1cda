import {createContext, useContext} from "react";
import {BaoHiemTaiSanContextProps} from "./index.model";
//createContext : khai báo 1 context mà component có thể cung cấp hoặc đọc
export const BaoHiemTaiSanContext = createContext<BaoHiemTaiSanContextProps>({
  //khởi tạo các giá trị mặc định, trong context này có giá trị là onSubmit
  danhSachHopDongBaoHiemTaiSan: [],
  danhSachKhachHang: [],
  loading: false,
  tongSoDong: 0,
  defaultFormValue: {},
  listDoiTac: [],
  listSanPham: [],
  listChiNhanh: [],
  listPhongBan: [],
  listPhuongThucKhaiThac: [],
  listChuongTrinhBaoHiem: [],
  listDonViBoiThuong: [],
  filterParams: {
    ma_doi_tac_ql: "",
    ma_chi_nhanh_ql: "",
    ten: "",
    ma_sp: "",
    nd: "",
    ngay_d: "",
    ngay_c: "",
    trang_thai: "",
    trang: 1,
    so_dong: 13,
    nv: "TS",
  },
  tongSoDongCanBoQuanLy: 0,
  tongSoDongDataLDaiLy: 0,
  tongSoDongDataKhachHang: 0,
  listCanBo: [],
  // listDaiLyKhaiThac: [],
  // listKhachHang: [],
  danhSachDaiLy: [],
  danhSachDoiTuongBaoHiemTaiSan: [],
  tongSoDongDoiTuongBaoHiemTaiSan: 0,
  tongPhiBaoHiemTaiSanFromAPI: 0,
  listNhomDoiTuong: [],
  chiTietDoiTuongBaoHiemTaiSan: null,
  listTinhThanh: [],
  listPhuongXa: [],
  listDongBaoHiemHopDongTaiSan: [],
  listTaiBaoHiemHopDongTaiSan: [],
  listThongTinThanhToanHopDongTaiSan: [],
  listDonViDongTai: [],
  danhSachTaiSan: [],
  tongSoDongDanhSachTaiSan: 0,
  chiTietDanhSachTaiSan: null,
  danhSachDoiTuong: [],
  tongSoDongDoiTuong: 0,
  chiTietDoiTuong: {} as CommonExecute.Execute.IDoiTuong,
  setChiTietDoiTuong: () => {},
  layChiTietDoiTuong: () => Promise.resolve(null),
  updateDoiTuong: () => Promise.resolve(null),
  timKiemPhanTrangDanhSachDoiTuong: () => {},
  timKiemPhanTrangDanhSachTaiSan: () => {},
  layChiTietDanhSachTaiSan: () => Promise.resolve(null),
  capNhatDanhSachTaiSan: () => Promise.resolve(null),
  setListPhuongXa: () => {},
  getListPhuongXa: () => Promise.resolve(),
  layChiTietDoiTuongBaoHiemTaiSan: () => Promise.resolve(null),
  updateDoiTuongBaoHiemTaiSan: () => Promise.resolve(false),
  timKiemPhanTrangDoiTuongBaoHiemTaiSan: () => Promise.resolve({data: [], tong_so_dong: 0}),
  timKiemPhanTrangKhachHang: () => Promise.resolve({data: [], tong_so_dong: 0}),
  timKiemPhanTrangDaiLy: () => Promise.resolve({data: [], tong_so_dong: 0}),

  getListCanBoQuanLy: () => Promise.resolve({data: [], tong_so_dong: 0}),
  getListDaiLyKhaiThac: params => Promise.resolve({data: []}),
  layChiTietHopDongBaoHiemTaiSan: () => Promise.resolve(null),
  setFilterParams: () => {},
  timKiemPhanTrangHopDongBaoHiemTaiSan: () => Promise.resolve({data: [], tong_so_dong: 0}),
  chiTietHopDongBaoHiemTaiSan: null,
  updateHopDongBaoHiemTaiSan: () => Promise.resolve({success: false, isNewContract: false}),
  resetChiTietHopDongBaoHiemTaiSan: () => {},
  huyHopDongTaiSan: () => Promise.resolve(false),
  goHuyHopDongTaiSan: () => Promise.resolve(false),
  taoHopDongSuaDoiBoSung: () => Promise.resolve(null),
  getDanhSachFileThumbnailTheoDoiTuong: () => Promise.resolve(),
  uploadFileTheoDoiTuong: () => Promise.resolve(false),
  deleteFileTheoDoiTuong: () => Promise.resolve(false),
  phanLoaiFileTheoHangMucXe: () => Promise.resolve(false),
  layDanhSachCauHinhDongCuaHopDongBaoHiemTaiSan: () => Promise.resolve(),
  layDanhSachCauHinhTaiBHCuaHopDongBaoHiemTaiSan: () => Promise.resolve(),
  layThongTinThanhToanCuaHopDongBaoHiemTaiSan: () => Promise.resolve(),
  layChiTietThongTinCauHinhDongBH: () => Promise.resolve(null),
  xoaThongTinDongBH: () => Promise.resolve(false),
  lietKeDanhSachCacDoiTuongDaDuocApDungDongBH: () => Promise.resolve(),
  updateDoiTuongApDungTyLeDongBH: () => Promise.resolve(false),
  updateCauHinhDongBaoHiem: () => Promise.resolve(false),
  layChiTietThongTinCauHinhTaiBH: () => Promise.resolve(null),
  xoaThongTinTaiBH: () => Promise.resolve(false),
  updateCauHinhTaiBaoHiem: () => Promise.resolve(false),
  lietKeDanhSachCacDoiTuongDaDuocApDungTaiBH: () => Promise.resolve(),
  updateDoiTuongApDungTaiBH: () => Promise.resolve(false),
  layChiTietKyThanhToan: () => Promise.resolve(null),
  updateKyThanhToan: () => Promise.resolve(false),
  exportPdfHopDong: () => Promise.resolve(false),
  setChiTietDanhSachTaiSan: () => {},
});

//useContext(someContext): là React Hook cho phép bạn đọc và subscribe context từ component của mình.
// file .context muốn truy câp context thì sử dụng useLoginContext : const {onSubmit} = useLoginContext();
export const useBaoHiemTaiSanContext = () => useContext(BaoHiemTaiSanContext);

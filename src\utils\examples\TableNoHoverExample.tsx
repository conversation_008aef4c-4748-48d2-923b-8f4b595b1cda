/**
 * Example component demonstrating how to use tableNoHoverUtils
 * Các ví dụ sử dụng tableNoHoverUtils trong các trường hợp khác nhau
 */

import React, { useState } from "react";
import { Table, Button, Space, Card, Typography } from "antd";
import {
  useTableNoHover,
  injectTableNoHoverCSS,
  getTableClassName,
  getTableRowClassName,
  TABLE_NO_HOVER_PRESETS,
  removeTableNoHoverCSS,
} from "../tableNoHoverUtils";

const { Title, Text } = Typography;

// Sample data cho examples
const sampleData = [
  { id: 1, name: "<PERSON><PERSON><PERSON><PERSON>n <PERSON>", email: "<EMAIL>", status: "active" },
  { id: 2, name: "Trần Thị B", email: "<EMAIL>", status: "inactive" },
  { id: 3, name: "Lê Văn C", email: "<EMAIL>", status: "active" },
  { id: 4, name: "<PERSON><PERSON><PERSON>ị <PERSON>", email: "<EMAIL>", status: "pending" },
];

const columns = [
  { title: "ID", dataIndex: "id", key: "id" },
  { title: "Name", dataIndex: "name", key: "name" },
  { title: "Email", dataIndex: "email", key: "email" },
  { title: "Status", dataIndex: "status", key: "status" },
];

// ==================== EXAMPLE 1: Sử dụng React Hook (Recommended) ====================

export const Example1_ReactHook = () => {
  const [selectedId, setSelectedId] = useState<number | null>(null);

  // Sử dụng useTableNoHover hook - cách đơn giản nhất
  const { getTableClassName, getRowClassName, isInjected } = useTableNoHover({
    activeRowColor: "#96bf49", // Màu xanh lá
    styleId: "example-1-styles",
  });

  return (
    <Card title="Example 1: Sử dụng React Hook (Recommended)" style={{ marginBottom: 16 }}>
      <Text>CSS Injected: {isInjected ? "✅ Success" : "❌ Failed"}</Text>
      <Table
        dataSource={sampleData}
        columns={columns}
        rowKey="id"
        className={getTableClassName("bordered")}
        rowClassName={record => getRowClassName(selectedId === record.id)}
        onRow={record => ({
          onClick: () => setSelectedId(record.id),
        })}
        pagination={false}
      />
      <Space style={{ marginTop: 8 }}>
        <Text>Selected ID: {selectedId || "None"}</Text>
        <Button size="small" onClick={() => setSelectedId(null)}>
          Clear Selection
        </Button>
      </Space>
    </Card>
  );
};

// ==================== EXAMPLE 2: Sử dụng Manual Injection ====================

export const Example2_ManualInjection = () => {
  const [selectedId, setSelectedId] = useState<number | null>(null);

  // Manual injection với preset màu
  React.useEffect(() => {
    const result = injectTableNoHoverCSS({
      ...TABLE_NO_HOVER_PRESETS.BLUE,
      styleId: "example-2-styles",
    });
    console.log("Example 2 CSS injection:", result);

    // Cleanup khi component unmount
    return () => {
      removeTableNoHoverCSS("example-2-styles");
    };
  }, []);

  return (
    <Card title="Example 2: Manual Injection với Preset BLUE" style={{ marginBottom: 16 }}>
      <Table
        dataSource={sampleData}
        columns={columns}
        rowKey="id"
        className={getTableClassName("bordered", { styleId: "example-2-styles" })}
        rowClassName={record => 
          getTableRowClassName(selectedId === record.id, { styleId: "example-2-styles" })
        }
        onRow={record => ({
          onClick: () => setSelectedId(record.id),
        })}
        pagination={false}
      />
      <Space style={{ marginTop: 8 }}>
        <Text>Selected ID: {selectedId || "None"}</Text>
        <Button size="small" onClick={() => setSelectedId(null)}>
          Clear Selection
        </Button>
      </Space>
    </Card>
  );
};

// ==================== EXAMPLE 3: Multiple Tables với màu khác nhau ====================

export const Example3_MultipleTables = () => {
  const [selectedId1, setSelectedId1] = useState<number | null>(null);
  const [selectedId2, setSelectedId2] = useState<number | null>(null);

  // Table 1 - Màu đỏ
  const table1 = useTableNoHover({
    activeRowColor: "#ff4d4f",
    styleId: "table-1-red",
  });

  // Table 2 - Màu tím
  const table2 = useTableNoHover({
    activeRowColor: "#722ed1",
    styleId: "table-2-purple",
  });

  return (
    <Card title="Example 3: Multiple Tables với màu khác nhau" style={{ marginBottom: 16 }}>
      <div style={{ display: "flex", gap: 16 }}>
        <div style={{ flex: 1 }}>
          <Title level={5}>Table 1 - Red Active Row</Title>
          <Table
            dataSource={sampleData.slice(0, 2)}
            columns={columns}
            rowKey="id"
            className={table1.getTableClassName("bordered")}
            rowClassName={record => table1.getRowClassName(selectedId1 === record.id)}
            onRow={record => ({
              onClick: () => setSelectedId1(record.id),
            })}
            pagination={false}
            size="small"
          />
          <Text>Selected: {selectedId1 || "None"}</Text>
        </div>

        <div style={{ flex: 1 }}>
          <Title level={5}>Table 2 - Purple Active Row</Title>
          <Table
            dataSource={sampleData.slice(2, 4)}
            columns={columns}
            rowKey="id"
            className={table2.getTableClassName("bordered")}
            rowClassName={record => table2.getRowClassName(selectedId2 === record.id)}
            onRow={record => ({
              onClick: () => setSelectedId2(record.id),
            })}
            pagination={false}
            size="small"
          />
          <Text>Selected: {selectedId2 || "None"}</Text>
        </div>
      </div>
    </Card>
  );
};

// ==================== EXAMPLE 4: Dynamic Color Change ====================

export const Example4_DynamicColor = () => {
  const [selectedId, setSelectedId] = useState<number | null>(null);
  const [currentPreset, setCurrentPreset] = useState<keyof typeof TABLE_NO_HOVER_PRESETS>("GREEN");

  // Sử dụng preset động
  const { getTableClassName, getRowClassName } = useTableNoHover({
    ...TABLE_NO_HOVER_PRESETS[currentPreset],
    styleId: `dynamic-${currentPreset.toLowerCase()}`,
  });

  const presetOptions = Object.keys(TABLE_NO_HOVER_PRESETS) as Array<keyof typeof TABLE_NO_HOVER_PRESETS>;

  return (
    <Card title="Example 4: Dynamic Color Change" style={{ marginBottom: 16 }}>
      <Space style={{ marginBottom: 16 }}>
        <Text>Active Row Color:</Text>
        {presetOptions.map(preset => (
          <Button
            key={preset}
            size="small"
            type={currentPreset === preset ? "primary" : "default"}
            onClick={() => setCurrentPreset(preset)}
            style={{
              backgroundColor: currentPreset === preset ? undefined : TABLE_NO_HOVER_PRESETS[preset].activeRowColor,
              color: currentPreset === preset ? undefined : "#fff",
            }}
          >
            {preset}
          </Button>
        ))}
      </Space>

      <Table
        dataSource={sampleData}
        columns={columns}
        rowKey="id"
        className={getTableClassName("bordered")}
        rowClassName={record => getRowClassName(selectedId === record.id)}
        onRow={record => ({
          onClick: () => setSelectedId(record.id),
        })}
        pagination={false}
      />

      <Space style={{ marginTop: 8 }}>
        <Text>Selected ID: {selectedId || "None"}</Text>
        <Text>Current Color: {TABLE_NO_HOVER_PRESETS[currentPreset].activeRowColor}</Text>
      </Space>
    </Card>
  );
};

// ==================== MAIN DEMO COMPONENT ====================

export const TableNoHoverDemo = () => {
  return (
    <div style={{ padding: 24 }}>
      <Title level={2}>Table No Hover Utils - Examples</Title>
      <Text>
        Các ví dụ sử dụng tableNoHoverUtils để loại bỏ hover effect cho Ant Design Table
        nhưng vẫn giữ màu cho row active.
      </Text>

      <div style={{ marginTop: 24 }}>
        <Example1_ReactHook />
        <Example2_ManualInjection />
        <Example3_MultipleTables />
        <Example4_DynamicColor />
      </div>

      <Card title="Usage Summary" style={{ marginTop: 16 }}>
        <ul>
          <li><strong>Example 1:</strong> Cách đơn giản nhất với useTableNoHover hook</li>
          <li><strong>Example 2:</strong> Manual injection với cleanup</li>
          <li><strong>Example 3:</strong> Multiple tables với màu khác nhau</li>
          <li><strong>Example 4:</strong> Dynamic color change với presets</li>
        </ul>
      </Card>
    </div>
  );
};

export default TableNoHoverDemo;

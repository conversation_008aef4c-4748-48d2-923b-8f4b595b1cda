import {ReactQuery} from "@src/@types";
import {TableNhomHangMucXeColumnDataType} from "./index.configs";

/**
 * Interface định nghĩa các props và methods mà Provider cung cấp cho các component con
 * thông qua Context API
 */
export interface INhomHangMucXeProvider {

  listNhomHangMucXe: Array<CommonExecute.Execute.INhomHangMucXe>; 
  tongSoDong: number; 
  loading: boolean; 
  
  filterParams: ReactQuery.ITimKiemPhanTrangNhomHangMucXeParams; // Tham số tìm kiếm và phân trang
  /**
    Tìm kiếm danh sách nhóm hạng mục xe với phân trang
   */
  getListNhomHangMucXe: () => Promise<void>;
  /**
   * Lấy chi tiết thông tin nhóm hạng mục xe
   */
  getChiTietNhomHangMucXe: (record: TableNhomHangMucXeColumnDataType) => Promise<CommonExecute.Execute.INhomHangMucXe>;
  /**
    Cập nhật thông tin nhóm hạng mục xe (tạo mới hoặc chỉnh sửa)
  */
  capNhatChiTietNhomHangMucXe: (params: ReactQuery.ICapNhatNhomHangMucXeParams) => Promise<any>;
  /**
    Cập nhật tham số filter và trigger re-fetch data
   */
  setFilterParams: (params: ReactQuery.ITimKiemPhanTrangNhomHangMucXeParams & ReactQuery.IPhanTrang) => void;
}

/**
 * Export alias để tương thích với các file đang import INhomHangMucXeContextProps
 */
export type INhomHangMucXeContextProps = INhomHangMucXeProvider;
// * @param record - Bản ghi được chọn từ table
// * @returns Promise với dữ liệu chi tiết hoặc null
// * @param params - Dữ liệu cần cập nhật
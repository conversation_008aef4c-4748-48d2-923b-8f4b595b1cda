import {COLOR_PALETTE} from "@src/constants";
import {AliasToken} from "antd/es/theme/internal";

export const LIGHT_THEME_CONFIGS: Partial<AliasToken> = {
  colorPrimary: COLOR_PALETTE.green[70],
  colorError: COLOR_PALETTE.red[40],
  colorInfo: COLOR_PALETTE.blue[40],
  colorWarning: COLOR_PALETTE.yellow[40],
};

export const DARK_THEME_CONFIGS: Partial<AliasToken> = {
  colorPrimary: COLOR_PALETTE.pink[40],
  colorError: COLOR_PALETTE.red[40],
  colorInfo: COLOR_PALETTE.blue[40],
  colorWarning: COLOR_PALETTE.yellow[40],
};

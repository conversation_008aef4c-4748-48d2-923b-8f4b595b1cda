import React from 'react';
import ReactApex<PERSON>hart from 'react-apexcharts';
import { ApexOptions } from 'apexcharts';

interface BarLineChartProps {
  data: {
    labels: string[];
    barValues: number[];
    lineValues: number[];
  };
  height?: number;
  title?: string;
}

const BarLineChart: React.FC<BarLineChartProps> = ({ 
  data, 
  height = 320,
  title 
}) => {
  const options: ApexOptions = {
    chart: {
      type: 'line',
      height: height,
      toolbar: {
        show: false,
      },
      zoom: {
        enabled: false,
      },
    },
    title: title ? {
      text: title,
      align: 'left',
      style: {
        fontSize: '16px',
        fontWeight: '600',
        color: '#262626',
      },
    } : undefined,
    stroke: {
      width: [0, 3],
      curve: 'smooth',
    },
    plotOptions: {
      bar: {
        columnWidth: '50%',
        borderRadius: 4,
      },
    },
    fill: {
      opacity: [0.85, 1],
    },
    labels: data.labels,
    markers: {
      size: 0,
    },
    xaxis: {
      type: 'category',
      labels: {
        style: {
          colors: '#8c8c8c',
          fontSize: '12px',
        },
      },
      axisBorder: {
        show: false,
      },
      axisTicks: {
        show: false,
      },
    },
    yaxis: [
      {
        title: {
          text: 'Doanh thu (VND)',
          style: {
            color: '#8c8c8c',
            fontSize: '12px',
          },
        },
        labels: {
          style: {
            colors: '#8c8c8c',
            fontSize: '12px',
          },
          formatter: (value: number) => {
            return new Intl.NumberFormat('vi-VN', {
              notation: 'compact',
              compactDisplay: 'short',
            }).format(value);
          },
        },
      },
      {
        opposite: true,
        title: {
          text: 'Tăng trưởng (%)',
          style: {
            color: '#8c8c8c',
            fontSize: '12px',
          },
        },
        labels: {
          style: {
            colors: '#8c8c8c',
            fontSize: '12px',
          },
          formatter: (value: number) => `${value}%`,
        },
      },
    ],
    tooltip: {
      shared: true,
      intersect: false,
      y: [
        {
          formatter: (value: number) => {
            return new Intl.NumberFormat('vi-VN').format(value) + 'đ';
          },
        },
        {
          formatter: (value: number) => `${value}%`,
        },
      ],
    },
    legend: {
      position: 'top',
      horizontalAlign: 'right',
      offsetY: -10,
      markers: {
        width: 8,
        height: 8,
        radius: 2,
      },
    },
    grid: {
      borderColor: '#f0f0f0',
      strokeDashArray: 3,
      xaxis: {
        lines: {
          show: false,
        },
      },
      yaxis: {
        lines: {
          show: true,
        },
      },
    },
    colors: ['#52c41a', '#faad14'],
    dataLabels: {
      enabled: false,
    },
  };

  const series = [
    {
      name: 'Doanh thu',
      type: 'column',
      data: data.barValues,
    },
    {
      name: 'Tăng trưởng',
      type: 'line',
      data: data.lineValues,
    },
  ];

  return (
    <div style={{ width: '100%', height: '100%' }}>
      <ReactApexChart
        options={options}
        series={series}
        type="line"
        height={height}
      />
    </div>
  );
};

export default BarLineChart;

import "@react-pdf-viewer/core/lib/styles/index.css";
import {isEqual} from "lodash";
import {forwardRef, memo, useImperativeHandle, useMemo, useRef} from "react";
import {useHopDongConNguoiContext} from "@src/pages/App/BaoHiemConNguoi/HopDongConNguoi/index.context";
import {IThongTinTaiBaoHiemStep4Ref, ThongTinTaiBaoHiemStep4Props, TableCauHinhTaiBHDataType, tableCauHinhTaiBHColumn, IModalDoiTuongApDungTaiBHRef} from "./Constant";
import {IModalCauHinhTaiBHRef} from "./PheDuyetHopDongStep4_ModalNhapCauHinhTai";
import {Button, Popcomfirm} from "@src/components";
import {Table, TableColumnType} from "antd";
import {formatCurrencyUS} from "@src/utils";
import {CloseOutlined, PlusCircleOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {defaultTableProps, fillRowTableEmpty} from "@src/hooks";
import {ModalNhapCauHinhTai} from "./PheDuyetHopDongStep4_ModalNhapCauHinhTai";
import {ModalDoiTuongApDungTaiBH} from "./PheDuyetHopDongStep4_ModalDoiTuongApDungTaiBH";

const pageSize = 13;
const ThongTinTaiBaoHiemStep4Component = forwardRef<IThongTinTaiBaoHiemStep4Ref, ThongTinTaiBaoHiemStep4Props>(({}: ThongTinTaiBaoHiemStep4Props, ref) => {
  useImperativeHandle(ref, () => ({}));

  const {loading, listTaiBaoHiemHopDongConNguoi, layChiTietThongTinCauHinhTaiBH, xoaThongTinTaiBH, lietKeDanhSachCacDoiTuongDaDuocApDungTaiBH} = useHopDongConNguoiContext();

  const refModalCauHinhTai = useRef<IModalCauHinhTaiBHRef>(null);
  const refModalDoiTuongApDungTaiBH = useRef<IModalDoiTuongApDungTaiBHRef>(null);

  //bấm xoá 1 dòng trong bảng ngày thanh toán
  const handleDeleteRow = async (ma_dvi_tai: string) => {
    await xoaThongTinTaiBH({ma_dvi_tai});
  };

  // Tính tổng tiền nhượng đồng và tiền giữ lại
  const totals = useMemo(() => {
    return listTaiBaoHiemHopDongConNguoi.reduce(
      (acc, curr: any) => ({
        tien_nhuong_tai: acc.tien_nhuong_tai + (curr.tien_nhuong_tai || 0),
        tien_con: acc.tien_con + (curr.tien_con || 0),
      }),
      {tien_nhuong_tai: 0, tien_con: 0},
    );
  }, [listTaiBaoHiemHopDongConNguoi]);

  //DATA TABLE CẤU HÌNH ĐỒNG
  const dataTableListTaiBaoHiem = useMemo<Array<TableCauHinhTaiBHDataType>>(() => {
    try {
      const tableData = listTaiBaoHiemHopDongConNguoi.map((item, index) => {
        return {
          ...item,
          key: index.toString(),
          sott: item.sott || index + 1,
        };
      });
      const arrEmptyRow: Array<TableCauHinhTaiBHDataType> = fillRowTableEmpty(tableData.length, pageSize);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      console.log("dataTableListTaiBaoHiem error", error);
      return [];
    }
  }, [listTaiBaoHiemHopDongConNguoi]);

  const renderColumn = (column: TableColumnType<TableCauHinhTaiBHDataType>) => {
    if (!("dataIndex" in column)) return column;

    if (column.dataIndex === "action") {
      return {
        ...column,
        render: (_: any, record: TableCauHinhTaiBHDataType, index: number) =>
          record.key && !record.key.toString().startsWith("empty") ? (
            <span onClick={e => e.stopPropagation()}>
              <Popcomfirm
                title="Thông báo"
                onConfirm={() => record.ma_dvi_tai && handleDeleteRow(record.ma_dvi_tai)}
                htmlType="button"
                okText="Đồng ý"
                description="Bạn có chắc chắn muốn xoá thông tin cấu hình tái BH không?"
                buttonTitle={""}
                buttonColor="red"
                variant="text"
                className="h-auto"
                icon={<CloseOutlined />}
              />
            </span>
          ) : null,
      };
    }
    if (column.dataIndex === "sl_dt_bh") {
      return {
        ...column,
        render: (text: any, record: TableCauHinhTaiBHDataType, index: number) => {
          if (typeof record.sl_dt_bh === "number" && record.key && !record.key.toString().startsWith("empty")) {
            if (record.sl_dt_bh > 0) {
              return (
                <Button
                  className="h-auto"
                  type="link"
                  onClick={async e => {
                    e.stopPropagation();
                    const listDoiTuongDaApDungTaiBH = await lietKeDanhSachCacDoiTuongDaDuocApDungTaiBH(record as ReactQuery.IChiTietThongTinCauHinhDongBHParams);
                    refModalDoiTuongApDungTaiBH.current?.open(record as CommonExecute.Execute.ITaiBaoHiem, listDoiTuongDaApDungTaiBH);
                  }}
                  style={{padding: 0}}>
                  {`Có ${record.sl_dt_bh} đối tượng áp dụng`}
                </Button>
              );
            } else if (record.sl_dt_bh <= 0) {
              return (
                <Button
                  className="h-auto"
                  type="link"
                  onClick={async e => {
                    e.stopPropagation();
                    const listDoiTuongDaApDungDongBH = await lietKeDanhSachCacDoiTuongDaDuocApDungTaiBH(record as ReactQuery.IChiTietThongTinCauHinhDongBHParams);
                    console.log("listDoiTuongDaApDungTaiBH", listDoiTuongDaApDungDongBH);
                    refModalDoiTuongApDungTaiBH.current?.open(record as CommonExecute.Execute.ITaiBaoHiem, listDoiTuongDaApDungDongBH);
                  }}
                  style={{padding: 0}}>
                  Áp dụng tất cả
                </Button>
              );
            }
          }
          return text;
        },
      };
    }
    return column;
  };

  //render tổng cộng của cột
  const renderSummaryCell = (index: number, value: string) => {
    return (
      <Table.Summary.Cell index={index} className="!p-[8px] !pr-[24px]">
        <div className="text-right font-medium">{value}</div>
      </Table.Summary.Cell>
    );
  };

  //render bảng thông tin đồng
  const renderTable = () => (
    <div>
      <Table<TableCauHinhTaiBHDataType>
        {...defaultTableProps}
        className={`no-header-border-radius table-thong-tin-tai-bao-hiem mt-3`}
        dataSource={dataTableListTaiBaoHiem} //mảng dữ liệu record được hiển thị
        columns={
          (tableCauHinhTaiBHColumn || []).map(item => {
            return renderColumn(item);
          }) || []
        } //định nghĩa cột của table
        loading={loading} //hiển thị loading khi đang gọi API để loading data
        sticky
        onRow={record => {
          return {
            style: {cursor: loading ? "progress" : "pointer"}, //  thể hiện vị trí con trỏ là button(👈)
            onClick: async () => {
              if (!record.ma_dvi_tai) return;
              const response = await layChiTietThongTinCauHinhTaiBH(record as ReactQuery.IChiTietThongTinCauHinhDongBHParams);
              if (response) refModalCauHinhTai.current?.open(response);
            },
          };
        }}
        pagination={false}
        scroll={dataTableListTaiBaoHiem.length > pageSize ? {x: "max-content", y: 350} : {x: "max-content"}}
        summary={() => (
          <Table.Summary fixed>
            <Table.Summary.Row>
              <Table.Summary.Cell className="!p-[8px]" index={0} colSpan={5}>
                <div className="text-center font-medium">Tổng cộng</div>
              </Table.Summary.Cell>
              {renderSummaryCell(1, formatCurrencyUS(totals.tien_nhuong_tai))}
              {/* {renderSummaryCell(7, formatCurrencyUS(totals.tien_con))} */}
              <Table.Summary.Cell index={3} className="!p-[8px]" colSpan={2}>
                {/* <div className="text-center">
                  <Button type="link" onClick={() => refModalCauHinhTai.current?.open()} icon={<PlusCircleOutlined />} className="!h-auto !p-0">
                    Thêm tái BH
                  </Button>
                </div> */}
              </Table.Summary.Cell>
            </Table.Summary.Row>
          </Table.Summary>
        )}
      />
    </div>
  );

  return (
    <>
      {renderTable()}
      <ModalNhapCauHinhTai ref={refModalCauHinhTai} />
      <ModalDoiTuongApDungTaiBH ref={refModalDoiTuongApDungTaiBH} />
    </>
  );
});

ThongTinTaiBaoHiemStep4Component.displayName = "ThongTinTaiBaoHiemStep4Component";
export const ThongTinTaiBaoHiemStep4 = memo(ThongTinTaiBaoHiemStep4Component, isEqual);

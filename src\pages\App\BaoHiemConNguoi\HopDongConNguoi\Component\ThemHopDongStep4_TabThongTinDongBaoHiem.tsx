import "@react-pdf-viewer/core/lib/styles/index.css";
import {isEqual} from "lodash";
import {forwardRef, memo, useImperativeHandle, useMemo, useRef} from "react";
import {useHopDongConNguoiContext} from "../index.context";
import {IThongTinDongBaoHiemStep4Ref, ThongTinDongBaoHiemStep4Props, TableCauHinhDongBaoHiemDataType, tableCauHinhDongBaoHiemColumn, IModalCauHinhDongBHRef, IModalDoiTuongApDungRef} from "./Constant";
import {Button, Popcomfirm} from "@src/components";
import {Table, TableColumnType} from "antd";
import {formatCurrencyUS} from "@src/utils";
import {CloseOutlined, PlusCircleOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {defaultTableProps, fillRowTableEmpty} from "@src/hooks";
import {ModalNhapCauHinhDong} from "./ThemHopDongStep4_ModalNhapCauHinhDong";
import {ModalDoiTuongApDungDongBH} from "./ThemHopDongStep4_ModalDoiTuongApDungDongBH";

const pageSize = 15;
const ThongTinDongBaoHiemStep4Component = forwardRef<IThongTinDongBaoHiemStep4Ref, ThongTinDongBaoHiemStep4Props>(({}: ThongTinDongBaoHiemStep4Props, ref) => {
  useImperativeHandle(ref, () => ({}));

  const {loading, listDongBaoHiemHopDongConNguoi, layChiTietThongTinCauHinhDongBH, xoaThongTinDongBH, lietKeDanhSachCacDoiTuongDaDuocApDungDongBH} = useHopDongConNguoiContext();

  const refModalCauHinhDong = useRef<IModalCauHinhDongBHRef>(null);
  const refModalDoiTuongApDungDongBH = useRef<IModalDoiTuongApDungRef>(null);

  //bấm xoá 1 dòng trong bảng ngày thanh toán
  const handleDeleteRow = async (key: string, ma_dvi_dong: string) => {
    await xoaThongTinDongBH({ma_dvi_dong});
  };

  // Tính tổng tiền nhượng đồng và tiền giữ lại
  const totals = useMemo(() => {
    return listDongBaoHiemHopDongConNguoi.reduce(
      (acc, curr: any) => ({
        tien_nhuong_dong: acc.tien_nhuong_dong + (curr.tien_nhuong_dong || 0),
        tien_con: acc.tien_con + (curr.tien_con || 0),
      }),
      {tien_nhuong_dong: 0, tien_con: 0},
    );
  }, [listDongBaoHiemHopDongConNguoi]);

  //DATA TABLE CẤU HÌNH ĐỒNG
  const dataTableListDongBaoHiem = useMemo<Array<TableCauHinhDongBaoHiemDataType>>(() => {
    try {
      const tableData = listDongBaoHiemHopDongConNguoi.map((item, index) => {
        return {
          ...item,
          key: index.toString(),
          sott: item.sott || index + 1,
        };
      });
      const arrEmptyRow: Array<TableCauHinhDongBaoHiemDataType> = fillRowTableEmpty(tableData.length, pageSize);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      console.log("dataTableListDongBaoHiem error", error);
      return [];
    }
  }, [listDongBaoHiemHopDongConNguoi]);

  const renderColumn = (column: TableColumnType<TableCauHinhDongBaoHiemDataType>) => {
    if (!("dataIndex" in column)) return column;

    if (column.dataIndex === "action") {
      return {
        ...column,
        render: (_: any, record: TableCauHinhDongBaoHiemDataType, index: number) =>
          record.key && !record.key.toString().startsWith("empty") ? (
            <span onClick={e => e.stopPropagation()}>
              <Popcomfirm
                title="Thông báo"
                onConfirm={() => record.ma_dvi_dong && handleDeleteRow(record.key, record.ma_dvi_dong)}
                htmlType="button"
                okText="Đồng ý"
                description="Bạn có chắc chắn muốn xoá thông tin cấu hình đồng BH không?"
                buttonTitle={""}
                buttonColor="red"
                variant="text"
                className="h-auto"
                icon={<CloseOutlined />}
              />
            </span>
          ) : null,
      };
    }
    if (column.dataIndex === "sl_dt_bh") {
      return {
        ...column,
        render: (text: any, record: TableCauHinhDongBaoHiemDataType, index: number) => {
          if (typeof record.sl_dt_bh === "number" && record.key && !record.key.toString().startsWith("empty")) {
            if (record.sl_dt_bh > 0) {
              return (
                <Button
                  className="h-auto"
                  type="link"
                  onClick={async e => {
                    e.stopPropagation();
                    const listDoiTuongDaApDungDongBH = await lietKeDanhSachCacDoiTuongDaDuocApDungDongBH(record as ReactQuery.IChiTietThongTinCauHinhDongBHParams);
                    refModalDoiTuongApDungDongBH.current?.open(record as CommonExecute.Execute.IDongBaoHiem, listDoiTuongDaApDungDongBH);
                  }}
                  style={{padding: 0}}>
                  {`Có ${record.sl_dt_bh} đối tượng áp dụng`}
                </Button>
              );
            } else if (record.sl_dt_bh <= 0) {
              return (
                <Button
                  className="h-auto"
                  type="link"
                  onClick={async e => {
                    e.stopPropagation();
                    const listDoiTuongDaApDungDongBH = await lietKeDanhSachCacDoiTuongDaDuocApDungDongBH(record as ReactQuery.IChiTietThongTinCauHinhDongBHParams);
                    console.log("listDoiTuongDaApDungDongBH", listDoiTuongDaApDungDongBH);
                    refModalDoiTuongApDungDongBH.current?.open(record as CommonExecute.Execute.IDongBaoHiem, listDoiTuongDaApDungDongBH);
                  }}
                  style={{padding: 0}}>
                  Áp dụng tất cả
                </Button>
              );
            }
          }
          return text;
        },
      };
    }
    return column;
  };

  //render tổng cộng của cột
  const renderSummaryCell = (index: number, value: string) => {
    return (
      <Table.Summary.Cell index={index} className="!p-[8px] !pr-[24px]">
        <div className="text-right font-medium">{value}</div>
      </Table.Summary.Cell>
    );
  };

  //render bảng thông tin đồng
  const renderTable = () => (
    <div>
      <Table<TableCauHinhDongBaoHiemDataType>
        {...defaultTableProps}
        className={`no-header-border-radius table-thong-tin-dong-bao-hiem mt-3`}
        dataSource={dataTableListDongBaoHiem} //mảng dữ liệu record được hiển thị
        columns={
          (tableCauHinhDongBaoHiemColumn || []).map(item => {
            return renderColumn(item);
          }) || []
        } //định nghĩa cột của table
        loading={loading} //hiển thị loading khi đang gọi API để loading data
        sticky
        onRow={record => {
          return {
            style: {cursor: loading ? "progress" : "pointer"}, //  thể hiện vị trí con trỏ là button(👈)
            onClick: async () => {
              if (!record.ma_dvi_dong) return;
              const response = await layChiTietThongTinCauHinhDongBH(record as ReactQuery.IChiTietThongTinCauHinhDongBHParams);
              if (response) refModalCauHinhDong.current?.open(response);
            },
          };
        }}
        title={() => <></>}
        pagination={false}
        scroll={dataTableListDongBaoHiem.length > pageSize ? {x: "max-content", y: 350} : {x: "max-content"}}
        summary={() => (
          <Table.Summary fixed>
            <Table.Summary.Row>
              <Table.Summary.Cell className="!p-[8px]" index={0} colSpan={6}>
                <div className="text-center font-medium">Tổng cộng</div>
              </Table.Summary.Cell>
              {renderSummaryCell(6, formatCurrencyUS(totals.tien_nhuong_dong))}
              {renderSummaryCell(7, formatCurrencyUS(totals.tien_con))}
              <Table.Summary.Cell index={8} className="!p-[8px]" colSpan={2}>
                <div className="text-center">
                  <Button type="link" onClick={() => refModalCauHinhDong.current?.open()} icon={<PlusCircleOutlined />} className="!h-auto !p-0">
                    Thêm đồng BH
                  </Button>
                </div>
              </Table.Summary.Cell>
            </Table.Summary.Row>
          </Table.Summary>
        )}
      />
    </div>
  );

  return (
    <>
      {renderTable()}
      <ModalNhapCauHinhDong ref={refModalCauHinhDong} />
      <ModalDoiTuongApDungDongBH ref={refModalDoiTuongApDungDongBH} />
    </>
  );
});

ThongTinDongBaoHiemStep4Component.displayName = "ThongTinDongBaoHiemStep4Component";
export const ThongTinDongBaoHiemStep4 = memo(ThongTinDongBaoHiemStep4Component, isEqual);

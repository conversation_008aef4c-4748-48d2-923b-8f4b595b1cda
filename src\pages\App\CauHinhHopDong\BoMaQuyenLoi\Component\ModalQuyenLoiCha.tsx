import {forwardRef, memo, useCallback, useImperative<PERSON>andle, useMemo, useRef, useState} from "react";
import {FormTimKiemPhanTrangBoMaQuyenLoi, radioItemTrangThaiBoMaQuyenLoiTable, TRANG_THAI_BO_MA_QUYEN_LOI} from "../index.configs";
import {isEqual} from "lodash";
import {Col, Flex, Form, Input, InputRef, Modal, Row, Table, TableColumnType, Tag, Tooltip} from "antd";
import {ReactQuery} from "@src/@types";

import {Button, FormInput, Highlighter} from "@src/components";
import {CheckCircleOutlined, ClearOutlined, SearchOutlined} from "@ant-design/icons";
import {FilterDropdownProps} from "antd/es/table/interface";
import {COLOR_PALETTE} from "@src/constants";

import {BoMaQuyenLoiChaColumns, TableBoMaQuyenLoiChaDataType, TableQuyenLoiChaColumnDataIndex} from "./index.configs";
const {ma_doi_tac_ql, nd_tim, ma_sp} = FormTimKiemPhanTrangBoMaQuyenLoi;
import "../index.default.scss";
import {useBoMaQuyenLoiContext} from "../index.context";
import {defaultPaginationTableProps, defaultTableProps, fillRowTableEmpty} from "@src/hooks";
interface Props {
  onSelectQuyenLoiCha: (ma_ct: CommonExecute.Execute.IDanhSachBoMaQuyenLoi | null) => void;
  chiTietQuyenLoi: CommonExecute.Execute.IChiTietBoMaQuyenLoi | null;
}
export interface IModalTimQuyenLoiChaRef {
  open: (data?: CommonExecute.Execute.IDanhSachBoMaQuyenLoi) => void;
  close: () => void;
}
type DataIndexQLCha = keyof TableBoMaQuyenLoiChaDataType;

const ModalTimQuyenLoiChaComponent = forwardRef<IModalTimQuyenLoiChaRef, Props>(({onSelectQuyenLoiCha, chiTietQuyenLoi}: Props, ref) => {
  useImperativeHandle(ref, () => ({
    open: () => {
      setIsOpen(true);
      initData();
    },
    close: () => setIsOpen(false),
  }));
  const initData = async () => {
    try {
      if (chiTietQuyenLoi?.ma_doi_tac_ql) {
        await getListSanPhamTheoDoiTac({ma_doi_tac_ql: chiTietQuyenLoi.ma_doi_tac_ql, nv: chiTietQuyenLoi.nv});
      }
      await onSearchApi(filterParams);
    } catch (error) {
      console.log("error", error);
    }
  };
  const {layDanhSachBoMaQuyenLoiCha, loading, danhSachBoMaQuyenLoiCha, tongSoDongQLCha, listSanPham, getListSanPhamTheoDoiTac, listDoiTac, filterParams, setFilterParams} = useBoMaQuyenLoiContext();

  const [quyenLoiChaSelected, setQuyenLoiChaSelected] = useState<CommonExecute.Execute.IDanhSachBoMaQuyenLoi | null>(null);
  // const [tongSoDongDaiLyCha, setTongSoDongDaiLyCha] = useState<number>(0);
  const [IsOpen, setIsOpen] = useState(false);
  const [searchParams, setSearchParams] = useState<ReactQuery.ITimKiemPhanTrangDanhSachDaiLyParams>(filterParams);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchText, setSearchText] = useState("");
  const [searchedColumn, setSearchedColumn] = useState<TableQuyenLoiChaColumnDataIndex | "">("");
  const searchInput = useRef<InputRef>(null);

  const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: DataIndexQLCha) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);
  const handleReset = useCallback((clearFilters: () => void, confirm: () => void, dataIndex: DataIndexQLCha) => {
    clearFilters();
    confirm();
    setSearchText("");
    setSearchedColumn(dataIndex);
  }, []);

  const dataTableListQuyenLoi = useMemo<Array<TableBoMaQuyenLoiChaDataType>>(() => {
    try {
      const filteredData = danhSachBoMaQuyenLoiCha.filter(item => item.ma_doi_tac_ql === chiTietQuyenLoi?.ma_doi_tac_ql && item.nv === chiTietQuyenLoi?.nv && item.trang_thai === "D");
      const tableData = filteredData.map((item: any, index: number) => {
        return {
          key: index.toString(),
          stt: item.sott,
          ma: item.ma,
          ten: item.ten,
          doi_tac_ql_ten_tat: item.doi_tac_ql_ten_tat,
          trang_thai_ten: item.trang_thai_ten,
          nguoi_tao: item.nguoi_tao,
          ngay_tao: item.ngay_tao,
          nguoi_cap_nhat: item.nguoi_cap_nhat,
          ngay_cap_nhat: item.ngay_cap_nhat,
          ten_nv: item.ten_nv,
          loai: item.loai,
          ma_sp: item.ma_sp,
          nv: item.nv,
          qloi_gh: item.qloi_gh,
          bl_nt: item.bl_nt,
          bl_gt: item.bl_gt,
          bl_ra: item.bl_ra,
          ma_dtac: item.ma_dtac,
          trang_thai: item.trang_thai,
          ma_ct: item.ma_ct,
          ma_doi_tac_ql: item.ma_doi_tac_ql,
          ten_sp: item.ten_sp,
        };
      });
      const arrEmptyRow: Array<TableBoMaQuyenLoiChaDataType> = fillRowTableEmpty(tableData.length, 10);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      console.log("dataTableListQuyenLoi error", error);
      return [];
    }
  }, [danhSachBoMaQuyenLoiCha]);

  //Bấm tìm kiếm
  const onSearchApi = (values: ReactQuery.ITimKiemPhanTrangBoMaQuyenLoiParams & ReactQuery.IPhanTrang) => {
    // đoạn này để sửa các giá trị undefined => '' trước khi request lên API
    // Ant Design Select không coi "" là một giá trị "chưa chọn", mà là một giá trị đã chọn hợp lệ (dù nó rỗng), nên nó sẽ không hiển thị placeholder nữa.
    const cleanedValues = {
      ...values,
      ma_doi_tac_ql: chiTietQuyenLoi?.ma_doi_tac_ql ?? "",
      ma_sp: values.ma_sp ?? "",
      nd_tim: values.nd_tim ?? "",
      nv: chiTietQuyenLoi?.nv ?? "",
      trang_thai: "D",
      trang: values.trang ?? 1,
      so_dong: values.so_dong ?? 10,
    };
    setSearchParams(cleanedValues);
    setPage(1); // reset về trang 1 khi search mới
    layDanhSachBoMaQuyenLoiCha({...cleanedValues, trang: 1, so_dong: pageSize});
  };
  //Bấm xác nhận chọn
  const onPressXacNhan = async () => {
    try {
      onSelectQuyenLoiCha(quyenLoiChaSelected);
      console.log("quyenLoiChaSelected", quyenLoiChaSelected);
      setIsOpen(false);
    } catch (error) {
      console.log("onPressXacNhan error", error);
    }
  };
  //Gọi api khi chuyển trang
  const onChangePage = useCallback(
    (page: number, pageSize: number) => {
      setPage(page);
      setPageSize(pageSize);
      layDanhSachBoMaQuyenLoiCha({...searchParams, trang: page, so_dong: pageSize});
    },
    // console.log("pagesi")
    [searchParams],
  ); //tạo cột tìm kiếm
  //   const quyenLoiChaSelected = useRef<CommonExecute.Execute.IDanhMucDaiLy | null>(null);
  const getColumnSearchProps = (dataIndex: DataIndexQLCha, title: string): TableColumnType<TableBoMaQuyenLoiChaDataType> => ({
    filterDropdown:
      dataIndex !== "trang_thai_ten"
        ? ({setSelectedKeys, selectedKeys, confirm, clearFilters, close}) => (
            <div onKeyDown={e => e.stopPropagation()} className="header-cell-custom flex flex-row items-center p-2">
              <Input
                ref={searchInput}
                placeholder={`Tìm theo ${title}`}
                value={selectedKeys[0]}
                onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                onPressEnter={() => handleSearch(selectedKeys as string[], confirm, dataIndex)}
                style={{display: "block", marginRight: 8}}
              />
              <Tooltip title="Tìm kiếm">
                <Button type="primary" shape="circle" icon={<SearchOutlined />} onClick={() => handleSearch(selectedKeys as string[], confirm, dataIndex)} className="mr-2" />
              </Tooltip>
              <Tooltip title="Xoá">
                <Button type="primary" shape="circle" icon={<ClearOutlined />} onClick={() => clearFilters && handleReset(clearFilters, confirm, dataIndex)} />
              </Tooltip>
            </div>
          )
        : null,
    filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} />,
    onFilter: (value, record) =>
      record[dataIndex]
        ? record[dataIndex]
            .toString()
            .toLowerCase()
            .includes((value as string).toLowerCase())
        : false,
    filterDropdownProps: {
      onOpenChange(open) {
        if (open) {
          setTimeout(() => searchInput.current?.select(), 100);
        }
      },
    },
    filters: dataIndex === "trang_thai_ten" ? radioItemTrangThaiBoMaQuyenLoiTable : undefined,
    render: (text, record, index) => {
      if (dataIndex === "trang_thai_ten") {
        const color = text === "Đang sử dụng" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];
        if (record.key.toString().includes("empty")) return "";
        return (
          <Tag color={color} className="text-[11px]">
            {text}
          </Tag>
        );
      }
      return searchedColumn === dataIndex ? (
        <Highlighter searchWords={[searchText]} textToHighlight={text ? text.toString() : ""} />
      ) : text !== undefined ? (
        text
      ) : (
        <Tag color={"transparent"} className="!text-white text-[11px]">
          {"\u00A0"}
        </Tag>
      );
    },
  });

  const renderTable = () => {
    return (
      <Table<TableBoMaQuyenLoiChaDataType>
        className="quyen-loi-cha"
        {...defaultTableProps}
        // bordered={false}
        loading={loading}
        onRow={record => {
          return {
            style: {cursor: loading ? "progress" : "pointer"}, //  thể hiện vị trí con trỏ là button(👈)
            onClick: () => {
              if (record.key.toString().includes("empty")) return;
              const selectedItem: CommonExecute.Execute.IDanhSachBoMaQuyenLoi = {
                ...record,
                cap: 0, // Add default value for cap
              };
              setQuyenLoiChaSelected(selectedItem);
              //   console.log("DaiLySelected", quyenLoiChaSelected);
            }, // click row
            onDoubleClick: () => onPressXacNhan(),
          };
        }}
        columns={(BoMaQuyenLoiChaColumns || []).map(item => {
          // Đảm bảo item.key được định nghĩa, item.title là chuỗi và item.key không phải là "stt" trước khi thêm ô tìm kiếm
          return {
            ...item,
            ...(item.key && typeof item.title === "string" && item.key !== "stt" ? getColumnSearchProps(item.key as keyof TableBoMaQuyenLoiChaDataType, item.title) : {}),
          };
        })} //định nghĩa cột của table
        rowClassName={record => (record.ma && record.ma === quyenLoiChaSelected?.ma ? "custom-row-selected" : "")}
        dataSource={dataTableListQuyenLoi}
        // title={renderHeaderTableBoMaQuyenLoi}
        pagination={{
          ...defaultPaginationTableProps,
          defaultPageSize: 10,
          total: tongSoDongQLCha,
          onChange: (page, pageSize) => {
            onChangePage(page, pageSize);
          },
        }}
      />
    );
  };
  const renderFormInputColum = (props?: any, span = 3) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  //render header table
  const renderHeaderTableBoMaQuyenLoi = () => {
    return (
      <div
        style={{
          margin: "16px 0",
        }}>
        <Form initialValues={{trang_thai: TRANG_THAI_BO_MA_QUYEN_LOI[0].ma}} layout="vertical" className="[&_.ant-form-item]:!mb-0" onFinish={onSearchApi}>
          <div className="flex flex-col gap-5">
            <Row gutter={16} align={"bottom"}>
              {renderFormInputColum({...nd_tim})}
              {renderFormInputColum({...ma_sp, options: listSanPham})}
              <Col span={2}>
                <Form.Item className="mr-2">
                  <Flex wrap="wrap" gap="small" className="w-full">
                    <Button className="w-full" htmlType="submit" type="primary" icon={<SearchOutlined />} loading={loading}>
                      Tìm kiếm
                    </Button>
                  </Flex>
                </Form.Item>
              </Col>
            </Row>
          </div>
        </Form>
      </div>
    );
  };

  const renderFooter = () => {
    return (
      <Form.Item>
        <Tooltip title={quyenLoiChaSelected ? "" : "Vui lòng chọn quyền lợi cha"}>
          <Button type={"primary"} onClick={() => onPressXacNhan()} className="mr-2" iconPosition="end" icon={<CheckCircleOutlined />} disabled={quyenLoiChaSelected ? false : true}>
            Chọn
          </Button>
        </Tooltip>
      </Form.Item>
    );
  };
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        maskClosable={false}
        title="Chọn quyền lợi cha"
        // centered
        // bodyStyle={{maxHeight: "70vh", overflowY: "auto"}}
        className="modal-chon-quyen-loi-cha"
        open={IsOpen}
        onOk={() => setIsOpen(false)}
        onCancel={() => setIsOpen(false)}
        style={{
          top: 10,
        }}
        width={{
          xs: "85%",
          sm: "85%",
          md: "85%",
          lg: "85%",
          xl: "85%",
          xxl: "85%",
        }}
        footer={renderFooter}>
        {renderHeaderTableBoMaQuyenLoi()}
        {renderTable()}
      </Modal>
    </Flex>
  );
});
//   const tableHeight = useTableHeight(["footer", "header"]);
ModalTimQuyenLoiChaComponent.displayName = "ModalTimQuyenLoiChaComponent";
export const ModalTimQuyenLoiCha = memo(ModalTimQuyenLoiChaComponent, isEqual);

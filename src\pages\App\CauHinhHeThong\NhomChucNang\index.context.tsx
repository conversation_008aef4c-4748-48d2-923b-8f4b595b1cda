import {createContext, useContext} from "react";
import {NhomChucNangContextProps} from "./index.model";
//createContext : khai báo 1 context mà component có thể cung cấp hoặc đọc
export const NhomChucNangContext = createContext<NhomChucNangContextProps>({
  //khởi tạo các giá trị mặc định, trong context này có giá trị là onSubmit
  danhSachNhomChucNang: [],
  loading: false,
  onUpdateNhomChucNang: () => Promise.resolve(null),
  layDanhSachNhomChucNangPhanTrang: () => Promise.resolve(),
  tongSoDong: 0,
  layChiTietNhomChucNang: () => Promise.resolve(null),
  defaultFormValue: {},
  onSyncChucNang: () => Promise.resolve(),
  chiTietNhomChucNang: {} as CommonExecute.Execute.IChiTietNhomChucNang,
  onDeleteChucNangTheoNhom: () => Promise.resolve(null),
  setChiTietNhomChucNang: () => {},
  layDanhSachChucNangChuaPhanNhom: () => Promise.resolve(null),
});

//useContext(someContext): là React Hook cho phép bạn đọc và subscribe context từ component của mình.
// file .context muốn truy câp context thì sử dụng useLoginContext : const {onSubmit} = useLoginContext();
export const useNhomChucNangContext = () => useContext(NhomChucNangContext);

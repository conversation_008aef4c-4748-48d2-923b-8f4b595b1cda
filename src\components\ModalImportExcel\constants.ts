// Modal ref interface
export interface IModalImportExcelRef {
  openFilePicker: () => void;
  close: () => void;
}

// Modal props interface
export interface IModalImportExcelProps {
  onDataConfirm?: (data: IExcelRowData[]) => void;
  onCancel?: () => void;
}

// Excel row data interface với 30 cột
export interface IExcelRowData {
  // Required fields (*)
  stt: string;                    // *STT
  ten_chu_xe: string;            // *TÊN CHỦ XE
  dia_chi: string;               // *ĐỊA CHỈ
  bien_xe: string;               // *BIỂN XE
  so_khung: string;              // *SỐ KHUNG
  so_may: string;                // *SỐ MÁY
  so_gcn: string;                // *SỐ GCN
  nam_sx: string;                // *NĂM SX
  muc_dich_su_dung: string;      // *MỤC ĐÍCH SỬ DỤNG
  gia_tri_xe: string;            // *GIÁ TRỊ XE
  loai_xe: string;               // *LOẠI XE
  hang_xe: string;               // *HÃNG XE
  hieu_xe: string;               // *HIỆU XE
  ngay_cap: string;              // *NGÀY CẤP
  gio_hieu_luc: string;          // *GIỜ HIỆU LỰC
  ngay_hieu_luc: string;         // *NGÀY HIỆU LỰC
  gio_ket_thuc: string;          // *GIỜ KẾT THÚC
  ngay_ket_thuc: string;         // *NGÀY KẾT THÚC
  ma_dk_dkbs: string;            // *MÃ ĐK/ĐKBS
  so_tien_bh: string;            // *SỐ TIỀN BH
  tien_mien_thuong: string;      // *TIỀN MIỄN THƯỜNG
  phi_bh_chua_vat: string;       // *PHÍ BH (CHƯA VAT)
  thue_vat: string;              // *THUẾ (VAT)
  phi_giam_chua_vat: string;     // *PHÍ GIẢM (CHƯA VAT)
  thue_giam_vat: string;         // *THUẾ GIẢM (VAT)
  
  // Optional fields
  so_lai_phu_xe: string;         // SỐ LÁI PHỤ XE
  so_cho: string;                // SỐ CHỖ
  trong_tai: string;             // TRỌNG TẢI (TẤN)
  vip: string;                   // VIP
  
  // Table key for Ant Design
  key?: string;
}

// API response interface
export interface IUploadExcelResponse {
  data: IExcelRowData[];
  errors?: string[];
  total_rows?: number;
  valid_rows?: number;
  invalid_rows?: number;
}

// Upload progress interface
export interface IUploadProgress {
  percent: number;
  status: 'uploading' | 'processing' | 'done' | 'error';
  message?: string;
}

// Validation error interface
export interface IValidationError {
  row: number;
  column: string;
  message: string;
  value?: string;
}

// Required fields list
export const REQUIRED_FIELDS: (keyof IExcelRowData)[] = [
  'stt',
  'ten_chu_xe',
  'dia_chi',
  'bien_xe',
  'so_khung',
  'so_may',
  'so_gcn',
  'nam_sx',
  'muc_dich_su_dung',
  'gia_tri_xe',
  'loai_xe',
  'hang_xe',
  'hieu_xe',
  'ngay_cap',
  'gio_hieu_luc',
  'ngay_hieu_luc',
  'gio_ket_thuc',
  'ngay_ket_thuc',
  'ma_dk_dkbs',
  'so_tien_bh',
  'tien_mien_thuong',
  'phi_bh_chua_vat',
  'thue_vat',
  'phi_giam_chua_vat',
  'thue_giam_vat'
];

// Column labels mapping
export const COLUMN_LABELS: Record<keyof IExcelRowData, string> = {
  stt: '*STT',
  ten_chu_xe: '*TÊN CHỦ XE',
  dia_chi: '*ĐỊA CHỈ',
  bien_xe: '*BIỂN XE',
  so_khung: '*SỐ KHUNG',
  so_may: '*SỐ MÁY',
  so_gcn: '*SỐ GCN',
  nam_sx: '*NĂM SX',
  so_lai_phu_xe: 'SỐ LÁI PHỤ XE',
  muc_dich_su_dung: '*MỤC ĐÍCH SỬ DỤNG',
  gia_tri_xe: '*GIÁ TRỊ XE',
  loai_xe: '*LOẠI XE',
  hang_xe: '*HÃNG XE',
  hieu_xe: '*HIỆU XE',
  so_cho: 'SỐ CHỖ',
  trong_tai: 'TRỌNG TẢI (TẤN)',
  vip: 'VIP',
  ngay_cap: '*NGÀY CẤP',
  gio_hieu_luc: '*GIỜ HIỆU LỰC',
  ngay_hieu_luc: '*NGÀY HIỆU LỰC',
  gio_ket_thuc: '*GIỜ KẾT THÚC',
  ngay_ket_thuc: '*NGÀY KẾT THÚC',
  ma_dk_dkbs: '*MÃ ĐK/ĐKBS',
  so_tien_bh: '*SỐ TIỀN BH',
  tien_mien_thuong: '*TIỀN MIỄN THƯỜNG',
  phi_bh_chua_vat: '*PHÍ BH (CHƯA VAT)',
  thue_vat: '*THUẾ (VAT)',
  phi_giam_chua_vat: '*PHÍ GIẢM (CHƯA VAT)',
  thue_giam_vat: '*THUẾ GIẢM (VAT)',
  key: 'Key'
};

// Error messages
export const ERROR_MESSAGES = {
  INVALID_FILE_TYPE: 'Chỉ chấp nhận file Excel (.xls, .xlsx)',
  FILE_TOO_LARGE: 'File quá lớn. Kích thước tối đa là 10MB',
  UPLOAD_FAILED: 'Upload file thất bại. Vui lòng thử lại',
  INVALID_DATA: 'Dữ liệu không hợp lệ',
  REQUIRED_FIELD_MISSING: 'Trường bắt buộc không được để trống',
  NETWORK_ERROR: 'Lỗi kết nối mạng. Vui lòng kiểm tra và thử lại',
  PROCESSING_ERROR: 'Lỗi xử lý dữ liệu. Vui lòng kiểm tra định dạng file'
} as const;

// File validation constants
export const FILE_VALIDATION = {
  ACCEPTED_TYPES: ['.xls', '.xlsx'],
  MAX_SIZE_MB: 10,
  MAX_SIZE_BYTES: 10 * 1024 * 1024
} as const;

// Modal size constants
export const MODAL_CONFIG = {
  WIDTH: '90vw',
  HEIGHT: '70vh',
  MIN_WIDTH: 800,
  MIN_HEIGHT: 600
} as const;

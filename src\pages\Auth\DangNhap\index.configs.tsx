import {Authen} from "@src/@types/Authentication";

// <PERSON><PERSON>nh nghĩa cấu hình các field của Form Login
export const LoginFormConfigs: Authen.Login.IFormFieldsConfig = {
  tai_khoan: {
    name: "tai_khoan",
    placeholder: "<PERSON>ài khoản",
    // label: "Email", // cho label vào thì sẽ thành input with label
    rules: [
      // {type: "email", message: "Email sai định dạng"},
      {
        required: true,
        message: "Thông tin bắt buộc",
      },
    ],
  },
  mat_khau: {
    name: "mat_khau",
    placeholder: "<PERSON><PERSON>t khẩu",
    type: "password", //loại mật khẩu
    component: "input-password",
    rules: [
      {
        required: true,
        message: "Thông tin bắt buộc",
      },
    ],
  },
};

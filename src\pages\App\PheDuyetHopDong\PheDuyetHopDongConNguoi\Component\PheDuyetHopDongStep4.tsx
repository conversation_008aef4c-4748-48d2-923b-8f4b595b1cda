import "@react-pdf-viewer/core/lib/styles/index.css";
import {isEqual} from "lodash";
import {forwardRef, memo, useEffect, useImperativeHandle} from "react";
import {useHopDongConNguoiContext} from "@src/pages/App/BaoHiemConNguoi/HopDongConNguoi/index.context";
import {IPheDuyetHopDongStep4Ref, PheDuyetHopDongStep4Props} from "./Constant";
import {Tabs} from "antd";
import {ThongTinThanhToanStep4} from "./PheDuyetHopDongStep4_TabThongTinThanhToan";
import {ThongTinDongBaoHiemStep4} from "./PheDuyetHopDongStep4_TabThongTinDongBaoHiem";
import {ThongTinTaiBaoHiemStep4} from "./PheDuyetHopDongStep4_TabThongTinTaiBaoHiem";

const PheDuyetHopDongStep4Component = forwardRef<IPheDuyetHopDongStep4Ref, PheDuyetHopDongStep4Props>(({}: PheDuyetHopDongStep4Props, ref) => {
  useImperativeHandle(ref, () => ({}));

  const {layDanhSachCauHinhDongCuaHopDongBaoHiemConNguoi, layDanhSachCauHinhTaiBHCuaHopDongBaoHiemConNguoi, chiTietHopDong} = useHopDongConNguoiContext();

  useEffect(() => {
    layDanhSachCauHinhDongCuaHopDongBaoHiemConNguoi({so_id: chiTietHopDong?.so_id}); // KHỞI TẠO DATA CHO TAB ĐỒNG BẢO HIỂM
    layDanhSachCauHinhTaiBHCuaHopDongBaoHiemConNguoi({so_id: chiTietHopDong?.so_id}); //KHỞI TẠO DATA CHO TAB THÔNG TIN TÁI BẢO HIỂM
  }, [chiTietHopDong]);

  return (
    <Tabs
      className="mt-3 [&_.ant-tabs-nav]:mb-0"
      animated={false}
      size="small"
      // tabPosition={"left"}
      defaultActiveKey="1"
      // type="card"
      items={[
        {
          key: "1",
          label: "Thông tin thanh toán",
          children: <ThongTinThanhToanStep4 />,
        },
        {
          key: "2",
          label: "Thông tin đồng bảo hiểm",
          children: <ThongTinDongBaoHiemStep4 />,
        },
        {
          key: "3",
          label: "Thông tin tái bảo hiểm",
          children: <ThongTinTaiBaoHiemStep4 />,
        },
      ]}
      // onChange={onChange}
    />
  );
});

PheDuyetHopDongStep4Component.displayName = "PheDuyetHopDongStep4Component";
export const PheDuyetHopDongStep4 = memo(PheDuyetHopDongStep4Component, isEqual);

import {IFormInput} from "@src/@types";
import {REGUlAR_EXPRESSION} from "@src/constants";
import {colWidthByKey, defaultTableColumnsProps, ruleInputMessage} from "@src/hooks";
import {TableProps} from "antd";

//ĐỊNH NGHĨA CẤU HÌNH TABLE ĐỐI TÁC GỒM NHỮNG CỘT NÀO, THỨ TỰ CỘT SẼ HIỂN THỊ THEO THỨ TỰ KHAI BÁO Ở DƯỚI
export interface TableDanhMucBangMaBenhColumnDataType {
  key: string;
  sott?: number;
  ma?: string;
  ten?: string;
  trang_thai?: string;
  ngay_tao?: string;
  nguoi_tao?: string;
  ngay_cap_nhat?: string;
  nguoi_cap_nhat?: string;
  trang_thai_ten?: string;
}

// ĐỊNH NGHĨA Column RENDER RA TỪNG Ô TRONG CỘT
//đọc https://ant.design/components/table#column để biết thêm về column
export const tableDanhMucBangMaBenhColumn: TableProps<TableDanhMucBangMaBenhColumnDataType>["columns"] = [
  {
    title: "STT",
    dataIndex: "sott",
    key: "sott",
    align: "center",
    width: colWidthByKey.sott,
    ...defaultTableColumnsProps,
  },
  {
    title: "Mã bảng mã bệnh",
    dataIndex: "ma",
    key: "ma",
    align: "center",
    width: 100,
    ...defaultTableColumnsProps,
  },
  {
    title: "Tên bảng mã bệnh",
    dataIndex: "ten",
    key: "ten",
    width: 180,
    ...defaultTableColumnsProps,
  },
  {
    title: "Ngày tạo",
    dataIndex: "ngay_tao",
    key: "ngay_tao",
    width: colWidthByKey.ngay_tao,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Người tạo",
    dataIndex: "nguoi_tao",
    key: "nguoi_tao",
    width: colWidthByKey.nguoi_tao,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Ngày cập nhật",
    dataIndex: "ngay_cap_nhat",
    key: "ngay_cap_nhat",
    width: colWidthByKey.ngay_cap_nhat,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Người cập nhật",
    dataIndex: "nguoi_cap_nhat",
    key: "nguoi_cap_nhat",
    width: colWidthByKey.nguoi_cap_nhat,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Trạng thái",
    dataIndex: "trang_thai_ten",
    key: "trang_thai_ten",
    width: 130,
    align: "center",
    ...defaultTableColumnsProps,
  },
];

//keyof: return ra key của inteface TableDanhMucBangMaBenhColumnDataType;
export type TableDanhMucBangMaBenhColumnDataIndex = keyof TableDanhMucBangMaBenhColumnDataType;

//radio trong table
export const radioItemTrangThaiDanhMucBangMaBenhTable = [
  {value: "Đang sử dụng", text: "Đang sử dụng"},
  {value: "Ngưng sử dụng", text: "Ngưng sử dụng"},
];

//form tìm kiếm
//radio trong search
export const radioItemTrangThaiDanhMucBangMaBenhSelect: Array<{ma: string; ten: string}> = [
  {ma: "D", ten: "Đang sử dụng"},
  {ma: "K", ten: "Ngưng sử dụng"},
];
export interface IFormTimKiemDanhMucBangMaBenhFieldsConfig {
  ten: IFormInput;
  ma: IFormInput;
  trang_thai: IFormInput;
}
export const FormTimKiemQuanLyDanhMucBangMaBenh: IFormTimKiemDanhMucBangMaBenhFieldsConfig = {
  ten: {
    component: "input",
    name: "ten",
    label: "Tên bảng mã bệnh",
    placeholder: "Nhập tên bảng mã bệnh",
    className: "!mb-0",
  },
  ma: {
    component: "input",
    name: "ma",
    label: "Mã bảng mã bệnh",
    placeholder: "Nhập mã bảng mã bệnh",
    className: "!mb-0",
  },
  trang_thai: {
    component: "select",
    name: "trang_thai",
    label: "Trạng thái",
    placeholder: "Chọn trạng thái",
    className: "!mb-0",
  },
};

//form update / create
export interface IFormTaoMoiDanhMucBangMaBenhFieldsConfig {
  ma: IFormInput;
  ten: IFormInput;
  stt: IFormInput;
  trang_thai: IFormInput;
}

// const ruleRequired = {
//   required: true,
//   message: "Thông tin bắt buộc",
// };

export const FormTaoMoiDanhMucBangMaBenh: IFormTaoMoiDanhMucBangMaBenhFieldsConfig = {
  ma: {
    component: "input",
    label: "Mã",
    name: "ma",
    placeholder: "Nhập mã bảng mã bệnh",
    rules: [ruleInputMessage.required],
  },
  ten: {
    component: "input",
    label: "Tên",
    name: "ten",
    placeholder: "Nhập tên bảng mã bệnh",
    rules: [ruleInputMessage.required],
  },
  stt: {
    component: "input",
    label: "Số thứ tự",
    placeholder: "Nhập số thứ tự",
    name: "stt",
    rules: [ruleInputMessage.required],
  },
  trang_thai: {
    component: "select",
    label: "Trạng thái",
    name: "trang_thai",
    placeholder: "Chọn trạng thái",
    rules: [ruleInputMessage.required],
  },
};

export const TRANG_THAI_TAO_MOI_DANH_MUC_BANG_MA_BENH = [
  {ten: "Đang sử dụng", ma: "D"},
  {ten: "Ngừng sử dụng", ma: "K"},
];

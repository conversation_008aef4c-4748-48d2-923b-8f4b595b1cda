import {initReactI18next} from "react-i18next";
import {LANGUAGE_VALUES} from "@src/constants";
import en from "./translations/en";
import vi from "./translations/vi";

import i18n, {Resource} from "i18next";

const resources: Resource = {
  en: {
    translation: en,
  },
  vi: {
    translation: vi,
  },
};

i18n.use(initReactI18next).init({
  resources,
  lng: "auto",
  fallbackLng: LANGUAGE_VALUES.EN,
  interpolation: {
    escapeValue: false, // react already safes from xss
  },
});

export default i18n;

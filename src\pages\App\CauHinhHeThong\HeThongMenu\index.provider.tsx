import {ReactQuery} from "@src/@types";
import {ACTION_CODE} from "@src/constants";
import {CommonExecuteResponse, useCommonExecute} from "@src/services/react-queries";
import {message} from "antd";
import React, {PropsWithChildren, useCallback, useEffect, useMemo, useState} from "react";
import {HeThongMenuContextProps} from "./index.model";
import {HeThongMenuContext} from "./index.context";

/* 
  file Provider : Chứa logic quản lý state, gọi API, wrap context
*/

const HeThongMenuProvider: React.FC<PropsWithChildren> = props => {
  const {children} = props;
  const mutateUseCommonExecute = useCommonExecute();
  const [danhSachMenu, setDanhSachMenu] = useState<Array<CommonExecute.Execute.IHeThongMenu>>([]);
  const [danhSachMenuCha, setDanhSachMenuCha] = useState<Array<CommonExecute.Execute.IHeThongMenu>>([]);
  //   const [chiTietMenu, setChiTietMenu] = useState<CommonExecute.Execute.IHeThongMenu>({});
  const [tongSoDong, setTongSoDong] = useState<number>(0);
  const [filterParams, setFilterParams] = useState<ReactQuery.ITimkiemPhanTrangHeThongMenuParams & ReactQuery.IPhanTrang>({
    ma: "",
    ten: "",
    nhom: "",
    trang_thai: "",
    trang: 1,
    so_dong: 13,
    actionCode: "",
  });
  useEffect(() => {
    initData();
  }, []);

  const initData = () => {
    layDanhSachMenuPhanTrang(filterParams);
  };
  useEffect(() => {
    layDanhSachMenuPhanTrang(filterParams);
  }, [filterParams]);
  //DS menu phân trang
  const layDanhSachMenuPhanTrang = useCallback(
    async (body: ReactQuery.ITimkiemPhanTrangHeThongMenuParams & ReactQuery.IPhanTrang) => {
      try {
        const params = {
          ...body,
          nhom: body.nhom as "CLIENT" | "ADMIN" | undefined,
          actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_MENU,
        };

        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        const data = response.data.data;
        setDanhSachMenu(data);
        setTongSoDong(response.data.tong_so_dong);
      } catch (error: any) {
        console.log("Lấy danh sách menu error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );
  // const layDanhSachMenuCha = useCallback(
  //   async (body: ReactQuery.ITimkiemMenuChaParams & ReactQuery.IPhanTrang) => {
  //     try {
  //       const params = {
  //         ...body,
  //         actionCode: ACTION_CODE.LIET_KE_MENU_CHA,
  //       };
  //       console.log("lieykeneu cha", params);
  //       const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
  //       const data = response.data.data;
  //       setDanhSachMenu(data);
  //       // setTongSoDong(response.data.tong_so_dong);
  //     } catch (error: any) {
  //       console.log("Lấy danh sách menu error ", error.message | error);
  //     }
  //   },
  //   [mutateUseCommonExecute],
  // );
  //Lấy danh sách mennu cha
  const layDanhSachMenuCha = useCallback(
    async (params?: any) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          actionCode: ACTION_CODE.LIET_KE_MENU_CHA,
          data: params,
        } as any);
        const data = response.data;

        setDanhSachMenuCha(data);
      } catch (error) {
        console.log("getListMenu error ", error);
      }
    },
    [mutateUseCommonExecute],
  );
  //Lấy chi tiết 1 menu
  const layChiTietMenu = useCallback(
    async (item: ReactQuery.IChiTietHeThongMenuParams): Promise<CommonExecute.Execute.IHeThongMenu | null> => {
      try {
        const params = {
          ma: item.ma,
          actionCode: ACTION_CODE.CHI_TIET_MENU,
        };
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);

        return responseData.data as CommonExecute.Execute.IHeThongMenu;
      } catch (error: any) {
        console.log("layChiTietmenu error ", error.message || error);
        return null;
      }
    },
    [mutateUseCommonExecute],
  );

  //Cập nhật hoặc tạo mới 1 menu
  const onUpdateHeThongMenu = useCallback(
    async (body: ReactQuery.ICapNhatHeThongMenuParams): Promise<number | null | undefined> => {
      try {
        const params = {
          ...body,
          nhom: body.nhom as "CLIENT" | "ADMIN" | undefined,
          actionCode: ACTION_CODE.CAP_NHAT_MENU,
        };
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        if (responseData && (responseData.data as unknown as number) === -1) {
          console.log("response capapj nhật", responseData.data);
          message.success("Cập nhật thông tin thành công!");
          initData();
          // Chuyển đổi responseData.data thành number
          return responseData.data as unknown as number; // Chuyển đổi responseData.data thành number
        }
      } catch (error: any) {
        console.log("onUpdateMenu error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );

  //khai báo giá trị của context để truyền vào Component
  const value = useMemo<HeThongMenuContextProps>(
    () => ({
      layDanhSachMenuPhanTrang,
      loading: mutateUseCommonExecute.isLoading,
      onUpdateHeThongMenu,
      layChiTietMenu,
      tongSoDong,
      danhSachMenu,
      filterParams,
      setFilterParams,
      layDanhSachMenuCha,
      danhSachMenuCha,
    }),
    [mutateUseCommonExecute, layChiTietMenu, danhSachMenu, filterParams, setFilterParams, onUpdateHeThongMenu, tongSoDong, layDanhSachMenuPhanTrang, layDanhSachMenuCha, danhSachMenuCha],
  );

  return <HeThongMenuContext.Provider value={value}>{children}</HeThongMenuContext.Provider>;
};

export default HeThongMenuProvider;

import {ReactQuery} from "@src/@types";
import {ACTION_CODE} from "@src/constants";
import {CommonExecuteResponse, useCommonExecute} from "@src/services/react-queries";
import {message} from "antd";
import React, {PropsWithChildren, useCallback, useMemo, useState} from "react";
import {HopDongTrinhDuyetContext} from "./index.context";
import {IPheDuyetHopDongXCGContextProps} from "./index.model";

const PheDuyetHopDongXCGProvider: React.FC<PropsWithChildren> = props => {
  const {children} = props;
  const mutateUseCommonExecute = useCommonExecute();

  const [danhSachHopDongTrinhDuyetXCG, setDanhSachHopDongTrinhDuyetXCG] = useState<CommonExecute.Execute.IDoiTac[]>([]);
  const [tongSoDong, setTongSoDong] = useState<number>(0);
  //khởi tạo dữ liệu ban đàu

  //tìm kiếm phân trang data bảng phê duyệt hợp đồng xe
  const timKiemPhanTrangHopDongTrinhDuyetXCG = useCallback(
    async (body?: ReactQuery.ITimKiemPhanTrangHopDongTrinhDuyetParams & ReactQuery.IPhanTrang) => {
      if (!body) return;
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_DS_HOP_DONG_TRINH_DUYET,
        };
        const response = await mutateUseCommonExecute.mutateAsync(params);

        if (response.data) {
          setDanhSachHopDongTrinhDuyetXCG(response.data.data);
          setTongSoDong(response.data.tong_so_dong);
        }
      } catch (error) {
        console.log("timKiemPhanTrangDanhSachHopDongTrinhDuyetXCG error ", error);
      }
    },
    [mutateUseCommonExecute],
  );

  const xemChiTietHopDongTrinhDuyet = useCallback(
    async (data: ReactQuery.IXemChiTietHopDongTrinhDuyetParams) => {
      try {
        const params = {
          bt: data.bt,
          actionCode: ACTION_CODE.XEM_CHI_TIET_HOP_DONG_TRINH_DUYET,
        };
        const response = await mutateUseCommonExecute.mutateAsync(params);
        return response.data as CommonExecute.Execute.IHopDongTrinhDuyetXCG;
      } catch (error) {
        console.log("xemChiTietHopDongTrinhDuyet err", error);
        return {} as CommonExecute.Execute.IHopDongTrinhDuyetXCG;
      }
    },
    [mutateUseCommonExecute],
  );

  //
  const capNhatChiTietDoiTac = useCallback(
    async (data: ReactQuery.IUpdateDoiTacParams) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...data,
          actionCode: ACTION_CODE.UPDATE_CHI_TIET_DOI_TAC,
        });
        return response.data;
      } catch (error) {
        console.log("capNhatChiTietDoiTac err", error);
        // return {} as CommonExecute.Execute.IDoiTac;
      }
    },
    [mutateUseCommonExecute],
  );

  //Phê duyệt HĐ
  const handlePheDuyetHopDong = useCallback(
    async (body: ReactQuery.ICapNhatHopDongTrinhDuyetParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.PHE_DUYET_HOP_DONG_TRINH_DUYET,
        };
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        if ((response.data as unknown as number) === -1) {
          message.success("Phê duyệt hợp đồng thành công!");

          return true;
        } else return false;
      } catch (error: any) {
        console.log("handlePheDuyetHopDong error ", error.message | error);
        return false;
      }
    },
    [mutateUseCommonExecute],
  );
  //Gỡ duyệt HĐ
  const handleGoDuyetHopDong = useCallback(
    async (body: ReactQuery.ICapNhatHopDongTrinhDuyetParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.GO_DUYET_HOP_DONG_TRINH_DUYET,
        };
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        if ((response.data as unknown as number) === -1) {
          message.success("Gỡ duyệt hợp đồng thành công!");
          return true;
        } else return false;
      } catch (error: any) {
        console.log("handlePheDuyetHopDong error ", error.message | error);
        return false;
      }
    },
    [mutateUseCommonExecute],
  );

  //Từ chối duyệt HĐ
  const handleTuChoiDuyetHopDong = useCallback(
    async (body: ReactQuery.ICapNhatHopDongTrinhDuyetParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.TU_CHOI_DUYET_HOP_DONG_TRINH_DUYET,
        };
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        if ((response.data as unknown as number) === -1) {
          message.success("Từ chối duyệt hợp đồng thành công!");
          return true;
        } else return false;
      } catch (error: any) {
        console.log("handlePheDuyetHopDong error ", error.message | error);
        return false;
      }
    },
    [mutateUseCommonExecute],
  );

  const value = useMemo<IPheDuyetHopDongXCGContextProps>(
    () => ({
      danhSachHopDongTrinhDuyetXCG,
      tongSoDong,
      loading: mutateUseCommonExecute.isLoading,
      timKiemPhanTrangHopDongTrinhDuyetXCG,
      xemChiTietHopDongTrinhDuyet,
      handlePheDuyetHopDong,
      handleGoDuyetHopDong,
      handleTuChoiDuyetHopDong,
    }),
    [
      danhSachHopDongTrinhDuyetXCG,
      tongSoDong,
      mutateUseCommonExecute,
      timKiemPhanTrangHopDongTrinhDuyetXCG,
      xemChiTietHopDongTrinhDuyet,
      capNhatChiTietDoiTac,
      handlePheDuyetHopDong,
      handleGoDuyetHopDong,
      handleTuChoiDuyetHopDong,
    ],
  );

  return <HopDongTrinhDuyetContext.Provider value={value}>{children}</HopDongTrinhDuyetContext.Provider>;
};

export default PheDuyetHopDongXCGProvider;

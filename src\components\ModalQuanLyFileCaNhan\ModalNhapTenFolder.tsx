import {Button} from "@src/components";
import {Flex, Input, Modal} from "antd";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useImperativeHandle, useState} from "react";
// import "../index.default.scss";

export interface IModalNhapTenFolderRef {
  open: (folderSelectedData?: File.GetFolder.IGetFolder) => void;
}
export interface ModalNhapTenFolderProps {
  luuTenThuMuc: (inputName: string, folderSelectedData: File.GetFolder.IGetFolder) => void;
}

const ModalNhapTenFolderComponent = forwardRef<IModalNhapTenFolderRef, ModalNhapTenFolderProps>(({luuTenThuMuc}: ModalNhapTenFolderProps, ref) => {
  useImperativeHandle(ref, () => ({
    open: (folderSelectedData?: File.GetFolder.IGetFolder) => {
      if (folderSelectedData) {
        setFolderSelectedData(folderSelectedData);
        setInputName(folderSelectedData.ten_alias || "");
      }

      setIsOpen(true);
    },
    close: () => {},
  }));

  const [isOpen, setIsOpen] = useState(false);
  const [inputName, setInputName] = useState<string>("");
  const [folderSelectedData, setFolderSelectedData] = useState<any>(null);

  const closeModal = useCallback(() => {
    setIsOpen(false);
    setInputName("");
    setFolderSelectedData(null);
  }, []);
  const onChangeInputName = (event: any) => {
    setInputName(event.target.value);
  };
  const onClickLuu = () => {
    console.log("onClickLuu");
    luuTenThuMuc(inputName, folderSelectedData);
    closeModal();
  };

  // RENDER
  const renderFooter = () => {
    return (
      <div className="flex flex-row justify-end">
        <Button type={"primary"} onClick={() => setIsOpen(false)} className="mr-3" color="default" variant="outlined">
          Để sau
        </Button>
        <Button onClick={onClickLuu} disabled={inputName === "" ? true : false}>
          Lưu
        </Button>
      </div>
    );
  };

  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        className="modal-nhap-ten-folder"
        title={!folderSelectedData ? "Tạo thư mục mới" : "Cập nhật tên"}
        centered
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width={"30%"}
        maskClosable={false}
        styles={{
          body: {
            height: "5vh",
          },
        }}
        footer={renderFooter}>
        <Input placeholder="Nhập tên thư mục" value={inputName} onChange={onChangeInputName} />
      </Modal>
    </Flex>
  );
});

ModalNhapTenFolderComponent.displayName = "ModalNhapTenFolderComponent";
export const ModalNhapTenFolder = memo(ModalNhapTenFolderComponent, isEqual);

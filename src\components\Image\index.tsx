import { memo } from "react";
import { Image as AntImage, ImageProps } from "antd";
import { twMerge } from "tailwind-merge";

import { isEqual } from "lodash";

const ImageComponent: React.FC<ImageProps> = (props: ImageProps) => {
  const { className = "", ...etc } = props;
  return <AntImage className={twMerge(`custom-image`, className)} {...etc} />;
};

const Image = memo(ImageComponent, isEqual);

export default Image;

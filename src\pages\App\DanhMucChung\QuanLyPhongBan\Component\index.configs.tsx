import {ruleInputMessage} from "@src/hooks";

export const initFormFields = (form: any, chiTietPhongBan: any) => {
  if (!chiTietPhongBan) return;

  const fields = Object.entries(chiTietPhongBan).map(([name, value]) => ({
    name,
    value: value ?? "",
  }));
  form.setFields(fields);
};

export const FormInputConfigs = {
  ma_chi_nhanh: {
    component: "select",
    name: "ma_chi_nhanh",
    placeholder: "Chọn chi nhánh",
    label: "Chi nhánh",
    rules: [ruleInputMessage.required],
  },
  trang_thai: {
    component: "select",
    name: "trang_thai",
    placeholder: "Chọn trạng thái",
    label: "Trạng thái",
    rules: [ruleInputMessage.required],
  },
  ma_doi_tac: {
    component: "select",
    name: "ma_doi_tac",
    placeholder: "Chọn đối tác",
    label: "Đ<PERSON><PERSON> tác",
    rules: [ruleInputMessage.required],
  },
  dthoai: {
    component: "input",
    name: "dthoai",
    placeholder: "<PERSON>i<PERSON>n thoại",
    label: "<PERSON><PERSON><PERSON><PERSON> thoại",
    rules: [ruleInputMessage.required, ruleInputMessage.phone],
  },
  ten: {
    component: "input",
    name: "ten",
    placeholder: "Nhập tên phòng ban",
    label: "Tên phòng ban",
    rules: [ruleInputMessage.required],
  },
  ma: {
    component: "input",
    name: "ma",
    placeholder: "Nhập mã phòng ban",
    label: "Mã phòng ban",
    rules: [ruleInputMessage.required],
  },
};

export const TRANG_THAI = [
  {ten: "Đang sử dụng", ma: "D"},
  {ten: "Ngừng sử dụng", ma: "K"},
];

import {ReactQuery} from "@src/@types";

// khai báo interface props Context của Login
export interface DanhMucDaiLyContextProps {
  danhSachDaiLy: Array<CommonExecute.Execute.IDanhMucDaiLy>;
  loading: boolean;
  listDoiTac: Array<CommonExecute.Execute.IDoiTac>;
  filterParams: ReactQuery.ITimKiemPhanTrangDanhSachDaiLyParams & ReactQuery.IPhanTrang;
  daiLyQuanLy: Array<NonNullable<CommonExecute.Execute.IChiTietDanhMucDaiLy["dl_qly"]>[0]>;
  daiLyQuanLySelected: Array<NonNullable<CommonExecute.Execute.IChiTietDanhMucDaiLy["dl_qly"]>[0]>;
  setDaiLyQuanLySelected: React.Dispatch<React.SetStateAction<Array<NonNullable<CommonExecute.Execute.IChiTietDanhMucDaiLy["dl_qly"]>[0]>>>;
  setFilterParams: React.Dispatch<React.SetStateAction<ReactQuery.ITimKiemPhanTrangDanhSachDaiLyParams & ReactQuery.IPhanTrang>>;
  getListDoiTac: () => Promise<void>;
  onUpdateDanhMucDaiLy: (item: ReactQuery.IUpdateDaiLyParams) => Promise<number | null | undefined>;
  layDanhSachDaiLyPhanTrang: (params: ReactQuery.ITimKiemPhanTrangDanhSachDaiLyParams) => Promise<{data: Array<CommonExecute.Execute.IDanhMucDaiLy>; tong_so_dong: number} | null>;
  tongSoDong: number;
  layChiTietDaiLy: (params: ReactQuery.IChiTietDanhMucDaiLyParams) => Promise<CommonExecute.Execute.IChiTietDanhMucDaiLy | null>;
  // defaultFormValue: object;
}

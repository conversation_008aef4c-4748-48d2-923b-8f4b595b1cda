import {createContext, useContext} from "react";

import {IPheDuyetHopDongConNguoiContextProps} from "./index.model";

//khởi tạo giá trị mặc định
export const HopDongTrinhDuyetContext = createContext<IPheDuyetHopDongConNguoiContextProps>({
  danhSachHopDongTrinhDuyetConNguoi: [],
  tongSoDong: 0,
  loading: false,
  tim<PERSON>iemPhanTrangHopDongTrinhDuyetConNguoi: () => Promise.resolve(),
  xemChiTietHopDongTrinhDuyet: () => Promise.resolve({}),
  handlePheDuyetHopDong: () => Promise.resolve(false),
  handleGoDuyetHopDong: () => Promise.resolve(false),
  handleTuChoiDuyetHopDong: () => Promise.resolve(false),
});

export const useHopDongTrinhDuyetContext = () => useContext(HopDongTrinhDuyetContext);

import {IFormInput, ReactQuery} from "@src/@types";
import {colWidthByKey, defaultTableColumnsProps} from "@src/hooks";
import {TableProps} from "antd";
export interface TableDanhMucHangXeDataType {
  key: number | string;
  sott?: number;
  ma?: string;
  ten?: string;
  trang_thai_ten?: string;
  ngay_tao?: string;
  nguoi_tao?: string;
  ngay_cap_nhat?: string;
  nguoi_cap_nhat?: string;
  nv?: string;
}

// ĐỊNH NGHĨA Column RENDER RA TỪNG Ô TRONG CỘT
//đọc https://ant.design/components/table#column để biết thêm về column
export const DanhMucHangXeColumns: TableProps<TableDanhMucHangXeDataType>["columns"] = [
  {...defaultTableColumnsProps, title: "STT", dataIndex: "sott", width: colWidthByKey.sott},
  {...defaultTableColumnsProps, title: "<PERSON><PERSON> hãng xe", dataIndex: "ma", key: "ma", width: 150},
  {...defaultTableColumnsProps, title: "Tên hãng xe", dataIndex: "ten", key: "ten", width: 200},
  {...defaultTableColumnsProps, title: "Nghiệp vụ", dataIndex: "nv", key: "nv", width: 130},
  {...defaultTableColumnsProps, title: "Ngày tạo", dataIndex: "ngay_tao", key: "ngay_tao", width: colWidthByKey.ngay_tao},
  {...defaultTableColumnsProps, title: "Người tạo", dataIndex: "nguoi_tao", key: "nguoi_tao", width: colWidthByKey.nguoi_tao},
  {...defaultTableColumnsProps, title: "Ngày cập nhật", dataIndex: "ngay_cap_nhat", key: "Ngay_cap_nhat", width: colWidthByKey.ngay_cap_nhat},
  {...defaultTableColumnsProps, title: "Người cập nhật", dataIndex: "nguoi_cap_nhat", key: "nguoi_cap_nhat", width: colWidthByKey.nguoi_cap_nhat},
  {...defaultTableColumnsProps, title: "Trạng thái", dataIndex: "trang_thai_ten", key: "trang_thai_ten", width: 130},
];

export const setFormFields = (form: any, chiTietHopDong: any) => {
  if (chiTietHopDong) {
    form.setFields([
      {
        name: "ten",
        value: chiTietHopDong.ten || "",
      },
      {
        name: "ma",
        value: chiTietHopDong.ma || "",
      },
      {
        name: "loai",
        value: chiTietHopDong.loai || "",
      },
      {
        name: "kieu_ad",
        value: chiTietHopDong.kieu_ad || "",
      },
    ]);
  }
};

export const radioItemTrangThaiHopDongTable = [
  {value: "Đang sử dụng", text: "Đang sử dụng"},
  {value: "Ngưng sử dụng", text: "Ngưng sử dụng"},
];

//option select trạng thái
export const optionTrangThaiHopDongSelect = [
  {ma: "D", ten: "Đang sử dụng"},
  {ma: "K", ten: "Ngưng sử dụng"},
];
//Form
export interface IFormTimKiemDanhMucHangXeFieldsConfig {
  ma: IFormInput;
  ten: IFormInput;
  trang_thai: IFormInput;
  nv: IFormInput;
}

export const FormTimKiemDanhMucHangXe: IFormTimKiemDanhMucHangXeFieldsConfig = {
  ma: {
    component: "input",
    name: "ma",
    label: "Mã hãng xe",
    placeholder: "Chọn mã hãng xe",
  },
  ten: {
    component: "input",
    name: "ten",
    label: "Tên",
    placeholder: "Chọn tên",
  },
  trang_thai: {
    component: "select",
    name: "trang_thai",
    label: "Trạng thái",
    placeholder: "Chọn trạng thái",
  },
  nv: {
    component: "select",
    name: "nv",
    label: "Nghiệp vụ",
    placeholder: "Chọn nghiệp vụ",
  },
};
// keyof: return ra kay của interface TableBoMaNguyenTeColumDataType;
export type TableDanhMucHangXeDataIndex = keyof TableDanhMucHangXeDataType;
// defaultFormValue tìm kiếm phân trang bộ mã nguyên tệ
export const defaultFormValue: ReactQuery.ITimKiemPhanTrangDanhMucHangXeParams = {
  ma: "",
  ten: "",
  trang_thai: "",
  trang: 1,
  so_dong: 13,
};
export const TRANG_THAI_CHI_TIET_DANH_MUC_HANG_XE = [
  {ten: "Đang sử dụng", ma: "D"},
  {ten: "Ngưng sử dụng", ma: "K"},
];

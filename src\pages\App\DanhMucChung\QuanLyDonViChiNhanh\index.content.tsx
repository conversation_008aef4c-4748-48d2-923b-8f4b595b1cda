import {PlusCircleOutlined, SearchOutlined} from "@ant-design/icons";
import {IFormInput, ReactQuery} from "@src/@types";
import {Button, FormInput, Highlighter, TableFilterDropdown} from "@src/components";
import {COLOR_PALETTE, ID_PAGE} from "@src/constants";
import {defaultPaginationTableProps, defaultTableProps, fillRowTableEmpty, useTableHeight} from "@src/hooks";
import {Col, Form, InputRef, Row, Table, TableColumnType, Tag} from "antd";
import {FilterDropdownProps} from "antd/es/table/interface";
import {isEqual} from "lodash";
import React, {memo, useCallback, useMemo, useRef, useState} from "react";
import {ModalChiTietDonViChiNhanh} from "./Component";
import {IModalChiTietDonViChiNhanhRef} from "./Component/Constant";
import {
  FormTimKiemQuanLyDonViChiNhanh,
  radioItemTrangThaiDonViChiNhanhSelect,
  radioItemTrangThaiDonViChiNhanhTable,
  tableDonViChiNhanhColumn,
  TableDonViChiNhanhColumnDataIndex,
  TableDonViChiNhanhColumnDataType,
} from "./index.configs";
import {useQuanLyDonViChiNhanhContext} from "./index.context";
import "./index.default.scss";
import {useTableStyle} from "./index.styles";

const {ten, mst, trang_thai} = FormTimKiemQuanLyDonViChiNhanh;

const QuanLyDonViChiNhanhContent: React.FC = memo(() => {
  const {listDonViChiNhanh, loading, filterParams, getChiTietDonViChiNhanh, setFilterParams, tongSoDong} = useQuanLyDonViChiNhanhContext();
  const tableHeight = useTableHeight(["footer", "header"]);
  const tableStyle = useTableStyle();

  const refModalChiTietDonViChiNhanh = useRef<IModalChiTietDonViChiNhanhRef>(null);
  const refSearchInputTable = useRef<InputRef>(null);
  const [searchTextTable, setSearchTextTable] = useState<string>(""); //lưu textsearch để hiển thị vào cell
  const [searchedColumn, setSearchedColumn] = useState<TableDonViChiNhanhColumnDataIndex | "">(""); //key column đang được search

  const dataTableListDonViChiNhanh = useMemo<Array<TableDonViChiNhanhColumnDataType>>(() => {
    try {
      const tableData = listDonViChiNhanh.map(itemDonViChiNhanh => {
        return {
          key: itemDonViChiNhanh.ma,
          ma: itemDonViChiNhanh.ma,
          sott: itemDonViChiNhanh.sott,
          ten: itemDonViChiNhanh.ten,
          mst: itemDonViChiNhanh.mst,
          dchi: itemDonViChiNhanh.dchi,
          dthoai: itemDonViChiNhanh.dthoai,

          ngay_tao: itemDonViChiNhanh.ngay_tao,
          nguoi_tao: itemDonViChiNhanh.nguoi_tao,
          ngay_cap_nhat: itemDonViChiNhanh.ngay_cap_nhat,
          nguoi_cap_nhat: itemDonViChiNhanh.nguoi_cap_nhat,
          trang_thai_ten: itemDonViChiNhanh.trang_thai_ten,
          ma_doi_tac: itemDonViChiNhanh.ma_doi_tac,
        };
      });
      const arrEmptyRow: Array<TableDonViChiNhanhColumnDataType> = fillRowTableEmpty(tableData.length, defaultPaginationTableProps.defaultPageSize);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      console.log("dataTableListDonViChiNhanh error", error);
      return [];
    }
  }, [listDonViChiNhanh]);

  const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: TableDonViChiNhanhColumnDataIndex) => {
    confirm();
    setSearchTextTable(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);

  const handleReset = useCallback(
    (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: TableDonViChiNhanhColumnDataIndex) => {
      clearFilters();
      setSearchTextTable("");
      handleSearch([""], confirm, dataIndex);
    },
    [handleSearch],
  );

  const onSearchApi = (values: ReactQuery.ILayDanhSachDonViChiNhanhParams) => {
    setFilterParams({...filterParams, ...values, trang: 1, so_dong: defaultPaginationTableProps.defaultPageSize});
  };

  // RENDER

  const renderFormInputColum = (props: IFormInput, span = 4) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  const renderHeaderTableQuanLyDonViChiNhanh = () => {
    return (
      <>
        <Form
          // initialValues={{trang_thai: undefined}}
          layout="vertical" //Tất cả các input nằm trên 1 dòng
          onFinish={onSearchApi}
          className="[&_.ant-form-item]:mb-0">
          {/* <div className="flex w-full flex-wrap items-end gap-4"> */}
          <Row gutter={16} align={"bottom"}>
            {renderFormInputColum(ten)}
            {renderFormInputColum(mst)}
            {renderFormInputColum({...trang_thai, options: radioItemTrangThaiDonViChiNhanhSelect})}
            <Col span={3}>
              <Button type="primary" htmlType="submit" loading={loading} icon={<SearchOutlined />} block>
                Tìm kiếm
              </Button>
            </Col>
            <Col span={3}>
              <Button type="primary" block icon={<PlusCircleOutlined />} onClick={() => refModalChiTietDonViChiNhanh.current?.open()}>
                Tạo mới
              </Button>
            </Col>
          </Row>
          {/* </div> */}
        </Form>
      </>
    );
  };

  // dataIndex : là các key của column, title : tiêu đề của column
  const getColumnSearchProps = (dataIndex: TableDonViChiNhanhColumnDataIndex, title: string): TableColumnType<TableDonViChiNhanhColumnDataType> => ({
    /**
     *  filterDropdown: UI tuỳ chỉnh của ô filter (component render ra trong dropdown filter)
     * @param param0
     * @returns
     * dataIndex !== "trang_thai_ten"
     */
    filterDropdown:
      dataIndex !== "trang_thai_ten"
        ? filterDropdownParams => (
            <TableFilterDropdown
              ref={refSearchInputTable}
              title={title}
              dataIndex={dataIndex}
              handleSearch={(selectedKeys, confirm, dataIndex) => handleSearch(selectedKeys, confirm, dataIndex)}
              handleReset={(clearFilters, confirm, dataIndex) => clearFilters && handleReset(clearFilters, confirm, dataIndex)}
              {...filterDropdownParams}
            />
          )
        : undefined,
    /**
     * onFilter: hàm filter dữ liệu tại client-side (bắt buộc phải khai báo để filter hoạt động)
     * do hàm onFilter luôn phải trả về boolean, nên thêm || false vào để k bị lỗi
     * @param value : giá trị filter người dùng nhập vào
     * @param record : từng bản ghi trong dataSource
     * @returns
     */
    onFilter: (value, record) => {
      return (
        record[dataIndex]
          ?.toString()
          .toLowerCase()
          .includes((value as string).toLowerCase()) || false
      );
    },
    /**
     * filterDropdownProps: thuộc tính tuì chỉnh hành vi dropdown
     */
    filterDropdownProps: {
      // onOpenChange: callback khi dropdown mở/ tắt thì sẽ focus vào input
      onOpenChange(open) {
        if (open) setTimeout(() => refSearchInputTable.current?.select(), 100); // Focus/ select input khi mở filter
      },
    },
    filters: dataIndex === "trang_thai_ten" ? radioItemTrangThaiDonViChiNhanhTable : undefined,
    render: (
      text,
      record,
      //  index
    ) => {
      if (dataIndex === "trang_thai_ten") {
        const color = text === "Đang sử dụng" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];
        if (record.key.toString().includes("empty")) return "";
        return (
          <Tag color={color} className="text-[11px]">
            {text}
          </Tag>
        );
      }
      return searchedColumn === dataIndex ? (
        <Highlighter searchWords={[searchTextTable]} textToHighlight={text ? text.toString() : ""} />
      ) : text !== undefined ? (
        text
      ) : (
        <Tag color={"transparent"} className="!text-white text-[11px]">
          {"\u00A0"}
        </Tag>
      );
    },
  });

  return (
    <div id={ID_PAGE.QUAN_LY_DON_VI_CHI_NHANH}>
      <Table<TableDonViChiNhanhColumnDataType>
        {...defaultTableProps}
        dataSource={dataTableListDonViChiNhanh} //mảng dữ liệu record được hiển thị
        columns={
          tableDonViChiNhanhColumn?.map(item => {
            //Nếu là cột stt thì không hiển thị search
            return {...item, ...(item.key === "sott" ? {} : getColumnSearchProps(item.key as keyof TableDonViChiNhanhColumnDataType, item.title as string))};
          }) || []
        } //định nghĩa cột của table
        loading={loading} //hiển thị loading khi đang gọi API để loading data
        pagination={{
          ...defaultPaginationTableProps,
          total: tongSoDong,
          onChange: (page, pageSize) => {
            setFilterParams({...filterParams, trang: page, so_dong: pageSize});
          },
        }}
        title={renderHeaderTableQuanLyDonViChiNhanh}
        onRow={record => {
          return {
            style: {cursor: loading ? "progress" : "pointer"}, //  thể hiện vị trí con trỏ là button(👈)
            onClick: async () => {
              const response = await getChiTietDonViChiNhanh(record as ReactQuery.IChiTietDonViChiNhanhParams);
              if (response?.ma) refModalChiTietDonViChiNhanh.current?.open(response);
            }, // click row
          };
        }}
      />
      <ModalChiTietDonViChiNhanh ref={refModalChiTietDonViChiNhanh} listDonViChiNhanh={listDonViChiNhanh} />
    </div>
  );
}, isEqual);

QuanLyDonViChiNhanhContent.displayName = "QuanLyDonViChiNhanhContent";

export default QuanLyDonViChiNhanhContent;

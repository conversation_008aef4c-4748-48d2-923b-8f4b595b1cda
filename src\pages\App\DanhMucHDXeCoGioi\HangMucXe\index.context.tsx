import {createContext, useContext} from "react";

import {IHangMucXeProvider} from "./index.model";

/**
 * Context cho phép các component con truy cập state và functions mà không cần prop drilling
 */
const HangMucXeContext = createContext<IHangMucXeProvider>({
  // ===== DATA STATES - TRẠNG THÁI DỮ LIỆU MẶC ĐỊNH =====
  listHangMucXe: [], 
  listNhomHangMucXe: [], 
  loading: false, 
  tongSoDong: 0, 
  
  // ===== FILTER PARAMETERS - THAM SỐ LỌC MẶC ĐỊNH =====
  filterParams: {
    ma: "",
    ten: "", 
    nv: "",
    loai: "", 
    nhom: "",
    vi_tri: "", 
    trang_thai: "", 
    trang: 1, 
    so_dong: 13, 
  },
  
  // ===== API FUNCTIONS - CÁC HÀM GỌI API MẶC ĐỊNH (EMPTY) =====
  searchHangMucXe: async () => {}, 
  getChiTietHangMucXe: async () => ({} as CommonExecute.Execute.IHangMucXe), // Lấy chi tiết
  capNhatChiTietHangMucXe: async () => {},
  getListNhomHangMucXe: async () => {}, // Lấy danh sách nhóm hạng mục xe

  
  setFilterParams: () => {}, // Cập nhật filter params
});

/**
 * ===== CUSTOM HOOK - HOOK ĐỂ CÁC COMPONENT CON CÓ THỂ DỄ DÀNG ACCESS CONTEXT =====
 * Hook này giúp đơn giản hóa việc sử dụng context trong các component
 */
export const useHangMucXeContext = () => useContext(HangMucXeContext);

export default HangMucXeContext;

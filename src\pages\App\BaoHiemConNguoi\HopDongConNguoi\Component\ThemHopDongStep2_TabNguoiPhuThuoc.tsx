import {Highlighter, TableFilterDropdown} from "@src/components";
import {defaultTableProps, fillRowTableEmpty} from "@src/hooks";
import {InputRef, Table, TableColumnType} from "antd";
import {FilterDropdownProps} from "antd/es/table/interface";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState} from "react";
import {useHopDongConNguoiContext} from "../index.context";
import {
  IModalChiTietNguoiPhuThuocRef,
  tableNguoiPhuThuocColumn,
  TableNguoiPhuThuocColumnDataIndex,
  TableNguoiPhuThuocColumnDataType,
  ThemHopDongStep2_TabNguoiPhuThuocProps,
  ThemHopDongStep2_TabNguoiPhuThuocRef,
} from "./Constant";
import {ModalChiTietNguoiPhuThuoc} from "./ThemHopDongStep2_ModalChiTietNguoiPhuThuoc";

const TabNguoiPhuThuocComponent = forwardRef<ThemHopDongStep2_TabNguoiPhuThuocRef, ThemHopDongStep2_TabNguoiPhuThuocProps>(
  ({tabActive, chiTietHopDong}: ThemHopDongStep2_TabNguoiPhuThuocProps, ref) => {
    useImperativeHandle(ref, () => ({
      getListNguoiPhuThuoc: () => {
        return listNguoiPhuThuoc;
      },
      openModalChiTietNguoiPhuThuoc: () => refModalChiTietNguoiPhuThuoc.current?.open(),
    }));
    const {chiTietNguoiDuocBaoHiem, loadingQuyenLoi, windowHeight, getListNguoiPhuThuoc, getChiTietNguoiPhuThuoc} = useHopDongConNguoiContext();

    const [listNguoiPhuThuoc, setListNguoiPhuThuoc] = useState<CommonExecute.Execute.IChiTietNguoiPhuThuoc[]>([]); // LIST QUYỀN LỢI ĐƯỢC HIỂN THỊ TRÊN TABLE
    console.log("listNguoiPhuThuoc", listNguoiPhuThuoc);
    const refModalChiTietNguoiPhuThuoc = useRef<IModalChiTietNguoiPhuThuocRef>(null);

    const refSearchInputTable = useRef<InputRef>(null);
    const [searchTextTable, setSearchTextTable] = useState<string>(""); //lưu textsearch để hiển thị vào cell
    const [searchedColumn, setSearchedColumn] = useState<TableNguoiPhuThuocColumnDataIndex | "">(""); //key column đang được search

    const dataTableListNguoiPhuThuoc = useMemo<Array<TableNguoiPhuThuocColumnDataType>>(() => {
      try {
        const tableData = listNguoiPhuThuoc.map((itemNguoiPhuThuoc, index) => {
          return {
            key: index + 1,
            sott: index + 1,
            bt: itemNguoiPhuThuoc.bt,
            ten: itemNguoiPhuThuoc.ten,
            ngay_sinh: itemNguoiPhuThuoc.ngay_sinh,
            gioi_tinh_ten: itemNguoiPhuThuoc.gioi_tinh_ten,
            so_cmt: itemNguoiPhuThuoc.so_cmt,
            moi_qhe_ten: itemNguoiPhuThuoc.moi_qhe_ten,
            dthoai: itemNguoiPhuThuoc.dthoai,
            email: itemNguoiPhuThuoc.email,
            so_id: itemNguoiPhuThuoc.so_id,
            so_id_dt: itemNguoiPhuThuoc.so_id_dt,
          };
        });
        /* fill emptyRow động theo screen màn hình
         * (windowHeight / 110) * 80 : chuyển từ windowHeight -> 80vw(view width)
         * 44 : footer modal height,
         * 26 : header modal height,
         * 54 : table header
         * 20 : bonus thêm cho chuẩn
         */
        const tableHeight = (windowHeight / 100) * 80 - 44 - 26 - 54 - 50;
        let emptyRow = 0;
        if (tableHeight > 0) {
          emptyRow = Math.round(tableHeight / 25); //29 là height của 1 row
          if (emptyRow < 10) emptyRow = 10;
        }
        const arrEmptyRow: Array<TableNguoiPhuThuocColumnDataType> = fillRowTableEmpty(tableData.length, emptyRow);
        return [...tableData, ...arrEmptyRow];
      } catch (error) {
        console.log("dataTableListDoiTac error", error);
        return [];
      }
    }, [listNguoiPhuThuoc, windowHeight]);

    useEffect(() => {
      if (tabActive === "4") {
        initData();
      }
    }, [chiTietNguoiDuocBaoHiem, tabActive]);

    const initData = () => {
      initListNguoiPhuThuoc();
    };
    const initListNguoiPhuThuoc = async () => {
      try {
        const response = await getListNguoiPhuThuoc({
          so_id: chiTietNguoiDuocBaoHiem?.so_id,
          so_id_dt: +(chiTietNguoiDuocBaoHiem?.so_id_dt || 0),
        });
        console.log("response", response);
        if (response) setListNguoiPhuThuoc(response);
      } catch (error) {}
    };

    const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: TableNguoiPhuThuocColumnDataIndex) => {
      confirm();
      setSearchTextTable(selectedKeys[0]);
      setSearchedColumn(dataIndex);
    }, []);

    const handleReset = useCallback(
      (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: TableNguoiPhuThuocColumnDataIndex) => {
        clearFilters();
        setSearchTextTable("");
        handleSearch([""], confirm, dataIndex);
      },
      [handleSearch],
    );

    // dataIndex : là các key của column, title : tiêu đề của column
    const getColumnSearchProps = (dataIndex: TableNguoiPhuThuocColumnDataIndex, title: string): TableColumnType<TableNguoiPhuThuocColumnDataType> => ({
      /**
       *  filterDropdown: UI tuỳ chỉnh của ô filter (component render ra trong dropdown filter)
       * @param param0
       * @returns
       * dataIndex !== "trang_thai_ten"
       */
      filterDropdown: filterDropdownParams => (
        <TableFilterDropdown
          ref={refSearchInputTable}
          title={title}
          dataIndex={dataIndex}
          handleSearch={(selectedKeys, confirm, dataIndex) => handleSearch(selectedKeys, confirm, dataIndex)}
          handleReset={(clearFilters, confirm, dataIndex) => clearFilters && handleReset(clearFilters, confirm, dataIndex)}
          {...filterDropdownParams}
        />
      ),

      /**
       * onFilter: hàm filter dữ liệu tại client-side (bắt buộc phải khai báo để filter hoạt động)
       * do hàm onFilter luôn phải trả về boolean, nên thêm || false vào để k bị lỗi
       * @param value : giá trị filter người dùng nhập vào
       * @param record : từng bản ghi trong dataSource
       * @returns
       */
      onFilter: (value, record) => {
        return (
          record[dataIndex]
            ?.toString()
            .toLowerCase()
            .includes((value as string).toLowerCase()) || false
        );
      },
      render: (
        text,
        record,
        //  index
      ) => {
        return searchedColumn === dataIndex ? <Highlighter searchWords={[searchTextTable]} textToHighlight={text ? text.toString() : ""} /> : text !== undefined ? text : "\u00A0";
      },
    });

    return (
      <div>
        <Table<TableNguoiPhuThuocColumnDataType>
          {...defaultTableProps}
          className="table-nguoi-phu-thuoc"
          dataSource={dataTableListNguoiPhuThuoc} //mảng dữ liệu record được hiển thị
          columns={
            tableNguoiPhuThuocColumn?.map(item => {
              //Nếu là cột sott thì không hiển thị search
              return {...item, ...(item.key === "sott" ? {} : getColumnSearchProps(item.key as keyof TableNguoiPhuThuocColumnDataType, item.title as string))};
            }) || []
          } //định nghĩa cột của table
          loading={loadingQuyenLoi} //hiển thị loading khi đang gọi API để loading data
          onRow={record => {
            return {
              style: {cursor: loadingQuyenLoi ? "progress" : "pointer"}, //  thể hiện vị trí con trỏ là button(👈)
              onClick: async () => {
                if (record.key.toString().includes("empty")) return;
                const response = await getChiTietNguoiPhuThuoc(record as CommonExecute.Execute.IChiTietNguoiPhuThuoc);
                if (response?.bt) refModalChiTietNguoiPhuThuoc.current?.open(response);
              }, // click row
            };
          }}
          pagination={false}
          sticky
          title={() => <></>}
        />
        <ModalChiTietNguoiPhuThuoc ref={refModalChiTietNguoiPhuThuoc} chiTietHopDong={chiTietHopDong} listNguoiPhuThuoc={listNguoiPhuThuoc} initListNguoiPhuThuoc={initListNguoiPhuThuoc} />
      </div>
    );
  },
);

TabNguoiPhuThuocComponent.displayName = "TabNguoiPhuThuocComponent";
export const ThemHopDongStep2_TabNguoiPhuThuoc = memo(TabNguoiPhuThuocComponent, isEqual);

import {ClearOutlined, PlusCircleOutlined, SearchOutlined} from "@ant-design/icons";
import {IFormInput, ReactQuery} from "@src/@types";
import {Button, FormInput, Highlighter, TableFilterDropdown} from "@src/components";
import {COLOR_PALETTE, ID_PAGE} from "@src/constants";
import {defaultPaginationTableProps, defaultTableProps, fillRowTableEmpty} from "@src/hooks";
import {Col, Form, Input, InputRef, Row, Table, TableColumnType, Tag, Tooltip} from "antd";
import {createStyles} from "antd-style";
import {FilterDropdownProps} from "antd/es/table/interface";
import {isEqual} from "lodash";
import React, {memo, useCallback, useMemo, useRef, useState} from "react";
import {ModalChiTietTaiKhoanDonViThuHo} from "./Component";
import {IModalChiTietTaiKhoanDonViThuHoRef} from "./Component/Constant";
import {FormTimKiemQuanLyTaiKhoanDonViThuHo, radioItemTrangThaiTaiKhoanDonViThuHoSelect, radioItemTrangThaiTaiKhoanDonViThuHoTable, tableTaiKhoanDonViThuHoColumn, TableTaiKhoanDonViThuHoColumnDataIndex, TableTaiKhoanDonViThuHoColumnDataType} from "./index.configs";
import {useQuanLyTaiKhoanDonViThuHoContext} from "./index.context";
import "./index.default.scss";

const {ma,ten,ma_dvi,trang_thai} = FormTimKiemQuanLyTaiKhoanDonViThuHo;

const QuanLyTaiKhoanDonViThuHoContent: React.FC = memo(() => {
  const {listTaiKhoanDonViThuHo,listDonVi, loading, tongSoDong, filterParams, getChiTietTaiKhoanDonViThuHo, setFilterParams} = useQuanLyTaiKhoanDonViThuHoContext();
  const refModalChiTietTaiKhoanDonViThuHo = useRef<IModalChiTietTaiKhoanDonViThuHoRef>(null);
  const refSearchInputTable = useRef<InputRef>(null);
  const [searchTextTable, setSearchTextTable] = useState<string>(""); //lưu textsearch để hiển thị vào cell
  const [searchedColumn, setSearchedColumn] = useState<TableTaiKhoanDonViThuHoColumnDataIndex | "">(""); //key column đang được search

  const dropdownOptionsDonVi = useMemo(() => {
    if (!listDonVi || !Array.isArray(listDonVi)) {
      return [{ma: "", ten: "Chọn đơn vị"}];
    }
    const allOption = [{ma: "", ten: "Tất cả"}];
    const validProvinces = listDonVi
      .filter(item => {
        const hasValidData = item && item.ma && item.ten;
        if (!hasValidData) return false;
        
        const trangThai = item.trang_thai?.toString();
        const isActive = trangThai === "1" || trangThai === "D" || trangThai === "Đang sử dụng" || !trangThai;
        
        return hasValidData && isActive;
      })
      .map(item => ({
        ma: item.ma?.toString() || "",
        ten: item.ten?.toString() || "",
      }));
    
    return [...allOption, ...validProvinces];
  }, [listDonVi]);

  const dataTableListTaiKhoanDonViThuHo = useMemo<Array<TableTaiKhoanDonViThuHoColumnDataType>>(() => {
    try {
      const tableData = listTaiKhoanDonViThuHo.map((itemTaiKhoanDonViThuHo, index) => {
        return {
          key: itemTaiKhoanDonViThuHo.ma || `row-${index}`, // Đảm bảo key luôn là string
          sott: itemTaiKhoanDonViThuHo.sott,
          ma: itemTaiKhoanDonViThuHo.ma,
          ten: itemTaiKhoanDonViThuHo.ten,
          ma_dvi: itemTaiKhoanDonViThuHo.ma_dvi,
          mat_khau: itemTaiKhoanDonViThuHo.mat_khau,
          email: itemTaiKhoanDonViThuHo.email,
          dthoai: itemTaiKhoanDonViThuHo.dthoai,
          ngay_hl: itemTaiKhoanDonViThuHo.ngay_hl,
          ngay_kt: itemTaiKhoanDonViThuHo.ngay_kt,
          trang_thai: itemTaiKhoanDonViThuHo.trang_thai,
          ngay_tao: itemTaiKhoanDonViThuHo.ngay_tao,
          nguoi_tao: itemTaiKhoanDonViThuHo.nguoi_tao,
          ngay_cap_nhat: itemTaiKhoanDonViThuHo.ngay_cap_nhat,
          nguoi_cap_nhat: itemTaiKhoanDonViThuHo.nguoi_cap_nhat,
          trang_thai_ten: itemTaiKhoanDonViThuHo.trang_thai_ten,
        };
      });
      
      const arrEmptyRow: Array<TableTaiKhoanDonViThuHoColumnDataType> = fillRowTableEmpty(tableData.length, defaultPaginationTableProps.defaultPageSize);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      console.log("dataTableListTaiKhoanDonViThuHo error", error);
      return [];
    }
  }, [listTaiKhoanDonViThuHo]);

  const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: TableTaiKhoanDonViThuHoColumnDataIndex) => {
    confirm();
    setSearchTextTable(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);

  const handleReset = useCallback(
    (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: TableTaiKhoanDonViThuHoColumnDataIndex) => {
      clearFilters();
      setSearchTextTable("");
      handleSearch([""], confirm, dataIndex);
    },
    [handleSearch],
  );
  // chức năng search
  const onSearchApi = (values: ReactQuery.ITimKiemPhanTrangTaiKhoanDonViThuHoParams) => {
    setFilterParams({...filterParams, ...values, trang: 1, so_dong: defaultPaginationTableProps.defaultPageSize});
  };

  // RENDER
  const renderFormInputColum = (props: IFormInput, span = 4) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );

  const renderHeaderTableQuanLyTaiKhoanDonViThuHo = () => {
    return (
      <>
        <Form initialValues={{}} layout="vertical" className="[&_.ant-form-item]:mb-0" onFinish={onSearchApi}>
          <Row gutter={16} align={"bottom"}>
            {renderFormInputColum(ma)}
            {renderFormInputColum(ten)}
            {renderFormInputColum({...ma_dvi, options: dropdownOptionsDonVi})}
            {renderFormInputColum({...trang_thai, options: radioItemTrangThaiTaiKhoanDonViThuHoSelect})}
            <Col span={2}>
              <Button type="primary" htmlType="submit" loading={loading} icon={<SearchOutlined />} block>
                Tìm kiếm
              </Button>
            </Col>
            <Col span={2}>
              <Button
                type="primary"
                block
                icon={<PlusCircleOutlined />}
                onClick={() => {
                  refModalChiTietTaiKhoanDonViThuHo.current?.open();
                }}>
                Tạo mới
              </Button>
            </Col>
          </Row>
        </Form>
      </>
    );
  };

  // dataIndex : là các key của column, title : tiêu đề của column
  const getColumnSearchProps = (dataIndex: TableTaiKhoanDonViThuHoColumnDataIndex, title: string): TableColumnType<TableTaiKhoanDonViThuHoColumnDataType> => ({
    /**
     *  filterDropdown: UI tuỳ chỉnh của ô filter (component render ra trong dropdown filter)
     * @param param0
     * @returns
     * dataIndex !== "trang_thai_ten"
     */
    filterDropdown:
      dataIndex !== "trang_thai_ten"
        ? filterDropdownParams => (
            <TableFilterDropdown
              ref={refSearchInputTable}
              title={title}
              dataIndex={dataIndex}
              handleSearch={(selectedKeys, confirm, dataIndex) => handleSearch(selectedKeys, confirm, dataIndex)}
              handleReset={(clearFilters, confirm, dataIndex) => clearFilters && handleReset(clearFilters, confirm, dataIndex)}
              {...filterDropdownParams}
            />
          )
        : undefined,
    /**
     * onFilter: hàm filter dữ liệu tại client-side (bắt buộc phải khai báo để filter hoạt động)
     * do hàm onFilter luôn phải trả về boolean, nên thêm || false vào để k bị lỗi
     * @param value : giá trị filter người dùng nhập vào
     * @param record : từng bản ghi trong dataSource
     * @returns
     */
    onFilter: (value, record) => {
      return (
        record[dataIndex]
          ?.toString()
          .toLowerCase()
          .includes((value as string).toLowerCase()) || false
      );
    },
    /**
     * filterDropdownProps: thuộc tính tuì chỉnh hành vi dropdown
     */
    filterDropdownProps: {
      // onOpenChange: callback khi dropdown mở/ tắt thì sẽ focus vào input
      onOpenChange(open) {
        if (open) {
          setTimeout(() => refSearchInputTable.current?.select(), 100); // Focus/ select input khi mở filter
        }
      },
    },
    filters: dataIndex === "trang_thai_ten" ? radioItemTrangThaiTaiKhoanDonViThuHoTable : undefined,
    render: (
      text,
      record,
      //  index
    ) => {
      if (dataIndex === "trang_thai_ten") {
        const color = text === "Đang sử dụng" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];
        if (record.key.toString().includes("empty")) return "";
        return (
          <Tag color={color} className="text-[11px]">
            {text}
          </Tag>
        );
      }
      return searchedColumn === dataIndex ? (
        <Highlighter searchWords={[searchTextTable]} textToHighlight={text ? text.toString() : ""} />
      ) : text !== undefined ? (
        text
      ) : (
        <Tag color={"transparent"} className="!text-white text-[11px]">
          {"\u00A0"}
        </Tag>
      );
    },
  });

  return (
    <div id={ID_PAGE.TAI_KHOAN_DON_VI_THU_HO} className="[&_.ant-space]:w-full">
      <Table<TableTaiKhoanDonViThuHoColumnDataType>
        {...defaultTableProps}
        dataSource={dataTableListTaiKhoanDonViThuHo} //mảng dữ liệu record được hiển thị
        columns={
          tableTaiKhoanDonViThuHoColumn?.map(item => {
            //Nếu là cột stt thì không hiển thị search
            return {...item, ...(item.key === "sott" ? {} : getColumnSearchProps(item.key as keyof TableTaiKhoanDonViThuHoColumnDataType, item.title as string))};
          }) || []
        } //định nghĩa cột của table
        loading={loading} //hiển thị loading khi đang gọi API để loading data
        pagination={{
          ...defaultPaginationTableProps,
          total: tongSoDong,
          onChange: (page, pageSize) => {
            setFilterParams({...filterParams, trang: page, so_dong: pageSize});
          },
        }}
        title={renderHeaderTableQuanLyTaiKhoanDonViThuHo}
        onRow={record => {
          return {
            style: {cursor: loading ? "progress" : "pointer"}, 
            onClick: async () => {
              
              if (record.key.toString().includes("empty")) {
                return;
              }              
              const response = await getChiTietTaiKhoanDonViThuHo(record);
              
              
              if (response?.ma) {
                refModalChiTietTaiKhoanDonViThuHo.current?.open(response);
              } else {
                console.log("[TaiKhoanDonViThuHo] Không có response.ma, không mở modal");
              }
            }, // click row
          };
        }}
      />
      <ModalChiTietTaiKhoanDonViThuHo ref={refModalChiTietTaiKhoanDonViThuHo} listTaiKhoanDonViThuHo={listTaiKhoanDonViThuHo} />
    </div>
  );
}, isEqual);

QuanLyTaiKhoanDonViThuHoContent.displayName = "QuanLyTaiKhoanDonViThuHoContent";

export default QuanLyTaiKhoanDonViThuHoContent;

const antCls = ".ant";
const useStyle = createStyles(({css}) => {
  return {
    customTable: css`
      ${antCls}-table {
        ${antCls}-table-container {
          ${antCls}-table-body,
          ${antCls}-table-content {
            scrollbar-width: thin;
            scrollbar-color: #eaeaea transparent;
            scrollbar-gutter: stable;
          }
        }
      }
    `,
  };
});

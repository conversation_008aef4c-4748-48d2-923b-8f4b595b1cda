export interface ChiTietDonViThuHoProps {
  listDonViThuHo: Array<CommonExecute.Execute.IDonViThuHo>;
}

//định nghĩa các hàm, biến mà modal sẽ expose để component bên ngoài sử dụng thông qua ref
export interface IModalChiTietDonViThuHoRef {
  open: (data?: CommonExecute.Execute.IDonViThuHo) => void;
  close: () => void;
}

export interface IModalChonQRCodeRef {
  open: (data?: CommonExecute.Execute.IDonViThuHo) => void;
  close: () => void;
}
export interface QRCodeProps {
  chiTietDonViThuHo: CommonExecute.Execute.IDonViThuHo | null;
  onClickChonFileSuccess?: (fileSelected: File.GetFolder.IGetFolder[]) => void;
}

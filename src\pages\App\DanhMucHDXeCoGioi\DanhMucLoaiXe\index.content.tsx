import React, {memo, useCallback, useMemo, useRef, useState} from "react";
import {Col, Form, Input, InputRef, Row, Table, TableColumnType, Tag, Tooltip} from "antd";
import {FilterDropdownProps} from "antd/es/table/interface";
import {PlusCircleOutlined, SearchOutlined, ClearOutlined} from "@ant-design/icons";
import {isEqual} from "lodash";

import {Button, FormInput, Highlighter} from "@src/components";
import {COLOR_PALETTE, ID_PAGE} from "@src/constants";
import {IFormInput, ReactQuery} from "@src/@types";
import {defaultPaginationTableProps, defaultTableProps, fillRowTableEmpty} from "@src/hooks";

import {useDanhMucLoaiXeContext} from "./index.context";
import {FormTimKiemDanhMucLoaiXe, getColumns, TableDanhMucLoaiXeDataType, TRANG_THAI_LOAI_XE, NGHIEP_VU_TIM_KIEM, defaultFormValue, trangThaiOptions} from "./index.configs";
import {ModalChiTietDanhMucLoaiXe, IModalChiTietDanhMucLoaiXeRef} from "./Component";
import "./index.default.scss";

type DataIndex = keyof TableDanhMucLoaiXeDataType;

const {ma, ten, trang_thai} = FormTimKiemDanhMucLoaiXe; //thêm tìm kiếm nv ở đây

const DanhMucLoaiXeContent: React.FC = memo(() => {
  const {danhSachDanhMucLoaiXe, loading, tongSoDong, layDanhSachDanhMucLoaiXe, layChiTietDanhMucLoaiXe, onUpdateDanhMucLoaiXe} = useDanhMucLoaiXeContext();

  // ===== STATE MANAGEMENT =====
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(13);
  const [searchText, setSearchText] = useState("");
  const [searchedColumn, setSearchedColumn] = useState<DataIndex | "">("");

  // State để lưu current search parameters
  const [currentSearchParams, setCurrentSearchParams] = useState<ReactQuery.ITimKiemPhanTrangDanhMucLoaiXeParams>(defaultFormValue);

  // ===== REFS =====
  const searchInput = useRef<InputRef>(null);
  const modalChiTietRef = useRef<IModalChiTietDanhMucLoaiXeRef>(null);

  /** Danh sách nghiệp vụ cho dropdown */
  const nghiepVuOptions = useMemo(() => NGHIEP_VU_TIM_KIEM, []);

  /** Data cho bảng với STT theo pagination */
  const dataTableWithSTT = useMemo(() => {
    try {
      const tableData =
        danhSachDanhMucLoaiXe?.map((item, index) => ({
          ...item,
          key: index.toString(),
          sott: (page - 1) * pageSize + index + 1,
        })) || [];

      const arrEmptyRow = fillRowTableEmpty(tableData.length, 13);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      console.log("dataTableWithSTT error", error);
      return [];
    }
  }, [danhSachDanhMucLoaiXe, page, pageSize]);

  /** Xử lý search API từ form */
  const onSearchApi = useCallback(
    (values: ReactQuery.ITimKiemPhanTrangDanhMucLoaiXeParams) => {
      const cleanedValues = {
        ma: values.ma || "", 
        ten: values.ten || "", 
        trang_thai: values.trang_thai || "", 
        nv: "", 
      };

      // Lưu lại search params hiện tại
      setCurrentSearchParams(cleanedValues);

      // Reset về trang 1 khi search mới
      setPage(1);
      layDanhSachDanhMucLoaiXe({...cleanedValues, trang: 1, so_dong: pageSize});
    },
    [layDanhSachDanhMucLoaiXe, pageSize],
  );

  /** Xử lý thay đổi trang */
  const onChangePage = useCallback(
    (newPage: number, newPageSize: number) => {
      setPage(newPage);
      setPageSize(newPageSize);
      // Sử dụng currentSearchParams thay vì defaultFormValue để giữ filter
      layDanhSachDanhMucLoaiXe({...currentSearchParams, trang: newPage, so_dong: newPageSize});
    },
    [layDanhSachDanhMucLoaiXe, currentSearchParams],
  );

  /** Xử lý search trong column */
  const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: DataIndex) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);

  /** Xử lý reset search trong column */
  const handleReset = useCallback(
    (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: DataIndex) => {
      clearFilters();
      setSearchText("");
      handleSearch([""], confirm, dataIndex);
    },
    [handleSearch],
  );

  /** Xử lý click vào row */
  const handleRowClick = useCallback(async (record: TableDanhMucLoaiXeDataType) => {
    if (record.key.toString().includes("empty")) return;
    
    try {
      //cặp khóa chính (ma, nv)
      if (!record.ma || !record.nv) {
        console.log("Thiếu thông tin khóa chính", {ma: record.ma, nv: record.nv});
        return;
      }
      
      const response = await layChiTietDanhMucLoaiXe({ma: record.ma, nv: record.nv});
      if (response) {
        modalChiTietRef.current?.open(response);
      }
    } catch (error) {
      console.log("handleRowClick error", error);
    }
  }, [layChiTietDanhMucLoaiXe]);

  /** Xử lý thêm mới */
  const handleThemMoi = useCallback(() => {
    modalChiTietRef.current?.open();
  }, []);

  /** Xử lý sau khi save thành công */
  const handleAfterSave = useCallback(() => {
    // Sử dụng currentSearchParams để giữ nguyên filter hiện tại
    layDanhSachDanhMucLoaiXe({...currentSearchParams, trang: page, so_dong: pageSize});
  }, [layDanhSachDanhMucLoaiXe, currentSearchParams, page, pageSize]);

  /** Render form input column */
  const renderFormInputColumn = useCallback(
    (props: IFormInput, span = 4) => (
      <Col span={span}>
        <FormInput {...props} />
      </Col>
    ),
    [],
  );

  /** Render header search form */
  const renderHeaderTableDanhMucLoaiXe = useCallback(
    () => (
      <Form
        // initialValues={defaultFormValue}
        layout="vertical"
        className="[&_.ant-form-item]:mb-0"
        onFinish={onSearchApi}>
        <Row gutter={16} align="bottom">
          {renderFormInputColumn(ma)}
          {renderFormInputColumn(ten)}
          {/* tìm kiếm nv */}
          {/* {renderFormInputColumn({...nv, options: nghiepVuOptions})} */}
          {renderFormInputColumn({...trang_thai, options: TRANG_THAI_LOAI_XE})}
          <Col span={3}>
            <Button type="primary" htmlType="submit" loading={loading} icon={<SearchOutlined />} block>
              Tìm kiếm
            </Button>
          </Col>
          <Col span={3}>
            <Button type="primary" block icon={<PlusCircleOutlined />} onClick={handleThemMoi}>
              Tạo mới
            </Button>
          </Col>
        </Row>
      </Form>
    ),
    [ma, ten, trang_thai, nghiepVuOptions, loading, onSearchApi, handleThemMoi, renderFormInputColumn],
  ); //thêm tìm kiếm nv ở đây

  /** Get column search properties */
  const getColumnSearchProps = useCallback(
    (dataIndex: DataIndex, title: string): TableColumnType<TableDanhMucLoaiXeDataType> => ({
      filterDropdown:
        dataIndex !== "trang_thai_ten"
          ? ({setSelectedKeys, selectedKeys, confirm, clearFilters}) => (
              <div onKeyDown={e => e.stopPropagation()} className="flex flex-row items-center p-2">
                <Input
                  ref={searchInput}
                  placeholder={`Nhập ${title}`}
                  value={selectedKeys[0]}
                  onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                  onPressEnter={() => handleSearch(selectedKeys as string[], confirm, dataIndex)}
                  className="mr-2 flex p-2"
                />
                <Tooltip title="Tìm kiếm">
                  <Button type="primary" shape="circle" icon={<SearchOutlined />} onClick={() => handleSearch(selectedKeys as string[], confirm, dataIndex)} className="mr-2" />
                </Tooltip>
                <Tooltip title="Xoá">
                  <Button type="primary" shape="circle" icon={<ClearOutlined />} onClick={() => clearFilters && handleReset(clearFilters, confirm, dataIndex)} />
                </Tooltip>
              </div>
            )
          : undefined,

      // filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} />,

      onFilter: (value, record) => {
        const recordValue = record[dataIndex]?.toString() || "";
        const searchValue = (value as string) || "";
        return recordValue.toLowerCase().includes(searchValue.toLowerCase());
      },

      onFilterDropdownOpenChange: visible => {
        if (visible) {
          setTimeout(() => searchInput.current?.select(), 100);
        }
      },

      filters: dataIndex === "trang_thai_ten" ? trangThaiOptions.map(opt => ({text: opt.label, value: opt.label})) : undefined,

      render: (text: string, record: TableDanhMucLoaiXeDataType) => {
        // Xử lý empty rows - return &nbsp; để giữ chiều cao đồng nhất
        if (record.key.toString().includes("empty")) return <span>&nbsp;</span>;

        // Xử lý column trạng thái
        if (dataIndex === "trang_thai_ten") {
          const color = text === "Đang sử dụng" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];
          return (
            <Tag color={color} className="text-[11px]">
              {text}
            </Tag>
          );
        }

        // Xử lý highlight search
        return searchedColumn === dataIndex ? <Highlighter searchWords={[searchText]} textToHighlight={text || ""} /> : text !== undefined ? text : "\u00A0";
      },
    }),
    [handleSearch, handleReset, searchText, searchedColumn],
  );

  // ===== MAIN RENDER =====
  return (
    <div id={ID_PAGE.DANH_MUC_LOAI_XE} className="[&_.ant-space]:w-full">
      <Table<TableDanhMucLoaiXeDataType>
        {...defaultTableProps}
        dataSource={dataTableWithSTT}
        columns={
          getColumns()?.map(item => ({
            ...item,
            ...(item.key === "stt" ? {} : getColumnSearchProps(item.key as DataIndex, item.title as string)),
          })) || []
        }
        loading={loading}
        pagination={{
          ...defaultPaginationTableProps,
          total: tongSoDong,
          current: page,
          pageSize: pageSize,
          onChange: onChangePage,
          // showSizeChanger: false,
          // showQuickJumper: true,
          // showTotal: (total, range) => `${range[0]}-${range[1]} của ${total} bản ghi`,
        }}
        title={renderHeaderTableDanhMucLoaiXe}
        onRow={record => ({
          style: {cursor: loading ? "progress" : "pointer"},
          onClick: () => handleRowClick(record),
        })}
      />
      <ModalChiTietDanhMucLoaiXe ref={modalChiTietRef} onAfterSave={handleAfterSave} />
    </div>
  );
}, isEqual);

DanhMucLoaiXeContent.displayName = "DanhMucLoaiXeContent";

export default DanhMucLoaiXeContent;

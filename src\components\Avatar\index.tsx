import React, {memo} from "react";
import {Avatar as AntAvatar, AvatarProps} from "antd";
import {twMerge} from "tailwind-merge";
import {isEqual} from "lodash";

const AvatarComponent: React.FC<AvatarProps> = props => {
  const {className = "", ...etc} = props;
  return <AntAvatar className={twMerge("custom-avatar", className)} {...etc} />;
};

const Avatar = memo(AvatarComponent, isEqual);

export default Avatar;

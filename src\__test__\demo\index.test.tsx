import {describe, jest, test} from "@jest/globals";
import {axiosInstance} from "@services/axios";
// import Demo from "@src/pages/demo";
import "@src/__mocks__";
import {waitFor} from "@testing-library/react";

const mockData = {
  affenpinscher: [],
  african: [],
  appenzeller: [],
  australian: ["shepherd"],
  basenji: [],
};

jest.mock("react-query", () => ({
  useQuery: jest.fn(() => ({
    loading: false,
    data: {
      message: mockData,
      status: "success",
    },
  })),
}));

jest.mock("@services/axios");

jest.mock("@utils/env", () => ({
  env: {
    MODE: "development",
    VITE_BASE_URL: "test_base_url",
  },
}));

const mockedAxios = axiosInstance as jest.Mocked<typeof axiosInstance>;

describe("Demo page test", () => {
  test("render page list", async () => {
    mockedAxios.mockResolvedValue(mockData);
    // const renderedComponent = render(<Demo />);
    await waitFor(() => {
      // const affenpinscher = renderedComponent.getByText("affenpinscher");
      // expect(affenpinscher).toBeInTheDocument();
    });
  });
});

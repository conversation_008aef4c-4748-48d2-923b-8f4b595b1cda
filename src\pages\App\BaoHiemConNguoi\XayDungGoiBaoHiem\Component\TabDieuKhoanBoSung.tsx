import {Checkbox, Highlighter, InputCellTable, TableFilterDropdown} from "@src/components";
import {COLOR_PALETTE} from "@src/constants";
import {defaultTableProps, fillRowTableEmpty} from "@src/hooks";
import {InputRef, Table, TableColumnType} from "antd";
import {FilterDropdownProps} from "antd/es/table/interface";
import {debounce, isEqual} from "lodash";
import {forwardRef, memo, useCallback, useImperativeHandle, useMemo, useRef, useState} from "react";
import {useXayDungGoiBaoHiemContext} from "../index.context";
import ButtonPopoverCellTable from "./ButtonPopoverCellTable";
import {IModalThemQuyenLoiRef, ITabDieuKhoanBoSungRef, listKieuApDung, TabDieuKhoanBoSungProps, tableQuyenLoiColumn, TableQuyenLoiColumnDataIndex, TableQuyenLoiColumnDataType} from "./Constant";
import InputPopoverCellTable from "./InputPopoverCellTable";
import {ModalThemQuyenLoi} from "./ModalThemQuyenLoi";

const TabDieuKhoanBoSungComponent = forwardRef<ITabDieuKhoanBoSungRef, TabDieuKhoanBoSungProps>(({}: TabDieuKhoanBoSungProps, ref) => {
  useImperativeHandle(ref, () => ({
    setListDieuKhoan: (data: CommonExecute.Execute.IDieuKhoanQuyenLoiConNguoi[]) => {
      setListQuyenLoi(data);
    },
    getListDieuKhoan: () => listQuyenLoi,
    openModalThemQuyenLoi: () => {
      setIsOpenQuyenLoiTruLui(true);
    },
  }));
  //  type: "THEM_MOI_QUYEN_LOI" | "TRU_LUI_QUYEN_LOI";
  const {listNguyenTe, listQuyenLoiRoot, windowHeight} = useXayDungGoiBaoHiemContext();

  const [listQuyenLoi, setListQuyenLoi] = useState<CommonExecute.Execute.IDieuKhoanQuyenLoiConNguoi[]>([]);

  //data xử lý modal xem quyền lợi trừ lùi
  const refModalThemQuyenLoi = useRef<IModalThemQuyenLoiRef>(null);
  const [isOpenModalThemQuyenLoi, setIsOpenQuyenLoiTruLui] = useState<boolean>(false);
  const [quyenLoiDangXem, setQuyenLoiDangXem] = useState<CommonExecute.Execute.IDieuKhoanQuyenLoiConNguoi>();
  const [checkStrictly, setCheckStrictly] = useState(false);

  const refSearchInputTable = useRef<InputRef>(null);
  const [searchTextTable, setSearchTextTable] = useState<string>(""); //lưu textsearch để hiển thị vào cell
  const [searchedColumn, setSearchedColumn] = useState<TableQuyenLoiColumnDataIndex | "">(""); //key column đang được search

  const [checkboxKieuApDung, setCheckboxKieuAppDung] = useState(true);
  const [checkboxNguyenTeSTBH, setCheckboxNguyenTeSTBH] = useState(true);

  const dataTableListDieuKhoanBoSung = useMemo<Array<TableQuyenLoiColumnDataType>>(() => {
    try {
      const tableData = listQuyenLoi.map((itemQuyenLoi, index) => {
        const prefix =
          Array((itemQuyenLoi.cap || 0) * 3)
            .fill("\u00A0")
            .join("") + Array(itemQuyenLoi.cap).fill("+").join("");
        return {
          key: itemQuyenLoi.ma_qloi || index + "",
          stt: index + 1,
          ten_qloi: prefix + itemQuyenLoi.ma_qloi + " - " + itemQuyenLoi.ten_qloi,
          gh_lan_ngay: itemQuyenLoi.gh_lan_ngay,
          gh_tien_lan_ngay: itemQuyenLoi.gh_tien_lan_ngay,
          gh_tien_nam: itemQuyenLoi.gh_tien_nam,
          tgian_cho: itemQuyenLoi.tgian_cho,
          kieu_ad_ten: itemQuyenLoi.kieu_ad_ten,
          kieu_ad: itemQuyenLoi.kieu_ad,
          gh_lan_ngay_dtri: itemQuyenLoi.gh_lan_ngay_dtri,
          gh_tien_lan_ngay_dtri: itemQuyenLoi.gh_tien_lan_ngay_dtri,
          nt_tien_bh: itemQuyenLoi.nt_tien_bh,
          ma_qloi_tru_lui: itemQuyenLoi.ma_qloi_tru_lui,
          tl_dct: itemQuyenLoi.tl_dct,
          phi_bh: itemQuyenLoi.phi_bh,
          cap: itemQuyenLoi.cap,
          ma_qloi: itemQuyenLoi.ma_qloi || "",
          ghi_chu: itemQuyenLoi.ghi_chu,
        };
      });
      /* fill emptyRow động theo screen màn hình
       * (windowHeight / 110) * 78 : chuyển từ windowHeight -> 78vw(view width)
       * 44 : footer modal height,
       * 26 : header modal height,
       * 141 : form height,
       * 54 : table header
       * 20 : bonus thêm cho chuẩn
       */
      const tableHeight = (windowHeight / 100) * 78 - 44 - 26 - 141 - 54 - 20;
      let emptyRow = 0;
      if (tableHeight > 0) {
        emptyRow = Math.round(tableHeight / 29); //29 là height của 1 row
        if (emptyRow < 10) emptyRow = 10;
      }
      const arrEmptyRow: Array<TableQuyenLoiColumnDataType> = fillRowTableEmpty(tableData.length, emptyRow);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      console.log("dataTableListDieuKhoanBoSung error", error);
      return [];
    }
  }, [listQuyenLoi, windowHeight]);

  const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: TableQuyenLoiColumnDataIndex) => {
    confirm();
    setSearchTextTable(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);

  const handleReset = useCallback(
    (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: TableQuyenLoiColumnDataIndex) => {
      clearFilters();
      setSearchTextTable("");
      handleSearch([""], confirm, dataIndex);
    },
    [handleSearch],
  );

  const onChangeDataCellTableQuyenLoi = useCallback((index: number, dataIndex: string, value?: string) => {
    setListQuyenLoi(prev => {
      const next = [...prev];
      next[index] = {...next[index], [dataIndex]: value};
      return next;
    });
  }, []);
  //sử dụng debound để tranhs render table lại nhiều lần
  const deboundInputTableQuyenLoi = useMemo(() => debounce(onChangeDataCellTableQuyenLoi, 350), [onChangeDataCellTableQuyenLoi]); //cái này để xử lý delay 0,5s rồi mới gọi api search
  //set kiểu áp dụng cho tất cả list
  const onChangeAllKieuApDung = (itemSelect?: {ma?: string; ten?: string}) => {
    const listQuyenLoiTmp = listQuyenLoi;
    listQuyenLoiTmp.map(item => {
      item.kieu_ad = itemSelect?.ma || "";
      item.kieu_ad_ten = itemSelect?.ten || "";
      return item;
    });
    setListQuyenLoi([...listQuyenLoiTmp]);
  };

  //set kiểu áp dụng cho tất cả list
  const onChangeAlllNguyenTeSTBH = useCallback(
    (itemSelect?: {ma?: string; ten?: string}) => {
      const listQuyenLoiTmp = listQuyenLoi;
      listQuyenLoiTmp.map(item => {
        item.nt_tien_bh = itemSelect?.ma || "";
        return item;
      });
      setListQuyenLoi([...listQuyenLoiTmp]);
    },
    [listQuyenLoi],
  );

  const onPressLuuQuyenLoiTruLui = (listMaQuyenLoiSelect: string[], maQuyenLoiDangxem: string | undefined) => {
    const indexQuyenLoiDangXem = listQuyenLoi.findIndex(item => item.ma_qloi === maQuyenLoiDangxem); //tìm ra vị trí Row đang xử lý
    if (indexQuyenLoiDangXem >= 0) onChangeDataCellTableQuyenLoi(indexQuyenLoiDangXem, "ma_qloi_tru_lui", listMaQuyenLoiSelect.join(";"));
  };
  const onPressThemQuyenLoi = (quyenLoiDuocThem: string[]) => {
    try {
      let listQuyenLoiMoi = listQuyenLoiRoot.filter(item => quyenLoiDuocThem.includes(item.ma));
      listQuyenLoiMoi = listQuyenLoiMoi.map(itemQuyenLoiMoi => {
        const itemDataTrongListQUyenLoiCu = listQuyenLoi.find(itemQuyenLoiCu => itemQuyenLoiCu.ma_qloi === itemQuyenLoiMoi.ma);
        if (itemDataTrongListQUyenLoiCu) return {...itemQuyenLoiMoi, ...itemDataTrongListQUyenLoiCu};
        return {
          ...itemQuyenLoiMoi,
          //khởi tạo data cho các quyền lợi mới
          ten_qloi: itemQuyenLoiMoi.ten,
          ma_qloi: itemQuyenLoiMoi.ma,
          ma_qloi_ct: itemQuyenLoiMoi.ma_ct,
          ma_qloi_tru_lui: "",
          gh_tien_lan_ngay: 0,
          gh_tien_nam: 0,
          gh_tien_lan_ngay_dtri: 0,
          phi_bh: 0,
          gh_lan_ngay: 0,
          tgian_cho: 0,
          gh_lan_ngay_dtri: 0,
          tl_dct: 0,
          kieu_ad: checkboxKieuApDung && listQuyenLoi.length > 0 ? listQuyenLoi[0].kieu_ad : "",
          kieu_ad_ten: checkboxKieuApDung && listQuyenLoi.length > 0 ? listQuyenLoi[0].kieu_ad_ten : "",
          nt_tien_bh: checkboxNguyenTeSTBH && listQuyenLoi.length > 0 ? listQuyenLoi[0].nt_tien_bh : "",
          ghi_chu: "",
        };
      });
      setListQuyenLoi([...listQuyenLoiMoi] as unknown as CommonExecute.Execute.IDieuKhoanQuyenLoiConNguoi[]);
    } catch (error) {
      console.log("onPressThemQuyenLoi error", error);
    }
  };

  /* RENDER */

  const getColumnTitle = (dataIndex: string, title: any) => {
    if (dataIndex === "kieu_ad")
      return (
        <Checkbox checked={checkboxKieuApDung} onChange={event => setCheckboxKieuAppDung(event.target.checked)}>
          <span className="text-white text-[11px]">{title}</span>
        </Checkbox>
      );
    else if (dataIndex === "nt_tien_bh")
      return (
        <Checkbox checked={checkboxNguyenTeSTBH} onChange={event => setCheckboxNguyenTeSTBH(event.target.checked)}>
          <span className="text-white text-[11px]">{title}</span>
        </Checkbox>
      );
    return title;
  };

  // dataIndex : là các key của column, title : tiêu đề của column
  const getColumnSearchProps = (dataIndex: TableQuyenLoiColumnDataIndex, title: string): TableColumnType<TableQuyenLoiColumnDataType> => ({
    filterDropdown: ["ten_qloi"].includes(dataIndex)
      ? filterDropdownParams => (
          <TableFilterDropdown
            ref={refSearchInputTable}
            title={title}
            dataIndex={dataIndex}
            handleSearch={(selectedKeys, confirm, dataIndex) => handleSearch(selectedKeys, confirm, dataIndex)}
            handleReset={(clearFilters, confirm, dataIndex) => clearFilters && handleReset(clearFilters, confirm, dataIndex)}
            {...filterDropdownParams}
          />
        )
      : false,
    onFilter: (value, record) =>
      record[dataIndex]
        ?.toString()
        .toLowerCase()
        .includes((value as string).toLowerCase()) || false,
    filterDropdownProps: {
      onOpenChange(open) {
        if (open) {
          setTimeout(() => refSearchInputTable.current?.select(), 100); // Focus/ select input khi mở filter
        }
      },
    },
    title: getColumnTitle(dataIndex, title), // thêm checkbox vào header cell
    render: (text, record, index) => {
      if (record.key.includes("empty")) {
        return (
          <InputCellTable
            disabled
            component="input"
            key={`${record.ten_qloi}-${dataIndex}`}
            value={text}
            index={index}
            dataIndex={dataIndex}
            onChange={deboundInputTableQuyenLoi}
            className="!border-transparent !text-transparent"
          />
        );
      }
      if (searchedColumn === dataIndex) return <Highlighter searchWords={[searchTextTable]} textToHighlight={text ? text.toString() : ""} />;
      else if (["gh_tien_lan_ngay", "gh_tien_nam", "gh_tien_lan_ngay_dtri", "phi_bh"].includes(dataIndex)) {
        return <InputCellTable component="input-price" key={`${record.ten_qloi}-${dataIndex}`} value={text} index={index} dataIndex={dataIndex} onChange={deboundInputTableQuyenLoi} />;
      } else if (["gh_lan_ngay", "tgian_cho", "gh_lan_ngay_dtri", "tl_dct"].includes(dataIndex)) {
        return <InputCellTable component="input" key={`${record.ten_qloi}-${dataIndex}`} value={text} index={index} dataIndex={dataIndex} onChange={deboundInputTableQuyenLoi} />;
      } else if (["kieu_ad"].includes(dataIndex)) {
        return (
          <ButtonPopoverCellTable
            listData={listKieuApDung}
            btnTxt={listKieuApDung.find(item => item.ma === text)?.ten || "Chọn"}
            popOverTitle="Chọn kiểu áp dụng"
            buttonStyle={{color: !text ? COLOR_PALETTE.red[50] : COLOR_PALETTE.blue[40]}}
            maSelected={text}
            showSearch={false}
            onClickOption={itemSelected => {
              if (checkboxKieuApDung) {
                onChangeAllKieuApDung(itemSelected);
              } else {
                onChangeDataCellTableQuyenLoi(index, dataIndex, itemSelected.ma);
                onChangeDataCellTableQuyenLoi(index, "kieu_ad_ten", itemSelected.ten);
              }
            }}
          />
        );
      } else if (["nt_tien_bh"].includes(dataIndex)) {
        return (
          <ButtonPopoverCellTable
            listData={listNguyenTe}
            btnTxt={text || "Chọn"}
            popOverTitle="Chọn nguyên tệ"
            buttonStyle={{color: !text ? COLOR_PALETTE.red[50] : COLOR_PALETTE.blue[40]}}
            maSelected={text}
            onClickOption={itemSelected => {
              if (checkboxNguyenTeSTBH) onChangeAlllNguyenTeSTBH(itemSelected);
              else onChangeDataCellTableQuyenLoi(index, dataIndex, itemSelected.ma);
            }}
          />
        );
      } else if (["ma_qloi_tru_lui"].includes(dataIndex)) {
        let length = 0;
        if (text) length = text.split(";").length;
        return (
          <span
            className="font-bold text-[#699AFC]"
            onClick={() => {
              setIsOpenQuyenLoiTruLui(true);
              setQuyenLoiDangXem(record as CommonExecute.Execute.IDieuKhoanQuyenLoiConNguoi);
            }}>
            Xem{length > 0 ? `(${length})` : ""}
          </span>
        );
      } else if (["ghi_chu"].includes(dataIndex)) {
        return <InputPopoverCellTable value={text} onClickApDung={newVal => onChangeDataCellTableQuyenLoi(index, "ghi_chu", newVal)} />;
      }
      return text;
    },
    onCell: data => {
      let className = "";
      if (dataIndex === "ten_qloi") {
        if (data.cap === 1) className = "font-bold";
        else if (data.cap === 3) className = "italic";
      }
      return {
        className: `!py-1 !px-1 text-[11px] ${className}`,
      };
    },
  });

  return (
    <>
      <Table<TableQuyenLoiColumnDataType>
        {...defaultTableProps}
        rowKey="ma_qloi" // tránh remount dòng
        className="table-quyen-loi"
        dataSource={dataTableListDieuKhoanBoSung} //mảng dữ liệu record được hiển thị
        // dataSource={listQuyenLoi} //mảng dữ liệu record được hiển thị
        columns={
          tableQuyenLoiColumn?.map(item => ({
            ...item,
            ...getColumnSearchProps(item.key as keyof TableQuyenLoiColumnDataType, item.title as string),
          })) || []
        }
        pagination={false}
        title={() => <></>}
        // scroll={{x: "100%", y: 400}}
        scroll={listQuyenLoi.length >= 11 ? {x: "100%", y: 340} : {x: "100%"}}
      />
      {isOpenModalThemQuyenLoi && (
        <ModalThemQuyenLoi
          ref={refModalThemQuyenLoi}
          //trường hợp ấn nút Xem ở Quyền lợi trừ lùi
          quyenLoiDangXem={quyenLoiDangXem}
          listQuyenLoi={listQuyenLoi} //list quyền lợi trong table Cấu hình quyền lợi
          checkStrictly={checkStrictly}
          //listQuyenLoiRoot : list quyền lợi với cấu hình quyền lợi
          //trường hợp là chọn quyền lợi trừ lùi -> listQuyenLoiRoot là listQuyenLoi trong table, thêm mới thì sẽ là list quyền lợi từ server
          listQuyenLoiRoot={
            quyenLoiDangXem
              ? listQuyenLoi.map(item => ({...item, ma: item.ma_qloi || "", ma_ct: item.ma_qloi_ct || "", ten: item.ten_qloi, cap: item.cap || 0}))
              : listQuyenLoiRoot.filter(item => item.loai === "DKBS")
          }
          onPressLuuQuyenLoiTruLui={onPressLuuQuyenLoiTruLui}
          onPressThemQuyenLoi={onPressThemQuyenLoi}
          setIsOpenQuyenLoiTruLui={setIsOpenQuyenLoiTruLui}
        />
      )}
    </>
  );
});

TabDieuKhoanBoSungComponent.displayName = "TabDieuKhoanBoSungComponent";
export const TabDieuKhoanBoSung = memo(TabDieuKhoanBoSungComponent, isEqual);

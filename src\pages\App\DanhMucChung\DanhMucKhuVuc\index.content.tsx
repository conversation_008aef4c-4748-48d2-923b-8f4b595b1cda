import React, {memo, useCallback, useMemo, useRef, useState, useContext} from "react";
import {Col, Form, Input, InputRef, Row, Table, TableColumnType, Tag, Tooltip} from "antd";
import {FilterDropdownProps} from "antd/es/table/interface";
import {PlusCircleOutlined, SearchOutlined, ClearOutlined, DeleteOutlined, EditOutlined, PlusOutlined} from "@ant-design/icons";
import {isEqual} from "lodash";

import {Button, FormInput, Highlighter, TableFilterDropdown} from "@src/components";
import {COLOR_PALETTE, ID_PAGE} from "@src/constants";
import {IFormInput, ReactQuery} from "@src/@types";
import {defaultPaginationTableProps, defaultTableProps, fillRowTableEmpty} from "@src/hooks";

import DanhMucKhuVucContext from "./index.context";
import {FormTimKiemDanhMucKhuVuc, tableKhuVucColumn, TableKhuVucColumnDataType, TableKhuVucColumnDataIndex, radioItemTrangThaiKhuVucSelect, radioItemTrangThaiKhuVucTable} from "./index.configs";
import {ModalChiTietKhuVuc, IModalChiTietKhuVucRef} from "./Component";
import "./index.default.scss";

type DataIndex = keyof TableKhuVucColumnDataType;

// Destructuring form search values
const {
  ma_chau_luc,
  ma,
  ten,
  trang_thai,
} = FormTimKiemDanhMucKhuVuc;

const DanhMucKhuVucContent: React.FC = memo(() => {
  const {
    listKhuVuc,
    listChauLuc,
    loading,
    tongSoDong,
    getChiTietKhuVuc,
    searchKhuVuc,
    filterParams,
    setFilterParams,
  } = useContext(DanhMucKhuVucContext);

  const [searchText, setSearchText] = useState("");
  const [searchedColumn, setSearchedColumn] = useState<DataIndex | "">("");
  const searchInput = useRef<HTMLInputElement>(null);
  const modalChiTietRef = useRef<IModalChiTietKhuVucRef>(null);

  const dropdownOptionsChauLuc = useMemo(() => {
    if (!listChauLuc || !Array.isArray(listChauLuc)) {
      return [{ma: "", ten: "Chọn châu lục"}];
    }
    
    const allOption = [{ma: "", ten: "Tất cả"}];
    const validProvinces = listChauLuc
      .filter(item => {
        const hasValidData = item && item.ma && item.ten;
        if (!hasValidData) return false;
        
        const trangThai = item.trang_thai?.toString();
        const isActive = trangThai === "1" || trangThai === "D" || trangThai === "Đang sử dụng" || !trangThai;
        
        return hasValidData && isActive;
      })
      .map(item => ({
        ma: item.ma?.toString() || "",
        ten: item.ten?.toString() || "",
      }));
    
    return [...allOption, ...validProvinces];
  }, [listChauLuc]);

  const dataTableWithSTT = useMemo(() => {
    try {
      const currentPage = filterParams?.trang || 1;
      const currentPageSize = (filterParams?.so_dong || defaultPaginationTableProps.defaultPageSize) as number;
      
      const tableData =
        listKhuVuc?.map((item, index) =>{ 
          return {
            ...item,
            key: item.ma_chau_luc && item.ma ? `${item.ma_chau_luc}-${item.ma}` : `row-${index}`,
            sott: (currentPage - 1) * currentPageSize + index + 1,
          };
        }) || [];

      const arrEmptyRow = fillRowTableEmpty(tableData.length, currentPageSize);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      return [];
    }
  }, [listKhuVuc, filterParams?.trang, filterParams?.so_dong]);

  const onSearchApi = useCallback(
    (values: ReactQuery.ITimKiemPhanTrangDanhMucKhuVucParams) => {
      // Xử lý submit form tìm kiếm và cập nhật filter parameters
      setFilterParams({
        ma_chau_luc: values.ma_chau_luc || "",
        ma: values.ma || "",
        ten: values.ten || "",
        trang_thai: values.trang_thai || "",
        trang: 1,
        so_dong: filterParams?.so_dong || defaultPaginationTableProps.defaultPageSize,
      });
    },
    [setFilterParams, filterParams?.so_dong],
  );

  const onChangePage = useCallback(
    (newPage: number, newPageSize: number) => {
      // Xử lý thay đổi trang và số dòng hiển thị trong pagination
      setFilterParams(prev => ({
        ...prev,
        trang: newPage,
        so_dong: newPageSize,
      }));
    },
    [setFilterParams],
  );

  const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: DataIndex) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);

  const handleReset = useCallback(
    (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: DataIndex) => {
      clearFilters();
      setSearchText("");
      handleSearch([""], confirm, dataIndex);
    },
    [handleSearch],
  );

  const handleRowClick = useCallback(async (record: TableKhuVucColumnDataType) => {
    // Xử lý click vào row để hiển thị modal chi tiết khu vực
    if (record.key.toString().includes("empty")) {
      return;
    }
    
    if (loading) {
      return;
    }
    
    try {
      if (!record.ma) {
        return;
      }
      
      // Test mở modal trước khi call API
      if (!modalChiTietRef.current) {
        return;
      }
      
      const response = await getChiTietKhuVuc({
        ma: record.ma,
      });
      
      // Mở modal với data hoặc empty data để test
      try {
        modalChiTietRef.current.open(response);
      } catch (modalError) {
        //Xử lý lỗi thầm lặng
      }
      
    } catch (error) {
      //Xử lý lỗi thầm lặng
    }
  }, [getChiTietKhuVuc, loading]);

  const handleThemMoi = useCallback(() => {
    // Mở modal để tạo mới khu vực
    modalChiTietRef.current?.open();
  }, []);

  const handleAfterSave = useCallback(() => {
    // Reload danh sách sau khi lưu thành công
    searchKhuVuc();
  }, [searchKhuVuc]);

  const renderFormInputColumn = useCallback(
    (props: IFormInput, span = 4) => (
      <Col span={span}>
        <FormInput {...props} />
      </Col>
    ),
    [],
  );

  const renderHeaderTableKhuVuc = useCallback(
    () => (
      <Form
        layout="vertical"
        className="[&_.ant-form-item]:mb-0"
        onFinish={onSearchApi}>
        <Row gutter={16} align="bottom">
          {renderFormInputColumn({...ma_chau_luc, options: dropdownOptionsChauLuc})}
          {renderFormInputColumn(ma)}
          {renderFormInputColumn(ten)}
          {renderFormInputColumn({...trang_thai, options: radioItemTrangThaiKhuVucSelect})}
          <Col span={2}>
            <Button type="primary" htmlType="submit" loading={loading} icon={<SearchOutlined />} block>
              Tìm kiếm
            </Button>
          </Col>
          <Col span={2}>
            <Button type="primary" block icon={<PlusCircleOutlined />} onClick={handleThemMoi}>
              Tạo mới
            </Button>
          </Col>
        </Row>
      </Form>
    ),
    [ma_chau_luc, ma, ten, trang_thai, dropdownOptionsChauLuc, loading, onSearchApi, handleThemMoi, renderFormInputColumn],
  );

  const getColumnSearchProps = useCallback(
    (dataIndex: DataIndex, title: string): TableColumnType<TableKhuVucColumnDataType> => ({
      filterDropdown: ({setSelectedKeys, selectedKeys, confirm, clearFilters}) => (
        <TableFilterDropdown
          ref={searchInput}
          title={title}
          selectedKeys={selectedKeys}
          dataIndex={dataIndex}
          setSelectedKeys={setSelectedKeys}
          handleSearch={handleSearch}
          confirm={confirm}
          clearFilters={clearFilters}
          handleReset={handleReset}
        />
      ),
      filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? COLOR_PALETTE.blue[60] : undefined}} />,
      onFilter: (value, record) => record[dataIndex]?.toString().toLowerCase().includes((value as string).toLowerCase()) || false,
      onFilterDropdownOpenChange: (visible) => {
        if (visible) {
          setTimeout(() => searchInput.current?.select(), 100);
        }
      },
      render: (text, record) => {
        if (record.key?.toString().includes("empty")) return <span style={{height: '22px', display: 'inline-block'}}>&nbsp;</span>;
        
        if (dataIndex === "trang_thai_ten") {
          if (!text) return "";
          const color = text === "Đang sử dụng" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];
          return (
            <Tag color={color} className="text-[11px]">
              {text}
            </Tag>
          );
        }
        
        
        return searchedColumn === dataIndex ? (
          <Highlighter
            searchWords={[searchText]}
            textToHighlight={text ? text.toString() : ""}
          />
        ) : text !== undefined ? (
          text
        ) : (
          <Tag color={"transparent"} className="!text-white text-[11px]">
            {"\u00A0"}
          </Tag>
        );
      },
    }),
    [handleSearch, handleReset, searchText, searchedColumn],
  );

  const columnsWithSearch = useMemo(() => {
    return tableKhuVucColumn?.map((col: any) => {
      if (col.dataIndex && ["ma", "ten", "ma_chau_luc", "ten_chau_luc", "trang_thai_ten", "ngay_tao", "nguoi_tao", "ngay_cap_nhat", "nguoi_cap_nhat"].includes(col.dataIndex)) {
        return {
          ...col,
          ...getColumnSearchProps(col.dataIndex as DataIndex, col.title as string),
        };
      }
      return col;
    });
  }, [getColumnSearchProps]);

  return (
    <div id={ID_PAGE.DANH_MUC_KHU_VUC} className="[&_.ant-space]:w-full">
      <Table<TableKhuVucColumnDataType>
        {...defaultTableProps}
        dataSource={dataTableWithSTT}
        columns={columnsWithSearch}
        loading={loading}
        pagination={{
          ...defaultPaginationTableProps,
          current: filterParams?.trang || 1,
          pageSize: (filterParams?.so_dong || defaultPaginationTableProps.defaultPageSize) as number,
          total: tongSoDong,
          onChange: onChangePage,
          showQuickJumper: true,
          showTotal: (total, range) => `${range[0]}-${range[1]} của ${total} bản ghi`,
        }}
        title={renderHeaderTableKhuVuc}
        onRow={record => ({
          style: {cursor: loading ? "progress" : "pointer"},
          onClick: () => handleRowClick(record),
        })}
      />
      <ModalChiTietKhuVuc
        ref={modalChiTietRef}
        onAfterSave={handleAfterSave}
        danhSachChauLuc={dropdownOptionsChauLuc}
      />
    </div>
  );
});

DanhMucKhuVucContent.displayName = "DanhMucKhuVucContent";
export default DanhMucKhuVucContent;

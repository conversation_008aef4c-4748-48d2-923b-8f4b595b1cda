import {createContext, useContext} from "react";
import {QuanLyTaiKhoanNguoiDungContextProps} from "./index.model";

//createContext : khai báo 1 context mà component có thể cung cấp hoặc đọc
export const QuanLyTaiKhoanNguoiDungContext = createContext<QuanLyTaiKhoanNguoiDungContextProps>({
  //khởi tạo các giá trị mặc định, trong context này có giá trị là onSubmit
  danhSachTaiKhoanNguoiDung: [],
  loading: false,
  loadingVT: false,
  listDoiTac: [],
  listChiNhanh: [],
  menuNguoiSuDung: [],
  chucNangNguoiSuDung: [],
  donViQuanLyNguoiSuDung: [],
  defaultFormValue: {},
  tongSoDong: 0,
  tongSoDongVaiTro: 0,
  tongSoDongNhom: 0,
  danhSachVaiTroChucNang: [],
  danhSachNhomChucNang: [],
  chucNangSelected: [],
  donViSelected: [],
  menuSelected: [],
  listChucDanh: [],
  setMenuNguoiSuDung: () => {},
  setChucNangNguoiSuDung: () => {},
  setDonViQuanLyNguoiSuDung: () => {},
  getListChucDanh: () => Promise.resolve(),
  setDonViQuanLySelected: () => {},
  setMenuSelected: () => {},
  setChucNangSelected: () => {},
  layChucNangTheoVaiTro: () => Promise.resolve(null),
  layChucNangTheoNhom: () => Promise.resolve(null),
  getListChiNhanhTheoDoiTac: () => Promise.resolve(null),
  getListDoiTac: () => Promise.resolve(),
  onUpdateTaiKhoanNguoiDung: () => Promise.resolve(),
  layDanhSachTaiKhoanNguoiDungPhanTrang: () => Promise.resolve(),
  layChiTietTaiKhoanNguoiDung: () => Promise.resolve(null),
  layDanhSachVaiTroChucNangPhanTrang: () => Promise.resolve(null),
  layDanhSachNhomChucNangPhanTrang: () => Promise.resolve(null),
});

//useContext(someContext): là React Hook cho phép bạn đọc và subscribe context từ component của mình.
// file .context muốn truy câp context thì sử dụng useLoginContext : const {onSubmit} = useLoginContext();
export const useQuanLyTaiKhoanNguoiDungContext = () => useContext(QuanLyTaiKhoanNguoiDungContext);

import {CloseOutlined, PlusCircleOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {Button, Popcomfirm} from "@src/components";
import {defaultTableProps, fillRowTableEmpty} from "@src/hooks";
import {formatCurrencyUS} from "@src/utils/number";
import {Table, TableColumnType} from "antd";
import {isEqual} from "lodash";
import {forwardRef, memo, useImperativeHandle, useMemo, useRef} from "react";
import {useBaoHiemTaiSanContext} from "../../index.context";
import {tableCauHinhTaiBHColumn, TableCauHinhTaiBHDataType} from "./Constant";
import {ModalDoiTuongApDungTaiBH} from "./Modal_DoiTuongApDungTaiBH";
import {ModalNhapCauHinhTai} from "./Modal_NhapCauHinhTai";

const RenderTabCauHinhTaiBaoHiemComponent = forwardRef<any, {disabled?: boolean; pageSize?: number}>(({disabled, pageSize = 20}, ref) => {
  useImperativeHandle(ref, () => ({
    resetForm: () => {},
  }));

  const {loading, listTaiBaoHiemHopDongTaiSan, layChiTietThongTinCauHinhTaiBH, xoaThongTinTaiBH, lietKeDanhSachCacDoiTuongDaDuocApDungTaiBH} = useBaoHiemTaiSanContext();

  const refModalCauHinhTaiBH = useRef<any>(null);
  const refModalDoiTuongApDung = useRef<any>(null);

  //bấm xoá 1 dòng trong bảng ngày thanh toán
  const handleDeleteRow = async (ma_dvi_tai: string) => {
    if (disabled) return;
    await xoaThongTinTaiBH({ma_dvi_tai});
  };

  // Tính tổng tiền nhượng tái và tiền giữ lại
  const totals = useMemo(() => {
    return listTaiBaoHiemHopDongTaiSan.reduce(
      (acc, curr: any) => ({
        tien_nhuong_tai: acc.tien_nhuong_tai + (curr.tien_nhuong_tai || 0),
        tien_con: acc.tien_con + (curr.tien_con || 0),
      }),
      {tien_nhuong_tai: 0, tien_con: 0},
    );
  }, [listTaiBaoHiemHopDongTaiSan]);

  //DATA TABLE CẤU HÌNH ĐỒNG
  const dataTableListTaiBH = useMemo<Array<TableCauHinhTaiBHDataType>>(() => {
    try {
      const tableData = listTaiBaoHiemHopDongTaiSan.map((item, index) => {
        return {
          ...item,
          key: index.toString(),
          sott: item.sott || index + 1,
        };
      });
      const arrEmptyRow: Array<TableCauHinhTaiBHDataType> = fillRowTableEmpty(tableData.length, pageSize);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      console.log("dataTableListDongBaoHiem error", error);
      return [];
    }
  }, [listTaiBaoHiemHopDongTaiSan, pageSize]);

  // RENDER

  // //render header table thông tin các kỳ thanh toán
  // const renderHeaderTable = () => {
  //   return (
  //     <Row gutter={16} justify="end">
  //       <Col>
  //         <Button type="primary" onClick={() => refModalCauHinhTaiBH.current.open()} icon={<PlusCircleOutlined />} className="w-full" disabled={disabled}>
  //           Thêm tái BH
  //         </Button>
  //       </Col>
  //     </Row>
  //   );
  // };

  const renderColumn = (column: TableColumnType<TableCauHinhTaiBHDataType>) => {
    if (!("dataIndex" in column)) return column;

    if (column.dataIndex === "action") {
      return {
        ...column,
        render: (_: any, record: TableCauHinhTaiBHDataType, index: number) =>
          record.key && !record.key.toString().startsWith("empty") ? (
            <span onClick={e => e.stopPropagation()}>
              <Popcomfirm
                title="Thông báo"
                onConfirm={() => record.ma_dvi_tai && handleDeleteRow(record.ma_dvi_tai)}
                htmlType="button"
                okText="Đồng ý"
                description="Bạn có chắc chắn muốn xoá thông tin cấu hình tái BH không?"
                buttonTitle={""}
                buttonColor="red"
                variant="text"
                className="h-auto"
                buttonDisable={disabled}
                icon={<CloseOutlined />}
              />
            </span>
          ) : null,
      };
    }
    if (column.dataIndex === "sl_dt_bh") {
      return {
        ...column,
        render: (text: any, record: TableCauHinhTaiBHDataType, index: number) => {
          if (typeof record.sl_dt_bh === "number" && record.key && !record.key.toString().startsWith("empty")) {
            if (record.sl_dt_bh > 0) {
              return (
                <Button
                  className="h-auto"
                  type="link"
                  onClick={async e => {
                    e.stopPropagation();
                    const listDoiTuongDaApDungTaiBH = await lietKeDanhSachCacDoiTuongDaDuocApDungTaiBH(record as ReactQuery.IChiTietThongTinCauHinhTaiBHParams);
                    refModalDoiTuongApDung.current?.open(record, listDoiTuongDaApDungTaiBH);
                  }}
                  style={{padding: 0}}>
                  {`Có ${record.sl_dt_bh} đối tượng áp dụng`}
                </Button>
              );
            } else if (record.sl_dt_bh <= 0) {
              return (
                <Button
                  className="h-auto"
                  type="link"
                  onClick={async e => {
                    e.stopPropagation();
                    const listDoiTuongDaApDungTaiBH = await lietKeDanhSachCacDoiTuongDaDuocApDungTaiBH(record as ReactQuery.IChiTietThongTinCauHinhTaiBHParams);
                    refModalDoiTuongApDung.current?.open(record, listDoiTuongDaApDungTaiBH);
                  }}
                  style={{padding: 0}}>
                  Áp dụng tất cả
                </Button>
              );
            }
          }
          return text;
        },
      };
    }

    return column;
  };

  //render tổng cộng của cột
  const renderSummaryCell = (index: number, value: string) => {
    return (
      <Table.Summary.Cell index={index} className="!p-[8px] !pr-[24px]">
        <div className="text-right font-medium">{value}</div>
      </Table.Summary.Cell>
    );
  };

  //render bảng thông tin đồng bh
  const renderTable = () => (
    <div>
      <Table<TableCauHinhTaiBHDataType>
        {...defaultTableProps}
        className={`no-header-border-radius mt-3`}
        dataSource={dataTableListTaiBH} //mảng dữ liệu record được hiển thị
        columns={
          (tableCauHinhTaiBHColumn || []).map(item => {
            return renderColumn(item);
          }) || []
        } //định nghĩa cột của table
        loading={loading} //hiển thị loading khi đang gọi API để loading data
        sticky
        onRow={record => {
          return {
            style: {cursor: loading ? "progress" : "pointer"}, //  thể hiện vị trí con trỏ là button(👈)
            onClick: async () => {
              if (!record.ma_dvi_tai || disabled) return;
              const response = await layChiTietThongTinCauHinhTaiBH(record as ReactQuery.IChiTietThongTinCauHinhTaiBHParams);
              if (response) refModalCauHinhTaiBH.current?.open(response);
            },
          };
        }}
        pagination={false}
        scroll={dataTableListTaiBH.length > pageSize ? {x: "max-content", y: 350} : {x: "max-content"}}
        summary={() => (
          <Table.Summary fixed>
            <Table.Summary.Row>
              <Table.Summary.Cell className="!p-[8px]" index={0} colSpan={5}>
                <div className="text-center font-medium">Tổng cộng</div>
              </Table.Summary.Cell>
              {renderSummaryCell(1, formatCurrencyUS(totals.tien_nhuong_tai))}
              {/* {renderSummaryCell(6, formatCurrencyUS(totals.tien_con))} */}
              <Table.Summary.Cell index={3} className="!p-[8px]" colSpan={2}>
                <div className="text-center">
                  <Button type="link" onClick={() => refModalCauHinhTaiBH.current.open()} icon={<PlusCircleOutlined />} className="!h-auto !p-0" disabled={disabled}>
                    Thêm tái BH
                  </Button>
                </div>
              </Table.Summary.Cell>
            </Table.Summary.Row>
          </Table.Summary>
        )}
      />
    </div>
  );

  return (
    <>
      {/* {renderHeaderTable()} */}
      {renderTable()}
      <ModalNhapCauHinhTai disabled={disabled} ref={refModalCauHinhTaiBH} />
      <ModalDoiTuongApDungTaiBH disabled={disabled} ref={refModalDoiTuongApDung} />
    </>
  );
});

RenderTabCauHinhTaiBaoHiemComponent.displayName = "RenderTabCauHinhTaiBaoHiemComponent";
export const RenderTabCauHinhTaiBaoHiem = memo(RenderTabCauHinhTaiBaoHiemComponent, isEqual);

import {IFormInput} from "@src/@types";
import {colWidthByKey, defaultTableColumnsProps, ruleInputMessage} from "@src/hooks";
import {TableProps} from "antd";

//ĐỊNH NGHĨA CẤU HÌNH TABLE ĐỐI TÁC GỒM NHỮNG CỘT NÀO, THỨ TỰ CỘT SẼ HIỂN THỊ THEO THỨ TỰ KHAI BÁO Ở DƯỚI
export interface TableDonViChiNhanhColumnDataType {
  key: string;
  sott?: number; //CỘT 1
  ten?: string; //CỘT 2
  // ten_tat: string;
  mst?: string; //CỘT 3
  dchi?: string; //...
  dthoai?: string;
  // ten_e: string;
  // trang_thai: string;
  ngay_tao?: string;
  nguoi_tao?: string;
  ngay_cap_nhat?: string;
  nguoi_cap_nhat?: string;
  trang_thai_ten?: string;
}

// ĐỊNH NGHĨA Column RENDER RA TỪNG Ô TRONG CỘT
//đọc https://ant.design/components/table#column để biết thêm về column
export const tableDonViChiNhanhColumn: TableProps<TableDonViChiNhanhColumnDataType>["columns"] = [
  {
    title: "STT",
    dataIndex: "sott",
    key: "sott",
    width: 60,
    ...defaultTableColumnsProps,
  },
  {
    title: "Mã",
    dataIndex: "ma",
    key: "ma",
    width: 80,
    ...defaultTableColumnsProps,
  },
  {
    title: "Tên",
    dataIndex: "ten",
    key: "ten",
    width: 200,
    ...defaultTableColumnsProps,
  },
  {
    title: "MST",
    dataIndex: "mst",
    key: "mst",
    width: 120,
    ...defaultTableColumnsProps,
  },
  {
    title: "Địa chỉ",
    dataIndex: "dchi",
    key: "dchi",
    width: 250,
    ...defaultTableColumnsProps,
  },
  {
    title: "Điện thoại",
    dataIndex: "dthoai",
    key: "dthoai",
    width: 120,
    ...defaultTableColumnsProps,
  },
  {
    title: "Ngày tạo",
    dataIndex: "ngay_tao",
    key: "ngay_tao",
    width: colWidthByKey.ngay_tao,
    ...defaultTableColumnsProps,
  },
  {
    title: "Người tạo",
    dataIndex: "nguoi_tao",
    key: "nguoi_tao",
    width: colWidthByKey.nguoi_tao,
    ...defaultTableColumnsProps,
  },
  {
    title: "Ngày cập nhật",
    dataIndex: "ngay_cap_nhat",
    key: "ngay_cap_nhat",
    width: colWidthByKey.ngay_cap_nhat,
    ...defaultTableColumnsProps,
  },
  {
    title: "Người cập nhật",
    dataIndex: "nguoi_cap_nhat",
    key: "nguoi_cap_nhat",
    width: colWidthByKey.nguoi_cap_nhat,
    ...defaultTableColumnsProps,
  },
  {
    title: "Trạng thái",
    dataIndex: "trang_thai_ten",
    key: "trang_thai_ten",
    width: 130,
    ...defaultTableColumnsProps,
  },
];

//keyof: return ra key của inteface TableDonViChiNhanhColumnDataType;
export type TableDonViChiNhanhColumnDataIndex = keyof TableDonViChiNhanhColumnDataType;

//radio trong table
export const radioItemTrangThaiDonViChiNhanhTable = [
  {value: "Đang sử dụng", text: "Đang sử dụng"},
  {value: "Ngưng sử dụng", text: "Ngưng sử dụng"},
];

//form tìm kiếm
//radio trong search
export const radioItemTrangThaiDonViChiNhanhSelect: Array<{ten: string; ma: string}> = [
  {ma: "D", ten: "Đang sử dụng"},
  {ma: "K", ten: "Ngưng sử dụng"},
];
export interface IFormTimKiemDonViChiNhanhFieldsConfig {
  ten: IFormInput;
  mst: IFormInput;
  trang_thai: IFormInput;
}
export const FormTimKiemQuanLyDonViChiNhanh: IFormTimKiemDonViChiNhanhFieldsConfig = {
  ten: {
    component: "input",
    name: "ten",
    label: "Tên đơn vị",
    placeholder: "Nhập tên",
    className: "!mb-0",
  },
  mst: {component: "input", name: "mst", label: "Mã số thuế", placeholder: "Nhâp mã số thuế", className: "!mb-0"},
  trang_thai: {component: "select", name: "trang_thai", label: "Trạng thái", className: "!mb-0", placeholder: "Chọn trạng thái"},
};

//form update / create
export interface IFormTaoMoiDonViChiNhanhFieldsConfig {
  ma: IFormInput;
  ten: IFormInput;
  ten_tat: IFormInput;
  email: IFormInput;
  mst: IFormInput;
  dchi: IFormInput;
  dthoai: IFormInput;
  stt: IFormInput;
  trang_thai: IFormInput;
  ma_doi_tac: IFormInput;
}

export const FormTaoMoiDonViChiNhanh: IFormTaoMoiDonViChiNhanhFieldsConfig = {
  ma: {
    component: "input",
    label: "Mã",
    name: "ma",
    placeholder: "Nhập mã đơn vị / chi nhánh",
    rules: [ruleInputMessage.required],
  },
  ten: {
    component: "input",
    label: "Tên",
    name: "ten",
    placeholder: "Nhập tên",
    rules: [ruleInputMessage.required],
  },
  ten_tat: {
    component: "input",
    label: "Tên tắt",
    name: "ten_tat",
    placeholder: "Nhập tên tắt",
    rules: [ruleInputMessage.required],
  },
  email: {
    component: "input",
    label: "Email",
    name: "email",
    placeholder: "...@gmail.com",
    rules: [ruleInputMessage.required, ruleInputMessage.email],
  },
  mst: {
    component: "input",
    label: "Mã số thuế",
    name: "mst",
    placeholder: "Nhập mã số thuế",
    rules: [ruleInputMessage.required],
  },
  dchi: {
    component: "input",
    label: "Địa chỉ",
    name: "dchi",
    placeholder: "Nhập địa chỉ",
    rules: [ruleInputMessage.required],
  },
  dthoai: {
    component: "input",
    label: "Điện thoại",
    name: "dthoai",
    placeholder: "Nhập điện thoại",
    rules: [ruleInputMessage.required, ruleInputMessage.phone],
  },
  stt: {
    component: "input",
    label: "Số thứ tự",
    name: "stt",
    placeholder: "Nhập số thứ tự",
    rules: [ruleInputMessage.required],
  },
  trang_thai: {
    component: "select",
    label: "Trạng thái",
    name: "trang_thai",
    placeholder: "Chọn trạng thái",
    rules: [ruleInputMessage.required],
  },
  ma_doi_tac: {
    component: "select",
    label: "Đối tác",
    name: "ma_doi_tac",
    placeholder: "Chọn đối tác",
    rules: [ruleInputMessage.required],
  },
};

export const TRANG_THAI_TAO_MOI_DON_VI_CHI_NHANH = [
  {ten: "Đang sử dụng", ma: "D"},
  {ten: "Ngừng sử dụng", ma: "K"},
];

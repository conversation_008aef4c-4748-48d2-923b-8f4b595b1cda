import {ArrowLeftOutlined, CheckOutlined, ClearOutlined, CloseOutlined, SearchOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {Button, FormInput, HeaderModal, Highlighter, Popcomfirm} from "@src/components";
import {COLOR_PALETTE} from "@src/constants";
import {defaultTableProps, fillRowTableEmpty} from "@src/hooks";
import {Col, Form, Input, InputRef, Modal, Row, Table, TableColumnType, Tag, Tooltip} from "antd";
import {forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState} from "react";

import {optionTrangThaiNhomChucNangSelect} from "../index.configs";
import {useNhomChucNangContext} from "../index.context";
import {chucNangChuaXacDinhColumns, chucNangColumns, DataIndexChuaXacDinh, DataIndexChucNang, FormInputConfigs, TableChucNangChuaXacDinhDataType, TableChucNangDataType} from "./index.configs";
import {FilterDropdownProps} from "antd/es/table/interface";
interface Props {
  danhSachNhomChucNang: Array<CommonExecute.Execute.IDanhSachHeThongChucNangPhanTrang>;
}

export interface IModalChiTietNhomChucNangRef {
  open: (data?: CommonExecute.Execute.IChiTietNhomChucNang) => void;
  close: () => void;
}

const ModalChiTietNhomChucNang = forwardRef<IModalChiTietNhomChucNangRef, Props>(({danhSachNhomChucNang}: Props, ref) => {
  const {onUpdateNhomChucNang, loading, layChiTietNhomChucNang, chiTietNhomChucNang, onDeleteChucNangTheoNhom, setChiTietNhomChucNang, layDanhSachChucNangChuaPhanNhom} = useNhomChucNangContext();
  const {ten, ma, trang_thai, stt} = FormInputConfigs;
  const [form] = Form.useForm();
  const formValues = Form.useWatch([], form);
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  // const [chiTietChucNang, setChiTietChucNang] = useState<CommonExecute.Execute.IChiTietChucNang | null>(null);
  const searchInput = useRef<InputRef>(null);
  const [searchText, setSearchText] = useState("");
  const [searchedColumn, setSearchedColumn] = useState("");
  const [selectedChucNang, setSelectedChucNang] = useState<string[]>([]);
  const pageSize = 8;

  const [danhSachChucNangChuaXacDinh, setDanhSachChucNangChuaXacDinh] = useState<Array<{ma?: string; ten?: string; loai?: string}>>([]);

  useImperativeHandle(ref, () => ({
    open: (dataChiTietNhomChucNang?: CommonExecute.Execute.IChiTietNhomChucNang) => {
      setIsOpen(true);
      form.resetFields();
      if (dataChiTietNhomChucNang) setChiTietNhomChucNang(dataChiTietNhomChucNang);
      console.log("chi tiết nhóm chức năng", chiTietNhomChucNang);
    },
    close: () => {
      setIsOpen(false);
    },
  }));
  const closeModal = () => {
    setIsOpen(false);

    form.resetFields();
    setChiTietNhomChucNang({});
    setSelectedChucNang([]);
  };
  useEffect(() => {
    if (chiTietNhomChucNang) {
      layDanhSachChucNangChuaPhanNhom().then(response => {
        console.log("response", response);
        setDanhSachChucNangChuaXacDinh(response || []);
      });
    } else {
      setDanhSachChucNangChuaXacDinh([]);
    }
  }, [chiTietNhomChucNang]);
  const danhSachChucNang = useMemo(() => {
    return chiTietNhomChucNang?.ds_cn;
  }, [chiTietNhomChucNang]);
  useEffect(() => {
    if (chiTietNhomChucNang) {
      console.log("chi tiết nhóm chức năng", chiTietNhomChucNang);
    }
  }, [chiTietNhomChucNang]);

  useEffect(() => {
    if (chiTietNhomChucNang) {
      const arrFormData = [];
      for (const key in chiTietNhomChucNang.nhom_cn) {
        arrFormData.push({
          name: key as keyof CommonExecute.Execute.IChiTietNhomChucNang,
          value: (chiTietNhomChucNang.nhom_cn as any)[key],
        });
      }
      form.setFields(arrFormData);
    }
  }, [chiTietNhomChucNang, form]);
  //xử lý validate form
  useEffect(() => {
    form
      .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
      .then(() => {
        setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
      })
      .catch(() => {
        setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
      });
  }, [form, formValues]);

  //Bấm Update
  const onPressUpdateNhomChucNang = async () => {
    try {
      const values: ReactQuery.IUpdateNhomChucNangParams = form.getFieldsValue(); //lấy ra values của form
      const params: ReactQuery.IUpdateNhomChucNangParams = {
        ...values,
        ds: selectedChucNang.map(row => ({
          ma: row,
        })),
      };
      console.log("params cập nhật ", params);

      const response = await onUpdateNhomChucNang(params);
      if (response === -1) {
        layChiTietNhomChucNang({ma: values.ma});
        setSelectedChucNang([]);
        closeModal();
      } else {
        console.log("cập nhật thất bại");
      }
    } catch (error: any) {
      console.log("onConfirm", error);
    }
  };

  //bấm huỷ / để sau xoá dữ liệu của modal khi nào mở thì gọi lại
  const onPressDeSau = () => {
    closeModal();
  };
  const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: DataIndexChuaXacDinh) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);
  const handleReset = useCallback(
    (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: DataIndexChuaXacDinh) => {
      clearFilters();
      setSearchedColumn("");
      handleSearch([""], confirm, dataIndex);
    },
    [handleSearch],
  );
  const handleSearchChucNang = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: DataIndexChucNang) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);
  const handleResetChucNang = useCallback(
    (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: DataIndexChucNang) => {
      clearFilters();
      setSearchedColumn("");
      handleSearchChucNang([""], confirm, dataIndex);
    },
    [handleSearchChucNang],
  );
  //Render
  //tạo cột tìm kiếm
  const getColumnSearchProps = (dataIndex: DataIndexChuaXacDinh, title: string): TableColumnType<TableChucNangChuaXacDinhDataType> => ({
    filterDropdown: ({setSelectedKeys, selectedKeys, confirm, clearFilters, close}) => (
      <div style={{padding: 8}} onKeyDown={e => e.stopPropagation()} className="flex flex-row items-center p-2">
        <Input
          ref={searchInput}
          placeholder={`Tìm theo ${title}`}
          value={selectedKeys[0]}
          onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
          onPressEnter={() => handleSearch(selectedKeys as string[], confirm, dataIndex)}
          style={{display: "block", marginRight: 8}}
        />
        <Tooltip title="Tìm kiếm">
          <Button type="primary" shape="circle" icon={<SearchOutlined />} onClick={() => handleSearch(selectedKeys as string[], confirm, dataIndex)} className="mr-2" />
        </Tooltip>
        <Tooltip title="Xoá">
          <Button type="primary" shape="circle" icon={<ClearOutlined />} onClick={() => clearFilters && handleReset(clearFilters, confirm, dataIndex)} />
        </Tooltip>
      </div>
    ),
    filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} />,
    onFilter: (value, record) =>
      record[dataIndex]
        ? record[dataIndex]
            .toString()
            .toLowerCase()
            .includes((value as string).toLowerCase())
        : false,
    filterDropdownProps: {
      onOpenChange(open) {
        if (open) {
          setTimeout(() => searchInput.current?.select(), 100);
        }
      },
    },

    render: (text, record, index) => {
      if (dataIndex === "is_checked") {
        const color = text === "Đang sử dụng" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];
        if (record.key.toString().includes("empty")) return "";
        return (
          <Tag color={color} className="text-[11px]">
            {text}
          </Tag>
        );
      }
      return searchedColumn === dataIndex ? (
        <Highlighter searchWords={[searchText]} textToHighlight={text ? text.toString() : ""} />
      ) : text !== undefined ? (
        text
      ) : (
        <Tag color={"transparent"} className="!text-white text-[11px]">
          {"\u00A0"}
        </Tag>
      );
    },
  });
  const getColumnChucNangSearchProps = (dataIndex: DataIndexChucNang, title: string): TableColumnType<TableChucNangDataType> => ({
    filterDropdown: ({setSelectedKeys, selectedKeys, confirm, clearFilters, close}) => (
      <div style={{padding: 8}} onKeyDown={e => e.stopPropagation()} className="flex flex-row items-center p-2">
        <Input
          ref={searchInput}
          placeholder={`Tìm theo ${title}`}
          value={selectedKeys[0]}
          onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
          onPressEnter={() => handleSearchChucNang(selectedKeys as string[], confirm, dataIndex)}
          style={{display: "block", marginRight: 8}}
        />
        <Tooltip title="Tìm kiếm">
          <Button type="primary" shape="circle" icon={<SearchOutlined />} onClick={() => handleSearchChucNang(selectedKeys as string[], confirm, dataIndex)} className="mr-2" />
        </Tooltip>
        <Tooltip title="Xoá">
          <Button type="primary" shape="circle" icon={<ClearOutlined />} onClick={() => clearFilters && handleResetChucNang(clearFilters, confirm, dataIndex)} />
        </Tooltip>
      </div>
    ),
    filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} />,
    onFilter: (value, record) =>
      record[dataIndex]
        ? record[dataIndex]
            .toString()
            .toLowerCase()
            .includes((value as string).toLowerCase())
        : false,
    filterDropdownProps: {
      onOpenChange(open) {
        if (open) {
          setTimeout(() => searchInput.current?.select(), 100);
        }
      },
    },

    render: (text, record, index) => {
      return searchedColumn === dataIndex ? (
        <Highlighter searchWords={[searchText]} textToHighlight={text ? text.toString() : ""} />
      ) : text !== undefined ? (
        text
      ) : (
        <Tag color={"transparent"} className="!text-white text-[11px]" style={{height: 18}}>
          {"\u00A0"}
        </Tag>
      );
    },
  });

  const listChucNangChuaXacDinh = useMemo<Array<TableChucNangChuaXacDinhDataType>>(() => {
    try {
      if (!chiTietNhomChucNang) return [];
      console.log("danhSachChucNangChuaXacDinh", danhSachChucNangChuaXacDinh);
      const tableData = (danhSachChucNangChuaXacDinh || []).map((item: any, index: number) => {
        return {
          stt: item.sott,
          ma: item.ma,
          ten: item.ten,
          loai: item.loai,
          key: index.toString(),
          is_checked: selectedChucNang.includes(item.ma) ? "1" : "0",
        };
      });

      const arrEmptyRow: Array<TableChucNangChuaXacDinhDataType> = fillRowTableEmpty(tableData.length);

      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      console.log("dataTableListChucNang chưa xác định error", error);
      return [];
    }
  }, [danhSachChucNangChuaXacDinh, selectedChucNang]);
  const dataTableListChucNang = useMemo<Array<TableChucNangDataType>>(() => {
    try {
      console.log("danhSachChucNang", danhSachChucNang);
      const tableData = (danhSachChucNang || []).map((item: any, index: number) => {
        return {
          stt: item.sott,
          ma: item.ma,
          ten: item.ten,
          loai: item.loai,
          key: index.toString(),
          hanh_dong: () => renderDeleteButton(chiTietNhomChucNang?.nhom_cn?.ma, item.ma) || null,
        };
      });

      const arrEmptyRow: Array<TableChucNangDataType> = fillRowTableEmpty(tableData.length, pageSize);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      console.log("dataTableListChucNang error", error);
      return [];
    }
  }, [danhSachChucNang]);
  const handleChucNangChuaXacDinhRowClick = (record: TableChucNangChuaXacDinhDataType) => {
    handleInputChange(record?.ma || "", "is_checked", record.is_checked === "1" ? "0" : "1");
  };
  const handleInputChange = (ma: string, field: keyof TableChucNangChuaXacDinhDataType, value: any) => {
    if (!ma) {
      return;
    }
    setSelectedChucNang(prev => {
      if (value === "1") {
        // Thêm hoặc cập nhật chức năng khi được chọn
        return prev.includes(ma) ? prev : [...prev, ma];
      } else {
        // Xóa chức năng khi bỏ chọn
        return prev.filter(p => p !== ma);
      }
    });
  };
  const onDelete = useCallback(
    async (ma: string, ma_chuc_nang: string) => {
      try {
        const params: ReactQuery.IDeleteChucNangTheoNhomParams = {
          ma: ma,
          ma_chuc_nang: ma_chuc_nang,
        };
        console.log("params", params);
        await onDeleteChucNangTheoNhom(params);
        layChiTietNhomChucNang({ma: params.ma});
      } catch (error) {
        console.log("onDelete error", error);
      }
    },
    [onDeleteChucNangTheoNhom],
  );
  const renderDeleteButton = useCallback(
    (ma?: string, ma_chuc_nang?: string) => {
      if (!ma || !ma_chuc_nang) return null;

      return (
        <Popcomfirm
          title="Thông báo"
          onConfirm={() => onDelete(ma, ma_chuc_nang)}
          htmlType="button"
          okText="Xóa"
          description="Bạn có chắc muốn xóa chức năng?"
          buttonTitle={""}
          buttonColor="red"
          okButtonProps={{
            style: {
              backgroundColor: "white",
              borderColor: "red",
              color: "red",
            },
          }}
          variant="text"
          className="d-block h-auto"
          icon={<CloseOutlined />}
          buttonIcon
          style={{
            width: "fit-content",
          }}
        />
      );
    },
    [onDelete],
  );
  const renderColumn = (column: any) => {
    if (column.dataIndex === "is_checked") {
      return {
        ...column,
        render: (_: any, record: TableChucNangChuaXacDinhDataType) => {
          const isEmptyRow = !record.ma;

          return (
            <div className="custom-checkbox-cell" style={{height: 20, alignItems: "center", verticalAlign: "midle"}}>
              <FormInput
                className="!mb-0"
                component="checkbox"
                checked={record.is_checked === "1"}
                onChange={e => {
                  if (!record.ma) {
                    return;
                  }
                  handleInputChange(record.ma, "is_checked", e.target.checked ? "1" : "0");
                }}
              />
            </div>
          );
        },
      };
    }
    return {
      ...column,
      ...(column.key && typeof column.title === "string" && column.key !== "stt" ? getColumnSearchProps(column.key, column.title) : {}),
      render: (text: string) => text || "", // Đảm bảo hiển thị chuỗi rỗng nếu text undefined
    };
  };
  const renderTableChucNangChuaXacDinh = () => {
    return (
      <Table<TableChucNangChuaXacDinhDataType>
        className="table-vai-tro no-header-border-radius"
        {...defaultTableProps}
        onRow={(record, rowIndex) => {
          return {
            style: {
              cursor: "pointer",
              // background: record.ma === selectedVaiTro ? "#96BF49" : undefined, // Làm nổi bật hàng được chọn
            },
            onClick: () => handleChucNangChuaXacDinhRowClick(record), // Xử lý nhấp vào hàng
          };
        }}
        columns={(chucNangChuaXacDinhColumns || []).map(renderColumn)}
        rowClassName={(record, index) => (record.key.includes("empty") ? "empty-row" : "")}
        title={null}
        pagination={false}
        dataSource={listChucNangChuaXacDinh}
        scroll={{y: 230}}
      />
    );
  };
  const renderTableChucNang = () => {
    return (
      <Table<TableChucNangDataType>
        className="table-vai-tro no-header-border-radius"
        {...defaultTableProps}
        columns={(chucNangColumns || []).map(item => {
          // Đảm bảo item.key được định nghĩa, item.title là chuỗi và item.key không phải là "stt" trước khi thêm ô tìm kiếm
          return {
            ...item,
            ...(item.key && typeof item.title === "string" && item.key !== "stt" ? getColumnChucNangSearchProps(item.key as keyof TableChucNangDataType, item.title) : {}),
          };
        })}
        dataSource={dataTableListChucNang}
        title={null}
        pagination={false}
        // scroll={{y: 230}}
        scroll={dataTableListChucNang.length > pageSize ? {y: 230} : undefined}
      />
    );
  };
  //renderFooter Modal
  const renderFooter = () => {
    return (
      <Form.Item className="mb-0">
        <Button type="default" htmlType="reset" form="formUpdateNhomChucNang" onClick={onPressDeSau} className="mr-2" icon={<ArrowLeftOutlined />}>
          Quay lại
        </Button>
        <Button type="primary" disabled={disableSubmit} iconPosition="end" onClick={() => onPressUpdateNhomChucNang()} className="mr-2" icon={<CheckOutlined />}>
          Lưu
        </Button>
      </Form.Item>
    );
  };
  //Render

  const renderTable = () => {
    return (
      <Row>
        <Col span={12} style={{paddingRight: 16}}>
          {renderTableChucNangChuaXacDinh()}
        </Col>
        <Col span={12}>{renderTableChucNang()}</Col>
      </Row>
    );
  };
  // form input
  const renderFormInput = (config: any, span = 6) => {
    return (
      <Col span={span}>
        <FormInput {...config} />
      </Col>
    );
  };

  // render modal
  return (
    <Modal
      title={
        <HeaderModal
          title={chiTietNhomChucNang && Object.keys(chiTietNhomChucNang).length > 0 ? `Chi tiết nhóm chức năng ${chiTietNhomChucNang?.nhom_cn?.ten}` : "Tạo mới nhóm chức năng"}
          trang_thai_ten={chiTietNhomChucNang?.nhom_cn?.trang_thai === "D" ? "Đang sử dụng" : "Ngưng sử dụng"}
          trang_thai={chiTietNhomChucNang?.nhom_cn?.trang_thai}
        />
      }
      centered
      closable
      open={isOpen}
      onOk={() => closeModal()}
      onCancel={() => {
        closeModal();
      }}
      maskClosable={false}
      width={"70vw"}
      className="modal-nhom-chuc-nang"
      footer={renderFooter}>
      {/* Content Modal */}
      <Form id="formUpdateNhomChucNang" onFinish={onPressUpdateNhomChucNang} form={form} layout="vertical" autoComplete="on">
        <Row gutter={16}>
          {renderFormInput(ma)}
          {renderFormInput(ten)}
          {renderFormInput(stt)}
          {renderFormInput({...trang_thai, options: optionTrangThaiNhomChucNangSelect})}
        </Row>
      </Form>
      {renderTable()}
    </Modal>
  );
});

export default ModalChiTietNhomChucNang;
ModalChiTietNhomChucNang.displayName = "ModalChiTietNhomChucNang";

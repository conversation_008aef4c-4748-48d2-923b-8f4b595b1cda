import {forwardRef, memo, useEffect, useImperativeHandle, useState} from "react";
import {Col, DatePicker, Flex, Form, Modal, Row} from "antd";
import {useBaoHiemTaiSanContext} from "../../index.context";
import {isEqual} from "lodash";
import {Button, FormInput, HeaderModal} from "@src/components";
import {ArrowLeftOutlined, CheckOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {FormchiTietDanhSachTaiSan, IModalchiTietDanhSachTaiSanRef, PropsTaiSan, TRANG_THAI} from "./Constant";
const {so_id, so_id_dt, so_id_ts, ten, so_luong, don_gia, tong_tien, nam_sx, han_sd, trang_thai} = FormchiTietDanhSachTaiSan;
import dayjs from "dayjs";

const ModalChiTietTaiSanComponent = forwardRef<IModalchiTietDanhSachTaiSanRef, PropsTaiSan>(({doiTuongTaiSanSelected}: PropsTaiSan, ref) => {
  useImperativeHandle(ref, () => ({
    open: (dataDanhMucSanPham?: CommonExecute.Execute.IDanhSachTaiSan) => {
      setIsOpen(true);
      if (dataDanhMucSanPham) setChiTietDanhSachTaiSan(dataDanhMucSanPham);
    },
    close: () => setIsOpen(false),
  }));
  //   const [chiTietDanhSachTaiSan, setchiTietDanhSachTaiSan] = useState<CommonExecute.Execute.IDanhMucSanPham | null>(null);
  const [isOpen, setIsOpen] = useState(false);

  const {loading, capNhatDanhSachTaiSan, filterParams, setFilterParams, timKiemPhanTrangDanhSachTaiSan, chiTietDanhSachTaiSan, setChiTietDanhSachTaiSan, chiTietHopDongBaoHiemTaiSan} =
    useBaoHiemTaiSanContext();

  const [form] = Form.useForm();
  const formValues = Form.useWatch([], form);
  const watchSoLuong = Form.useWatch("so_luong", form);
  const watchDonGia = Form.useWatch("don_gia", form);
  useEffect(() => {
    if (chiTietDanhSachTaiSan) {
      const arrFormData = [];
      for (const key in chiTietDanhSachTaiSan) {
        arrFormData.push({
          name: key as keyof CommonExecute.Execute.IDanhSachTaiSan,
          value: chiTietDanhSachTaiSan[key as keyof CommonExecute.Execute.IDanhSachTaiSan],
        });
      }
      form.setFields(arrFormData);
    }
  }, [chiTietDanhSachTaiSan, form]);
  useEffect(() => {
    if (chiTietDanhSachTaiSan) {
      fillValueVaoForm();
    }
  }, [chiTietDanhSachTaiSan]);
  //xử lý dữ liệu đầu vào form dạng date-picker
  const fillValueVaoForm = () => {
    const arrFormData = [];
    const arrInputFormThongTinTaiSan = Object.keys(FormchiTietDanhSachTaiSan); //lấy ra key của form
    for (const key in chiTietDanhSachTaiSan) {
      if (arrInputFormThongTinTaiSan.includes(key)) {
        let value: any = chiTietDanhSachTaiSan[key as keyof CommonExecute.Execute.IDanhSachTaiSan];
        //xử lý các key đặc biệt
        if (key === "han_sd") value = dayjs(value + "", "YYYYMMDD").isValid() ? dayjs(value + "", "YYYYMMDD") : "";
        else if (key === "nam_sx") value = dayjs(value + "", "YYYY").isValid() ? dayjs(value + "", "YYYY") : "";
        arrFormData.push({
          name: key as keyof CommonExecute.Execute.IDanhSachTaiSan,
          value: value,
        });
      }
    }
    form.setFields(arrFormData);
  };
  const parseNumber = (value: any): number => {
    if (typeof value === "number") return value;
    if (typeof value === "string") {
      return parseFloat(value.replace(/,/g, "")) || 0;
    }
    return 0;
  };
  useEffect(() => {
    form.setFieldsValue({
      tong_tien: parseNumber(watchSoLuong) * parseNumber(watchDonGia),
    });
  }, [watchSoLuong, watchDonGia]);
  useEffect(() => {
    form
      .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
      .then(() => {
        setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
      })
      .catch(() => {
        setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
      });
  }, [form, formValues]);
  const closeModal = () => {
    setIsOpen(false);
    setChiTietDanhSachTaiSan(null);
    form.resetFields();
    setFilterParams(filterParams);
  };

  const renderFormInputColum = (props?: any, span = 8) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  const onConfirm = async () => {
    try {
      const values: ReactQuery.ICapNhatDanhSachTaiSanParams = form.getFieldsValue(); //lấy ra values của form

      const params: ReactQuery.ICapNhatDanhSachTaiSanParams = {
        ...values,
        han_sd: Number(dayjs(values.han_sd).format("YYYYMMDD")),
        nam_sx: Number(dayjs(values.nam_sx).format("YYYY")),
        so_id: chiTietHopDongBaoHiemTaiSan?.so_id,
        so_id_dt: doiTuongTaiSanSelected?.gcn?.so_id_dt,
        so_id_ts: chiTietDanhSachTaiSan?.so_id_ts || 0,
      };
      await capNhatDanhSachTaiSan(params);
      await timKiemPhanTrangDanhSachTaiSan({so_id: chiTietHopDongBaoHiemTaiSan?.so_id, so_id_dt: doiTuongTaiSanSelected?.gcn?.so_id_dt});
    } catch (error) {
      console.log("onConfirm", error);
    }
  };
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  //render footer
  const renderFooter = () => {
    return (
      <Form.Item>
        <Button type="default" onClick={() => setIsOpen(false)} className="mr-2" icon={<ArrowLeftOutlined />}>
          Quay lại
        </Button>
        <Button type="primary" disabled={disableSubmit} iconPosition="end" onClick={() => onConfirm()} className="mr-2" icon={<CheckOutlined />}>
          Lưu
        </Button>
        {/* <Popcomfirm
          title="Thông báo"
          onConfirm={onConfirm}
          okText="Lưu"
          description="Bạn có chắc muốn lưu thông tin?"
          buttonTitle="Lưu"
          buttonDisable={disableSubmit}
          buttonIcon={<CheckOutlined />}
          iconPosition="end"
          loading={loading}
        /> */}
      </Form.Item>
    );
  };
  const renderForm = () => (
    <Form form={form} layout="vertical" initialValues={{trang_thai: TRANG_THAI[0].ma}}>
      {/* MÃ */}
      <Row gutter={16}>
        {/* {renderFormInputColum({...so_id_dt, options: listDoiTac, disabled: chiTietDanhSachTaiSan ? true : false})}
        {renderFormInputColum({...so_id_ts, disabled: chiTietDanhSachTaiSan ? true : false})}
        {renderFormInputColum({...so_id, disabled: chiTietDanhSachTaiSan ? true : false})} */}
        {renderFormInputColum({...ten})}
        {/* <Col span={4}>
          <Form.Item name="so_luong" label="Số lượng" rules={[{required: true, message: "Vui lòng chọn số lượng"}]}>
            <InputNumber name="so_luong" className="!h-100 w-full" />
          </Form.Item>
        </Col> */}

        {renderFormInputColum({...so_luong})}
        {renderFormInputColum({...don_gia})}
        {renderFormInputColum({...tong_tien, disabled: true})}
        {/* {renderFormInputColum({...nam_sx})} */}
        <Col span={4}>
          <Form.Item name="nam_sx" label="Năm sản xuất" rules={[{required: true, message: "Vui lòng chọn năm sản xuất"}]}>
            <DatePicker name="nam_sx" className="w-full" picker="year" />
          </Form.Item>
        </Col>

        {renderFormInputColum({...han_sd}, 5)}
        {renderFormInputColum({...trang_thai, options: TRANG_THAI}, 7)}

        {renderFormInputColum({...so_id_dt})}
        {renderFormInputColum({...so_id_ts})}
        {renderFormInputColum({...so_id})}
      </Row>
    </Form>
  );
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        title={
          <HeaderModal
            title={chiTietDanhSachTaiSan ? `Chi tiết tài sản ${chiTietDanhSachTaiSan.ten}` : "Tạo mới tài sản"}
            trang_thai_ten={chiTietDanhSachTaiSan?.trang_thai_ten}
            trang_thai={chiTietDanhSachTaiSan?.trang_thai}
          />
        }
        maskClosable={false}
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width={{
          xs: "55%",
          sm: "55%",
          md: "55%",
          lg: "55%",
          xl: "55%",
          xxl: "55%",
        }}
        // styles={{
        //   body: {
        //     height: "60vh",
        //   },
        // }}
        footer={renderFooter}
        className="modal-chi_tiet_tai_san [&_.ant-space]:w-full">
        {renderForm()}
      </Modal>
    </Flex>
  );
});
ModalChiTietTaiSanComponent.displayName = "ModalChiTietTaiSanComponent";
export const ModalchiTietDanhSachTaiSan = memo(ModalChiTietTaiSanComponent, isEqual);

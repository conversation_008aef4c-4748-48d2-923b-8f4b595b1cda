import {TablePaginationConfig} from "antd";
import {ColumnType} from "antd/es/table";
import {debounce} from "lodash";
import {useEffect, useRef, useState} from "react";
import {SearchOutlined} from "@ant-design/icons";
/**
 * Tính dynamic Table height
 * @param selectors - Các selector DOM cần trừ chiều cao (ví dụ header, footer,...)
 * @param padding - <PERSON><PERSON>ảng cách padding thêm (nếu cần)
 */
export function useTableHeight(selectors: string[] = [], padding = 0) {
  const [height, setHeight] = useState<number>(0);
  const resizeTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  const calculateHeight = () => {
    let offsetHeight = 0;

    selectors.forEach(selector => {
      const el = document.querySelector(selector);
      if (el) {
        offsetHeight += el.clientHeight;
      }
    });

    const windowHeight = window.innerHeight;
    setHeight(windowHeight - offsetHeight - padding);
  };

  useEffect(() => {
    const handleResize = debounce(calculateHeight, 200);

    window.addEventListener("resize", handleResize);
    calculateHeight(); // Gọi lần đầu tiên

    return () => {
      window.removeEventListener("resize", handleResize);
      handleResize.cancel();
    };
  }, [selectors, padding]);

  return height;
}

/**
 * @description : fill nốt số dòng còn lại sao cho đủ row
 * @param tableCurrentLength : length hiện tại của table, MẶC ĐỊNH = 0
 * @param tableRowMaxLength : max số dòng trong 1 table, MẶC ĐỊNH = 10
 * @returns
 */
export const fillRowTableEmpty = (tableCurrentLength = 0, tableRowMaxLength = 13) => {
  let arrEmptyRow: Array<any> = [];
  try {
    if (tableCurrentLength < tableRowMaxLength) {
      arrEmptyRow = Array.from({length: tableRowMaxLength - tableCurrentLength}, (item, index) => ({
        key: `empty-${index}`,
        so_id: index,
      }));
    }
    return arrEmptyRow;
  } catch (error) {
    console.log("fillRowTableEmpty", error);
    return arrEmptyRow;
  }
};

export const defaultPaginationTableProps: TablePaginationConfig = {
  // pageSize: 10,
  // align: "end", ???
  // current: 2,//set current page
  // defaultCurrent : 1,//page khởi tạoe
  //disabled : Disable pagination
  //itemRender : Để tùy chỉnh item's innerHTML
  //pageSize : Số lượng mục dữ liệu trên mỗi trang
  // responsive : Nếu size không được chỉ định, Pagination sẽ thay đổi kích thước theo chiều rộng của cửa sổ
  // size: "small",//size của pagination : default / small
  defaultPageSize: 13, //Số lượng item dữ liệu mặc định trên mỗi trang
  // onShowSizeChange : Được gọi khi pageSize thay đổi
  // onShowSizeChange: (current, size) => {
  //   // console.log("onShowSizeChange", current, size);
  // },
  // showTitle: false,// Hiển thị tiêu đề của item trang
  //showTotal : hiển thị UI bao nhiêu / bao nhiêu bản ghi
  showTotal: (total: number, range: [number, number]) => {
    return `${range[0]}-${range[1]} của ${total} bản ghi`;
  },
  showQuickJumper: true, // Xác định xem bạn có thể nhảy trực tiếp đến các trang hay không
  /* showSizeChanger : Xác định xem có hiển thị pageSize select hay không, nó sẽ đúng khi tổng > 50,
                 kết hợp với pageSizeOptions, cái này có thể tuỳ chỉnh được nữa. xem DOC
          */
  showSizeChanger: false,
  position: ["bottomCenter"], //vị trí của pagination
  // hideOnSinglePage: false, //ẩn pagination khi chỉ có 1 page
  // pageSizeOptions: [10, 20, 50, 100], //Chỉ định các tùy chọn sizeChanger, kết hợp với showSizeChanger
  locale: {
    jump_to: "Tới trang",
    page: "",
    // items_per_page: " / trang",
  },
};

export const defaultTableProps: any = {
  scroll: {
    /* x : max-content : tự động tính toán theo độ rộng nội dung, thuộc tính ellipsis của từng cell sẽ không hoạt động 
    nếu text dài quá độ rộng của col thì sẽ xuống dòng mà k chuyển thành ... */
    /* x : true : tự động tính toán theo độ rộng nội dung, thuộc tính ellipsis của từng cell sẽ không hoạt động 
    nếu text dài quá độ rộng của col thì sẽ vẫn ở 1 dòng và kéo dài width của ô mà k chuyển thành ... */
    // x: true, //
    x: "100%",
  },
  // sticky: false, //giữ header khi scroll
  // showHeader :true //show header của table
  bordered: true, // set border cho cả table
};

export const defaultTableColumnsProps: ColumnType<any> = {
  onHeaderCell: () => ({
    className: "!p-2 !font-bold !text-center !text-[#FFF] !bg-[#96bf49]",
  }),
  onCell: () => ({
    className: "!py-1 text-[11px]",
  }),
  ellipsis: true,
  align: "center",
  /**
   * filterIcon : icon hiển thị trên header column khi filter
   * @param filtered :  // "filtered". là boolean, true nếu đang áp dụng filter,
   *  biến này thay đổi khi hàm confirm được gọi, hoặc hàm clearFilters được gọi
   * @returns
   */
  filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} size={40} />,
  filterSearch: true,
};

export const colWidthByKey = {
  sott: 60,
  ngay_tao: 130,
  nguoi_tao: 110,
  ngay_cap_nhat: 130,
  nguoi_cap_nhat: 120,
};

import {InputProps} from "@src/components/Input";
import {DatePickerProps, FormItemProps, InputNumberProps, SelectProps, SwitchProps} from "antd";
import {TimePickerProps} from "antd/lib";
import {NumericFormatProps} from "react-number-format";

export type RenderedComponent =
  | "select"
  | "multi-select"
  | "date-picker"
  | "time-picker"
  | "input"
  | "input-auto-complete"
  | "textarea"
  | "date-time-picker"
  | "switch"
  | "text-editor"
  | "dynamic-input"
  | "input-price"
  | "checkbox"
  | "segmented"
  | "radio-group"
  | "basic-radio-group"
  | "image-picker"
  | "input-password"
  | "input-currency"
  | "input-number";

export type CustomInputProps = {
  component?: RenderedComponent;
  errorMessage?: string;
  allowNegative?: boolean;
  normalize?: (value: any) => any;
  // subTitle?: string | ReactNode;
  // helpMessage?: string;
};

export type AntInputProps = InputProps & SwitchProps & FormItemProps & SelectProps & DatePickerProps & TimePickerProps & InputNumberProps & NumericFormatProps;

export type IFormInput = CustomInputProps & AntInputProps;

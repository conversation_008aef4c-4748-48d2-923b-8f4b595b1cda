import {isEqual} from "lodash";
import {memo} from "react";
import Highlighter from "react-highlight-words";
import "./index.dark.scss";
import "./index.default.scss";

const HighlighterTextSearch = Highlighter as unknown as React.FC<any>; //tạm thời vượt qua lỗi 'Highlighter' cannot be used as a JSX component...
interface HeaderProps {
  searchWords: string[];
  textToHighlight?: string;
  highlightClassName?: string;
}

const HighlighterComponent = ({searchWords, textToHighlight, highlightClassName = ""}: HeaderProps) => {
  return (
    <HighlighterTextSearch
      highlightClassName={`py-[0.25rem] bg-[#96bf49] ${highlightClassName}`} //style cho text word được hightlight
      className="text-[11px]" // style cho toàn bộ text
      searchWords={searchWords}
      autoEscape
      textToHighlight={textToHighlight ? textToHighlight.toString() : ""}
    />
  );
};
export default memo(HighlighterComponent, isEqual);

import React, {PropsWithChildren, useMemo} from "react";
import {DemoContext} from "./index.context";
import {IDemoContextProps} from "./index.model";

const DemoProvider: React.FC<PropsWithChildren> = props => {
  const {children} = props;

  const value = useMemo<IDemoContextProps>(() => ({}), []);

  return <DemoContext.Provider value={value}>{children}</DemoContext.Provider>;
};

export default DemoProvider;

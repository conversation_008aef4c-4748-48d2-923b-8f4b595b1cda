import {IFormInput} from "@src/@types";
import {colWidthByKey, defaultTableColumnsProps} from "@src/hooks";
import {TableProps} from "antd";

export interface TableHeThongChucNangDataType {
  key?: string;
  stt?: number;
  ma?: string;
  ten?: string;
  loai?: string;
  kieu_ad?: string;
  ten_nhom?: string;
  kieu_ad_ten?: string;
  ngay_tao?: string;
  nguoi_tao?: string;
  ngay_cap_nhat?: string;
  nguoi_cap_nhat?: string;
  trang_thai_ten?: string;
}

// ĐỊNH NGHĨA Column RENDER RA TỪNG Ô TRONG CỘT
//đọc https://ant.design/components/table#column để biết thêm về column
export const heThongChucNangColumns: TableProps<TableHeThongChucNangDataType>["columns"] = [
  {...defaultTableColumnsProps, title: "STT", dataIndex: "stt", key: "stt", width: colWidthByKey.sott, align: "center"},
  {...defaultTableColumnsProps, title: "Mã", dataIndex: "ma", key: "ma", width: 250, align: "left"},
  {...defaultTableColumnsProps, title: "Tên", dataIndex: "ten", key: "ten", width: 250, align: "left"},
  {...defaultTableColumnsProps, title: "Loại", dataIndex: "loai", key: "loai", width: 80, align: "center"},
  {
    ...defaultTableColumnsProps,
    title: "Tên nhóm",
    dataIndex: "ten_nhom",
    key: "ten_nhom",
    width: 200,
    align: "center",
    render: text => <span style={{color: "#e74c3c"}}>{text}</span>,
  },
  {...defaultTableColumnsProps, title: "Kiểu áp dụng", dataIndex: "kieu_ad_ten", key: "kieu_ad_ten", width: 200, align: "center"},
  {...defaultTableColumnsProps, title: "Ngày tạo", dataIndex: "ngay_tao", key: "ngay_tao", width: colWidthByKey.ngay_tao, align: "center"},
  {...defaultTableColumnsProps, title: "Người tạo", dataIndex: "nguoi_tao", key: "nguoi_tao", width: colWidthByKey.nguoi_tao, align: "center"},
  {...defaultTableColumnsProps, title: "Ngày cập nhật", dataIndex: "ngay_cap_nhat", key: "ngay_cap_nhat", width: colWidthByKey.ngay_cap_nhat, align: "center"},
  {...defaultTableColumnsProps, title: "Người cập nhật", dataIndex: "nguoi_cap_nhat", key: "nguoi_cap_nhat", width: colWidthByKey.nguoi_cap_nhat, align: "center"},
  {...defaultTableColumnsProps, title: "Trạng thái", dataIndex: "trang_thai_ten", key: "trang_thai_ten", width: 120, align: "center"},
];

export const setFormFields = (form: any, chiTietChucNang: any) => {
  if (chiTietChucNang) {
    form.setFields([
      {
        name: "ten",
        value: chiTietChucNang.ten || "",
      },
      {
        name: "ma",
        value: chiTietChucNang.ma || "",
      },
      {
        name: "loai",
        value: chiTietChucNang.loai || "",
      },
      {
        name: "kieu_ad",
        value: chiTietChucNang.kieu_ad || "",
      },
    ]);
  }
};

export const radioItemTrangThaiChucNangTable = [
  {value: "Đang sử dụng", text: "Đang sử dụng"},
  {value: "Ngưng sử dụng", text: "Ngưng sử dụng"},
];

//option select trạng thái
export const optionTrangThaiHeThongChucNangSelect = [
  {ma: "", ten: "Tất cả"},
  {ma: "D", ten: "Đang sử dụng"},
  {ma: "K", ten: "Ngưng sử dụng"},
];

//Form
export interface IFormTimKiemPhanTrangHeThongChucNangFieldsConfig {
  ten: IFormInput;
  loai: IFormInput;
  kieu_ad: IFormInput;
  trang_thai: IFormInput;
}
export const FormTimKiemPhanTrangHeThongChucNang: IFormTimKiemPhanTrangHeThongChucNangFieldsConfig = {
  ten: {
    component: "input",
    name: "ten",
    label: "Tên chức năng",
    placeholder: "Nhập tên chức năng",
  },
  loai: {
    component: "select",
    name: "loai",
    label: "Loại chức năng",
    placeholder: "Chọn loại chức năng",
  },
  kieu_ad: {
    component: "select",
    name: "kieu_ad",
    label: "Kiểu áp dụng",
    placeholder: "Chọn kiểu áp dụng",
  },
  trang_thai: {
    component: "select",
    name: "trang_thai",
    label: "Trạng thái",
    placeholder: "Chọn trạng thái",
  },
};
//option select loại chức năng
export const optionLoaiChucNangData = [
  {ma: "NHAP", ten: "NHAP"},
  {ma: "XEM", ten: "XEM"},
];
//option select kiểu áp dụng
export const optionLoaiKieuApDungData = [
  {ma: "K", ten: "AD riêng cho từng tài khoản"},
  {ma: "C", ten: "AD chung cho tất cả tài khoản"},
];

import {IFormInput, ReactQuery} from "@src/@types";
import {colWidthByKey, defaultTableColumnsProps} from "@src/hooks";
import {TableProps} from "antd";
export interface TablePhuongThucKhaiThacDataType {
  key: number | string;
  stt?: number;
  ma?: string;
  ma_doi_tac_ql?: string;
  ten?: string;
  doi_tac?: string;
  ngay_tao?: number;
  nguoi_tao?: string;
  ngay_cap_nhat?: string;
  nguoi_cap_nhat?: string;
  trang_thai?: string;
  trang_thai_ten?: string;
  doi_tac_ql_ten_tat?: string;
  ma_doi_tac?: string;
}

const onHeaderCell = () => ({
  className: "header-cell-custom",
});
// ĐỊNH NGHĨA Column RENDER RA TỪNG Ô TRONG CỘT
//đọc https://ant.design/components/table#column để biết thêm về column
export const Phuo<PERSON><PERSON><PERSON><PERSON><PERSON>haiThacColumns: TableProps<TablePhuongThucKhaiThacDataType>["columns"] = [
  {title: "STT", dataIndex: "stt", width: colWidthByKey.sott, align: "center", ...defaultTableColumnsProps},
  {title: "Mã", dataIndex: "ma", key: "ma", width: 80, align: "center", ...defaultTableColumnsProps},
  {title: "Tên", dataIndex: "ten", key: "ten", width: 200, align: "left", ...defaultTableColumnsProps},
  {title: "Đối tác", dataIndex: "doi_tac_ql_ten_tat", key: "doi_tac_ql_ten_tat", width: 200, align: "center", ...defaultTableColumnsProps},
  {title: "Ngày tạo", dataIndex: "ngay_tao", key: "ngay_tao", width: colWidthByKey.ngay_tao, align: "center", ...defaultTableColumnsProps},
  {title: "Người tạo", dataIndex: "nguoi_tao", key: "nguoi_tao", width: colWidthByKey.nguoi_tao, align: "center", ...defaultTableColumnsProps},
  {title: "Ngày cập nhật", dataIndex: "ngay_cap_nhat", key: "ngay_cap_nhat", width: colWidthByKey.ngay_cap_nhat, align: "center", ...defaultTableColumnsProps},
  {title: "Người cập nhật", dataIndex: "nguoi_cap_nhat", key: "nguoi_cap_nhat", width: colWidthByKey.nguoi_cap_nhat, align: "center", ...defaultTableColumnsProps},
  {title: "Trạng thái tên", dataIndex: "trang_thai_ten", key: "trang_thai_ten", width: 130, align: "center", ...defaultTableColumnsProps},
];

export const setFormFields = (form: any, chiTietHopDong: any) => {
  if (chiTietHopDong) {
    form.setFields([
      {
        name: "ten",
        value: chiTietHopDong.ten || "",
      },
      {
        name: "ma",
        value: chiTietHopDong.ma || "",
      },
      {
        name: "loai",
        value: chiTietHopDong.loai || "",
      },
      {
        name: "kieu_ad",
        value: chiTietHopDong.kieu_ad || "",
      },
    ]);
  }
};

export const radioItemTrangThaiHopDongTable = [
  {value: "Đang sử dụng", text: "Đang sử dụng"},
  {value: "Ngưng sử dụng", text: "Ngưng sử dụng"},
];

//option select trạng thái
export const optionTrangThaiHopDongSelect = [
  {ma: "", ten: "Tất cả"},
  {ma: "D", ten: "Đang sử dụng"},
  {ma: "K", ten: "Ngưng sử dụng"},
];

//option select nghiệp vụ
export const optionNghiepVuSelect = [
  {ma: "", ten: "Tất cả"},
  {ma: "D", ten: "Đang sử dụng"},
  {ma: "K", ten: "Ngưng sử dụng"},
];

//Form
export interface IFormTimKiemPhanTrangPhuongThucKhaiThacFieldsConfig {
  ma_doi_tac_ql: IFormInput;
  ma: IFormInput;
  ten: IFormInput;
  stt: IFormInput;
  trang_thai: IFormInput;
}

export const FormTimKiemPhanTrangPhuongThucKhaiThac: IFormTimKiemPhanTrangPhuongThucKhaiThacFieldsConfig = {
  ma: {
    component: "input",
    name: "ma",
    label: "Mã phương thức KT",
    placeholder: "Mã phương thức khai thác",
  },
  ma_doi_tac_ql: {
    component: "select",
    name: "ma_doi_tac_ql",
    label: "Đối tác",
    placeholder: "Chọn đối tác",
  },
  ten: {
    component: "input",
    name: "ten",
    label: "Tên phương thức KT",
    placeholder: "Tên phương thức khai thác",
  },
  stt: {
    name: "stt",
    component: "input",
    label: "Thứ tự hiển thị",
    placeholder: "Thứ tự hiển thị",
  },
  trang_thai: {
    component: "select",
    name: "trang_thai",
    label: "Trạng thái",
    placeholder: "Chọn trạng thái",
  },
};
//option select loại chức năng
export const optionLoaiChucNangData = [
  {value: "NHAP", label: "NHAP"},
  {value: "XEM", label: "XEM"},
];
//option select kiểu áp dụng
export const optionLoaiKieuApDungData = [
  {value: "K", label: "AD riêng cho từng tài khoản"},
  {value: "C", label: "AD chung cho tất cả tài khoản"},
];

// defaultFormValue tìm kiếm phân trang hđ xe
export const defaultFormValue: ReactQuery.ITimKiemPhanTrangPhuongThucKhaiThacParams = {
  ma_doi_tac_ql: "",
  ma: "",
  ten: "",
  trang_thai: "",
  // trang: 1,
  // so_dong: 10,
};

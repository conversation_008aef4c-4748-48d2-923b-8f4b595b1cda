import {createContext, useContext} from "react";

import {INhomHangMucXeContextProps} from "./index.model";

/**
 * Context cho phép các component con truy cập state và functions mà không cần prop drilling
 */
export const NhomHangMucXeContext = createContext<INhomHangMucXeContextProps>({
  //trạng thái dt
  listNhomHangMucXe: [], 
  tongSoDong: 0, 
  loading: false, 
  filterParams: {}, // Tham số filter và phân trang

  //hàm api
  getListNhomHangMucXe: async () => Promise.resolve(),
  getChiTietNhomHangMucXe: async () => Promise.resolve({} as CommonExecute.Execute.INhomHangMucXe),
  capNhatChiTietNhomHangMucXe: async () => Promise.resolve(), 

  //hàm update state  
  setFilterParams: () => {}, 
});

/**
 * CUSTOM HOOK - HOOK ĐỂ CÁC COMPONENT CON CÓ THỂ DỄ DÀNG ACCESS CONTEXT 
 */
export const useNhomHangMucXeContext = () => useContext(NhomHangMucXeContext);

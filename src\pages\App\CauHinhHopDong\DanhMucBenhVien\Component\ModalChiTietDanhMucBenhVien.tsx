/**
 * T<PERSON><PERSON> dụng: Modal hiển thị form chi tiết bệnh viện để thêm mới/chỉnh sửa
 */
import React, {forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useState} from "react";
import {Col, Form, Modal, Row} from "antd";

// import {Col, Form, Modal, Row} from "antd";
import {useForm} from "antd/es/form/Form";
import {FormInput, HeaderModal} from "@src/components";
import {ReactQuery} from "@src/@types";
import {useDanhMucBenhVienContext} from "../index.context";
import {FormTaoMoiBenhVien, radioItemTrangThaiBenhVienTable, radioItemNhomBenhVienSelect, radioItemLoaiBenhVienSelect, radioItemApDungBHYTSelect, radioItemBoiLeSelect} from "../index.configs";

export interface IModalChiTietBenhVienRef {
  open: (data?: CommonExecute.Execute.IDanhMucBenhVien) => void;
  close: () => void;
}

interface IModalChiTietBenhVienProps {
  onAfterSave?: () => void;
  danhSachTinhThanh?: any[];
  // danhSachQuanHuyen?: any[];
  danhSachNganHang?: any[];
  danhSachChiNhanhNganHang?: any[];
}

const ModalChiTietBenhVien = forwardRef<IModalChiTietBenhVienRef, IModalChiTietBenhVienProps>(({onAfterSave, danhSachTinhThanh = [], danhSachNganHang = [], danhSachChiNhanhNganHang = []}, ref) => {
  const [form] = useForm();
  const [isOpen, setIsOpen] = useState(false);
  const [chiTietBenhVien, setChiTietBenhVien] = useState<CommonExecute.Execute.IDanhMucBenhVien | null>(null);
  const [confirmLoading, setConfirmLoading] = useState(false);

  const {capNhatChiTietBenhVien, getListChiNhanhNganHang, listBenhVien} = useDanhMucBenhVienContext();

  //Tác dụng: Expose methods cho parent component
  useImperativeHandle(ref, () => ({
    open: async data => {
      setIsOpen(true);
      setChiTietBenhVien(data || null);

      // Load quận huyện theo tỉnh thành của data (nếu có)
      // if (data?.tinh_thanh) {
      //   await getListQuanHuyen({ma_tinh: data.tinh_thanh});
      // }
      //ngân hàng
      if (data?.tk_ngan_hang) {
        await getListChiNhanhNganHang({ma_ngan_hang: data.tk_ngan_hang});
      }
    },
    close: () => {
      setIsOpen(false);
      setChiTietBenhVien(null);
      form.resetFields();
    },
  }));

  //Tác dụng: Tạo dropdown options cho tỉnh thành
  const dropdownOptionsTinhThanh = useMemo(() => {
    if (!danhSachTinhThanh || !Array.isArray(danhSachTinhThanh)) {
      return [];
    }

    return danhSachTinhThanh
      .filter(item => item && item.ma && item.ten)
      .map(item => ({
        ma: item.ma?.toString() || "",
        ten: item.ten?.toString() || "",
      }));
  }, [danhSachTinhThanh]);

  //Tác dụng: Tạo dropdown options cho quận huyện
  // const dropdownOptionsQuanHuyen = useMemo(() => {
  //   if (!danhSachQuanHuyen || !Array.isArray(danhSachQuanHuyen)) {
  //     return [];
  //   }

  //   return danhSachQuanHuyen
  //     .filter(item => item && item.ma && item.ten)
  //     .map(item => ({
  //       ma: item.ma?.toString() || "",
  //       ten: item.ten?.toString() || "",
  //     }));
  // }, [danhSachQuanHuyen]);

  //Tác dụng: Tạo dropdown options cho ngân hàng
  // const dropdownOptionsNganHang = useMemo(() => {
  //   if (!danhSachNganHang || !Array.isArray(danhSachNganHang)) {
  //     return [];
  //   }

  //   return danhSachNganHang
  //     .filter(item => item && item.ma && item.ten)
  //     .map(item => ({
  //       ma: item.ma?.toString() || "",
  //       ten: item.ten?.toString() || "",
  //     }));
  // }, [danhSachNganHang]);
  //Tác dụng: Tạo dropdown options chi nhánh ngân hàng
  // const dropdownOptionsChiNhanhNganHang = useMemo(() => {
  //   if (!danhSachChiNhanhNganHang || !Array.isArray(danhSachChiNhanhNganHang)) {
  //     return [];
  //   }

  //   return danhSachChiNhanhNganHang
  //     .filter(item => item && item.ma && item.ten)
  //     .map(item => ({
  //       ma: item.ma?.toString() || "",
  //       ten: item.ten?.toString() || "",
  //     }));
  // }, [danhSachChiNhanhNganHang]);

  //Tác dụng: Load form data khi có chi tiết bệnh viện
  useEffect(() => {
    if (chiTietBenhVien && isOpen) {
      form.setFieldsValue({
        ma: chiTietBenhVien.ma || "",
        ten: chiTietBenhVien.ten || "",
        mst: chiTietBenhVien.mst || "",
        dia_chi: chiTietBenhVien.dia_chi || "",
        dthoai: chiTietBenhVien.dthoai || "",
        email: chiTietBenhVien.email || "",
        nhom_bv: chiTietBenhVien.nhom_bv || "",
        loai: chiTietBenhVien.loai || "",
        tinh_thanh: chiTietBenhVien.tinh_thanh || "",
        // quan_huyen: chiTietBenhVien.quan_huyen || "",
        ad_bhyt: chiTietBenhVien.ad_bhyt || "",
        bl_nt: chiTietBenhVien.bl_nt || "",
        bl_gt: chiTietBenhVien.bl_gt || "",
        bl_ra: chiTietBenhVien.bl_ra || "",
        tk_ngan_hang: chiTietBenhVien.tk_ngan_hang || "",
        tk_chi_nhanh: chiTietBenhVien.tk_chi_nhanh || "",
        tk_so: chiTietBenhVien.tk_so || "",
        tk_ten: chiTietBenhVien.tk_ten || "",
        nguoi_lhe: chiTietBenhVien.nguoi_lhe || "",
        dthoai_lhe: chiTietBenhVien.dthoai_lhe || "",
        email_lhe: chiTietBenhVien.email_lhe || "",
        stt: chiTietBenhVien.stt || "",
        trang_thai: chiTietBenhVien.trang_thai || "D",
      });
    } else if (!chiTietBenhVien && isOpen) {
      // Reset form cho trường hợp tạo mới
      form.setFieldsValue({
        ma: "",
        ten: "",
        mst: "",
        dia_chi: "",
        dthoai: "",
        email: "",
        nhom_bv: "",
        loai: "",
        tinh_thanh: "",
        // quan_huyen: "",
        ad_bhyt: "",
        bl_nt: "",
        bl_gt: "",
        bl_ra: "",
        tk_ngan_hang: "",
        tk_chi_nhanh: "",
        tk_so: "",
        tk_ten: "",
        nguoi_lhe: "",
        dthoai_lhe: "",
        email_lhe: "",
        stt: "",
        trang_thai: "D",
      });
    }
  }, [chiTietBenhVien, isOpen, form]);

  //Tác dụng: Xử lý thay đổi tỉnh thành để load quận huyện tương ứng
  // const handleTinhThanhChange = useCallback(async (value: any) => {
  //   // Reset quận huyện khi thay đổi tỉnh thành
  //   form.setFieldValue("quan_huyen", "");

  //   // Load danh sách quận huyện theo tỉnh thành được chọn
  //   if (value && value !== "") {
  //     await getListQuanHuyen({ma_tinh: value});
  //   }
  // }, [form, getListQuanHuyen]);

  //Tác dụng: Xử lý thay đổi tỉnh ngân hàng
  const handleSearchNganHangChange = useCallback(
    (value: any) => {
      //Khi chọn tỉnh thành khác, load danh sách quận huyện tương ứng
      if (value && value !== "") {
        getListChiNhanhNganHang({ma_ngan_hang: value});
      } else {
        //Nếu chọn "Tất cả", clear danh sách quận huyện
        getListChiNhanhNganHang({ma_ngan_hang: ""});
      }
    },
    [getListChiNhanhNganHang],
  );

  const onConfirm = useCallback(async () => {
    try {
      await form.validateFields();
      const values = form.getFieldsValue();

      setConfirmLoading(true);

      // Tạo object params, không bao gồm `ma` nếu là tạo mới
      const params: ReactQuery.ICapNhatDanhMucBenhVienParams = {
        ...(chiTietBenhVien ? {ma: chiTietBenhVien.ma} : {}),
        ten: values.ten,
        mst: values.mst,
        dia_chi: values.dia_chi,
        dthoai: values.dthoai,
        email: values.email,
        nhom_bv: values.nhom_bv,
        loai: values.loai,
        tinh_thanh: values.tinh_thanh,
        // quan_huyen: values.quan_huyen,
        ad_bhyt: values.ad_bhyt,
        bl_nt: values.bl_nt,
        bl_gt: values.bl_gt,
        bl_ra: values.bl_ra,
        tk_ngan_hang: values.tk_ngan_hang,
        tk_chi_nhanh: values.tk_chi_nhanh,
        tk_so: values.tk_so,
        tk_ten: values.tk_ten,
        nguoi_lhe: values.nguoi_lhe,
        dthoai_lhe: values.dthoai_lhe,
        email_lhe: values.email_lhe,
        stt: values.stt,
        trang_thai: values.trang_thai,
      };

      // Gọi API cập nhật (nếu có chiTietBenhVien thì là edit, ngược lại là thêm mới)
      await capNhatChiTietBenhVien(params, !!chiTietBenhVien);

      // Đóng modal và reset
      setIsOpen(false);
      setChiTietBenhVien(null);
      form.resetFields();
      onAfterSave?.();
    } catch (error) {
      // Error đã được xử lý ở provider
    } finally {
      setConfirmLoading(false);
    }
  }, [form, chiTietBenhVien, capNhatChiTietBenhVien, onAfterSave]);

  //Tác dụng: Xử lý đóng modal
  const onCancel = useCallback(() => {
    setIsOpen(false);
    setChiTietBenhVien(null);
    form.resetFields();
  }, [form]);

  const renderFormInput = useCallback(
    (props: any, span = 12) => (
      <Col span={span}>
        <FormInput {...props} />
      </Col>
    ),
    [],
  );
  const renderFormColumn = (props: any, colSpan = 12) => (
    <Col span={colSpan}>
      <FormInput {...props} />
    </Col>
  );

  const renderFormBenhVien = () => (
    <Form form={form} layout="vertical" className="mt-4">
      <Row gutter={16}>
        {renderFormColumn({...FormTaoMoiBenhVien.ma, disabled: true}, 5)}
        {renderFormColumn(FormTaoMoiBenhVien.ten, 13)}
        {renderFormColumn(FormTaoMoiBenhVien.mst, 6)}
      </Row>

      <Row gutter={16}>
        {renderFormColumn(FormTaoMoiBenhVien.dia_chi, 18)}
        {renderFormColumn(FormTaoMoiBenhVien.email, 6)}
      </Row>

      <Row gutter={16}>
        {renderFormColumn(FormTaoMoiBenhVien.dthoai, 6)}
        {renderFormColumn({...FormTaoMoiBenhVien.nhom_bv, options: radioItemNhomBenhVienSelect}, 6)}
        {renderFormColumn({...FormTaoMoiBenhVien.loai, options: radioItemLoaiBenhVienSelect}, 6)}
        {renderFormColumn(
          {
            ...FormTaoMoiBenhVien.tinh_thanh,
            options: dropdownOptionsTinhThanh,
            // onChange: handleTinhThanhChange
          },
          6,
        )}
      </Row>

      <Row gutter={16}>
        {renderFormColumn({...FormTaoMoiBenhVien.ad_bhyt, options: radioItemApDungBHYTSelect}, 6)}
        {renderFormColumn({...FormTaoMoiBenhVien.bl_nt, options: radioItemBoiLeSelect}, 6)}
        {renderFormColumn({...FormTaoMoiBenhVien.bl_gt, options: radioItemBoiLeSelect}, 6)}
        {renderFormColumn({...FormTaoMoiBenhVien.bl_ra, options: radioItemBoiLeSelect}, 6)}
      </Row>

      <Row gutter={16}>
        {renderFormColumn(
          {
            ...FormTaoMoiBenhVien.tk_ngan_hang,
            // options: dropdownOptionsNganHang,
            // onChange: handleSearchNganHangChange
          },
          10,
        )}
        {renderFormColumn(
          {
            ...FormTaoMoiBenhVien.tk_chi_nhanh,
            // options: dropdownOptionsChiNhanhNganHang
          },
          8,
        )}
        {renderFormColumn(FormTaoMoiBenhVien.tk_so, 6)}
      </Row>

      <Row gutter={16}>
        {renderFormColumn(FormTaoMoiBenhVien.tk_ten, 8)}
        {renderFormColumn(FormTaoMoiBenhVien.nguoi_lhe, 10)}
        {renderFormColumn(FormTaoMoiBenhVien.dthoai_lhe, 6)}
      </Row>

      <Row gutter={16}>
        {renderFormColumn(FormTaoMoiBenhVien.email_lhe, 12)}
        {renderFormColumn(FormTaoMoiBenhVien.stt, 6)}
        {renderFormColumn({...FormTaoMoiBenhVien.trang_thai, options: radioItemTrangThaiBenhVienTable}, 6)}
      </Row>
    </Form>
  );

  return (
    <Modal
      title={
        <HeaderModal title={chiTietBenhVien ? `${chiTietBenhVien.ten}` : "Tạo mới danh mục bệnh viện"} trang_thai_ten={chiTietBenhVien?.trang_thai_ten} trang_thai={chiTietBenhVien?.trang_thai} />
      }
      maskClosable={false}
      open={isOpen}
      onOk={onConfirm}
      onCancel={onCancel}
      width={1200}
      confirmLoading={confirmLoading}
      okText="Lưu"
      cancelText="Hủy"
      destroyOnClose
      styles={{
        body: {
          paddingTop: "8px",
          paddingBottom: "16px",
        },
      }}>
      {renderFormBenhVien()}
    </Modal>
  );
});

ModalChiTietBenhVien.displayName = "ModalChiTietBenhVien";
export default ModalChiTietBenhVien;

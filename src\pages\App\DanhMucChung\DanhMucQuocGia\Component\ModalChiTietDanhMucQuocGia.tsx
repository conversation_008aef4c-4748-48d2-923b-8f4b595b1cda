import React, {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useState} from "react";
import {Button, Col, Flex, Form, Modal, Row} from "antd";
import {ArrowLeftOutlined, CheckOutlined} from "@ant-design/icons";
import {isEqual} from "lodash";

import {FormInput, HeaderModal, Popcomfirm} from "@src/components";
import {ReactQuery} from "@src/@types";

import {useDanhMucQuocGiaContext} from "../index.context";
import {FormTaoMoiQuocGia, TRANG_THAI_TAO_MOI_QUOC_GIA} from "../index.configs";
import {IModalChiTietQuocGiaRef} from "./index.configs";

interface IModalChiTietQuocGiaProps {
  onAfterSave?: () => void;
  danhSachChauLuc?: Array<{ma: string; ten: string}>;
  danhSachKhuVuc?: Array<{ma: string; ten: string}>; // Danh sách khu vực cho dropdown
}

const ModalChiTietQuocGiaComponent = forwardRef<IModalChiTietQuocGiaRef, IModalChiTietQuocGiaProps>(({onAfterSave, danhSachChauLuc = [], danhSachKhuVuc = []}, ref) => {
  // ===== CONTEXT & FORM =====
  const {capNhatChiTietQuocGia, listQuocGia, loading, getListKhuVuc} = useDanhMucQuocGiaContext();
  const [form] = Form.useForm();

  // ===== STATE =====
  // Kiểm soát hiển thị/ẩn modal
  const [isOpen, setIsOpen] = useState<boolean>(false);
  // Dữ liệu quốc gia đang được chỉnh sửa (null = tạo mới)
  const [chiTietQuocGia, setChiTietQuocGia] = useState<CommonExecute.Execute.IDanhMucQuocGia | null>(null);
  // Kiểm soát button lưu có được enable hay không
  const [disableSubmit, setDisableSubmit] = useState<boolean>(true);
  // Danh sách khu vực động theo châu lục được chọn
  const [currentKhuVucOptions, setCurrentKhuVucOptions] = useState<Array<{ma: string; ten: string}>>(danhSachKhuVuc);

  // ===== FORM VALUES WATCHING =====
  const formValues = Form.useWatch([], form);

  // ===== FORM CONFIGURATIONS =====
  const {ma_chau_luc, ma_khu_vuc, ma, ten, stt, trang_thai} = FormTaoMoiQuocGia;

  // ===== DROPDOWN OPTIONS =====
  const ChauLucOptions = danhSachChauLuc.filter(item => item.ma !== ""); // Bỏ "Tất cả" option
  const trangThaiOptions = TRANG_THAI_TAO_MOI_QUOC_GIA;

  // Chức năng: Expose method để parent component có thể gọi mở modal từ bên ngoài
  useImperativeHandle(ref, () => ({
    open: async (data?: CommonExecute.Execute.IDanhMucQuocGia) => {
      // Reset state trước khi set data mới
      setChiTietQuocGia(null);
      setCurrentKhuVucOptions([]);
      form.resetFields();

      // Mở modal trước
      setIsOpen(true);

      // Sau đó set data
      if (data) {
        setChiTietQuocGia(data);

        // Nếu có châu lục, load khu vực ngay lập tức
        if (data.ma_chau_luc) {
          try {
            await getListKhuVuc({ma_chau_luc: data.ma_chau_luc});
          } catch (error) {
            // Xử lý lỗi thầm lặng
          }
        }
      }
    },
  }));

  // Chức năng: Điền dữ liệu vào form khi modal mở (chỉ cho create mode)
  useEffect(() => {
    if (!chiTietQuocGia && isOpen) {
      // Tạo mới - set giá trị mặc định
      form.setFields([
        {name: "ma_chau_luc", value: undefined},
        {name: "ma_khu_vuc", value: undefined},
        {name: "ma", value: ""},
        {name: "ten", value: ""},
        {name: "stt", value: ""},
        {name: "trang_thai", value: "D"},
      ]);
      setCurrentKhuVucOptions([]);
    }
  }, [chiTietQuocGia, isOpen, form]); // Chỉ xử lý create mode

  // Chức năng: Cập nhật danh sách khu vực khi props thay đổi từ provider
  useEffect(() => {
    const validOptions = danhSachKhuVuc.filter(item => item.ma !== ""); // Bỏ "Tất cả" option

    // Chỉ update nếu thực sự có thay đổi
    setCurrentKhuVucOptions(prev => {
      // So sánh để tránh update không cần thiết
      if (JSON.stringify(validOptions) !== JSON.stringify(prev)) {
        return validOptions;
      }
      return prev;
    });
  }, [danhSachKhuVuc]); // Bỏ currentKhuVucOptions khỏi dependency để tránh vòng lặp

  // Chức năng: Điền form khi khu vực đã load xong cho edit mode
  useEffect(() => {
    if (chiTietQuocGia && isOpen && danhSachKhuVuc.length > 0) {
      // Chỉ điền form khi đang edit và có khu vực từ props
      const validOptions = danhSachKhuVuc.filter(item => item.ma !== "");
      if (validOptions.length > 0) {
        // Điền lại form data
        const arrFormData = [];
        for (const key in chiTietQuocGia) {
          const fieldName = key;
          const fieldValue = chiTietQuocGia[key as keyof CommonExecute.Execute.IDanhMucQuocGia];

          arrFormData.push({
            name: fieldName,
            value: fieldValue,
          });
        }
        form.setFields(arrFormData);
      }
    }
  }, [chiTietQuocGia, isOpen, danhSachKhuVuc, form]); // Trigger khi có khu vực mới từ props

  // Chức năng: Re-validate form khi có thay đổi từ bên ngoài (chỉ cho edit mode)
  useEffect(() => {
    if (isOpen && chiTietQuocGia && danhSachKhuVuc.length > 0) {
      // Chỉ trigger re-validation khi đang edit và có dữ liệu khu vực từ props
      setTimeout(() => {
        form
          .validateFields({validateOnly: true})
          .then(() => setDisableSubmit(false))
          .catch(() => setDisableSubmit(true));
      }, 100);
    }
  }, [danhSachKhuVuc, isOpen, form, chiTietQuocGia]); // Dùng danhSachKhuVuc thay vì currentKhuVucOptions

  // Chức năng: Theo dõi form validation để enable/disable button lưu
  useEffect(() => {
    if (!isOpen) return;

    // Debounce validation để tránh gọi quá nhiều
    const timeoutId = setTimeout(() => {
      form
        .validateFields({validateOnly: true})
        .then(() => {
          setDisableSubmit(false);
        })
        .catch(() => {
          setDisableSubmit(true);
        });
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [form, formValues, isOpen]);

  // Chức năng: Đóng modal và reset toàn bộ state về trạng thái ban đầu
  const closeModal = useCallback(() => {
    setIsOpen(false);
    setChiTietQuocGia(null);
    setCurrentKhuVucOptions([]);
    form.resetFields();
  }, [form]);

  // Chức năng: Khi user chọn châu lục khác, reset khu vực và load danh sách khu vực mới
  const handleChauLucChange = useCallback(
    (value: any) => {
      // Reset khu vực khi thay đổi tỉnh
      form.setFieldValue("ma_quan", undefined);

      // Load khu vực cho tỉnh mới
      if (value) {
        getListKhuVuc({ma_chau_luc: value});
      } else {
        setCurrentKhuVucOptions([]);
      }
    },
    [form, getListKhuVuc],
  );

  // Chức năng: Xử lý lưu dữ liệu (tạo mới hoặc cập nhật), bao gồm validation trùng mã
  const onConfirm = async () => {
    try {
      const values: ReactQuery.ICapNhatDanhMucQuocGiaParams = form.getFieldsValue();

      // Validate mã quốc gia không trùng khi tạo mới
      // if (!chiTietQuocGia) {
      //   for (let i = 0; i < listQuocGia.length; i++) {
      //     if (listQuocGia[i].ma === values.ma) {
      //       form.setFields([
      //         {
      //           name: "ma",
      //           errors: ["Mã quốc gia đã tồn tại trong khu vực này!"],
      //         },
      //       ]);
      //       return;
      //     }
      //   }
      // }

      // Truyền thêm flag để phân biệt tạo mới vs cập nhật
      await capNhatChiTietQuocGia(values, chiTietQuocGia ? true : false);
      closeModal();
      onAfterSave?.();
    } catch (error) {
      console.error("Lỗi khi lưu quốc gia:", error);
    }
  };

  // Chức năng: Render footer modal với button Quay lại và Lưu
  const renderFooter = () => {
    return (
      <Form.Item>
        <Button type="default" onClick={() => closeModal()} className="mr-2" icon={<ArrowLeftOutlined />}>
          Quay lại
        </Button>
        <Popcomfirm
          title={disableSubmit ? "Vui lòng nhập đầy đủ thông tin" : ""}
          onConfirm={onConfirm}
          okText="Lưu"
          description="Bạn có chắc muốn lưu thông tin?"
          buttonTitle={"Lưu"}
          buttonDisable={disableSubmit}
          buttonClassName="w-40"
          buttonIcon={<CheckOutlined />}
          iconPosition="end"
          loading={loading}
        />
      </Form.Item>
    );
  };

  // Chức năng: Helper function để render 1 cột input trong form với custom column span
  const renderFormColumn = (props: any, colSpan = 8) => {
    return (
      <Col span={colSpan}>
        <FormInput {...props} />
      </Col>
    );
  };

  // Chức năng: Render form chính với layout các field quốc gia
  const renderForm = () => (
    <Form form={form} layout="vertical">
      <Row gutter={16}>
        {renderFormColumn({...ma, disabled: chiTietQuocGia ? true : false}, 8)}
        {renderFormColumn({...ten}, 8)}
        {renderFormColumn({...ma_chau_luc, options: ChauLucOptions, onChange: handleChauLucChange, disabled: chiTietQuocGia ? true : false}, 8)}
      </Row>

      <Row gutter={16}>
        {renderFormColumn({...ma_khu_vuc, options: currentKhuVucOptions, disabled: chiTietQuocGia ? true : false}, 8)}
        {renderFormColumn({...stt}, 8)}
        {renderFormColumn(
          {
            ...trang_thai,
            options: TRANG_THAI_TAO_MOI_QUOC_GIA,
          },
          8,
        )}
      </Row>
    </Form>
  );

  // ===== MAIN RENDER =====
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        title={<HeaderModal title={chiTietQuocGia ? `${chiTietQuocGia.ten}` : "Tạo mới quốc gia"} trang_thai_ten={chiTietQuocGia?.trang_thai_ten} trang_thai={chiTietQuocGia?.trang_thai} />}
        maskClosable={false}
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width={700}
        styles={{
          body: {
            paddingTop: "8px",
            paddingBottom: "16px",
          },
        }}
        footer={renderFooter()}>
        {renderForm()}
      </Modal>
    </Flex>
  );
});

ModalChiTietQuocGiaComponent.displayName = "ModalChiTietQuocGiaComponent";
export const ModalChiTietQuocGia = memo(ModalChiTietQuocGiaComponent, isEqual);

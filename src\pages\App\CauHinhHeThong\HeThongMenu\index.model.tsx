import {ReactQuery} from "@src/@types";

// khai báo interface props Context của Login
export interface HeThongMenuContextProps {
  danhSachMenu: Array<CommonExecute.Execute.IHeThongMenu>;
  loading: boolean;
  danhSachMenuCha: Array<CommonExecute.Execute.IHeThongMenu>;
  filterParams: ReactQuery.ITimkiemPhanTrangHeThongMenuParams & ReactQuery.IPhanTrang;
  setFilterParams: React.Dispatch<React.SetStateAction<ReactQuery.ITimkiemPhanTrangHeThongMenuParams & ReactQuery.IPhanTrang>>;
  onUpdateHeThongMenu: (item: ReactQuery.IUpdateDaiLyParams) => Promise<number | null | undefined>;
  layDanhSachMenuPhanTrang: (params: ReactQuery.ITimkiemPhanTrangHeThongMenuParams) => void;
  layDanhSachMenuCha: () => Promise<void>;
  tongSoDong: number;
  layChiTietMenu: (params: ReactQuery.IChiTietHeThongMenuParams) => Promise<CommonExecute.Execute.IHeThongMenu | null>;
}

import {Popconfirm as Ant<PERSON><PERSON>confirm, <PERSON><PERSON>, PopconfirmProps} from "antd";
import {isEqual} from "lodash";
import React, {memo} from "react";
import {twMerge} from "tailwind-merge";
import {ButtonProps} from "antd/lib";
import {ButtonColorType, ButtonType} from "antd/es/button";

const PopconfirmComponent: React.FC<
  PopconfirmProps &
    ButtonProps & {
      buttonType?: ButtonType;
      buttonTitle?: string;
      buttonDisable?: boolean;
      buttonClassName?: string;
      buttonColor?: ButtonColorType;
      buttonIcon?: React.ReactNode;
      buttonIconPosition?: "start" | "end";
      onConfirm?: (e?: React.MouseEvent<HTMLElement>) => void;
      onCancel?: (e?: React.MouseEvent<HTMLElement>) => void;
    }
> = props => {
  const {
    //Button props
    buttonTitle = "Lưu",
    buttonClassName = "",
    buttonDisable = false,
    buttonType = "primary",
    buttonIconPosition = "start",
    //Popconfirm props
    className = "",
    okText = "Đồng ý",
    cancelText = "Để sau",
    buttonColor,
    buttonIcon,
    onConfirm,
    onCancel,
    ...etc
  } = props;

  return (
    <AntPopconfirm className={twMerge("custom-popcomfirm", className)} okText={okText} cancelText={cancelText} {...etc} onConfirm={onConfirm} onCancel={onCancel}>
      <Button type={buttonType} icon={buttonIcon} {...etc} disabled={buttonDisable} className={buttonClassName} color={buttonColor} iconPosition={buttonIconPosition}>
        {buttonTitle}
      </Button>
    </AntPopconfirm>
  );
};

const Popconfirm = memo(PopconfirmComponent, isEqual);

export default memo(Popconfirm);

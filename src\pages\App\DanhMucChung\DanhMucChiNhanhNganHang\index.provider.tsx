import React, {PropsWithChildren, useCallback, useEffect, useMemo, useState} from "react";
import {message} from "antd";
import {ReactQuery} from "@src/@types";
import {CommonExecuteResponse, useCommonExecute} from "@src/services/react-queries";
import {ACTION_CODE} from "@src/constants";
import {defaultFormValue} from "./index.configs";
import {DanhMucChiNhanhNganHangContext} from "./index.context";

const DanhMucChiNhanhNganHangProvider: React.FC<PropsWithChildren> = ({children}) => {
  // Hook để gọi API thông qua service layer
  const mutateUseCommonExecute = useCommonExecute();
  
  // ===== STATE MANAGEMENT =====
  // Quản lý trạng thái loading khi gọi API (hiển thị spinner)
  const [loading, setLoading] = useState<boolean>(false);
  
  // Danh sách chi nh<PERSON>h ngân hàng hiển thị trên bảng (từ API search)
  const [danhSachChiNhanhNganHang, setDanhSachChiNhanhNganHang] = useState<Array<CommonExecute.Execute.IDanhSachChiNhanhNganHangPhanTrang>>([]);
  
  // Tổng số dòng để tính phân trang (từ API response)
  const [tongSoDong, setTongSoDong] = useState<number>(0);
  
  // Danh sách tất cả ngân hàng để hiển thị trong dropdown form
  const [listNganHang, setListNganHang] = useState<Array<CommonExecute.Execute.IDanhMucNganHang>>([]);

  // ===== KHỞI TẠO DỮ LIỆU BAN ĐẦU =====
  // Function để load dữ liệu khi component mount lần đầu
  const initData = () => {
    // Load danh sách chi nhánh với giá trị mặc định (trang 1, 20 dòng)
    layDanhSachChiNhanhNganHang(defaultFormValue);
    // Load danh sách ngân hàng cho dropdown
    getListNganHang();
  };

  // useEffect chạy 1 lần khi component mount
  useEffect(() => {
    initData();
  }, []);

  // ===== API FUNCTIONS =====
  
  /**
   * HÀM LẤY DANH SÁCH NGÂN HÀNG CHO DROPDOWN

   */
  const getListNganHang = useCallback(async () => {
    try {
      // Gọi API với action code lấy danh mục ngân hàng
      const response = await mutateUseCommonExecute.mutateAsync({
        ma: "",              // Không filter theo mã
        ten: "",             // Không filter theo tên  
        trang_thai: "",      // Lấy tất cả trạng thái
        trang: 1,            // Trang đầu tiên
        so_dong: 1000,       // Lấy nhiều để có đủ data cho dropdown
        actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_DANH_MUC_NGAN_HANG
      });
      
      // Xử lý response data với type safety để tránh lỗi runtime
      if (response?.data) {
        const responseData = response.data as any;
        // Kiểm tra cấu trúc response: có thể là {data: [...]} hoặc trực tiếp [...]
        if (responseData.data && Array.isArray(responseData.data)) {
          setListNganHang(responseData.data);  // Trường hợp: {data: {data: [...]}}
        } else if (Array.isArray(responseData)) {
          setListNganHang(responseData);       // Trường hợp: {data: [...]}
        } else {
          setListNganHang([]);                 // Fallback: không có data
        }
      } else {
        setListNganHang([]);                   // Fallback: response null/undefined
      }
    } catch (error) {
      // Log lỗi và set empty array để không crash UI
      console.log("getListNganHang error", error);
      setListNganHang([]);
    }
  }, [mutateUseCommonExecute]);

  /**
   * HÀM TÌM KIẾM DANH SÁCH CHI NHÁNH NGÂN HÀNG CÓ PHÂN TRANG
   
   */
  const layDanhSachChiNhanhNganHang = useCallback(
    async (params: ReactQuery.ITimKiemPhanTrangChiNhanhNganHangParams & ReactQuery.IPhanTrang) => {
      try {
        // Bật loading spinner
        setLoading(true);
        
        // Gọi API search với toàn bộ tham số (search + pagination)
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync({
          ...params,  // Spread tất cả params: ten, ma_ngan_hang, trang_thai, trang, so_dong
          actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_NGAN_HANG_CHI_NHANH
        });
        console.log("response", response);
        
        // Xử lý response data - API trả về structure: {data: {tong_so_dong: number, data: array}}
        const responseData = response.data || {};
        const data = (responseData as any).data || []; // Lấy mảng data từ response.data.data
        const tongSoDong = (responseData as any).tong_so_dong || 0; // Lấy tổng số dòng từ response.data.tong_so_dong
        
        setDanhSachChiNhanhNganHang(data); // Set data cho bảng
        setTongSoDong(tongSoDong); // Set total cho pagination
      } catch (error) {
        // Khi có lỗi: log và clear data để không hiển thị data cũ
        console.log("layDanhSachChiNhanhNganHang error", error);
        setDanhSachChiNhanhNganHang([]);
        setTongSoDong(0);
      } finally {
        // Luôn tắt loading dù thành công hay thất bại
        setLoading(false);
      }
    },
    [mutateUseCommonExecute]
  );

  /**
   * HÀM LẤY CHI TIẾT MỘT CHI NHÁNH NGÂN HÀNG
   * 
  
   */
  const layChiTietChiNhanhNganHang = useCallback(
    async (params: ReactQuery.IChiTietChiNhanhNganHangParams): Promise<CommonExecute.Execute.IChiTietChiNhanhNganHang | null> => {
      try {
        // Gọi API lấy chi tiết với mã chi nhánh và mã ngân hàng
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync({
          ...params,  // ma, ma_ngan_hang
          actionCode: ACTION_CODE.CHI_TIET_NGAN_HANG_CHI_NHANH
        });
        
        // Return data để modal sử dụng
        return response.data as CommonExecute.Execute.IChiTietChiNhanhNganHang;
      } catch (error) {
        // Log lỗi và return null để modal biết không có data
        console.log("layChiTietChiNhanhNganHang error", error);
        return null;
      }
    },
    [mutateUseCommonExecute]
  );

  /**
   * HÀM CẬP NHẬT (THÊM MỚI / CHỈNH SỬA) CHI NHÁNH NGÂN HÀNG

   */
  const capNhatChiNhanhNganHang = useCallback(
    async (params: ReactQuery.ICapNhatChiNhanhNganHangParams): Promise<boolean> => {
      try {
        // Bật loading cho nút Save trong modal
        setLoading(true);
        
        // Gọi API cập nhật với toàn bộ data từ form
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync({
          ...params,  // Tất cả field: ma, ten, ma_ngan_hang, trang_thai, etc.
          actionCode: ACTION_CODE.CAP_NHAT_NGAN_HANG_CHI_NHANH
        });
        
        // Kiểm tra response thành công (API trả về -1 = success)
        if (response.data === -1) {
          // Xác định đây là thêm mới hay chỉnh sửa
          const isEdit = params.ma && params.ma.trim() !== "";
          
          // Hiển thị message thành công
          message.success(`${isEdit ? "Cập nhật" : "Thêm mới"} chi nhánh ngân hàng thành công`);
          
          // Reload lại danh sách để hiển thị data mới nhất
          layDanhSachChiNhanhNganHang(defaultFormValue);
          
          return true;  // Báo cho modal biết đã thành công để đóng modal
        }
        return false;     // API không trả về success code
      } catch (error) {
        // Xử lý lỗi: log và hiển thị message lỗi cho user
        console.log("capNhatChiNhanhNganHang error", error);
        const isEdit = params.ma && params.ma.trim() !== "";
        message.error(`Có lỗi xảy ra khi ${isEdit ? "cập nhật" : "thêm mới"} chi nhánh ngân hàng`);
        return false;     // Báo cho modal biết thất bại, không đóng modal
      } finally {
        // Luôn tắt loading
        setLoading(false);
      }
    },
    [mutateUseCommonExecute, layDanhSachChiNhanhNganHang]
  );

  /**
   * PACKAGE TẤT CẢ STATE VÀ FUNCTIONS ĐỂ CUNG CẤP CHO COMPONENT CON
   * 
   * useMemo để tối ưu performance, chỉ re-render khi dependencies thay đổi
   * Các component con sẽ consume value này thông qua useContext
   */
  const value = useMemo(
    () => ({
      // ===== STATE =====
      loading,                           // Trạng thái loading
      danhSachChiNhanhNganHang,         // Data cho bảng
      tongSoDong,                       // Total count cho pagination
      listNganHang,                     // Data cho dropdown ngân hàng
      
      // ===== FUNCTIONS =====
      layDanhSachChiNhanhNganHang,      // Search function
      layChiTietChiNhanhNganHang,       // Get detail function  
      capNhatChiNhanhNganHang,          // Save function
      getListNganHang,                  // Load ngân hàng function
      
      // ===== CONFIGS =====
      defaultFormValue,                 // Giá trị mặc định cho form
    }),
    [
      // Dependencies: khi nào re-create value object
      loading,
      danhSachChiNhanhNganHang,
      tongSoDong,
      listNganHang,
      layDanhSachChiNhanhNganHang,
      layChiTietChiNhanhNganHang,
      capNhatChiNhanhNganHang,
      getListNganHang,
    ]
  );

  // ===== RENDER PROVIDER =====
  /**
   * Return Context Provider với value chứa tất cả state và functions
   * Tất cả component con bên trong {children} đều có thể access được value này
   */
  return (
    <DanhMucChiNhanhNganHangContext.Provider value={value}>
      {children}
    </DanhMucChiNhanhNganHangContext.Provider>
  );
};

export default DanhMucChiNhanhNganHangProvider;
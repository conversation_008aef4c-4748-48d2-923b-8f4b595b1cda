import React, {PropsWithChildren, useCallback, useEffect, useMemo, useState} from "react";

import {QuanLyDonViThuHoContext} from "./index.context";
import {IQuanLyDonViThuHoContextProps} from "./index.model";
import {ACTION_CODE} from "@src/constants";
import {ReactQuery} from "@src/@types";
import {TableDonViThuHoColumnDataType} from "./index.configs";
import {useCommonExecute} from "@src/services/react-queries";
import {message} from "antd";

const QuanLyDonViThuHoProvider: React.FC<PropsWithChildren> = props => {
  const {children} = props;
  const mutateUseCommonExecute = useCommonExecute();
  const [listNganHang, setListNganHang] = useState<Array<CommonExecute.Execute.IDanhMucNganHang>>([]);
  const [listDonViThuHo, setListDonViThuHo] = useState<Array<CommonExecute.Execute.IDonViThuHo>>([]);
  const [tongSoDong, setTongSoDong] = useState<number>(0);
  const [filterParams, setFilterParams] = useState<ReactQuery.ITimKiemPhanTrangDonViThuHoParams & ReactQuery.IPhanTrang>({ma: "", ten: "", trang_thai: "", trang: 1, so_dong: 20});

  //khởi tạo dữ liệu ban đàu
  useEffect(() => {
    //nè
    initData();
  }, []);

  //NGÂN HÀNG
  const getListNganHang = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        actionCode: ACTION_CODE.LIET_KE_DANH_MUC_NGAN_HANG,
      });
      setListNganHang(response?.data as Array<CommonExecute.Execute.IDanhMucNganHang>);
    } catch (error) {
      console.log("getListNganHang error ", error);
    }
  }, [mutateUseCommonExecute]);
  const getChiTietDonViThuHo = useCallback(
    async (data: TableDonViThuHoColumnDataType) => {
      try {
        console.log("[Provider] getChiTietDonViThuHo được gọi với data:", data);

        if (!data.ma) {
          console.log("[Provider] Không có mã để tìm kiếm, data.ma =", data.ma);
          return {} as CommonExecute.Execute.IDonViThuHo;
        }

        console.log(" [Provider] Gọi API với params:", {
          ma: data.ma,
          actionCode: ACTION_CODE.CHI_TIET_DON_VI_THU_HO_BHXH,
        });

        const response = await mutateUseCommonExecute.mutateAsync({
          ma: data.ma,
          actionCode: ACTION_CODE.CHI_TIET_DON_VI_THU_HO_BHXH,
        } as any);

        console.log("[Provider] Raw response từ API:", response);
        console.log("[Provider] Response.data:", response.data);

        // Xử lý structure response.data.lke
        const responseData = response.data as any; // Type casting để truy cập lke
        if (responseData && responseData.lke && Array.isArray(responseData.lke) && responseData.lke.length > 0) {
          const result = responseData.lke[0] as CommonExecute.Execute.IDonViThuHo;
          console.log("[Provider] Trả về data từ lke[0]:", result);
          return result;
        } else {
          console.log("[Provider] Không tìm thấy data trong response.data.lke");
          return {} as CommonExecute.Execute.IDonViThuHo;
        }
      } catch (error) {
        console.log("[Provider] getChiTietDonViThuHo error:", error);
        return {} as CommonExecute.Execute.IDonViThuHo;
      }
    },
    [mutateUseCommonExecute],
  );

  const getListDonViThuHo = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        ...filterParams,
        actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_DON_VI_THU_HO_BHXH,
      } as any);
      if (response.data) {
        setListDonViThuHo(response.data.data);
        setTongSoDong(response.data.tong_so_dong);
      }
    } catch (error) {
      console.log("getListDonViThuHo error ", error);
    }
  }, [mutateUseCommonExecute]);

  useEffect(() => {
    getListDonViThuHo();
  }, [filterParams]);

  const capNhatChiTietDonViThuHo = useCallback(
    async (data: ReactQuery.ICapNhatDonViThuHoParams, isEditMode = false) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...data,
          actionCode: ACTION_CODE.UPDATE_DON_VI_THU_HO_BHXH,
        } as any);
        message.success(isEditMode ? "Cập nhật đơn vị thu hộ  thành công" : "Thêm mới đơn vị thu hộ  thành công");
        return response.data;
      } catch (error) {
        message.error(isEditMode ? "Có lỗi xảy ra khi cập nhật đơn vị thu hộ " : "Có lỗi xảy ra khi thêm mới đơn vị thu hộ ");
        console.log("capNhatChiTietDonViThuHo err", error);
        // return {} as CommonExecute.Execute.IDonViThuHo;
      }
    },
    [mutateUseCommonExecute],
  );

  //initData chỉ dùng trong useEffect 1 lần, không truyền đi đâu cả, nên không cần memo hoá
  const initData = () => {
    getListNganHang();
  };

  const value = useMemo<IQuanLyDonViThuHoContextProps>(
    () => ({
      listDonViThuHo,
      tongSoDong,
      loading: mutateUseCommonExecute.isLoading,
      filterParams,
      listNganHang,
      getListDonViThuHo,
      getChiTietDonViThuHo,
      capNhatChiTietDonViThuHo,
      setFilterParams,
    }),
    [listDonViThuHo, tongSoDong, mutateUseCommonExecute, filterParams, listNganHang, getListDonViThuHo, getChiTietDonViThuHo, capNhatChiTietDonViThuHo],
  );

  return <QuanLyDonViThuHoContext.Provider value={value}>{children}</QuanLyDonViThuHoContext.Provider>;
};

export default QuanLyDonViThuHoProvider;

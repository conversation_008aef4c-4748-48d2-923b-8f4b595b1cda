import {defaultTableColumnsProps} from "@src/hooks";
import {ColumnsType} from "antd/es/table";
import {FormRule} from "antd";
import {ReactQuery} from "@src/@types";

// ===== INTERFACES =====
/**
 * INTERFACE CHO KIỂU DỮ LIỆU CỦA BẢNG
 *
 * Định nghĩa cấu trúc dữ liệu của mỗi dòng trong bảng danh sách chi nhánh ngân hàng.
 * Mỗi thuộc tính tương ứng với một cột trong bảng.
 */
export interface TableChiNhanhNganHangDataType {
  /** Key duy nhất cho mỗi dòng (bắt buộc của Ant Design Table) */
  key: string;
  /** Số thứ tự hiển thị */
  sott: number;
  /** Mã chi nhánh ngân hàng */
  ma: string;
  /** Tên chi nhánh ngân hàng */
  ten: string;
  /** Tên ngân hàng (hiển thị) */
  ten_ngan_hang: string;
  /** Trạng thái hiển thị (text) */
  trang_thai_ten: string;
  /** Ng<PERSON><PERSON> tạo (đã format) */
  ngay_tao: string;
  /** Người tạo */
  nguoi_tao: string;
  /** Mã ngân hàng (để lưu trữ) */
  ma_ngan_hang: string;
  /** Mã trạng thái (để lưu trữ) */
  trang_thai: string;
}

export const defaultFormValue: ReactQuery.ITimKiemPhanTrangChiNhanhNganHangParams & ReactQuery.IPhanTrang = {
  /** Tên chi nhánh (để trống = tìm tất cả) */
  ten: "",
  /** Mã ngân hàng (để trống = tất cả ngân hàng) */
  ma_ngan_hang: "",
  /** Trạng thái (để trống = tất cả trạng thái) */
  trang_thai: "",
  /** Trang hiện tại (bắt đầu từ 1) */
  // trang: 1,
  // /** Số dòng mỗi trang */
  // so_dong: 20,
};

export const TRANG_THAI_CHI_NHANH_NGAN_HANG = [
  // {ten: "Tất cả", ma: ""},
  {ten: "Đang sử dụng", ma: "D"},
  {ten: "Ngừng sử dụng", ma: "K"},
];

export const trangThaiOptions = [
  {label: "Đang sử dụng", value: "Đang sử dụng"},
  {label: "Ngừng sử dụng", value: "Ngừng sử dụng"},
];

export const getColumns = (): ColumnsType<TableChiNhanhNganHangDataType> => [
  {
    title: "STT",
    dataIndex: "sott",
    key: "sott",
    width: 20,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Mã chi nhánh",
    dataIndex: "ma",
    key: "ma",
    width: 50,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Tên chi nhánh",
    dataIndex: "ten",
    key: "ten",
    width: 100,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Ngân hàng",
    dataIndex: "ma_ngan_hang", //ten_ngan_hang
    key: "ten_ngan_hang",
    width: 60,
    align: "center",
    ...defaultTableColumnsProps,
  },

  {
    title: "Ngày tạo",
    dataIndex: "ngay_tao",
    key: "ngay_tao",
    width: 60,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Người tạo",
    //dataIndex: "ma_doi_tac",
    dataIndex: "nguoi_tao",
    key: "nguoi_tao",
    width: 60,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Ngày cập nhật",
    dataIndex: "ngay_cap_nhat",
    key: "ngay_cap_nhat",
    width: 60,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Người cập nhật",
    dataIndex: "nguoi_cap_nhat",
    key: "nguoi_cap_nhat",
    width: 60,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Trạng thái",
    dataIndex: "trang_thai_ten",
    key: "trang_thai_ten",
    width: 60,
    align: "center",
    ...defaultTableColumnsProps,
  },
];

export const FormTimKiemChiNhanhNganHang = {
  /** Field tìm kiếm theo tên chi nhánh */
  ten: {
    component: "input" as const,
    name: "ten",
    label: "Tên chi nhánh",
    placeholder: "Nhập tên chi nhánh",
  },
  /** Field chọn ngân hàng */
  ma_ngan_hang: {
    component: "select" as const,
    name: "ma_ngan_hang",
    label: "Ngân hàng",
    placeholder: "Chọn ngân hàng",
    options: [], // Sẽ được set từ API
  },
  /** Field chọn trạng thái */
  trang_thai: {
    component: "select" as const,
    name: "trang_thai",
    label: "Trạng thái",
    placeholder: "Chọn trạng thái",
    options: TRANG_THAI_CHI_NHANH_NGAN_HANG,
  },
};

// ===== VALIDATION RULES =====
/**
 * CÁC QUY TẮC VALIDATION CHO FORM
 *
 * Định nghĩa các rule kiểm tra dữ liệu đầu vào cho form thêm/sửa chi nhánh ngân hàng.
 * Sử dụng validation rules của Ant Design Form.
 */

export const validationRules = {
  /** Rules cho tên chi nhánh */
  ten: [
    {required: true, message: "Vui lòng nhập tên chi nhánh!"},
    {max: 200, message: "Tên chi nhánh không được vượt quá 200 ký tự!"},
  ] as FormRule[],
  /** Rules cho mã ngân hàng */
  ma_ngan_hang: [{required: true, message: "Vui lòng chọn ngân hàng!"}] as FormRule[],
  /** Rules cho trạng thái */
  trang_thai: [{required: true, message: "Vui lòng chọn trạng thái!"}] as FormRule[],
  /** Rules cho số thứ tự */
  stt: [
    {required: true, message: "Vui lòng nhập số thứ tự!"},
    {type: "number" as const, min: 1, message: "Số thứ tự phải lớn hơn 0!"},
  ] as FormRule[],
};

export const detailFormFields = [
  {
    name: "ten",
    label: "Tên chi nhánh",
    component: "Input",
    rules: validationRules.ten,
    required: true,
  },
  {
    name: "ma_ngan_hang",
    label: "Ngân hàng",
    component: "Select",
    rules: validationRules.ma_ngan_hang,
    required: true,
  },
  {
    name: "stt",
    label: "Số thứ tự",
    component: "InputNumber",
    rules: validationRules.stt,
    required: true,
  },
  {
    name: "trang_thai",
    label: "Trạng thái",
    component: "Select",
    rules: validationRules.trang_thai,
    required: true,
    options: TRANG_THAI_CHI_NHANH_NGAN_HANG.filter(item => item.ma !== ""), // Loại bỏ "Tất cả"
  },
];

import {IFormInput} from "@src/@types";
import {ruleInputMessage} from "@src/hooks";

/**
 * <PERSON><PERSON><PERSON> config riêng cho component modal CauHinhMauHopDong
 */

//INTERFACE ĐỊNH NGHĨA REF VÀ PROPS CHO MODAL
export interface IModalChiTietCauHinhMauHopDongRef {
  open: (data?: any) => void;
}

export interface IModalChiTietCauHinhMauHopDongProps {
  onAfterSave?: () => void;
}

//INTERFACE FORM MODAL CHI TIẾT - ĐỊNH NGHĨA CÁC FIELD TRONG MODAL TẠO MỚI/SỬA
export interface IFormTaoMoiCauHinhMauHopDongFieldsConfig {
  ma_doi_tac_ql: IFormInput; 
  nv: IFormInput;
  ma_sp: IFormInput; 
  ten: IFormInput; 
  ngay_ad: IFormInput; 
  id_file: IFormInput;
  trang_thai: IFormInput; 
}

/**
 * Object này định nghĩa các field trong modal để tạo mới hoặc chỉnh sửa cấu hình mẫu hợp đồng
 */
export const FormTaoMoiCauHinhMauHopDong: IFormTaoMoiCauHinhMauHopDongFieldsConfig = {
  ma_doi_tac_ql: {
    component: "select",
    label: "Đối tác quản lý",
    name: "ma_doi_tac_ql",
    placeholder: "Chọn đối tác quản lý",
    rules: [ruleInputMessage.required], 
  },
  nv: {
    component: "select",
    label: "Nghiệp vụ",
    name: "nv",
    placeholder: "Chọn nghiệp vụ",
    rules: [ruleInputMessage.required], 
  },
  ma_sp: {
    component: "select", 
    label: "Sản phẩm",
    name: "ma_sp",
    placeholder: "Chọn sản phẩm",
    rules: [ruleInputMessage.required], 
  },
  ten: {
    component: "input", 
    label: "Tên mẫu hợp đồng",
    name: "ten",
    placeholder: "Nhập tên mẫu hợp đồng",
    rules: [ruleInputMessage.required], 
  },
  ngay_ad: {
    component: "date-picker", 
    label: "Ngày áp dụng",
    name: "ngay_ad",
    placeholder: "Chọn ngày áp dụng",
    rules: [ruleInputMessage.required], 
  },
  id_file: {
    component: "input", 
    label: "File mẫu hợp đồng",
    name: "id_file",
    placeholder: "Chọn file mẫu hợp đồng", 
    rules: [ruleInputMessage.required], 
  },
  trang_thai: {
    component: "select", 
    label: "Trạng thái",
    name: "trang_thai",
    placeholder: "Chọn trạng thái",
    rules: [ruleInputMessage.required], 
  },
};

// ===== MODAL SETTINGS =====
export const MODAL_SETTINGS = {
  width: 800,
  destroyOnClose: true,
  titles: {
    create: "Tạo mới cấu hình mẫu hợp đồng",
    update: "Cập nhật cấu hình mẫu hợp đồng"
  },
  buttons: {
    create: "Tạo mới",
    update: "Cập nhật", 
    cancel: "Quay lại"
  }
};

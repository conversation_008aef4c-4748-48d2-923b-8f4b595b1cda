# Task List: Modal Import Excel Component

## Overview
Tạo component modal để import file Excel với bảng dữ liệu có thể chỉnh sửa (editable table) cho 30 cột dữ liệu xe bảo hiểm.

## Task Breakdown

### 1. Phân tích và thiết kế component
- [ ] Nghiên cứu các modal patterns hiện có trong dự án (ModalQuanLyFileCaNhan, ModalThemCauHoi)
- [ ] Phân tích cấu trúc file upload patterns (FileEndpoint.uploadFile)
- [ ] Thiết kế flow: Button → File picker → API upload → Modal hiển thị bảng
- [ ] Xác định responsive design cho modal 90% x 90% màn hình
- [ ] Thiết kế error handling và loading states

### 2. Tạo TypeScript interfaces và types
- [ ] Tạo file `src/components/ModalImportExcel/constants.ts`
- [ ] Định nghĩa interface `IModalImportExcelRef` (open, close methods)
- [ ] Định nghĩa interface `IModalImportExcelProps` (callback functions)
- [ ] Định nghĩa interface `IExcelRowData` cho 30 cột dữ liệu:
  ```typescript
  interface IExcelRowData {
    stt: string;           // *STT
    ten_chu_xe: string;    // *TÊN CHỦ XE
    dia_chi: string;       // *ĐỊA CHỈ
    bien_xe: string;       // *BIỂN XE
    so_khung: string;      // *SỐ KHUNG
    so_may: string;        // *SỐ MÁY
    so_gcn: string;        // *SỐ GCN
    nam_sx: string;        // *NĂM SX
    so_lai_phu_xe: string; // SỐ LÁI PHỤ XE
    muc_dich_su_dung: string; // *MỤC ĐÍCH SỬ DỤNG
    gia_tri_xe: string;    // *GIÁ TRỊ XE
    loai_xe: string;       // *LOẠI XE
    hang_xe: string;       // *HÃNG XE
    hieu_xe: string;       // *HIỆU XE
    so_cho: string;        // SỐ CHỖ
    trong_tai: string;     // TRỌNG TẢI (TẤN)
    vip: string;           // VIP
    ngay_cap: string;      // *NGÀY CẤP
    gio_hieu_luc: string;  // *GIỜ HIỆU LỰC
    ngay_hieu_luc: string; // *NGÀY HIỆU LỰC
    gio_ket_thuc: string;  // *GIỜ KẾT THÚC
    ngay_ket_thuc: string; // *NGÀY KẾT THÚC
    ma_dk_dkbs: string;    // *MÃ ĐK/ĐKBS
    so_tien_bh: string;    // *SỐ TIỀN BH
    tien_mien_thuong: string; // *TIỀN MIỄN THƯỜNG
    phi_bh_chua_vat: string;  // *PHÍ BH (CHƯA VAT)
    thue_vat: string;      // *THUẾ (VAT)
    phi_giam_chua_vat: string; // *PHÍ GIẢM (CHƯA VAT)
    thue_giam_vat: string; // *THUẾ GIẢM (VAT)
    key?: string;          // Ant Design table key
  }
  ```
- [ ] Định nghĩa interface `IUploadExcelResponse` cho API response
- [ ] Định nghĩa enum cho validation rules và error messages

### 3. Tạo cấu hình bảng (table columns)
- [ ] Tạo file `src/components/ModalImportExcel/tableConfig.ts`
- [ ] Định nghĩa `excelImportColumns` sử dụng pattern từ `defaultTableColumnsProps`
- [ ] Cấu hình 30 cột với:
  - Title (tên cột hiển thị)
  - DataIndex (key trong data)
  - Width (chiều rộng cột)
  - Required indicator (*) cho các cột bắt buộc
  - Render function sử dụng `InputCellTable`
- [ ] Implement responsive column widths
- [ ] Thêm validation styling cho required fields

### 4. Implement API integration
- [ ] Tạo endpoint mới trong `src/services/axios/endpoints/fileEndpoint.ts`:
  ```typescript
  export const uploadExcelFile = async (
    params: ReactQuery.IUploadExcelFileParams,
    callbacks?: {
      onProgress?: (event: {percent: number}) => void;
      onSuccess?: (body: any) => void;
      onError?: (error: Error) => void;
    }
  ): Promise<{data: IExcelRowData[]}> => {
    // Implementation
  };
  ```
- [ ] Thêm interface `IUploadExcelFileParams` vào `ReactQuery.d.ts`
- [ ] Thêm action code mới vào `constants/axios.ts`
- [ ] Implement error handling cho các trường hợp:
  - File không phải Excel
  - File quá lớn
  - Dữ liệu không hợp lệ
  - Network errors

### 5. Implement modal component chính
- [ ] Tạo file `src/components/ModalImportExcel/ModalImportExcel.tsx`
- [ ] Implement component structure:
  ```typescript
  interface IModalImportExcelRef {
    open: () => void;
    close: () => void;
  }
  
  interface IModalImportExcelProps {
    onDataConfirm?: (data: IExcelRowData[]) => void;
    onCancel?: () => void;
  }
  ```
- [ ] Sử dụng `forwardRef` và `useImperativeHandle` pattern
- [ ] Implement modal với kích thước 90% x 90%
- [ ] Thêm `HeaderModal` component với title phù hợp
- [ ] Implement loading states cho upload và processing
- [ ] Thêm progress indicator cho file upload

### 6. Implement file picker và upload logic
- [ ] Sử dụng Ant Design `Upload` component với `customRequest`
- [ ] Implement file validation:
  - Chỉ accept .xls, .xlsx files
  - Kiểm tra file size limit
  - Validate file structure
- [ ] Implement upload progress tracking
- [ ] Handle upload success/error states
- [ ] Parse API response và convert thành table data

### 7. Implement editable table với InputCellTable
- [ ] Sử dụng Ant Design `Table` component
- [ ] Integrate `InputCellTable` cho tất cả cells
- [ ] Implement cell change handler:
  ```typescript
  const handleCellChange = (index: number, dataIndex: string, value: string) => {
    // Update table data
  };
  ```
- [ ] Thêm validation cho required fields
- [ ] Implement row highlighting cho validation errors
- [ ] Thêm sticky header cho table
- [ ] Implement horizontal scroll cho 30 cột

### 8. Implement modal actions và footer
- [ ] Tạo footer với các buttons:
  - "Hủy" - đóng modal
  - "Xác nhận" - confirm data và callback
  - "Tải lại" - re-upload file
- [ ] Implement confirmation logic
- [ ] Validate required fields trước khi confirm
- [ ] Show validation summary nếu có lỗi
- [ ] Implement loading states cho actions

### 9. Tạo file index và export
- [ ] Tạo file `src/components/ModalImportExcel/index.ts`
- [ ] Export component và types:
  ```typescript
  export { default as ModalImportExcel } from './ModalImportExcel';
  export type { IModalImportExcelRef, IModalImportExcelProps, IExcelRowData } from './constants';
  ```
- [ ] Thêm component vào main components index file

### 10. Integration testing và validation
- [ ] Tạo test component để test modal
- [ ] Test file upload flow với file Excel mẫu
- [ ] Test editable table functionality
- [ ] Test validation cho required fields
- [ ] Test responsive design trên các screen sizes
- [ ] Test error handling scenarios
- [ ] Test performance với large datasets (1000+ rows)

### 11. Documentation và examples
- [ ] Tạo file README.md với usage examples
- [ ] Document API requirements
- [ ] Tạo sample Excel file template
- [ ] Document validation rules
- [ ] Thêm JSDoc comments cho các functions

### 12. Performance optimization
- [ ] Implement virtualization cho large tables (nếu cần)
- [ ] Optimize re-rendering với React.memo
- [ ] Implement debouncing cho cell changes
- [ ] Optimize table column widths calculation
- [ ] Add loading skeletons cho better UX

## Technical Notes

### Dependencies Required
- Ant Design components: Modal, Table, Upload, Button, Progress
- Existing components: InputCellTable, HeaderModal, FormInput
- Lodash utilities: debounce, isEqual
- React hooks: useState, useCallback, useImperativeHandle, forwardRef

### File Structure
```
src/components/ModalImportExcel/
├── index.ts
├── ModalImportExcel.tsx
├── constants.ts
├── tableConfig.ts
└── README.md
```

### API Requirements
- Endpoint nhận file Excel và trả về parsed data
- Support progress tracking
- Error handling cho invalid files
- Response format: `{data: IExcelRowData[], errors?: string[]}`

## Acceptance Criteria
- [ ] Modal mở được khi click button "Import Excel"
- [ ] File picker chỉ accept .xls/.xlsx files
- [ ] Upload progress được hiển thị
- [ ] Bảng 30 cột hiển thị đúng với data từ API
- [ ] Tất cả cells có thể edit được
- [ ] Required fields được highlight
- [ ] Validation errors được hiển thị rõ ràng
- [ ] Modal responsive trên mobile/desktop
- [ ] Performance tốt với 1000+ rows
- [ ] Error handling đầy đủ cho các edge cases

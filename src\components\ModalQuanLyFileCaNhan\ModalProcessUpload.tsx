import {CheckCircleOutlined, CloseCircleOutlined, FileExcelOutlined, <PERSON><PERSON>mageOutlined, FilePdfOutlined, FileUnknownOutlined, FileWordOutlined} from "@ant-design/icons";
import {COLOR_PALETTE} from "@src/constants";
import {Card, List, message, Progress, Typography, UploadFile} from "antd";
import {memo} from "react";
import {IModalProcessUploadProps} from "./Constant";

const getFileIcon = (data: UploadFile) => {
  const type = data.type;
  if (type === "image/jpeg" || type === "image/png") return <FileImageOutlined />;
  else if (type === "application/pdf") return <FilePdfOutlined style={{color: "#F40F02"}} />;
  else if (type?.includes("word")) return <FileWordOutlined style={{color: "#2b579a"}} />;
  else if (type?.includes("sheet")) return <FileExcelOutlined style={{color: "#1D6F42"}} />;
  return <FileUnknownOutlined style={{color: COLOR_PALETTE.green[50]}} />;
};

const ModalProcessUploadComponent = ({visible, listFileUpload, onClose}: IModalProcessUploadProps) => {
  if (!visible) return null;
  const showErrorInfo = (errorData: any) => {
    message.info(errorData?.error?.response?.data?.error_message);
  };
  const getCarTitle = () => {
    if (listFileUpload.filter(item => item.status === "uploading").length > 0) return `Đang tải lên ${listFileUpload.length} file`;
    if (listFileUpload.filter(item => item.status === "done").length === listFileUpload.length) return `${listFileUpload.length} file đã tải lên thành công`;
    if (listFileUpload.filter(item => item.status === "error").length > 0) return `${listFileUpload.filter(item => item.status === "error").length} file đã tải lên không thành công`;
    return "";
  };

  return (
    <div
      style={{
        position: "fixed",
        bottom: 140,
        right: 100,
        width: 360,
        zIndex: 9999,
        borderRadius: 10,
        boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)", // ✅ đổ bóng
      }}>
      <Card
        title={<span>{getCarTitle()}</span>}
        size="small"
        extra={
          <Typography.Link onClick={onClose}>
            <CloseCircleOutlined style={{fontSize: 20}} />
          </Typography.Link>
        }
        styles={{
          body: {padding: 0},
          header: {
            paddingTop: 10,
            paddingBottom: 10,
          },
        }}>
        <List
          itemLayout="horizontal"
          className="max-h-[60vh] min-h-[20vh] overflow-auto"
          dataSource={listFileUpload}
          renderItem={file => (
            <List.Item style={{padding: "8px 12px"}}>
              <List.Item.Meta avatar={getFileIcon(file)} title={file.name} />
              <div style={{display: "flex", alignItems: "center", gap: 8}}>
                {file.status === "uploading" ? (
                  <Progress type="circle" percent={Math.round(file.percent || 0)} size={15} />
                ) : file.status === "done" ? (
                  <CheckCircleOutlined style={{color: "green"}} />
                ) : (
                  <CloseCircleOutlined style={{color: "red"}} onClick={() => showErrorInfo(file)} />
                )}
              </div>
            </List.Item>
          )}
        />
      </Card>
    </div>
  );
};

export const ModalProcessUpload = memo(ModalProcessUploadComponent);

import {IFormInput} from "@src/@types";

const ruleRequired = {
  required: true,
  message: "Thông tin bắt buộc",
};

export interface IFormChiTietDanhMucNganHangFieldsConfig {
  //   ma_doi_tac_ql: IFormInput;
  ma: IFormInput;
  ten: IFormInput;
  //   nv: IFormInput;
  stt: IFormInput;
  trang_thai: IFormInput;
}
const FormChiTietDanhMucNganHang: IFormChiTietDanhMucNganHangFieldsConfig = {
  //   ma_doi_tac_ql: {
  //     component: "select",
  //     name: "ma_doi_tac_ql",
  //     label: "Đối tác",
  //     placeholder: "Chọn đối tác",
  //     rules: [ruleRequired],
  //   },
  ma: {
    component: "input",
    name: "ma",
    label: "Mã ngân hàng",
    placeholder: "<PERSON>ã ngân hàng",
    rules: [ruleRequired],
  },
  ten: {
    component: "input",
    name: "ten",
    label: "Tên ngân hàng",
    placeholder: "Tên ngân hàng",
    rules: [ruleRequired],
  },
  //   nv: {
  //     component: "select",
  //     name: "nv",
  //     label: "Nghiệp vụ",
  //     placeholder: "Chọn nghiệp vụ",
  //     rules: [ruleRequired],
  //   },
  stt: {
    component: "input",
    name: "stt",
    label: "Thứ tự hiển thị",
    placeholder: "Thứ tự hiển thị",
    rules: [ruleRequired],
  },
  trang_thai: {
    component: "select",
    name: "trang_thai",
    label: "Trạng thái",
    placeholder: "Chọn trạng thái",
    rules: [ruleRequired],
  },
};
export const TRANG_THAI_CHI_TIET_NGAN_HANG = [
  {ten: "Đang sử dụng", ma: "D"},
  {ten: "Ngừng sử dụng", ma: "K"},
];

export default FormChiTietDanhMucNganHang;
export interface Props {
  listDoiTac: Array<CommonExecute.Execute.IDoiTac>;
}

export interface IModalChiTietDanhMucNganHangRef {
  open: (data?: CommonExecute.Execute.IDanhMucNganHang) => void;
  close: () => void;
}

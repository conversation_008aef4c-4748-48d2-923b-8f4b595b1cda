export const initFormFields = (form: any, chiTietChucNang: any) => {
  if (!chiTietChucNang) return;

  const fields = Object.entries(chiTietChucNang).map(([name, value]) => ({
    name,
    value: value ?? "",
  }));
  form.setFields(fields);
};

const ruleRequired = {
  required: true,
  message: "Thông tin bắt buộc",
};

export const FormInputConfigs = {
  ten: {
    component: "input",
    name: "ten",
    placeholder: "Tên Menu",
    label: "Tên Menu", // cho label vào thì sẽ thành input with label
    rules: [ruleRequired],
  },
  ma: {
    component: "input",
    name: "ma",
    placeholder: "Mã chức năng",
    label: "Mã chức năng", // cho label vào thì sẽ thành input with label
    rules: [ruleRequired],
  },
  loai: {
    component: "select",
    name: "loai",
    placeholder: "<PERSON>ạ<PERSON> chức năng",
    label: "<PERSON><PERSON><PERSON> chức năng", // cho label vào thì sẽ thành input with label
    rules: [ruleRequired],
  },
  kieu_ad: {
    component: "select",
    name: "kieu_ad",
    placeholder: "Chọn kiểu áp dụng",
    label: "Kiểu áp dụng", // cho label vào thì sẽ thành input with label
  },
  trang_thai: {
    component: "select",
    name: "trang_thai",
    placeholder: "Chọn trạng thái",
    label: "Trạng thái", // cho label vào thì sẽ thành input with label
    rules: [ruleRequired],
  },
};

import {CheckOutlined} from "@ant-design/icons";
import {COLOR_PALETTE} from "@src/constants";
import {Input, List, Popover} from "antd";
import {debounce, isEqual} from "lodash";
import React, {memo, useMemo, useState} from "react";

interface ButtonPopoverCellTableProps {
  listData: Array<{ma?: string; ten?: string}>;
  popOverTitle: string;
  btnTxt: string;
  maSelected: string;
  className?: string;
  placement?: "top" | "left" | "right" | "bottom" | "topLeft" | "topRight" | "bottomLeft" | "bottomRight" | "leftTop" | "leftBottom" | "rightTop" | "rightBottom";
  showSearch?: boolean;
  onClickOption: (item: {ma?: string; ten?: string}) => void;
  // checkboxKieuApDung?: boolean;
  // checkboxNguyenTeSTBH?: boolean;
  buttonStyle?: React.CSSProperties;
}

const {Search} = Input;
/*
ButtonPopoverCellTableComponent k sử dụng được React.memo do có liên quan đến hàm onChangeAllKieuApDung, hàm này có gọi listQuyenLoi,
 mà có trường hợp listQuyenLoi thay đổi length, nếu sử dụng React.memo thì listQuyenLoi trong onChangeAllKieuApDung 

 */
const ButtonPopoverCellTableComponent = React.memo(
  ({listData = [], btnTxt = "", showSearch = true, onClickOption, maSelected = "", popOverTitle = "", className = "", placement = "top", buttonStyle}: ButtonPopoverCellTableProps) => {
    const [textSearch, setTextSearch] = useState<string>("");
    const deboundSearch = useMemo(() => debounce(event => setTextSearch(event.target.value), 350), []);

    const dataSource = useMemo(() => {
      if (!textSearch) return listData;
      else {
        const textSearchUpper = textSearch.toUpperCase();
        return listData.filter(item => item.ma?.toUpperCase().includes(textSearchUpper) || item.ten?.toUpperCase().includes(textSearchUpper));
      }
    }, [textSearch, listData]);

    return (
      <Popover
        content={
          <>
            {showSearch && <Search placeholder="Tìm kiếm" className="mb-1.5" onChange={deboundSearch} />}
            <List
              // chiều cao tối đa là 300px
              // overflowY: "auto": khi nội dung vượt quá, sẽ hiện thanh cuộn dọc.
              className={"max-h-[250px] overflow-y-auto" + className}
              size="small"
              bordered
              dataSource={dataSource}
              renderItem={item => (
                <List.Item onClick={() => onClickOption(item)} className="!justify-start">
                  <CheckOutlined style={{color: maSelected === item.ma ? COLOR_PALETTE.green[100] : COLOR_PALETTE.white[10]}} className="mr-1" />
                  <span className={maSelected === item.ma ? `font-bold text-[${COLOR_PALETTE.green[100]}]` : ""}>{item.ten}</span>
                </List.Item>
              )}
            />
          </>
        }
        title={popOverTitle}
        trigger="click"
        placement={placement}
        autoAdjustOverflow={true}>
        <span className="font-bold" style={buttonStyle}>
          {btnTxt}
        </span>
      </Popover>
    );
  },
  /* phải memo checkboxKieuApDung này thì ButtonPopoverCellTableComponent mới render lại để cho thằng checkboxKieuApDung được cập nhật giá trị khi checkbox thay đổi
   onClickOption={itemSelected => {
    if (checkboxKieuApDung) {
      onChangeAllKieuAppDung(itemSelected);
    } else {
      onChangeDataCellTableQuyenLoi(index, dataIndex, itemSelected.ma);
      onChangeDataCellTableQuyenLoi(index, "kieu_ad_ten", itemSelected.ten);
    }
  }}
    tương tự như với checkboxKieuApDung
  */
  (prevProps, nextProps) => false,
  // (prevProps, nextProps) =>
  //   prevProps.maSelected === nextProps.maSelected &&
  //   prevProps.checkboxKieuApDung === nextProps.checkboxKieuApDung &&
  //   prevProps.checkboxNguyenTeSTBH === nextProps.checkboxNguyenTeSTBH &&
  //   prevProps.listQuyenLoi === nextProps.listQuyenLoi,
);

ButtonPopoverCellTableComponent.displayName = "ButtonPopoverCellTableComponent";

export default memo(ButtonPopoverCellTableComponent, isEqual);

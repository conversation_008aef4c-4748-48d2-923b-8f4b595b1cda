import {LOCAL_STORAGE_KEY} from "@src/constants/localStorage";
import {create} from "zustand";
import {createJSONStorage, persist} from "zustand/middleware";

export interface IlistTinhThanhStore {
  listTinhThanh: Array<CommonExecute.Execute.IDanhMucTinhThanh>;
  setlistTinhThanh: (listTinhThanh: Array<CommonExecute.Execute.IDanhMucTinhThanh>) => void;
}

//create : hàm tạo store
export const useTinhThanh = create(
  //persist : middileware để lưu trạng thái vào localStorage
  persist<IlistTinhThanhStore, [], [], Pick<IlistTinhThanhStore, "listTinhThanh">>(
    //set, get phải đúng thứ tự này, nếu không sẽ báo lỗi vì sử dụng nhầm cách get/set
    (set, get) => ({
      //khởi tạo state menuNguoiDung từ cookie + localStorage
      listTinhThanh: get()?.listTinhThanh || [],
      setlistTinhThanh: (listTinhThanh: Array<CommonExecute.Execute.IDanhMucTinhThanh>) => set(() => ({listTinhThanh: [...listTinhThanh]})),
    }),
    //cấu hình persist
    {
      name: LOCAL_STORAGE_KEY.TINH_THANH, //key để lưu trong localStorate
      storage: createJSONStorage(() => localStorage), //nơi lưu trữ là localStorage
      //chỉ định những phần nào trong state sẽ được lưu, tránh lưu những thứ k cần thiết như các hàm set
      partialize: state => ({listTinhThanh: state.listTinhThanh}),
    },
  ),
);

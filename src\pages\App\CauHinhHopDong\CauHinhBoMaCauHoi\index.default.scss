#BO_MA_CAU_HOI {
  .ant-table-row:hover td {
    background-color: #e8f5e9 !important; //test
  }

  .header-cell-custom {
    background-color: #96bf49 !important;
    // background: linear-gradient(to right, #96bf49, #009a55) !important;
    color: #fff !important;
    font-weight: bold !important;
    text-align: center !important;
    border-radius: 0 !important;
  }

  /* 🔒 Ẩn cả thanh cuộn dọc và ngang */
  .antd-table-hide-scroll .ant-table-body {
    scrollbar-width: none;
    /* Firefox */
    -ms-overflow-style: none;
    /* IE 10+ */
  }

  .antd-table-hide-scroll .ant-table-body::-webkit-scrollbar {
    display: none;
    /* Chrome, Safari */
  }

  //cố định height cho bảng
  // .ant-table-container {
  //   min-height: 620px;
  // }

  // .ant-table-header {
  //   overflow-y: scroll !important;
  // }
}
.table-vai-tro .ant-table-content,
.table-nhom .ant-table-content {
  border-bottom: 1px solid #f0f0f0;
}

.table-chuc-nang .ant-table-thead > tr > th {
  padding-top: 8px !important;
  padding-bottom: 8px !important;
  height: 18px;
}
// .ant-form-item .ant-form-item-control-input {
//   min-height: 18px !important;
// }
.modal-them-cau-hoi-ct {
  .custom-checkbox-cell .ant-form-item .ant-form-item-control-input {
    min-height: 22px !important;
  }
  .no-header-border-radius .ant-table-container .ant-table-content .ant-table-thead > tr > th {
    border-radius: 0 !important;
  }
  .no-header-border-radius .ant-table .ant-table-container .ant-table-header {
    overflow: unset !important;
  }
  .no-header-border-radius .ant-table .ant-table-container .ant-table-header > .ant-table-thead > tr > th {
    border-radius: 0 !important;
  }
  // .empty-row {
  //   height: 15px; /* Đặt chiều cao mong muốn cho hàng trống */
  // }
  // .custom-checkbox-cell .ant-form-item .ant-form-item-control-input .ant-form-item-control-input-content > label {
  //   display: inline-grid;
  // }
}
.ant-input:focus {
  border-color: #96bf49;
}

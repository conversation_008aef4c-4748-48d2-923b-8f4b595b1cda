import {LooseObject} from "@src/@types";
import {Result} from "antd";
import {Component, ErrorInfo, PropsWithChildren} from "react";
import "./index.default.scss";

type ComponentState = {
  hasError: boolean;
  message: string;
};

class ErrorBoundary extends Component<PropsWithChildren, ComponentState> {
  constructor(props: LooseObject | Readonly<LooseObject>) {
    super(props);
    this.state = {hasError: false, message: ""};
  }

  static getDerivedStateFromError(data: any) {
    // Update state so the next render will show the fallback UI.
    return {hasError: true, message: data.message};
  }

  componentDidCatch(error: any, errorInfo: ErrorInfo) {
    // You can also log the error to an error reporting service
    console.log("Error in boundary component: ", {error, errorInfo});
  }

  render() {
    if (this.state.hasError) {
      // You can render any custom fallback UI
      return (
        <Result
          status={"warning"}
          extra={
            <>
              <div className="four_zero_four_bg">
                <p>{this.state.message}</p>
              </div>
              {/* <Button
                className="capitalize"
                type="primary"
                // onClick={() => navigateTo("admin/phong-ban")}
              >
                {"Go home!"}
              </Button> */}
            </>
          }
        />
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;

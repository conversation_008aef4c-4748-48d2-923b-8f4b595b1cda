/**
 * Chức năng: <PERSON><PERSON><PERSON> nghĩa React Context để chia sẻ state và functions giữa các components
 */

import {createContext} from "react";
import {ICauHinhMauHopDongProvider} from "./index.model";

/**
 * Context để chia sẻ state và methods cho các component con của CauHinhMauHopDong
 */
const CauHinhMauHopDongContext = createContext<ICauHinhMauHopDongProvider>({
  // DEFAULT STATE VALUES 
  danhSachCauHinhMauHopDong: [],
  listSanPham: [],
  tongSoDong: 0,
  loading: false,
  filterParams: {
    ma_doi_tac_ql: "",
    ma_sp: "",
    ten: "",
    nv: "",
    trang_thai: "",
    ngay_ad: undefined,
    trang: 1,
    so_dong: 20,
  },

  // DEFAULT API FUNCTIONS 
  searchCauHinhMauHopDong: async () => {},
  getChiTietCauHinhMauHopDong: async () => ({} as CommonExecute.Execute.ICauHinhMauHopDong),
  capNhatChiTietCauHinhMauHopDong: async () => ({}),
  getListSanPham: async () => {},
  
  // DEFAULT STATE SETTERS 
  setFilterParams: () => {},
});

export default CauHinhMauHopDongContext;

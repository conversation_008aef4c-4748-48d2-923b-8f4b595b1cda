import {useContext, createContext} from "react";

import {DanhMucHangXeProps} from "./index.model";

//craeteContext : khai báo 1 context mà component có thể cung cấp hoặc đọc
export const DanhMucHangXeContext = createContext<DanhMucHangXeProps>({
  listDoiTac: [],
  danhSachDanhMucHangXe: [],
  loading: false,
  layDanhSachDanhMucHangXe: params => {},
  tongSoDong: 0,
  layChiTietDanhMucHangXe: () => Promise.resolve(null),
  defaultFormValue: {},
  onUpdateDanhMucHangXe: () => Promise.resolve(null),
  getListDoiTac: () => Promise.resolve(),
});

export const useChiTietDanhMucHangXeContext = () => useContext(DanhMucHangXeContext);

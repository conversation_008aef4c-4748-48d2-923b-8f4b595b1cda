import {KJUR} from "jsrsasign";

/**
 * Mã hóa mật khẩu bằng SHA256 tài kho<PERSON>n + mật khẩu
 * @param taiKhoan - mã tài khoản
 * @param matKhau  - mật khẩu gốc
 * @returns
 */
export const hashPasswordWithAccount = (taiKhoan: string, matKhau: string): string => {
  const md = new KJUR.crypto.MessageDigest({alg: "sha256", prov: "cryptojs"});
  md.updateString(taiKhoan + matKhau);
  const passSHA256: string = md.digest();
  return passSHA256;
};
/**
 * Mã hóa mật khẩu bằng SHA256 mat khẩu
  
 * @param matKhau  - mật khẩu gốc
 * @returns 
 */
export const hashPasswordSHA256Only = (matKhau: string): string => {
  const md = new KJUR.crypto.MessageDigest({alg: "sha256", prov: "cryptojs"});
  md.updateString(matKhau);
  const passSHA256: string = md.digest();
  return passSHA256;
};

import {forwardRef, memo, useCallback, useImperativeHandle, useMemo, useRef, useState} from "react";
import {radioItemTrangThaiMenuTable} from "../index.configs";
import {isEqual} from "lodash";
import {Flex, Form, Input, InputRef, Modal, Table, TableColumnType, Tag, Tooltip} from "antd";
import {useHeThongMenuContext} from "../index.context";
import {Button, Highlighter} from "@src/components";
import {CheckCircleOutlined, ClearOutlined, SearchOutlined} from "@ant-design/icons";
import {FilterDropdownProps} from "antd/es/table/interface";
import {COLOR_PALETTE} from "@src/constants";

import {DataIndex, heThongMenuChaColumns, IModalTimMenuChaRef, MenuChaProps, TableHeThongMenuChaColumnDataIndex, TableHeThongMenuChaDataType, TRANG_THAI_MENU_CHA} from "./index.configs";

import "../index.default.scss";
import {defaultPaginationTableProps, defaultTableProps, fillRowTableEmpty} from "@src/hooks";

const ModalTimMenuChaComponent = forwardRef<IModalTimMenuChaRef, MenuChaProps>(({onSelectMenuCha}: MenuChaProps, ref) => {
  useImperativeHandle(ref, () => ({
    open: () => {
      setIsOpen(true);
      initData();
    },
    close: () => setIsOpen(false),
  }));
  const initData = async () => {
    layDanhSachMenuCha();
  };
  const {loading, danhSachMenuCha, layDanhSachMenuCha} = useHeThongMenuContext();
  const [page, setPage] = useState(1);
  const [searchText, setSearchText] = useState("");
  const [pageSize, setPageSize] = useState(10);
  const [menuChaSelected, setMenuChaSelected] = useState<CommonExecute.Execute.IHeThongMenu | null>(null);
  const [tongSoDongMenuCha, setTongSoDongMenuCha] = useState<number>(0);
  const [IsOpen, setIsOpen] = useState(false);

  const [searchedColumn, setSearchedColumn] = useState<TableHeThongMenuChaColumnDataIndex | "">("");
  const searchInput = useRef<InputRef>(null);
  const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: DataIndex) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);
  const handleReset = useCallback((clearFilters: () => void, confirm: () => void, dataIndex: DataIndex) => {
    clearFilters();
    confirm();
    setSearchText("");
    setSearchedColumn(dataIndex);
  }, []);

  //Gọi api khi chuyển trang
  const onChangePage = useCallback((page: number, pageSize: number) => {
    setPage(page);
    setPageSize(pageSize);
  }, []);
  const dataTableListMenu = useMemo<Array<TableHeThongMenuChaDataType>>(() => {
    try {
      const filteredData = danhSachMenuCha.filter(item => item.trang_thai === "D");
      const tableData = filteredData.map((item: any, index: number) => {
        return {
          key: index.toString(),
          // stt: item.stt ?? index + 1,
          stt: index + 1,
          ma: item.ma,
          ten: item.ten,
          ma_cha: item.ma_cha,
          url: item.url,
          nhom: item.nhom,
          trang_thai_ten: TRANG_THAI_MENU_CHA.find(tt => tt.ma === item.trang_thai)?.ten ?? "",
          trang_thai: item.trang_thai,
        };
      });
      const arrEmptyRow: Array<TableHeThongMenuChaDataType> = fillRowTableEmpty(tableData.length, 10);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      console.log("dataTableListMenu error", error);
      return [];
    }
  }, [danhSachMenuCha]);

  //Bấm xác nhận chọn
  const onPressXacNhan = () => {
    try {
      if (menuChaSelected) {
        onSelectMenuCha(menuChaSelected);
        console.log("MenuchaSelected", menuChaSelected);
      } else {
        onSelectMenuCha(null);
      }
      setIsOpen(false);
    } catch (error) {
      console.log("onPressXacNhan error", error);
    }
  };

  //   const MenuChaSelected = useRef<CommonExecute.Execute.IHeThongMenu | null>(null);
  const getColumnSearchProps = (dataIndex: DataIndex, title: string): TableColumnType<TableHeThongMenuChaDataType> => ({
    filterDropdown:
      dataIndex !== "trang_thai"
        ? ({setSelectedKeys, selectedKeys, confirm, clearFilters, close}) => (
            <div style={{padding: 8}} onKeyDown={e => e.stopPropagation()} className="header-cell-custom flex flex-row items-center p-2">
              <Input
                ref={searchInput}
                placeholder={`Tìm theo ${title}`}
                value={selectedKeys[0]}
                onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                onPressEnter={() => handleSearch(selectedKeys as string[], confirm, dataIndex)}
                style={{display: "block", marginRight: 8}}
              />
              <Tooltip title="Tìm kiếm">
                <Button type="primary" shape="circle" icon={<SearchOutlined />} onClick={() => handleSearch(selectedKeys as string[], confirm, dataIndex)} className="mr-2" />
              </Tooltip>
              <Tooltip title="Xoá">
                <Button type="primary" shape="circle" icon={<ClearOutlined />} onClick={() => clearFilters && handleReset(clearFilters, confirm, dataIndex)} />
              </Tooltip>
            </div>
          )
        : null,
    filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} />,
    onFilter: (value, record) =>
      record[dataIndex]
        ? record[dataIndex]
            .toString()
            .toLowerCase()
            .includes((value as string).toLowerCase())
        : false,
    filterDropdownProps: {
      onOpenChange(open) {
        if (open) {
          setTimeout(() => searchInput.current?.select(), 100);
        }
      },
    },
    filters: dataIndex === "trang_thai_ten" ? radioItemTrangThaiMenuTable : undefined,
    render: (text, record, index) => {
      if (dataIndex === "trang_thai_ten") {
        const color = text === "Đang sử dụng" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];
        if (record.key.toString().includes("empty")) return "";
        return (
          <Tag color={color} className="text-[11px]">
            {text}
          </Tag>
        );
      }
      return searchedColumn === dataIndex ? (
        <Highlighter searchWords={[searchText]} textToHighlight={text ? text.toString() : ""} />
      ) : text !== undefined ? (
        text
      ) : (
        <Tag color={"transparent"} className="!text-white text-[11px]">
          {"\u00A0"}
        </Tag>
      );
    },
  });

  const renderTable = () => {
    return (
      <Table<TableHeThongMenuChaDataType>
        {...defaultTableProps}
        // bordered={false}
        className="menu-cha"
        loading={loading}
        onRow={record => {
          return {
            style: {cursor: loading ? "progress" : "pointer"},
            onClick: () => {
              if (record.key.toString().includes("empty")) return;
              setMenuChaSelected(record as CommonExecute.Execute.IHeThongMenu);
            }, // click row
            onDoubleClick: () => onPressXacNhan(),
          };
        }}
        pagination={{
          ...defaultPaginationTableProps,
          defaultPageSize: 10,
          total: tongSoDongMenuCha,
          onChange: (page, pageSize) => {
            onChangePage(page, pageSize);
          },
          // showLessItems: true, //Hiển thị ít mục trang hơn
          // defaultPageSize: 2, //Số lượng item dữ liệu mặc định trên mỗi trang
          // onShowSizeChange : Được gọi khi pageSize thay đổi
          // showTitle: false,// Hiển thị tiêu đề của item trang
          //showTotal : hiển thị UI bao nhiêu / bao nhiêu bản ghi
        }}
        columns={(heThongMenuChaColumns || []).map(item => {
          // Đảm bảo item.key được định nghĩa, item.title là chuỗi và item.key không phải là "stt" trước khi thêm ô tìm kiếm
          return {
            ...item,
            ...(item.key && typeof item.title === "string" && item.key !== "stt" ? getColumnSearchProps(item.key as keyof TableHeThongMenuChaDataType, item.title) : {}),
          };
        })} //định nghĩa cột của table
        rowClassName={record => (record.ma && record.ma === menuChaSelected?.ma ? "custom-row-selected" : "")}
        dataSource={dataTableListMenu}
      />
    );
  };

  const renderFooter = () => {
    return (
      <Form.Item>
        <Tooltip title={menuChaSelected ? "" : "Vui lòng chọn menu cha"}>
          <Button type={"primary"} onClick={() => onPressXacNhan()} className="mr-2" iconPosition="end" icon={<CheckCircleOutlined />} disabled={menuChaSelected ? false : true}>
            Chọn
          </Button>
        </Tooltip>
      </Form.Item>
    );
  };
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        maskClosable={false}
        title="Chọn menu cha"
        style={{
          top: 50,
        }}
        // centered
        // bodyStyle={{maxHeight: "70vh", overflowY: "auto"}}
        className="modal-chon-menu-cha"
        open={IsOpen}
        onOk={() => setIsOpen(false)}
        onCancel={() => setIsOpen(false)}
        width={{
          xs: "70%",
          sm: "70%",
          md: "70%",
          lg: "70%",
          xl: "70%",
          xxl: "70%",
        }}
        footer={renderFooter}>
        {renderTable()}
      </Modal>
    </Flex>
  );
});
//   const tableHeight = useTableHeight(["footer", "header"]);
ModalTimMenuChaComponent.displayName = "ModalTimMenuChaComponent";
export const ModalTimMenuCha = memo(ModalTimMenuChaComponent, isEqual);

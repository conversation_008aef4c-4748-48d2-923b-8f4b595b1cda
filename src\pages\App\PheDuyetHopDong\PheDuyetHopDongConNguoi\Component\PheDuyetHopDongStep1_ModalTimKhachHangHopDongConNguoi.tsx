import {CheckCircleOutlined, SearchOutlined} from "@ant-design/icons";
import {IFormInput, ReactQuery} from "@src/@types";
import {Button, FormInput, Highlighter, TableFilterDropdown} from "@src/components";
import {COLOR_PALETTE} from "@src/constants";
import {defaultPaginationTableProps, defaultTableProps, fillRowTableEmpty} from "@src/hooks";
import {Col, Flex, Form, InputRef, Modal, Row, Table, TableColumnType, Tag, Tooltip} from "antd";
import {FilterDropdownProps} from "antd/es/table/interface";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState} from "react";
import {useHopDongConNguoiContext} from "@src/pages/App/BaoHiemConNguoi/HopDongConNguoi/index.context";
import "../index.default.scss";
import {
  defaultFilterKhachHangParams,
  FormTimKiemKhachHang,
  IModalTimKhachHangRef,
  ModalTimKhachHangProps,
  radioItemLoaiKhachHangSelect,
  radioItemLoaiKhachHangTable,
  radioItemTrangThaiKhachHangTable,
  tableKhachHangColumn,
  TableKhachHangColumnDataIndex,
  TableKhachHangColumnDataType,
} from "./Constant";

const {ma_doi_tac_ql, ma_chi_nhanh_ql, ten, loai_kh, dthoai, cmt, mst, nd_tim} = FormTimKiemKhachHang;

const ModalTimKhachHangHopDongConNguoiComponent = forwardRef<IModalTimKhachHangRef, ModalTimKhachHangProps>(
  ({onSelectKhachHang, maDoiTacSelected, maChiNhanhSelected, listChiNhanhSelect}: ModalTimKhachHangProps, ref) => {
    useImperativeHandle(ref, () => ({
      open: khachHangSelected => {
        setIsOpen(true);
        initData();
        if (khachHangSelected) setChiTietKhachHangSelected(khachHangSelected);
      },
      close: () => setIsOpen(false),
    }));

    const {listDoiTac, loading, searchKhachHang} = useHopDongConNguoiContext();

    // DATA TABLE KHÁCH HÀNG
    const [listKhachHang, setListKhachHang] = useState<Array<CommonExecute.Execute.IKhachHang>>([]);
    const [tongSoDongKhachHang, setTongSoDongKhachHang] = useState<number>(0);
    const [filterKhachHangParams, setFilterKhachHangParams] = useState<ReactQuery.ILayDanhSachKhachHangPhanTrangParams>(defaultFilterKhachHangParams);

    const [chiTietKhachHangSelected, setChiTietKhachHangSelected] = useState<CommonExecute.Execute.IKhachHang | null>(null);
    const [isOpen, setIsOpen] = useState<boolean>(false);

    const refSearchInputTable = useRef<InputRef>(null);
    const [searchTextTable, setSearchTextTable] = useState<string>(""); //lưu textsearch để hiển thị vào cell
    const [searchedColumn, setSearchedColumn] = useState<TableKhachHangColumnDataIndex | "">(""); //key column đang được search

    const [formTimKhachHang] = Form.useForm();

    const closeModal = useCallback(() => {
      setIsOpen(false);
      setChiTietKhachHangSelected(null);
      formTimKhachHang.resetFields();
    }, [formTimKhachHang]);

    //Bấm tiếp theo
    const onPressXacNhan = async (chiTietKhachHangSelected: CommonExecute.Execute.IKhachHang | null) => {
      try {
        onSelectKhachHang(chiTietKhachHangSelected);
        closeModal();
      } catch (error) {
        console.log("onPressXacNhan error", error);
      }
    };

    /* KHÁCH HÀNG */
    // TÌM KIẾM PHÂN TRANG KHÁCH HÀNG
    const onPressSearchKhachHang = useCallback(async () => {
      try {
        const response = await searchKhachHang({
          ...filterKhachHangParams,
        });
        if (response.data) {
          setListKhachHang(response.data);
          setTongSoDongKhachHang(response.tong_so_dong);
        }
      } catch (error) {
        console.log("onPressSearchKhachHang error ", error);
      }
    }, [filterKhachHangParams, searchKhachHang]);

    useEffect(() => {
      onPressSearchKhachHang();
    }, [filterKhachHangParams]);

    const initData = async () => {
      try {
        setFilterKhachHangParams({...filterKhachHangParams, ma_doi_tac_ql: maDoiTacSelected, ma_chi_nhanh_ql: maChiNhanhSelected});
        formTimKhachHang.setFields([
          {
            name: "ma_doi_tac_ql",
            value: maDoiTacSelected,
          },
          {
            name: "ma_chi_nhanh_ql",
            value: maChiNhanhSelected,
          },
        ]);
        await onPressSearchKhachHang();
      } catch (error) {
        console.log("error", error);
      }
    };

    //MAP VALUE CỦA LIST VÀO TABLE
    const dataTableListKhachHang = useMemo<Array<TableKhachHangColumnDataType>>(() => {
      try {
        const tableData = listKhachHang.map(itemKhachHang => {
          return {
            key: itemKhachHang.ma, // bắt buộc phải có key
            sott: itemKhachHang.sott,
            ten: itemKhachHang.ten,
            ten_loai_kh: itemKhachHang.ten_loai_kh,
            ma: itemKhachHang.ma,
            dchi: itemKhachHang.dchi,
            mst: itemKhachHang.mst,
            dthoai: itemKhachHang.dthoai,
            trang_thai_ten: itemKhachHang.trang_thai_ten,
          };
        });
        const arrEmptyRow: Array<TableKhachHangColumnDataType> = fillRowTableEmpty(tableData.length, 10);
        return [...tableData, ...arrEmptyRow];
      } catch (error) {
        console.log("dataTableListKhachHang error", error);
        return [];
      }
    }, [listKhachHang]);

    const onSearchKhachHang = (values: ReactQuery.ILayDanhSachKhachHangPhanTrangParams) => {
      setFilterKhachHangParams({...values, trang: 1, so_dong: 10});
    };

    const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: TableKhachHangColumnDataIndex) => {
      confirm();
      setSearchTextTable(selectedKeys[0]);
      setSearchedColumn(dataIndex);
    }, []);

    const handleReset = useCallback(
      (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: TableKhachHangColumnDataIndex) => {
        clearFilters();
        setSearchTextTable("");
        handleSearch([""], confirm, dataIndex);
      },
      [handleSearch],
    );

    // RENDER
    //FOOTER
    const renderFooter = () => {
      return (
        <Tooltip title={chiTietKhachHangSelected ? "" : "Vui lòng chọn khách hàng"}>
          <Button type={"primary"} onClick={() => onPressXacNhan(chiTietKhachHangSelected)} className="w-40" icon={<CheckCircleOutlined />} disabled={chiTietKhachHangSelected ? false : true}>
            Chọn
          </Button>
        </Tooltip>
      );
    };
    const renderFormInputColum = (props: IFormInput) => (
      <Col span={5}>
        <FormInput {...props} />
      </Col>
    );

    // FORM TÌM KIẾM TABLE KHÁCH HÀNG
    const renderHeaderTableQuanLyKhachHang = () => (
      <Form
        form={formTimKhachHang}
        initialValues={{}}
        layout="vertical" //Tất cả các input nằm trên 1 dòng
        onFinish={onSearchKhachHang}
        className="[&_.ant-form-item]:mb-0">
        <Row gutter={16}>
          {renderFormInputColum({...ma_doi_tac_ql, options: listDoiTac})}
          {renderFormInputColum({...ma_chi_nhanh_ql, options: listChiNhanhSelect})}
          {renderFormInputColum(ten)}
          {renderFormInputColum({...loai_kh, options: radioItemLoaiKhachHangSelect})}
        </Row>

        <Row gutter={16}>
          {renderFormInputColum(dthoai)}
          {renderFormInputColum(cmt)}
          {renderFormInputColum(mst)}
          {renderFormInputColum(nd_tim)}
          <Col span={4}>
            <Button type="primary" htmlType="submit" loading={loading} icon={<SearchOutlined />} block>
              Tìm kiếm
            </Button>
          </Col>
        </Row>
      </Form>
    );

    //lấy ra loại filter ở CELL HEADER tương ứng
    const getFilterTableKhachHang = (dataIndex: string) => {
      if (dataIndex === "trang_thai_ten") return radioItemTrangThaiKhachHangTable;
      else if (dataIndex === "ten_loai_kh") return radioItemLoaiKhachHangTable;
      return undefined;
    };

    // dataIndex : là các key của column, title : tiêu đề của column
    const getColumnSearchProps = (dataIndex: TableKhachHangColumnDataIndex, title: string): TableColumnType<TableKhachHangColumnDataType> => ({
      /**
       *  filterDropdown: UI tuỳ chỉnh của ô filter (component render ra trong dropdown filter)
       * @param param0
       * @returns
       * dataIndex !== "trang_thai_ten"
       */
      filterDropdown:
        dataIndex !== "trang_thai_ten" && dataIndex !== "ten_loai_kh"
          ? filterDropdownParams => (
              <TableFilterDropdown
                ref={refSearchInputTable}
                title={title}
                dataIndex={dataIndex}
                handleSearch={(selectedKeys, confirm, dataIndex) => handleSearch(selectedKeys, confirm, dataIndex)}
                handleReset={(clearFilters, confirm, dataIndex) => clearFilters && handleReset(clearFilters, confirm, dataIndex)}
                {...filterDropdownParams}
              />
            )
          : undefined,
      /**
       * filterIcon : icon hiển thị trên header column khi filter
       * @param filtered :  // "filtered". là boolean, true nếu đang áp dụng filter,
       *  biến này thay đổi khi hàm confirm được gọi, hoặc hàm clearFilters được gọi
       * @returns
       */
      filterIcon: (filtered: boolean) => {
        return <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} size={40} />;
      },
      /**
       * onFilter: hàm filter dữ liệu tại client-side (bắt buộc phải khai báo để filter hoạt động)
       * do hàm onFilter luôn phải trả về boolean, nên thêm || false vào để k bị lỗi
       * @param value : giá trị filter người dùng nhập vào
       * @param record : từng bản ghi trong dataSource
       * @returns
       */
      onFilter: (value, record) => {
        return (
          record[dataIndex]
            ?.toString()
            .toLowerCase()
            .includes((value as string).toLowerCase()) || false
        );
      },
      //filterDropdownProps: thuộc tính tuì chỉnh hành vi dropdown
      filterDropdownProps: {
        // onOpenChange: callback khi dropdown mở/ tắt thì sẽ focus vào input
        onOpenChange(open) {
          if (open) {
            setTimeout(() => refSearchInputTable.current?.select(), 100); // Focus/ select input khi mở filter
          }
        },
      },
      filterSearch: true,
      filters: getFilterTableKhachHang(dataIndex),
      render: (
        text,
        record,
        //  index
      ) => {
        if (dataIndex === "trang_thai_ten") {
          const color = text === "Đang sử dụng" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];
          if (record.key.toString().includes("empty")) return "";
          return (
            <Tag color={color} className="text-[11px]">
              {text}
            </Tag>
          );
        }
        return searchedColumn === dataIndex ? <Highlighter searchWords={[searchTextTable]} textToHighlight={text ? text.toString() : ""} /> : text !== undefined ? text : "\u00A0"; // xử lý chuyển text thành 1 dòng khi text quá dài
      },
    });

    const renderTable = () => (
      <Table<TableKhachHangColumnDataType>
        {...defaultTableProps}
        dataSource={dataTableListKhachHang} //mảng dữ liệu record được hiển thị
        columns={
          tableKhachHangColumn?.map(item => {
            //Nếu là cột stt thì không hiển thị search
            return {...item, ...(item.key === "sott" ? {} : getColumnSearchProps(item.key as keyof TableKhachHangColumnDataType, item.title as string))};
          }) || []
        } //định nghĩa cột của table
        rowClassName={record => (record.key === chiTietKhachHangSelected?.ma ? "custom-row-selected" : "")} // xử lý việc 1 row được selected -> row đấy sẽ được highlight lên
        loading={loading} //hiển thị loading khi đang gọi API để loading data
        pagination={{
          ...defaultPaginationTableProps,
          defaultPageSize: 10,
          total: tongSoDongKhachHang,
          onChange: (page, pageSize) => {
            setFilterKhachHangParams({...filterKhachHangParams, trang: page, so_dong: pageSize});
          },
        }}
        title={renderHeaderTableQuanLyKhachHang}
        onRow={record => {
          return {
            style: {cursor: loading ? "progress" : "pointer"}, //  thể hiện vị trí con trỏ là button(👈)
            onClick: () => {
              setChiTietKhachHangSelected(record as CommonExecute.Execute.IKhachHang);
            }, // click row
            onDoubleClick: () => {
              onPressXacNhan(record as CommonExecute.Execute.IKhachHang);
            },
          };
        }}
      />
    );

    return (
      <Flex vertical gap="middle" align="flex-start">
        <Modal
          className="modal-tim-khach-hang-hop-dong-con-nguoi"
          title="Chọn khách hàng"
          centered
          open={isOpen}
          onOk={() => closeModal()}
          onCancel={() => closeModal()}
          width={"95%"}
          maskClosable={false}
          styles={{
            body: {
              height: "70vh",
            },
          }}
          footer={renderFooter}>
          {renderTable()}
        </Modal>
      </Flex>
    );
  },
);

ModalTimKhachHangHopDongConNguoiComponent.displayName = "ModalTimKhachHangHopDongConNguoiComponent";
export const ModalTimKhachHangHopDongConNguoi = memo(ModalTimKhachHangHopDongConNguoiComponent, isEqual);

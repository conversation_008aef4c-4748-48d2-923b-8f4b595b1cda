import {Authen} from "@src/@types/Authentication";
import {AUTHEN_KEYS} from "@src/constants";

import Cookies from "js-cookie";

export const checkIsAuthenticated = () => {
  return !!Cookies.get(AUTHEN_KEYS.TOKEN);
};

export const setInfoToCookies = (data: Authen.Profile.IMetaData) => {
  Cookies.set(AUTHEN_KEYS.TOKEN, data.token);
  Cookies.set(AUTHEN_KEYS.USER_NAME, data.nsd.tai_khoan);
};

export const clearCookeis = () => {
  Cookies.remove(AUTHEN_KEYS.TOKEN);
  Cookies.remove(AUTHEN_KEYS.PW);
};

import {IFormInput} from "@src/@types";
import {colWidthByKey, defaultTableColumnsProps} from "@src/hooks";
import {formatDateTimeToNumber} from "@src/utils";
import {TableProps} from "antd";
import dayjs from "dayjs";

export const PAGE_SIZE = 13;
export const currentDate = dayjs();
export const ngayDauThang = dayjs().startOf("month");
export const NGHIEP_VU_CON_NGUOI = "NG";
//ĐỊNH NGHĨA CẤU HÌNH TABLE HỢP ĐỒNG CON NGƯỜI GỒM NHỮNG CỘT NÀO, THỨ TỰ CỘT SẼ HIỂN THỊ THEO THỨ TỰ KHAI BÁO Ở DƯỚI
export interface TableHopDongTrinhDuyetConNguoiColumnDataType {
  key?: string;
  bt?: number;
  kieu_hd?: string;
  kieu_hd_ten?: string;
  lan_trinh?: number;
  ma_chi_nhanh_duyet?: string;
  ma_chi_nhanh_ql?: string;
  ma_doi_tac?: string;
  ma_doi_tac_duyet?: string;
  ma_doi_tac_ql?: string;
  ma_kh?: string;
  ma_sp?: string;
  nd_duyet?: string | null;
  nd_tchoi?: string | null;
  nd_tra_lai?: string | null;
  nd_trinh?: string;
  ngay_duyet?: string;
  ngay_tchoi?: string;
  ngay_tra_lai?: string;
  ngay_trinh?: string;
  nsd_duyet?: string;
  nsd_trinh?: string;
  nv?: string;
  phong_ql?: string;
  so_hd?: string;
  so_id?: number;
  sott?: number;
  stt?: number | string;
  ten_chi_nhanh_ql?: string;
  ten_kh?: string;
  ten_sp?: string;
  tong_phi?: number;
  trang_thai?: string;
  trang_thai_ten?: string;
}

// ĐỊNH NGHĨA Column RENDER RA TỪNG Ô TRONG CỘT
//đọc https://ant.design/components/table#column để biết thêm về column
export const tableHopDongTrinhDuyetConNguoiColumn: TableProps<TableHopDongTrinhDuyetConNguoiColumnDataType>["columns"] = [
  {
    ...defaultTableColumnsProps,
    title: "STT",
    dataIndex: "sott",
    key: "sott",
    width: colWidthByKey.sott,
  },
  {
    ...defaultTableColumnsProps,
    title: "Ngày trình",
    dataIndex: "ngay_trinh",
    key: "ngay_trinh",
    width: 100,
  },
  {
    ...defaultTableColumnsProps,
    title: "Người trình",
    dataIndex: "nsd_trinh",
    key: "nsd_trinh",
    width: 150,
  },
  {
    ...defaultTableColumnsProps,
    title: "Đối tác cấp đơn",
    dataIndex: "ma_doi_tac_ql",
    key: "ma_doi_tac_ql",
    width: 120,
  },
  {
    ...defaultTableColumnsProps,
    title: "Chi nhánh cấp đơn",
    dataIndex: "ten_chi_nhanh_ql",
    key: "ten_chi_nhanh_ql",
    width: 250,
  },
  {
    ...defaultTableColumnsProps,
    title: "Số hợp đồng",
    dataIndex: "so_hd",
    key: "so_hd",
    width: 250,
  },
  {
    ...defaultTableColumnsProps,
    title: "Trạng thái",
    dataIndex: "trang_thai_ten",
    key: "trang_thai_ten",
    width: 130,
    align: "center",
  },
  {
    ...defaultTableColumnsProps,
    title: "Kiểu hợp đồng",
    dataIndex: "kieu_hd_ten",
    key: "kieu_hd_ten",
    width: 150,
    align: "center",
  },
  {
    ...defaultTableColumnsProps,
    title: "Tên sản phẩm",
    dataIndex: "ten_sp",
    key: "ten_sp",
    width: 200,
    align: "center",
  },
  {
    ...defaultTableColumnsProps,
    title: "Tổng phí",
    dataIndex: "tong_phi",
    key: "tong_phi",
    align: "right",
    width: 120,
  },
  {
    ...defaultTableColumnsProps,
    title: "Tên khách hàng",
    dataIndex: "ten_kh",
    key: "ten_kh",
    width: 250,
    align: "center",
  },
];

//keyof: return ra key của inteface TableHopDongTrinhDuyetConNguoiColumnDataType;
export type TableHopDongTrinhDuyetColumnDataIndex = keyof TableHopDongTrinhDuyetConNguoiColumnDataType;

//radio trong table
export const radioItemTrangThaiDuyetTable = [
  {value: "Đã duyệt", text: "Đã duyệt"},
  {value: "Chưa duyệt", text: "Chưa duyệt"},
];

//form tìm kiếm

export const optionTrangThaiDuyet: Array<{ma: string; ten: string}> = [
  {ma: "D", ten: "Đã duyệt"},
  {ma: "C", ten: "Chưa duyệt"},
  {ma: "T", ten: "Từ chối duyệt"},
];
export interface IFormTimKiemHopDongTrinhDuyetConNguoiFieldsConfig {
  nv: IFormInput;
  tu_ngay: IFormInput;
  den_ngay: IFormInput;
  so_hd: IFormInput;
  ten_kh: IFormInput;
  trang_thai: IFormInput;
}
export const FormTimKiemHopDongTrinhDuyetConNguoi: IFormTimKiemHopDongTrinhDuyetConNguoiFieldsConfig = {
  nv: {
    component: "input",
    name: "nv",
    label: "Loại hợp đồng",
  },
  tu_ngay: {
    component: "date-picker",
    name: "tu_ngay",
    label: "Từ ngày",
    placeholder: "Chọn ngày",
    className: "!mb-0",
  },
  den_ngay: {
    component: "date-picker",
    name: "den_ngay",
    label: "Đến ngày",
    placeholder: "Chọn ngày",
    className: "!mb-0",
  },
  so_hd: {
    component: "input",
    name: "so_hd",
    label: "Số hợp đồng",
    placeholder: "Nhập số hợp đồng",
    className: "!mb-0",
  },
  ten_kh: {
    component: "input",
    name: "ten_kh",
    label: "Tên khách hàng",
    placeholder: "Nhập tên khách hàng",
    className: "!mb-0",
  },
  trang_thai: {
    component: "select",
    name: "trang_thai",
    // label: "Trạng thái",
    placeholder: "Chọn trạng thái",
    className: "!mb-0 mt-1",
  },
};

export const TAO_MOI_DOI_TAC = [
  {ten: "Công ty Cổ Phần Bảo Hiểm Petrolimex", ma: "D"},
  {ten: "Công Ty Cổ Phần Bảo Hiểm escs", ma: "K"},
];

export const defaultValueFormTimKiemPhanTrang = {
  nv: NGHIEP_VU_CON_NGUOI,
  tu_ngay: formatDateTimeToNumber(ngayDauThang),
  den_ngay: formatDateTimeToNumber(currentDate),
  so_hd: "",
  ten_kh: "",
  trang_thai: "",
  trang: 1,
  so_dong: PAGE_SIZE,
};

import React, { memo, PropsWithChildren } from "react";
import { Skeleton as AntSkeleton } from "antd";
import { AvatarProps } from "antd/es/skeleton/Avatar";
import { twMerge } from "tailwind-merge";

import { isEqual } from "lodash";

interface AvatarSkeletonProps extends AvatarProps {
  loading?: boolean;
}

const AvatarSkeletonComponent: React.FC<
  PropsWithChildren<AvatarSkeletonProps>
> = (props) => {
  const {
    className = "",
    loading = true,
    children,
    active = true,
    ...etc
  } = props;

  return (
    <React.Fragment>
      {loading ? (
        <AntSkeleton.Avatar
          className={twMerge("custom-avatar-skeleton", className)}
          active={active}
          {...etc}
        />
      ) : (
        children
      )}
    </React.Fragment>
  );
};

const AvatarSkeleton = memo(AvatarSkeletonComponent, isEqual);

export default AvatarSkeleton;

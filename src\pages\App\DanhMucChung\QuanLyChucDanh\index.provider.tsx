import React, {PropsWithChildren, useCallback, useEffect, useMemo, useState} from "react";

import {QuanLyChucDanhContext} from "./index.context";
import {IQuanLyChucDanhContextProps} from "./index.model";
import {useCommonExecute} from "@src/services/react-queries";
import {ReactQuery} from "@src/@types";
import {ACTION_CODE} from "@src/constants";

const QuanLyChucDanhProvider: React.FC<PropsWithChildren> = props => {
  const {children} = props;

  const mutateUseCommonExecute = useCommonExecute();
  const [filterParams, setFilterParams] = useState<ReactQuery.ILayDanhSachChucDanhPhanTrangParams>({ma: "", ma_doi_tac_ql: "", ten: "", trang_thai: "", trang: 1, so_dong: 20});
  const [tongSoDong, setTongSoDong] = useState<number>(0);
  const [listChucDanh, setListChucDanh] = useState(Array<CommonExecute.Execute.IChiTietChucDanh>);
  const [listDoiTac, setListDoiTac] = useState<Array<CommonExecute.Execute.IDoiTac>>([]);

  useEffect(() => {
    initData();
  }, []);

  const getListDoiTac = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        actionCode: ACTION_CODE.LIET_KE_DANH_SACH_DOI_TAC,
      });
      if (response.data) {
        setListDoiTac(response.data.map(item => ({...item, ten: item.ma + " - " + item.ten})));
      }
    } catch (error) {
      console.log("getListDoiTac error ", error);
    }
  }, [mutateUseCommonExecute]);

  const getChiTietChucDanh = useCallback(
    async (data: ReactQuery.IChiTietChucDanhParams) => {
      try {
        console.log("data", data);
        const response = await mutateUseCommonExecute.mutateAsync({
          ma: data.ma,
          ma_doi_tac_ql: data.ma_doi_tac_ql,
          actionCode: ACTION_CODE.GET_CHI_TIET_CHUC_DANH,
        });
        console.log("response.data", response.data);
        return response.data as CommonExecute.Execute.IChiTietChucDanh;
      } catch (error) {
        console.log("getChiTietChucDanh err", error);
        return {} as CommonExecute.Execute.IChiTietChucDanh;
      }
    },
    [mutateUseCommonExecute],
  );

  const searchChucDanh = useCallback(async () => {
    try {
      console.log("filterParams", filterParams);
      const response = await mutateUseCommonExecute.mutateAsync({
        ...filterParams,
        actionCode: ACTION_CODE.TIM_KIEM_DANH_SACH_CHUC_DANH_PHAN_TRANG,
      });
      console.log("response", response);
      if (response.data) {
        setListChucDanh(response.data.data);
        setTongSoDong(response.data.tong_so_dong);
      }
    } catch (error) {
      console.log("searchChucDanh error ", error);
    }
  }, [mutateUseCommonExecute, filterParams]);

  useEffect(() => {
    searchChucDanh();
  }, [filterParams]);

  const capNhatChiTietChucDanh = useCallback(
    async (data: ReactQuery.IUpdateChucDanhParams) => {
      try {
        console.log("data", data);
        const response = await mutateUseCommonExecute.mutateAsync({
          ...data,
          actionCode: ACTION_CODE.UPDATE_CHI_TIET_CHUC_DANH,
        });
        console.log("response", response.data);
        if (response.data) {
          return response.data;
        } else {
          // if (response.response === 400) {
          // }
        }
      } catch (error) {
        console.log("capNhatChiTietChucDanh err", error);
        return {} as CommonExecute.Execute.IChiTietChucDanh;
      }
    },
    [mutateUseCommonExecute],
  );

  //initData chỉ dùng trong useEffect 1 lần, không truyền đi đâu cả, nên không cần memo hoá
  const initData = () => {
    getListDoiTac();
  };
  const value = useMemo<IQuanLyChucDanhContextProps>(
    () => ({
      listDoiTac,
      listChucDanh,
      tongSoDong,
      loading: mutateUseCommonExecute.isLoading,
      searchChucDanh,
      getChiTietChucDanh,
      capNhatChiTietChucDanh,
      filterParams,
      setFilterParams,
    }),
    [listChucDanh, tongSoDong, mutateUseCommonExecute, searchChucDanh, getChiTietChucDanh, capNhatChiTietChucDanh, filterParams],
  );

  return <QuanLyChucDanhContext.Provider value={value}>{children}</QuanLyChucDanhContext.Provider>;
};

export default QuanLyChucDanhProvider;

import {useBaoHiemTaiSanContext} from "../index.context";
import {Form, Row, Col, Table} from "antd";
import {useState, useMemo, useCallback, useEffect} from "react";
import {FormInput} from "@src/components";
import {defaultPaginationTableProps, fillRowTableEmpty} from "@src/hooks";
import {formatCurrencyUS} from "@src/utils";
import {TableDoiTuongColumnDataType} from "./StepComponent/Constant";
import {debounce} from "lodash";

interface RenderTableDoiTuongBaoHiemTaiSanProps {
  columns: any[];
  onRowClick?: (record: any) => void;
  summaryRender?: (tongPhiBaoHiem: number) => React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  pageSize?: number;
}

export function RenderTableDoiTuongBaoHiemTaiSan_Component({columns, onRowClick, summaryRender, style = {}, pageSize: pageSizeProp = 10}: RenderTableDoiTuongBaoHiemTaiSanProps) {
  const {danhSachDoiTuongBaoHiemTaiSan, loading, tongSoDongDoiTuongBaoHiemTaiSan, tongPhiBaoHiemTaiSanFromAPI, timKiemPhanTrangDoiTuongBaoHiemTaiSan, chiTietHopDongBaoHiemTaiSan} =
    useBaoHiemTaiSanContext();
  const [formTimKiem] = Form.useForm();
  const [searchParams, setSearchParams] = useState<any>({nd_tim: ""});
  const [page, setPage] = useState(1);
  const [doiTuongXeSelected, setDoiTuongXeSelected] = useState<any>(null);
  const [pageSize, setPageSize] = useState(pageSizeProp);

  useEffect(() => {
    if (pageSizeProp !== undefined && pageSizeProp !== null) {
      setPageSize(pageSizeProp);
      return;
    }
    function calculatePageSize() {
      // Giả sử header, search form, ... chiếm 300px, mỗi dòng table 48px
      const availableHeight = window.innerHeight - 300;
      const rowHeight = 48;
      const calculatedPageSize = Math.max(3, Math.floor(availableHeight / rowHeight)); // tối thiểu 3 dòng
      setPageSize(calculatedPageSize);
    }
    calculatePageSize();
    window.addEventListener("resize", calculatePageSize);
    return () => window.removeEventListener("resize", calculatePageSize);
  }, [pageSizeProp]);

  // DATA TABLE
  const dataTableListDoiTuongBaoHiemXe = useMemo(() => {
    try {
      const tableData = danhSachDoiTuongBaoHiemTaiSan.map((item: any, index: number) => ({
        ...item,
        ten: item.ten,
        key: index.toString(),
      }));
      if (tableData.length > 0 && !doiTuongXeSelected) setDoiTuongXeSelected(tableData[0]);
      const arrEmptyRow: Array<TableDoiTuongColumnDataType> = fillRowTableEmpty(tableData.length, pageSize);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      return [];
    }
  }, [danhSachDoiTuongBaoHiemTaiSan, pageSize]);
  //nhập ở ô input tìm kiếm tự động gọi API
  const deboundSearch = useMemo(
    () =>
      debounce(event => {
        const so_id = chiTietHopDongBaoHiemTaiSan?.so_id;
        const params = {
          so_id,
          nd_tim: event.target.value ?? "",
          dong_tai: "",
        };
        setSearchParams(params);
        timKiemPhanTrangDoiTuongBaoHiemTaiSan({...params, trang: 1, so_dong: pageSize});
      }, 500),
    [],
  );

  // Tìm kiếm và phân trang
  const handleSearchAndPagination = useCallback(
    (values?: any, pageArg?: number) => {
      const so_id = chiTietHopDongBaoHiemTaiSan?.so_id;
      if (values) {
        const cleanedValues = {
          ...values,
          nd_tim: values.nd_tim ?? "",
          dong_tai: values.dong_tai ?? "",
          so_id,
        };
        setSearchParams(cleanedValues);
        setPage(1);
        timKiemPhanTrangDoiTuongBaoHiemTaiSan({...cleanedValues, trang: 1, so_dong: pageSize});
      } else {
        const page = pageArg || 1;
        setPage(page);
        timKiemPhanTrangDoiTuongBaoHiemTaiSan({
          ...searchParams,
          so_id,
          trang: page,
          so_dong: pageSize,
        });
      }
    },
    [chiTietHopDongBaoHiemTaiSan, searchParams, pageSize],
  );

  // Header tìm kiếm
  const renderHeaderTable = () => (
    <div>
      <Form form={formTimKiem} layout="vertical" className="[&_.ant-form-item]:mb-0" onFinish={handleSearchAndPagination}>
        <Row gutter={8} className="items-end">
          <Col span={24}>
            <FormInput name="nd_tim" placeholder="Nhập tên đối tượng" onChange={deboundSearch} />
          </Col>
        </Row>
      </Form>
    </div>
  );

  // Summary mặc định
  const defaultSummary = () => (
    <Table.Summary fixed>
      <Table.Summary.Row>
        <Table.Summary.Cell index={0} colSpan={1} className="!p-[8px]">
          <div className="text-center font-medium">Tổng phí bảo hiểm</div>
        </Table.Summary.Cell>
        <Table.Summary.Cell index={1} align="right" className="!p-[8px]">
          <div className="text-right font-medium">{formatCurrencyUS(tongPhiBaoHiemTaiSanFromAPI)}</div>
        </Table.Summary.Cell>
      </Table.Summary.Row>
    </Table.Summary>
  );

  return (
    <Table<TableDoiTuongColumnDataType>
      className="custom-table-title-padding"
      style={style}
      dataSource={dataTableListDoiTuongBaoHiemXe}
      columns={columns}
      loading={loading}
      sticky
      rowClassName={(record: any) => (record.so_id_dt && record.so_id_dt === doiTuongXeSelected?.so_id_dt ? "table-row-active" : "")}
      pagination={{
        size: "small",
        ...defaultPaginationTableProps,
        total: tongSoDongDoiTuongBaoHiemTaiSan,
        pageSize: pageSize,
        current: page,
        onChange: (page: any) => handleSearchAndPagination(undefined, page),
        locale: {
          jump_to: "Tới trang",
          page: "",
        },
      }}
      showHeader={true}
      title={renderHeaderTable}
      bordered
      onRow={record => ({
        style: {cursor: loading ? "progress" : "pointer"},
        onClick: () => {
          onRowClick && onRowClick(record);
          setDoiTuongXeSelected(record);
        },
      })}
      summary={summaryRender ? () => summaryRender(tongPhiBaoHiemTaiSanFromAPI) : defaultSummary}
    />
  );
}

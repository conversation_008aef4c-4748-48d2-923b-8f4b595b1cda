import {CheckOutlined, ClearOutlined, CloseOutlined, SearchOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {Button, FormInput, Highlighter} from "@src/components";
import {COLOR_PALETTE} from "@src/constants";
import {defaultPaginationTableProps, fillRowTableEmpty, useDoiTac} from "@src/hooks";
import {Col, Empty, Form, Input, InputRef, Modal, Row, Table, TableColumnType, Tag, Tooltip} from "antd";
import {forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState} from "react";
import {useBaoHiemTaiSanContext} from "../index.context";
import {
  danhMucDaiLyColumns,
  defaultFormTimKiemPhanTrangDaiLyKhaiThac,
  FormTimKiemDanhMucDaiLy,
  LOAI_DAI_LY_TK,
  radioItemTrangThaiDaiLy,
  TableDanhMucDaiLyDataType,
  TRANG_THAI_CHI_TIET_DAI_LY,
} from "./Constant";
import {useTableNoHover} from "@src/utils/tableNoHoverUtils";
interface Props {
  onSelectDaiLy: (dly_kt: CommonExecute.Execute.IChiTietDanhMucDaiLy | null) => void;
}
export interface IModalDaiLyKhaiThacRef {
  open: (data?: CommonExecute.Execute.IChiTietDanhMucDaiLy, ma_doi_tac_ql?: string) => void;
  close: () => void;
}
const {ma_doi_tac_ql, ma, ten, loai, trang_thai} = FormTimKiemDanhMucDaiLy;
type DataIndex = keyof TableDanhMucDaiLyDataType;

const PAGE_SIZE = 10; //khai báo khác default do modal nhỏ hơn

const ModalDaiLyKhaiThac = forwardRef<IModalDaiLyKhaiThacRef, Props>(({onSelectDaiLy}: Props, ref) => {
  // Sử dụng useTableNoHover hook để tự động inject CSS và cung cấp utility functions
  const {getTableClassName: getNoHoverTableClassName, getRowClassName} = useTableNoHover({
    activeRowColor: "#96bf49", // Màu xanh lá cho row được chọn
    styleId: "modal-dai-ly-khai-thac-table-styles", // ID riêng cho modal này
  });
  const {listDoiTac} = useDoiTac();
  const {loading, tongSoDongDataLDaiLy, timKiemPhanTrangDaiLy, danhSachDaiLy} = useBaoHiemTaiSanContext();

  const [formThongTinDaiLy] = Form.useForm();

  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const [chiTietDaiLyKhaiThac, setChiTietDaiLyKhaiThac] = useState<CommonExecute.Execute.IChiTietDanhMucDaiLy | null>(null);
  const [searchParams, setSearchParams] = useState<ReactQuery.ITimKiemPhanTrangDanhSachDaiLyParams>(defaultFormTimKiemPhanTrangDaiLyKhaiThac);
  const [page, setPage] = useState(1);
  const [searchText, setSearchText] = useState("");
  const [searchedColumn, setSearchedColumn] = useState("");

  const searchInput = useRef<InputRef>(null);

  useImperativeHandle(ref, () => ({
    open: (dataChiTietDaiLy?: CommonExecute.Execute.IChiTietDanhMucDaiLy, doiTacSelected?: string) => {
      setIsOpen(true);

      formThongTinDaiLy.resetFields();
      if (dataChiTietDaiLy) setChiTietDaiLyKhaiThac(dataChiTietDaiLy);

      //đoạn này để set dữ liệu vào form và cho tìm kiếm với mã đối tác, mã chi nhánh từ ngoài truyền vào
      formThongTinDaiLy.setFieldsValue({
        ma_doi_tac_ql: doiTacSelected,
      });
      const bodySearch = {
        ...defaultFormTimKiemPhanTrangDaiLyKhaiThac,
        ma_doi_tac_ql: doiTacSelected,
      };
      setSearchParams(bodySearch);
      timKiemPhanTrangDaiLy(bodySearch);
    },
    close: () => setIsOpen(false),
  }));

  //MAP VALUE CỦA LIST VÀO TABLE
  const dataTableListDaiLy = useMemo<Array<TableDanhMucDaiLyDataType>>(() => {
    try {
      const tableData = danhSachDaiLy.map((item: any, index: number) => {
        return {
          ...item,
          stt: item.sott ?? index + 1,
          key: index.toString(),
        };
      });
      const arrEmptyRow: Array<TableDanhMucDaiLyDataType> = fillRowTableEmpty(tableData.length, PAGE_SIZE);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      console.log("dataTableListDaiLy error", error);
      return [];
    }
  }, [danhSachDaiLy]);

  // init form data gọi vào index.configs
  // useEffect(() => {
  //     initFormFields(form, chiTietKhachHang);
  // }, [chiTietKhachHang]);

  //xử lý validate form
  useEffect(() => {
    setDisableSubmit(!chiTietDaiLyKhaiThac); // Nếu có activeKey -> enable nút Lưu, ngược lại -> disable
  }, [chiTietDaiLyKhaiThac]);

  //Bấm tìm kiếm
  const onTimKiemPhanTrangDaiLy = (values: ReactQuery.ITimKiemPhanTrangDanhSachDaiLyParams & ReactQuery.IPhanTrang) => {
    // đoạn này để sửa các giá trị undefined => '' trước khi request lên API
    // Ant Design Select không coi "" là một giá trị "chưa chọn", mà là một giá trị đã chọn hợp lệ (dù nó rỗng), nên nó sẽ không hiển thị placeholder nữa.
    const cleanedValues = {
      ...values,
      ma_doi_tac_ql: values.ma_doi_tac_ql ?? "",
      ma: values.ma ?? "",
      ten: values.ten ?? "",
      loai: values.loai ?? "",
      trang_thai: values.trang_thai ?? "",
      trang: values.trang ?? 1,
    };
    setSearchParams(cleanedValues);
    setPage(1); // reset về trang 1 khi search mới
    timKiemPhanTrangDaiLy({...cleanedValues, trang: 1, so_dong: PAGE_SIZE});
  };

  //bấm huỷ / để sau xoá dữ liệu của modal khi nào mở thì gọi lại
  const onPressDeSau = () => {
    setIsOpen(false);
    setChiTietDaiLyKhaiThac(null);
  };

  //bấm xác nhận
  const handleSelectDaiLy = () => {
    if (chiTietDaiLyKhaiThac && onSelectDaiLy) {
      onSelectDaiLy(chiTietDaiLyKhaiThac); // Gọi callback để truyền khách hàng ra ngoài
      setIsOpen(false); // Đóng modal sau khi chọn
    }
  };

  //renderFooter Modal
  const renderFooter = () => {
    return (
      <Form.Item className="mb-0">
        <Button type="default" htmlType="reset" form="formSelectDaiLy" onClick={onPressDeSau} className="mr-2" icon={<CloseOutlined />}>
          Đóng
        </Button>
        <Button disabled={disableSubmit} type="primary" form="formSelectDaiLy" onClick={handleSelectDaiLy} icon={<CheckOutlined />}>
          Chọn
        </Button>
      </Form.Item>
    );
  };
  //Render

  //search client
  const handleSearch = (selectedKeys: string[], confirm: () => void, dataIndex: DataIndex) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  };

  const handleReset = (clearFilters: () => void, confirm: () => void, dataIndex: DataIndex) => {
    clearFilters();
    confirm();
    setSearchText("");
    setSearchedColumn(dataIndex);
  };

  //Gọi api khi chuyển trang
  const onChangePage = useCallback(
    (page: number, pageSize: number) => {
      setPage(page);
      timKiemPhanTrangDaiLy({...searchParams, trang: page, so_dong: pageSize});
    },
    [searchParams],
  );

  const renderFormInputColum = (props?: any) => (
    <Col span={4}>
      <FormInput {...props} />
    </Col>
  );

  const getColumnSearchProps = (dataIndex: DataIndex, title: string): TableColumnType<TableDanhMucDaiLyDataType> => ({
    filterDropdown:
      dataIndex !== "trang_thai_ten"
        ? ({setSelectedKeys, selectedKeys, confirm, clearFilters, close}) => (
            <div style={{padding: 8}} onKeyDown={e => e.stopPropagation()} className="flex flex-row items-center p-2">
              <Input
                ref={searchInput}
                placeholder={`Tìm theo ${title}`}
                value={selectedKeys[0]}
                onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                onPressEnter={() => handleSearch(selectedKeys as string[], confirm, dataIndex)}
                style={{display: "block", marginRight: 8}}
              />
              <Tooltip title="Tìm kiếm">
                <Button type="primary" shape="circle" icon={<SearchOutlined />} onClick={() => handleSearch(selectedKeys as string[], confirm, dataIndex)} className="mr-2" />
              </Tooltip>
              <Tooltip title="Xoá">
                <Button type="primary" shape="circle" icon={<ClearOutlined />} onClick={() => clearFilters && handleReset(clearFilters, confirm, dataIndex)} />
              </Tooltip>
            </div>
          )
        : null,
    filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} />,
    onFilter: (value, record) =>
      record[dataIndex]
        ? record[dataIndex]
            .toString()
            .toLowerCase()
            .includes((value as string).toLowerCase())
        : false,
    filterDropdownProps: {
      onOpenChange(open) {
        if (open) {
          setTimeout(() => searchInput.current?.select(), 100);
        }
      },
    },
    filters: dataIndex === "trang_thai_ten" ? radioItemTrangThaiDaiLy : undefined,
    render: (text, record, index) => {
      if (dataIndex === "trang_thai_ten") {
        // Xác định màu dựa vào text (trạng thái hiển thị)
        const color = text === "Đang sử dụng" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];
        if (record.key.toString().includes("empty")) return "";
        return <Tag color={color}>{text}</Tag>;
      }
      return searchedColumn === dataIndex ? (
        <Highlighter searchWords={[searchText]} textToHighlight={text ? text.toString() : ""} />
      ) : text !== undefined ? (
        text
      ) : (
        <Tag color={"transparent"} className="!text-white text-[11px]">
          {"\u00A0"}
        </Tag>
      );
    },
  });

  // render modal

  const renderTable = () => {
    return (
      <div
        style={{
          overflow: "auto",
        }}>
        <Table<TableDanhMucDaiLyDataType>
          className={getNoHoverTableClassName("custom-table no-header-border-radius")}
          sticky
          scroll={{y: "100vh"}}
          loading={loading}
          style={{cursor: "pointer"}}
          onRow={(record, rowIndex) => {
            if (record.key.toString().includes("empty")) return {};
            return {
              onClick: async event => {
                setChiTietDaiLyKhaiThac(record as CommonExecute.Execute.IChiTietDanhMucDaiLy);
              },
              onDoubleClick: async event => {
                await setChiTietDaiLyKhaiThac(record as CommonExecute.Execute.IChiTietDanhMucDaiLy);
                handleSelectDaiLy();
              },
            };
          }}
          rowClassName={record => (chiTietDaiLyKhaiThac?.ma ? getRowClassName(chiTietDaiLyKhaiThac.ma === record.ma) : "")}
          columns={(danhMucDaiLyColumns || []).map(item => {
            // Đảm bảo item.key được định nghĩa, item.title là chuỗi và item.key không phải là "stt" trước khi thêm ô tìm kiếm
            return {
              ...item,
              ...(item.key && typeof item.title === "string" && item.key !== "sott" ? getColumnSearchProps(item.key as keyof TableDanhMucDaiLyDataType, item.title) : {}),
            };
          })} //định nghĩa cột của table
          dataSource={dataTableListDaiLy}
          pagination={{
            ...defaultPaginationTableProps,
            defaultPageSize: PAGE_SIZE,
            total: tongSoDongDataLDaiLy, //quan trọng - để hiển thị toàn bộ số lượng dòng dữ liệu phục vụ việc phân trang
            current: page, //set current page
            pageSize: PAGE_SIZE, //Số lượng mục dữ liệu trên mỗi trang
            onChange: (page, pageSize) => {
              onChangePage(page, pageSize);
            },
            locale: {
              jump_to: "Tới trang",
              page: "",
            },
          }}
          bordered
        />
      </div>
    );
  };

  return (
    <div id={"MODAL_DLY"}>
      <Modal
        title={"Danh sách đại lý"}
        open={isOpen}
        onOk={() => setIsOpen(false)}
        onCancel={() => {
          formThongTinDaiLy.resetFields();
          setIsOpen(false);
          setChiTietDaiLyKhaiThac(null);
        }}
        footer={renderFooter}
        closable
        maskClosable={false}
        width="95vw"
        style={{
          top: "5vh",
          left: 0,
          padding: 0,
        }}
        styles={{
          body: {
            // height: "75vh",
            overflow: "hidden",
          },
        }}
        className="custom-full-modal">
        <Form id="form" onFinish={onTimKiemPhanTrangDaiLy} form={formThongTinDaiLy} layout="vertical" autoComplete="on" className="mt-1">
          <Row gutter={16} align="bottom">
            {renderFormInputColum({...ma_doi_tac_ql, options: listDoiTac})}
            {renderFormInputColum({...ma})}
            {renderFormInputColum({...ten})}
            {renderFormInputColum({...loai, options: LOAI_DAI_LY_TK})}
            {renderFormInputColum({...trang_thai, options: TRANG_THAI_CHI_TIET_DAI_LY})}

            <Col>
              <Form.Item className="!mb-2">
                <Button type="primary" htmlType="submit" loading={loading} icon={<SearchOutlined />} className="w-full">
                  Tìm kiếm
                </Button>
              </Form.Item>
            </Col>
            {/* <Form.Item className="w-32">
              <Button
                className="w-full"
                type="primary"
                icon={<PlusCircleOutlined />}
                // onClick={() => refModalThemHopDongBaoHiemXe.current?.open()}
                loading={loading}>
                Thêm mới
              </Button>
            </Form.Item> */}
          </Row>
        </Form>
        {dataTableListDaiLy.length > 0 ? renderTable() : <Empty description="Không có dữ liệu" />}
      </Modal>
    </div>
  );
});

ModalDaiLyKhaiThac.displayName = "ModalDaiLyKhaiThac";
export default ModalDaiLyKhaiThac;

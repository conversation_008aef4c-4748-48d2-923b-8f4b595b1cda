import {IFormInput, ReactQuery} from "@src/@types";
import {colWidthByKey, defaultTableColumnsProps} from "@src/hooks";
import {TableProps} from "antd";

//form tìm kiếm
export interface IFormTimKiemDanhMucNghiepVuFieldsConfig {
  ma: IFormInput;
  ten: IFormInput;

  trang_thai: IFormInput;
}
export const FormTimKiemDanhMucNghiepVu: IFormTimKiemDanhMucNghiepVuFieldsConfig = {
  ma: {
    component: "input",
    name: "ma",
    label: "Mã nghiệp vụ",
    placeholder: "Mã nghiệp vụ",
  },
  ten: {
    component: "input",
    name: "ten",
    label: "Tên nghiệp vụ",
    placeholder: "Tên nghiệp vụ",
  },

  trang_thai: {
    component: "select",
    name: "trang_thai",
    label: "Trạng thái",
    placeholder: "Chọn trạng thái",
  },
};

export interface TableDanhMucNghiepVuDataType {
  key: string;
  stt?: number;
  ma?: string;
  ten?: string;
  ngay_tao?: string;
  nguoi_tao?: string;
  ngay_cap_nhat?: string;
  nguoi_cap_nhat?: string;
  trang_thai_ten?: string;
  trang_thai?: string;
}

export const danhMucNghiepVuColumns: TableProps<TableDanhMucNghiepVuDataType>["columns"] = [
  {title: "STT", dataIndex: "stt", key: "stt", width: colWidthByKey.sott, align: "center", ...defaultTableColumnsProps},
  {title: "Mã", dataIndex: "ma", key: "ma", width: 110, align: "center", ...defaultTableColumnsProps},
  {title: "Tên", dataIndex: "ten", key: "ten", width: 200, align: "left", ...defaultTableColumnsProps},
  {title: "Ngày tạo", dataIndex: "ngay_tao", key: "ngay_tao", width: colWidthByKey.ngay_tao, align: "center", ...defaultTableColumnsProps},
  {title: "Người tạo", dataIndex: "nguoi_tao", key: "nguoi_tao", width: colWidthByKey.nguoi_tao, align: "center", ...defaultTableColumnsProps},
  {title: "Ngày cập nhật", dataIndex: "ngay_cap_nhat", key: "ngay_cap_nhat", width: colWidthByKey.ngay_cap_nhat, align: "center", ...defaultTableColumnsProps},
  {title: "Người cập nhật", dataIndex: "nguoi_cap_nhat", key: "nguoi_cap_nhat", width: colWidthByKey.nguoi_cap_nhat, align: "center", ...defaultTableColumnsProps},
  {title: "Trạng Thái", dataIndex: "trang_thai_ten", key: "trang_thai_ten", width: 130, align: "center", ...defaultTableColumnsProps},
];
export const defaultFormValue: ReactQuery.ITimKiemPhanTrangDanhSachNghiepVuParams = {
  ma: "",
  ten: "",
  trang_thai: "",
  // trang: 1,
  // so_dong: 15,
  // actionCode: "",
};
export const radioItemTrangThaiNghiepVuTable = [
  {value: "Đang sử dụng", text: "Đang sử dụng"},
  {value: "Ngừng sử dụng", text: "Ngừng sử dụng"},
];
export const TRANG_THAI = [
  {ten: "Đang sử dụng", ma: "D"},
  {ten: "Ngừng sử dụng", ma: "K"},
];
export type DataIndex = keyof TableDanhMucNghiepVuDataType;

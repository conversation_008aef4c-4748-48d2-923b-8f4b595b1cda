import {createContext, useContext} from "react";

import {IQuanLyMucDoTonThatXeContextProps} from "./index.model";

//khởi tạo giá trị mặc định
export const QuanLyMucDoTonThatXeContext = createContext<IQuanLyMucDoTonThatXeContextProps>({
  listMucDoTonThatXe: [],
  tongSoDong: 0,
  loading: false,
  filterParams: {},
  getListMucDoTonThatXe: async () => Promise.resolve(),
  getChiTietMucDoTonThatXe: async () => Promise.resolve({} as CommonExecute.Execute.IMucDoTonThatXe),
  capNhatChiTietMucDoTonThatXe: async () => Promise.resolve(),
  setFilterParams: () => {},
});

export const useQuanLyMucDoTonThatXeContext = () => useContext(QuanLyMucDoTonThatXeContext);

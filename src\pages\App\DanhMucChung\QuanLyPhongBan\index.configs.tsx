import {IFormInput} from "@src/@types";
import {colWidthByKey, defaultTableColumnsProps} from "@src/hooks";
import {TableProps} from "antd";

export interface TablePhongBanDataType {
  ten?: string;
  ma_doi_tac?: string;
  ma_chi_nhanh?: string;
  ma?: string;
  stt?: number;
  sott?: number;
  ngay_tao?: string;
  nguoi_tao?: string;
  ngay_cap_nhat?: string;
  nguoi_cap_nhat?: string;
  trang_thai_ten?: string;
  trang_thai?: string;
  doi_tac_ten_tat?: string;
  chi_nhanh_ten_tat?: string;
  key?: string | number;
}

// ĐỊNH NGHĨA Column RENDER RA TỪNG Ô TRONG CỘT
//đọc https://ant.design/components/table#column để biết thêm về column
export const phongBanColumns: TableProps<TablePhongBanDataType>["columns"] = [
  {
    title: "STT",
    dataIndex: "stt",
    key: "stt",
    width: 60,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Mã",
    dataIndex: "ma",
    key: "ma",
    width: 80,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Tên phòng ban",
    dataIndex: "ten",
    key: "ten",
    width: 250,
    ...defaultTableColumnsProps,
  },
  {
    title: "Chi nhánh",
    dataIndex: "chi_nhanh_ten_tat",
    key: "doi_tac_ten_tat",
    width: 150,
    ...defaultTableColumnsProps,
  },
  {
    title: "Đối tác",
    dataIndex: "doi_tac_ten_tat",
    key: "doi_tac_ten_tat",
    width: 150,
    ...defaultTableColumnsProps,
  },
  {
    title: "Ngày tạo",
    dataIndex: "ngay_tao",
    key: "ngay_tao",
    width: colWidthByKey.ngay_tao,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Người tạo",
    dataIndex: "nguoi_tao",
    key: "nguoi_tao",
    width: colWidthByKey.nguoi_tao,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Ngày cập nhật",
    dataIndex: "ngay_cap_nhat",
    key: "ngay_cap_nhat",
    width: colWidthByKey.ngay_cap_nhat,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Người cập nhật",
    dataIndex: "nguoi_cap_nhat",
    key: "nguoi_cap_nhat",
    width: colWidthByKey.nguoi_cap_nhat,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Trạng thái",
    dataIndex: "trang_thai_ten",
    key: "trang_thai_ten",
    width: 130,
    align: "center",
    ...defaultTableColumnsProps,
  },
];

export const setFormFields = (form: any, chiTietPhongBan: any) => {
  if (!chiTietPhongBan) return;
  const fields = [
    {name: "ten", value: chiTietPhongBan.ten || ""},
    {name: "ma", value: chiTietPhongBan.ma || ""},
    {name: "stt", value: chiTietPhongBan.stt || ""},
    {name: "dthoai", value: chiTietPhongBan.dthoai || ""},
    {name: "trang_thai", value: chiTietPhongBan.trang_thai},
    {name: "ma_doi_tac", value: chiTietPhongBan.ma_doi_tac},
    {name: "ma_chi_nhanh", value: chiTietPhongBan.ma_chi_nhanh},
  ];

  form.setFields(fields);
};

//option select trạng thái
export const optionTrangThaiPhongBanSelect: Array<{ma: string; ten: string}> = [
  {ma: "D", ten: "Đang sử dụng"},
  {ma: "K", ten: "Ngưng sử dụng"},
];

//Form
export interface IFormTimKiemPhanTrangPhongBanFieldsConfig {
  ma_doi_tac: IFormInput;
  ma_chi_nhanh: IFormInput;
  ma: IFormInput;
  ten: IFormInput;
  trang_thai: IFormInput;
}
export const FormTimKiemPhanTrangPhongBan: IFormTimKiemPhanTrangPhongBanFieldsConfig = {
  ma_doi_tac: {
    component: "select",
    name: "ma_doi_tac",
    label: "Đối tác",
    placeholder: "Chọn đối tác",
    className: "!mb-0",
  },
  ma_chi_nhanh: {
    component: "select",
    name: "ma_chi_nhanh",
    label: "Chi nhánh",
    placeholder: "Chọn chi nhánh",
    className: "!mb-0",
  },
  ma: {
    name: "ma",
    label: "Mã phòng ban",
    component: "input",
    placeholder: "Nhập mã phòng ban",
    className: "!mb-0",
  },
  ten: {
    name: "ten",
    label: "Tên phòng ban",
    component: "input",
    placeholder: "Nhập tên phòng ban",
    className: "!mb-0",
  },
  trang_thai: {
    component: "select",
    name: "trang_thai",
    label: "Trạng thái",
    placeholder: "Chọn trạng thái",
    className: "!mb-0",
  },
};

export const radioItemTrangThaiPhongBanTable = [
  {value: "Đang sử dụng", text: "Đang sử dụng"},
  {value: "Ngưng sử dụng", text: "Ngưng sử dụng"},
];

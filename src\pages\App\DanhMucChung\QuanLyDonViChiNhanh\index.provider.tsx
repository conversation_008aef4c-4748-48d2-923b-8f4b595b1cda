import React, {PropsWithChildren, useCallback, useEffect, useMemo, useState} from "react";

import {ReactQuery} from "@src/@types";
import {ACTION_CODE} from "@src/constants";
import {useCommonExecute} from "@src/services/react-queries";
import {QuanLyDonViChiNhanhContext} from "./index.context";
import {IQuanLyDonViChiNhanhContextProps} from "./index.model";

const QuanLyDonViChiNhanhProvider: React.FC<PropsWithChildren> = props => {
  const {children} = props;

  const mutateUseCommonExecute = useCommonExecute();

  const [listDoiTac, setListDoiTac] = useState<Array<CommonExecute.Execute.IDoiTac>>([]);
  const [listDonViChiNhanh, setlistDonViChiNhanh] = useState<Array<CommonExecute.Execute.IChiNhanh>>([]);
  const [tongSoDong, setTongSoDong] = useState<number>(0);
  const [filterParams, setFilterParams] = useState<ReactQuery.ILayDanhSachDonViChiNhanhParams & ReactQuery.IPhanTrang>({ma: "", ten: "", mst: "", trang_thai: "", trang: 1, so_dong: 13});

  useEffect(() => {
    initData();
  }, []);

  const getChiTietDonViChiNhanh = useCallback(
    async (data: ReactQuery.IChiTietDonViChiNhanhParams) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ma: data.ma,
          ma_doi_tac: data.ma_doi_tac,
          actionCode: ACTION_CODE.GET_CHI_TIET_DON_VI_CHI_NHANH,
        });
        return response.data as CommonExecute.Execute.IChiNhanh;
      } catch (error) {
        console.log("getChiTietDonViChiNhanh err", error);
        return {} as CommonExecute.Execute.IChiNhanh;
      }
    },
    [mutateUseCommonExecute],
  );

  const getListDoiTac = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        actionCode: ACTION_CODE.LIET_KE_DANH_SACH_DOI_TAC,
      });
      if (response.data) {
        setListDoiTac(response.data);
      }
    } catch (error) {
      console.log("getListDoiTac error ", error);
    }
  }, [mutateUseCommonExecute]);

  const searchDonViChiNhanh = useCallback(async () =>
    // params: ReactQuery.ILayDanhSachDonViChiNhanhParams & ReactQuery.IPhanTrang = filterParamsInit
    {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...filterParams,
          actionCode: ACTION_CODE.TIM_KIEM_DANH_SACH_DON_VI_CHI_NHANH_PHAN_TRANG,
        });
        if (response.data) {
          setlistDonViChiNhanh(response.data.data);
          setTongSoDong(response.data.tong_so_dong);
        }
      } catch (error) {
        console.log("getListDonViChiNhanh error ", error);
      }
    }, [mutateUseCommonExecute, filterParams]);

  useEffect(() => {
    searchDonViChiNhanh();
  }, [filterParams]);

  const capNhatChiTietDonViChiNhanh = useCallback(
    async (data: ReactQuery.IUpdateDonViChiNhanhParams) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...data,
          actionCode: ACTION_CODE.UPDATE_CHI_TIET_DON_VI_CHI_NHANH,
        });
        return response.data;
      } catch (error) {
        console.log("capNhatChiTietDonViChiNhanh err", error);
        return {} as CommonExecute.Execute.IChiNhanh;
      }
    },
    [mutateUseCommonExecute],
  );

  //initData chỉ dùng trong useEffect 1 lần, không truyền đi đâu cả, nên không cần memo hoá
  const initData = () => {
    getListDoiTac();
  };
  const value = useMemo<IQuanLyDonViChiNhanhContextProps>(
    () => ({
      listDoiTac,
      listDonViChiNhanh,
      tongSoDong,
      loading: mutateUseCommonExecute.isLoading,
      searchDonViChiNhanh,
      getChiTietDonViChiNhanh,
      capNhatChiTietDonViChiNhanh,
      filterParams,
      setFilterParams,
    }),
    [listDoiTac, listDonViChiNhanh, tongSoDong, mutateUseCommonExecute, searchDonViChiNhanh, getChiTietDonViChiNhanh, capNhatChiTietDonViChiNhanh, filterParams],
  );

  return <QuanLyDonViChiNhanhContext.Provider value={value}>{children}</QuanLyDonViChiNhanhContext.Provider>;
};

export default QuanLyDonViChiNhanhProvider;

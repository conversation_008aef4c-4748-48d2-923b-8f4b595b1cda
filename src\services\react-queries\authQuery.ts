import {ReactQuery} from "@src/@types";
import {Authen} from "@src/@types/Authentication";
import {useMutation} from "react-query";
import {AuthEndpoint} from "../axios";

interface UseDangNhapProps {
  onSuccess?: (data: any) => void;
  onError?: (error: Error) => void;
}

type DangNhapResponse = {
  data: Authen.Profile.IMetaData;
  output: any;
};

// Tên react hook phải bắt đầu bằng chữ use
export const useDangNhap = ({onSuccess, onError}: UseDangNhapProps = {}) => {
  //type của useMutation<ReturnType, ErrorType, InputType>()
  return useMutation<DangNhapResponse, any, ReactQuery.ILoginParams>({
    mutationFn: AuthEndpoint.dangNhap, //  Đ<PERSON><PERSON> là hàm thực sự gọi API từ axios
    onSuccess: data => onSuccess && onSuccess(data),
    onError: error => onError && onError(error),
  });
};

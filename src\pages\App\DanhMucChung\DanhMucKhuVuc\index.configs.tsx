import React from "react";
import {TableColumnsType, TableProps} from "antd";

import {IFormInput, ReactQuery} from "@src/@types";
import {REGUlAR_EXPRESSION} from "@src/constants";
import {colWidthByKey, defaultTableColumnsProps, ruleInputMessage, validationRules} from "@src/hooks";

//Check empty value
const isEmptyValue = (value: any): boolean => {
  return value === null || value === undefined || value === '' || (typeof value === 'string' && value.trim() === '');
};
//===== FORM TÌM KIẾM =====
export interface IFormTimKiemKhuVucFieldsConfig {
  ma_chau_luc: IFormInput;
  ma: IFormInput;
  ten: IFormInput;
  trang_thai: IFormInput;
}

//Cấu hình form tìm kiếm khu vực ở header table
export const FormTimKiemDanhMucKhuVuc: IFormTimKiemKhuVucFieldsConfig = {
  ma_chau_luc: {
    component: "select",
    name: "ma_chau_luc",
    label: "Châu lục",
    placeholder: "Chọn châu lục",
    className: "!mb-0",
  },
  ma: {
    component: "input",
    name: "ma",
    label: "Mã khu vực",
    placeholder: "Nhập mã khu vực",
    className: "!mb-0",
  },
  ten: {
    component: "input",
    name: "ten",
    label: "Tên khu vực",
    placeholder: "Nhập tên khu vực",
    className: "!mb-0",
  },
  trang_thai: {
    component: "select",
    name: "trang_thai",
    label: "Trạng thái",
    placeholder: "Chọn trạng thái",
    className: "!mb-0",
  },
};

//===== FORM MODAL CHI TIẾT (TẠO MỚI/SỬA) =====
export interface IFormTaoMoiKhuVucFieldsConfig {
  ma_chau_luc: IFormInput;
  ma: IFormInput;
  ten: IFormInput;
  stt: IFormInput;
  trang_thai: IFormInput;
}

//Cấu hình form tạo mới/chỉnh sửa khu vực trong modal
export const FormTaoMoiKhuVuc: IFormTaoMoiKhuVucFieldsConfig = {
  ma_chau_luc: {
    component: "select",
    label: "Châu lục",
    name: "ma_chau_luc",
    placeholder: "Chọn châu lục",
    rules: [ruleInputMessage.required],
  },
  ma: {
    component: "input",
    label: "Mã khu vực",
    name: "ma",
    placeholder: "Nhập mã khu vực",
    rules: [ruleInputMessage.required],
  },
  ten: {
    component: "input",
    label: "Tên khu vực",
    name: "ten",
    placeholder: "Nhập tên khu vực",
    rules: [ruleInputMessage.required],
  },
  stt: {
    component: "input",
    label: "Số thứ tự",
    name: "stt",
    placeholder: "Nhập số thứ tự",
    rules: [],
  },
  trang_thai: {
    component: "select",
    label: "Trạng thái",
    name: "trang_thai",
    placeholder: "Chọn trạng thái",
    rules: [ruleInputMessage.required],
  },
};

//===== FORM MODAL CHI TIẾT (LEGACY) =====
export const FormModalChiTietKhuVuc: Record<string, IFormInput> = {
  ma_chau_luc: {
    name: "ma_chau_luc",
    label: "Châu lục",
    required: true,
    placeholder: "Chọn châu lục",
    component: "select",
    options: [], //Sẽ được cung cấp từ component
  },
  ma: {
    name: "ma",
    label: "Mã khu vực",
    required: true,
    placeholder: "Nhập mã khu vực",
    component: "input",
  },
  ten: {
    name: "ten",
    label: "Tên khu vực",
    required: true,
    placeholder: "Nhập tên khu vực",
    component: "input",
  },
  trang_thai: {
    name: "trang_thai",
    label: "Trạng thái",
    required: true,
    placeholder: "Chọn trạng thái",
    component: "select",
    options: [], //Sẽ được cung cấp từ component
  },
};

//===== RADIO ITEMS - TRẠNG THÁI =====
//Options cho dropdown trạng thái trong form tìm kiếm
export const radioItemTrangThaiKhuVucSelect: Array<{ma: string; ten: string}> = [
  {ma: "", ten: "Tất cả"},
  {ma: "D", ten: "Đang sử dụng"},
  {ma: "K", ten: "Ngưng sử dụng"},
];

export const radioItemTrangThaiKhuVucTable = [
  {value: "Đang sử dụng", text: "Đang sử dụng"},
  {value: "Ngưng sử dụng", text: "Ngưng sử dụng"},
];

//===== TRẠNG THÁI TẠO MỚI =====
export const TRANG_THAI_TAO_MOI_KHU_VUC = [
  {value: "D", label: "Đang sử dụng"},
  {value: "K", label: "Ngưng sử dụng"},
];

//===== KIỂU DỮ LIỆU TABLE =====
export interface TableKhuVucColumnDataType {
  key: string;
  sott?: number;
  ma?: string;
  ten?: string;
  ma_chau_luc?: string;
  ten_chau_luc?: string;
  stt?: number;
  trang_thai?: string;
  ngay_tao?: string;
  nguoi_tao?: string;
  ngay_cap_nhat?: string;
  nguoi_cap_nhat?: string;
  trang_thai_ten?: string;
}

//===== CỘT TABLE =====
//Cấu hình các cột hiển thị trong bảng danh sách khu vực
export const tableKhuVucColumn: TableProps<TableKhuVucColumnDataType>["columns"] = [
  {
    title: "STT",
    dataIndex: "sott",
    key: "sott",
    align: "center",
    width: colWidthByKey.sott,
    ...defaultTableColumnsProps,
  },
  {
    title: "Mã khu vực",
    dataIndex: "ma",
    key: "ma",
    align: "center",
    width: 150,
    ...defaultTableColumnsProps,
  },
  {
    title: "Tên khu vực",
    dataIndex: "ten",
    key: "ten",
    ...defaultTableColumnsProps,
  },
  {
    title: "Châu lục",
    dataIndex: "ten_chau_luc",
    key: "ten_chau_luc",
    width: 120,
    ...defaultTableColumnsProps,
  },
  {
    title: "Ngày tạo",
    dataIndex: "ngay_tao",
    key: "ngay_tao",
    width: colWidthByKey.ngay_tao,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Người tạo",
    dataIndex: "nguoi_tao",
    key: "nguoi_tao",
    width: colWidthByKey.nguoi_tao,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Ngày cập nhật",
    dataIndex: "ngay_cap_nhat",
    key: "ngay_cap_nhat",
    width: colWidthByKey.ngay_cap_nhat,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Người cập nhật",
    dataIndex: "nguoi_cap_nhat",
    key: "nguoi_cap_nhat",
    width: colWidthByKey.nguoi_cap_nhat,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Trạng thái",
    dataIndex: "trang_thai_ten",
    key: "trang_thai_ten",
    width: 130,
    align: "center",
    ...defaultTableColumnsProps,
  },
];

//keyof: return ra key của interface TableKhuVucColumnDataType;
export type TableKhuVucColumnDataIndex = keyof TableKhuVucColumnDataType;

import {
  CheckCircleOutlined,
  CloseOutlined,
  DeleteOutlined,
  EditOutlined,
  FileAddOutlined,
  FileExcelOutlined,
  FileGifOutlined,
  FilePdfOutlined,
  FileUnknownOutlined,
  FileWordOutlined,
  FolderAddOutlined,
  FolderOutlined,
  InboxOutlined,
  MoreOutlined,
  UploadOutlined,
  VerticalRightOutlined,
} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {Button, Image, Input} from "@src/components";
import {ACTION_CODE, COLOR_PALETTE} from "@src/constants";
import {useProfile} from "@src/hooks";
import {FileEndpoint} from "@src/services/axios";
import {env} from "@src/utils";
import {Breadcrumb, Col, Divider, Flex, List, Modal, Popconfirm, Popover, Row, Upload, UploadFile} from "antd";
import {debounce, isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState} from "react";
import {
  arrExtensionExcel,
  arrExtensionGif,
  arrExtensionImage,
  arrExtensionPdf,
  arrExtensionWord,
  arrExtensionXml,
  IGetFolderExtend,
  IModalQuanLyFileCaNhanProps,
  IModalQuanLyFileCaNhanRef,
} from "./Constant";
import {IModalNhapTenFolderRef, ModalNhapTenFolder} from "./ModalNhapTenFolder";
import {ModalProcessUpload} from "./ModalProcessUpload";
const {Dragger} = Upload;

const ModalQuanLyFileCaNhanComponent = forwardRef<IModalQuanLyFileCaNhanRef, IModalQuanLyFileCaNhanProps>(({onClickChonFile}: IModalQuanLyFileCaNhanProps, ref) => {
  useImperativeHandle(ref, () => ({
    open: () => {
      setIsOpen(true);
      setRouteFolderMap([
        {
          ten: "Folder của tôi",
          id: "",
          index: 0,
        },
      ]);
      initData();
    },
    close: closeModal,
  }));
  const {profile} = useProfile.getState();
  const [isOpen, setIsOpen] = useState(false);

  /*khi modal preview image open, thao tác vào các toobar sẽ gọi sự kiện của onClickFileItem -> lưu lại khi previewImageOpen mở thì sẽ không gọi sự kiện onClickFileItem nữa */
  const [isPreViewImageOpen, setIsPreviewImageOpen] = useState(false); //biến này để xử lý khi previewImageOpen

  const refModalNhapTenFolder = useRef<IModalNhapTenFolderRef>(null);

  const [listFile, setListFile] = useState<Array<File.GetFolder.IGetFolder & IGetFolderExtend>>([]); //list file hiển thị

  const [fileSelectedData, setFileSelectedData] = useState<File.GetFolder.IGetFolder>(); //file được chọn để thao tác

  const [routeFolderMap, setRouteFolderMap] = useState<Array<File.GetFolder.IGetFolder & {index: number}>>([]); //maps route folder

  // XỬ LÝ SEARCH FILE
  const [textSearch, setTextSearch] = useState<string>("");
  const deboundSearch = useMemo(() => debounce(event => setTextSearch(event.target.value), 350), []);

  // XỬ LÝ VIỆC UPLOAD
  const [listFileUpload, setListFileUpload] = useState<UploadFile[]>([]); //list file đang upload lên server
  const [isVisibleModalProcessUpload, setIsVisibleModalProcessUpload] = useState<boolean>(false);

  const [loading, setLoading] = useState<boolean>(false);
  const [loaiUpload, setLoaiUpload] = useState<"FILE" | "FOLDER">("FILE"); //loại đang upload : folder hoặc file
  const [uploadFileKey, setUploadFileKey] = useState<number>(0); //sử dụng key reset component, Khi key thay đổi → component sẽ bị "recreate" → fileList cũng bị reset.
  const [uploadFolderKey, setUploadFolderKey] = useState<number>(0); //sử dụng key reset component, Khi key thay đổi → component sẽ bị "recreate" → fileList cũng bị reset.

  /* XỬ LÝ CTRL+A / CMD+A -> chọn tất cả các file */
  const listFileRef = useRef(listFile); //do listFile bên trong handleKeyDown sẽ không được update, nên phải chuyển sang dùng listFileRef
  useEffect(() => {
    listFileRef.current = listFile; //update lại listFileRef
  }, [listFile]);
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      const target = e.target as HTMLElement;
      const tagName = target.tagName.toLowerCase();
      const isEditable = target.isContentEditable;
      // Nếu đang focus vào input/textarea/element cho phép nhập → bỏ qua
      if (["input", "textarea", "select"].includes(tagName) || isEditable) return;
      const isMac = navigator.platform.toUpperCase().includes("MAC"); //check xem nếu là macbook -> CMD + A
      const isSelectAll = (isMac && e.metaKey && e.key.toLowerCase() === "a") || (!isMac && e.ctrlKey && e.key.toLowerCase() === "a"); //nếu là window -> ctrl+A
      if (isSelectAll) {
        e.preventDefault(); //ngăn k cho select text
        const listFileTmp = listFileRef.current.map(itemFile => (itemFile.loai === "FILE" ? {...itemFile, selected: true} : itemFile));
        setListFile(listFileTmp);
      }
    };
    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, []);
  /* END XỬ LÝ CTRL+A / CMD+A -> chọn tất cả các file */

  useEffect(() => {
    if (textSearch) timKiemFolder({ten: textSearch, id: routeFolderMap[routeFolderMap.length - 1].id});
    else timKiemFolder({id: routeFolderMap.length > 0 ? routeFolderMap[routeFolderMap.length - 1].id : ""});
  }, [textSearch]);

  const initData = () => {
    timKiemFolder();
  };

  const closeModal = useCallback(() => {
    setIsOpen(false);
    setTextSearch("");
    setUploadFileKey(0);
    setUploadFolderKey(0);
    setIsVisibleModalProcessUpload(false);
    setListFileUpload([]);
    setRouteFolderMap([]);
    setListFile([]);
  }, []);

  const onChangeUploadTaiFile = (data: any) => {
    const {file, fileList} = data;
    setListFileUpload([...fileList]);
  };

  //UPLOAD ẢNH LÊN SERVER
  const uploadImageToServer = async (data: any) => {
    try {
      console.log("uploadImageToServer", data);
      const {file, onProgress, onSuccess, onError} = data;
      setIsVisibleModalProcessUpload(true);
      const params: ReactQuery.IUploadFileParams = {
        ma_doi_tac_ql: profile.nsd?.ma_doi_tac || "",
        file: file,
        ten: file.name,
        thumbnail: 1,
        nhom: "CA_NHAN",
        actionCode: ACTION_CODE.UPLOAD_FILE,
        bt: 0,
        stt: 0,
        file_public: 0,
        id_folder: routeFolderMap[routeFolderMap.length - 1].id,
      };
      console.log("params", params);
      const response = await FileEndpoint.uploadFile(params, {
        onProgress: res => {
          onProgress(res); // Báo ngược lại cho Ant Design Upload
        },
        onSuccess: res => {
          onSuccess("success"); // Báo ngược lại cho Ant Design Upload
          if (res?.output?.bt) timKiemFolder({id: routeFolderMap[routeFolderMap.length - 1].id});
        },
        onError: err => {
          onError(err); // Báo lỗi ngược lại cho Upload
        },
      });
      console.log("response", response);
    } catch (error) {
      console.log("uploadImageToServer", error);
    }
  };

  //CẬP NHẬT - TẠO MỚI FOLDER
  const updateFolder = async (tenFolder: string, fileSelectedData?: File.GetFolder.IGetFolder) => {
    try {
      const params: ReactQuery.ITaoFolderParams = {
        id: fileSelectedData ? fileSelectedData.id : "", // nếu là update tên file -> thì truyền ID lên
        ten: tenFolder,
        id_cha: routeFolderMap[routeFolderMap.length - 1].id,
        actionCode: ACTION_CODE.TAO_FOLDER,
      };
      const response = await FileEndpoint.updateFolder(params);
      console.log("response", response);
      if (response.output?.id) {
        timKiemFolder({id: routeFolderMap[routeFolderMap.length - 1].id});
      }
    } catch (error) {
      console.log("updateFolder", error);
    }
  };

  //cập nhật tên file
  const updateFile = async (tenFile: string, fileDangUpdate?: File.GetFolder.IGetFolder) => {
    try {
      const params: ReactQuery.ICapNhatFileParams = {
        bt: fileDangUpdate ? fileDangUpdate.id : "", // nếu là update tên file -> thì truyền ID lên
        ten_alias: tenFile,
        actionCode: ACTION_CODE.CAP_NHAT_FILE,
      };
      const response = await FileEndpoint.capNhatFile(params);
      if (response.data === -1) timKiemFolder({id: routeFolderMap[routeFolderMap.length - 1].id});
    } catch (error) {
      console.log("updateFile", error);
    }
  };

  //TÌM KIẾM THEO TÊN + GET ALL FOLDER THEO ID
  const timKiemFolder = async (paramsTimKiem: ReactQuery.ITimKiemFolderParams = {id: "", ten: ""}) => {
    try {
      setLoading(true);
      const params: ReactQuery.ITimKiemFolderParams = {
        ...paramsTimKiem,
      };
      const response = await FileEndpoint.timKiemTrongFolder({...params, actionCode: ACTION_CODE.TIM_KIEM_FOLDER});
      setLoading(false);
      if (response.data) {
        setListFile(
          response.data.map(item => {
            return {...item, openPopover: false};
          }),
        );
      }
    } catch (error) {
      setLoading(false);
      console.log("timKiemFolder", error);
    }
  };

  const xoaFolder = async () => {
    try {
      setLoading(true);
      const params: ReactQuery.ITimKiemFolderParams = {
        id: fileSelectedData?.id,
      };
      const response = await FileEndpoint.xoaFolder({...params, actionCode: ACTION_CODE.XOA_FOLDER});
      console.log("response", response);
      setLoading(false);
      if (response.data === -1) timKiemFolder({id: routeFolderMap[routeFolderMap.length - 1].id});
    } catch (error) {
      setLoading(false);
      console.log("timKiemFolder", error);
    }
  };

  const xoaFile = async (arrFileIdXoa: {bt: number | string}[]) => {
    try {
      const params: ReactQuery.IXoaFileParams = {
        file: arrFileIdXoa,
        actionCode: ACTION_CODE.XOA_FILE,
      };
      const response = await FileEndpoint.xoaFile(params);
      if (response.data === -1) timKiemFolder({id: routeFolderMap[routeFolderMap.length - 1].id});
    } catch (error) {
      console.log("xoaFile", error);
    }
  };

  const onClickFileItem = (fileSelected: File.GetFolder.IGetFolder & IGetFolderExtend) => {
    if (isPreViewImageOpen) return;
    const listFileTmp = listFile;
    listFileTmp.map(itemFile => {
      if (itemFile.id === fileSelected.id && itemFile.loai === "FILE") itemFile.selected = !itemFile.selected;
    });
    setListFile([...listFileTmp]);
  };

  const togglePopover = (itemSelected: File.GetFolder.IGetFolder & IGetFolderExtend) => {
    const listFileTmp = listFile;
    listFileTmp.map(itemFile => {
      if (itemFile.id === itemSelected.id) itemFile.openPopover = !itemFile.openPopover;
      else itemFile.openPopover = false;
    });
    setListFile([...listFileTmp]);
  };

  const clearAllFileSelect = () => {
    const listFileTmp = listFile;
    listFileTmp.map(itemFile => {
      itemFile.selected = false;
    });
    setListFile([...listFileTmp]);
  };

  const deleteAllFileSelect = async () => {
    setLoading(true);
    const arrFileIdXoa: {bt: number | string}[] = [];
    for (let i = 0; i < listFile.length; i++) {
      if (listFile[i].selected) arrFileIdXoa.push({bt: listFile[i].id});
    }
    await xoaFile(arrFileIdXoa);
  };

  // XEM CHI TIẾT FOLDER
  const xemChiTietFolder = (folderData: File.GetFolder.IGetFolder) => {
    //push folder vào route
    const routeFolderMapTmp = routeFolderMap;
    routeFolderMapTmp.push({...folderData, index: routeFolderMapTmp.length});
    setRouteFolderMap([...routeFolderMapTmp]);
    timKiemFolder({id: folderData.id});
  };

  // CLICK VÀO ROUTE -> XEM CHI TIẾT FOLDER
  const onClickFolderRouteItem = (folderSelect: File.GetFolder.IGetFolder & {index: number}) => {
    timKiemFolder({id: folderSelect.id});
    let routeFolderMapTmp = routeFolderMap;
    routeFolderMapTmp = routeFolderMapTmp.slice(0, folderSelect.index + 1);
    setRouteFolderMap([...routeFolderMapTmp]);
  };
  // RENDER

  const getFileIcon = (data: File.GetFolder.IGetFolder) => {
    const extension = data.extension || "";
    if (data.loai === "FILE") {
      if (arrExtensionImage.includes(extension))
        return (
          <Image
            onClick={event => {
              event.stopPropagation(); //NGĂN click lan lên List.Item
            }}
            src={env.VITE_BASE_URL + data.url_file + "&thumbnail=1"}
            className="mr-2 !h-10 !w-10"
            preview={{
              src: env.VITE_BASE_URL + data.url_file, //cấu hình URL preview sẽ hiển thị url full của ảnh
              onVisibleChange: (value: boolean, prevValue: boolean) => {
                setIsPreviewImageOpen(value);
              },
            }}
          />
        );
      else if (arrExtensionPdf.includes(extension)) return <FilePdfOutlined style={{fontSize: 40, color: "#F40F02"}} />;
      else if (arrExtensionExcel.includes(extension)) return <FileExcelOutlined style={{fontSize: 40, color: "#1D6F42"}} />;
      else if (arrExtensionWord.includes(extension)) return <FileWordOutlined style={{fontSize: 40, color: "#2b579a"}} />;
      else if (arrExtensionGif.includes(extension)) return <FileGifOutlined style={{fontSize: 40, color: COLOR_PALETTE.gray[50]}} />;
      else if (arrExtensionXml.includes(extension)) return <VerticalRightOutlined style={{fontSize: 40, color: COLOR_PALETTE.blue[50]}} />;
      return <FileUnknownOutlined style={{fontSize: 40, color: COLOR_PALETTE.green[50]}} />;
    } else if (data.loai === "FOLDER") return <FolderOutlined style={{fontSize: 40, color: COLOR_PALETTE.gray[50]}} />;
  };

  const renderActionView = () => (
    <div className="flex flex-col">
      <Popover
        content={
          <div className="flex flex-col">
            <Button icon={<FolderAddOutlined />} onClick={() => refModalNhapTenFolder.current?.open()}>
              Tạo thư mục
            </Button>
            <Divider className="my-2" />
            <Upload
              key={uploadFileKey}
              // name={""} //
              accept=".jpg,.jpeg,.png,.gif,.pdf,.xml,.xls,.xlsx,.doc,.docx" //: Chỉ định loại file được phép upload (ví dụ: .jpg, .png, application/pdf,...)
              // action={env.VITE_BASE_URL + "/api/file/upload"} //: Đường dẫn URL để gửi request upload
              // beforeUpload : Hàm xử lý trước khi upload, có thể dùng để validate hoặc sửa file trước khi gửi
              customRequest={uploadImageToServer} //: Tuỳ chỉnh request upload (dùng khi không muốn dùng action)
              // data={file => {
              // }} //: Dữ liệu đi kèm khi upload
              // defaultFileList : Danh sách file mặc định
              // directory : hỗ trợ upload cả thư mục
              // disabled //: Vô hiệu hoá upload
              // fileList={listFileUpload} // : Danh sách file được điều khiển từ bên ngoài
              // headers={{
              // }} //: Header kèm theo request
              // iconRender : tuỳ chỉnh icon show
              // isImageUrl : custom nếu render <img/> trong thubnail
              // itemRender : custom item của upload List
              // listType : Kiểu hiển thị danh sách file upload (text, picture, picture-card)
              // maxCount : Giới hạn số file tối đa
              // method="POST" //: method http của request
              multiple //: Cho phép chọn nhiều file, CHỌN 10 FILE SẼ UPLOAD LUÔN 10 REQUEST, KHÔNG PHẢI UPLOAD TỪNG REQUEST MỘT
              // name : Tên field khi gửi lên server
              // openFileDialogOnClick : Có tự động mở file dialog khi click hay không
              // pastable : hỗ trợ paste file
              // previewFile : Tuỳ chỉnh cách tạo bản preview cho ảnh
              // progress : Tuỳ chỉnh hiển thị tiến trình
              showUploadList={false} //: Có hiển thị danh sách file hay không, hoặc cấu hình chi tiết
              // withCredentials : Gửi cookie cùng với request
              onChange={onChangeUploadTaiFile} //: Gọi khi danh sách file thay đổi (upload, remove, ...)
              onDrop={data => {
                console.log("onDrop", data);
              }}
              onDownload={data => {
                console.log("onDownload", data);
              }}
              // onPreview : Gọi khi click vào preview
              onPreview={data => {
                console.log("onPreview", data);
              }}
              // onRemove :	Gọi khi xóa file, có thể trả về boolean hoặc Promise để chặn
              onRemove={data => {
                console.log("onRemove", data);
              }}
              className="btn-upload"
              //Extends File with additional props.
            >
              <Button icon={<FileAddOutlined />} className="mb-1" onClick={() => setLoaiUpload("FILE")}>
                Tải file
              </Button>
            </Upload>

            <Upload
              key={uploadFolderKey}
              accept=".jpg,.jpeg,.png,.gif,.pdf,.xml,.xls,.xlsx,.doc,.docx" //: Chỉ định loại file được phép upload (ví dụ: .jpg, .png, application/pdf,...)
              customRequest={uploadImageToServer} //: Tuỳ chỉnh request upload (dùng khi không muốn dùng action)
              directory
              multiple //: Cho phép chọn nhiều file, CHỌN 10 FILE SẼ UPLOAD LUÔN 10 REQUEST, KHÔNG PHẢI UPLOAD TỪNG REQUEST MỘT
              showUploadList={false} //: Có hiển thị danh sách file hay không, hoặc cấu hình chi tiết
              onChange={onChangeUploadTaiFile} //: Gọi khi danh sách file thay đổi (upload, remove, ...)
              //xử lý để chỉ upload file ở thư mục cấp 1 được chọn, file ở thư mục cấp 2 sẽ không được upload
              beforeUpload={file => {
                console.log("beforeUpload file", file);
                const path = file.webkitRelativePath || "";
                const slashCount = path.split("/").length - 1;
                // Chỉ chấp nhận file có đúng 1 thư mục (Folder1/FileA.txt)
                if (slashCount === 1) return true;
                return Upload.LIST_IGNORE; // bỏ qua file trong folder con
              }}>
              <Button icon={<FolderAddOutlined />} className="justify-start" onClick={() => setLoaiUpload("FOLDER")}>
                Tải thư mục
              </Button>
            </Upload>
          </div>
        }>
        <Button icon={<UploadOutlined />} size="large" className="justify-start">
          Tải lên
        </Button>
      </Popover>
      <Button
        icon={<FolderOutlined />}
        className="mt-2 justify-start"
        size="large"
        variant="outlined"
        color="default"
        onClick={() => {
          timKiemFolder();
          setRouteFolderMap([
            {
              ten: "Folder của tôi",
              id: "",
              index: 0,
            },
          ]);
        }}>
        File của tôi
      </Button>
      <Button icon={<InboxOutlined />} className="mt-2 justify-start" size="large" variant="outlined" color="default">
        Trash
      </Button>
    </div>
  );
  const renderFooter = () => {
    if (!onClickChonFile) return;
    return (
      <Button type={"primary"} onClick={() => onClickChonFile && onClickChonFile(listFile.filter(item => item.selected))} className="w-40" icon={<CheckCircleOutlined />} disabled={false}>
        {"Chọn " + (listFile.filter(item => item.selected).length > 0 ? "(" + listFile.filter(item => item.selected).length + ")" : "")}
      </Button>
    );
  };

  const renderFileItemView = (item: File.GetFolder.IGetFolder & IGetFolderExtend) => {
    return (
      <List.Item
        onClick={event => {
          onClickFileItem(item);
        }}
        onDoubleClick={() => {
          // setFolderSelectedData(item);
          if (item.loai === "FOLDER") xemChiTietFolder(item);
        }}
        className={`my-1 ${item.selected ? "bg-[#d9e8bd]" : ""}`}>
        <Col span={1}>{getFileIcon(item)}</Col>
        <Col span={18}>{item.ten_alias || item.ten}</Col>
        <Col span={4}>{item.ngay_tao}</Col>
        <Col span={1}>
          {/* DO POPOVER HIỂN THỊ THEO THẮNG NÀO ACTIVE NÊN PHẢI XỬ LÝ THÊM TRONG onOpenChange ĐỂ KHI CLICK VÀO CHỖ KHÁC THÌ POPOVER SẼ ẨN ĐI */}
          <Popover
            trigger="click"
            open={item.openPopover}
            onOpenChange={visible => {
              const listFileTmp = listFile.map(itemFile => ({
                ...itemFile,
                openPopover: itemFile.id === item.id ? visible : false,
              }));
              setListFile(listFileTmp);
              setFileSelectedData(item);
            }}
            content={
              <div className="flex flex-col">
                <Button
                  icon={<EditOutlined />}
                  onClick={event => {
                    event.stopPropagation(); // NGĂN click lan lên List.Item
                    togglePopover(item);
                    refModalNhapTenFolder.current?.open(fileSelectedData);
                  }}
                  className="mb-1">
                  Sửa tên
                </Button>
                <Button
                  icon={<CloseOutlined />}
                  onClick={event => {
                    event.stopPropagation(); // NGĂN click lan lên List.Item
                    togglePopover(item);
                    if (item.loai === "FILE") xoaFile([{bt: fileSelectedData?.id || -1}]);
                    else if (item.loai === "FOLDER") xoaFolder();
                  }}
                  color="danger"
                  variant="solid">
                  {`Xoá ${item.loai === "FILE" ? "file" : "folder"}`}
                </Button>
              </div>
            }>
            <Button
              icon={<MoreOutlined style={{fontSize: 20}} />}
              color="default"
              variant="link"
              onClick={event => {
                event.stopPropagation(); // NGĂN click lan lên List.Item
                setFileSelectedData(item);
                togglePopover(item);
              }}
            />
          </Popover>
        </Col>
      </List.Item>
    );
  };

  const renderActionFileSelected = () => {
    const listFileSelected = listFile.filter(item => item.selected);
    if (listFileSelected.length > 0)
      return (
        <div className="mb-[10px] rounded-lg bg-gray-30 p-[10px]">
          <Button icon={<CloseOutlined />} onClick={clearAllFileSelect} shape="circle" size="small" className="mr-2" />
          <span>{listFileSelected.length} file được chọn</span>
          <Popconfirm title="Xoá file" description={`Bạn có chắc muốn xoá ${listFileSelected.length} file này`} onConfirm={deleteAllFileSelect} okText="Xoá" cancelText="Để sau">
            <Button icon={<DeleteOutlined />} shape="circle" size="small" className="ml-2" color="danger" variant="solid" />
          </Popconfirm>
        </div>
      );
  };

  const renderListFileView = () => (
    <div className="ml-3">
      <Breadcrumb
        className="mb-1"
        separator={<a className="!h-auto py-1 text-[18px]">{">"}</a>}
        items={routeFolderMap.map(item => {
          return {
            ...item,
            title: (
              <a type="link" onClick={() => onClickFolderRouteItem(item)} className="!h-auto py-1 text-[18px]">
                {item.ten}
              </a>
            ),
          };
        })}
      />
      <Input placeholder="Tìm kiếm trong folder" onChange={deboundSearch} className="mb-[10px]" />
      {renderActionFileSelected()}
      {/* <Dragger
        accept=".jpg,.jpeg,.png,.gif,.pdf,.xml,.xls,.xlsx,.doc,.docx" //: Chỉ định loại file được phép upload (ví dụ: .jpg, .png, application/pdf,...)
        customRequest={uploadImageToServer} //: Tuỳ chỉnh request upload (dùng khi không muốn dùng action)
        onChange={onChangeUploadTaiFile} //: Gọi khi danh sách file thay đổi (upload, remove, ...)
        onDrop={data => {
          console.log("onDrop", data);
        }}
        multiple> */}
      <List
        bordered
        dataSource={listFile}
        renderItem={renderFileItemView}
        className={"overflow-auto pl-3 " + (listFile.filter(item => item.selected).length > 0 ? "h-[57vh]" : "h-[64vh]")}
        locale={{emptyText: "Chưa có dữ liệu"}}
        size="small"
        loading={loading}
      />
      {/* </Dragger> */}
    </div>
  );

  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        className="modal-quan-ly-file-ca-nhan"
        title="Quản lý file"
        centered
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width={"95%"}
        maskClosable={false}
        styles={{
          body: {
            height: "75vh",
          },
        }}
        // footer={false}
        footer={renderFooter}>
        <Row>
          <Col span={3}>{renderActionView()}</Col>
          <Col span={21}>{renderListFileView()}</Col>
        </Row>
        <ModalNhapTenFolder
          ref={refModalNhapTenFolder}
          luuTenThuMuc={(tenFile, fileSelectedData) => {
            if (fileSelectedData?.loai === "FOLDER" || !fileSelectedData) updateFolder(tenFile, fileSelectedData);
            else if (fileSelectedData.loai === "FILE") updateFile(tenFile, fileSelectedData);
          }}
        />
        <ModalProcessUpload
          listFileUpload={listFileUpload}
          visible={isVisibleModalProcessUpload}
          onClose={() => {
            setIsVisibleModalProcessUpload(false);
            if (loaiUpload === "FILE") setUploadFileKey(uploadFileKey + 1);
            if (loaiUpload === "FOLDER") setUploadFolderKey(uploadFolderKey + 1);
          }}
        />
      </Modal>
    </Flex>
  );
});

ModalQuanLyFileCaNhanComponent.displayName = "ModalQuanLyFileCaNhanComponent";
export default memo(ModalQuanLyFileCaNhanComponent, isEqual);
export * from "./Constant";

import {create} from "zustand";

export interface IListDoiTacStore {
  listDoiTac: Array<CommonExecute.Execute.IDoiTac>;
  setListDoiTac: (listDoiTac: Array<CommonExecute.Execute.IDoiTac>) => void;
}

export const useDoiTac = create<IListDoiTacStore>(set => ({
  listDoiTac: [],
  setListDoiTac: listDoiTac => set({listDoiTac}),
}));

//create : hàm tạo store
// export const useDoiTac = create(
//   //persist : middileware để lưu trạng thái vào localStorage
//   persist<IListDoiTacStore, [], [], Pick<IListDoiTacStore, "listDoiTac">>(
//     //set, get phải đúng thứ tự này, nếu không sẽ báo lỗi vì sử dụng nhầm cách get/set
//     (set, get) => ({
//       //khởi tạo state menuNguoiDung từ cookie + localStorage
//       listDoiTac: get()?.listDoiTac || [],
//       setListDoiTac: (listDoiTac: Array<CommonExecute.Execute.IDoiTac>) => set(state => ({listDoiTac: [...listDoiTac]})),
//     }),
//     //cấu hình persist
//     {
//       name: LOCAL_STORAGE_KEY.DOI_TAC, //key để lưu trong localStorate
//       storage: createJSONStorage(() => localStorage), //nơi lưu trữ là localStorage
//       //chỉ định những phần nào trong state sẽ được lưu, tránh lưu những thứ k cần thiết như các hàm set
//       partialize: state => ({listDoiTac: state.listDoiTac}),
//     },
//   ),
// );

import strings from "@src/assets/strings";
import {Avatar, Button, FormInput, Skeleton, Switch, Typography} from "@src/components";
import {DEFAULT_GUTTER} from "@src/constants";
import {useProfile} from "@src/hooks";
import {useNotiContext} from "@src/providers/NotiProvider";
import {Col, Flex, Row} from "antd";
import {isEqual} from "lodash";
import React, {memo, PropsWithChildren} from "react";
import {useTrangChuContext} from "./index.context";

const CellWrapper: React.FC<PropsWithChildren<{title: string}>> = ({children, title}) => (
  <Col span={6}>
    <div className="border-purple-20 dark:border-pink-30 border-solid border-2 min-h-[160px] p-4 rounded-md">
      <Typography type="paragraph" className="font-bold">
        {title}
      </Typography>
      <Flex className="justify-center items-center">{children}</Flex>
    </div>
  </Col>
);

const TrangChuContent: React.FC = memo(() => {
  const {profile} = useProfile();
  const {loading, setLoading} = useTrangChuContext();

  const {noti} = useNotiContext();

  return (
    <Row gutter={DEFAULT_GUTTER.GRID}>
      <CellWrapper title={strings().label_switch}>
        <Switch value={loading} onChange={checked => setLoading(checked)} />
      </CellWrapper>
      <CellWrapper title={strings().label_button}>
        <Skeleton.Button loading={loading}>
          <Button
            onClick={async () => {
              noti.info({
                message: strings().label_button,
              });
            }}>
            Test
          </Button>
        </Skeleton.Button>
      </CellWrapper>
      <CellWrapper title={strings().label_avatar}>
        <Skeleton.Avatar loading={loading} size={"large"}>
          <Avatar size={"large"}>{profile.nsd.ten?.charAt(0).toUpperCase()}</Avatar>
        </Skeleton.Avatar>
      </CellWrapper>
      <CellWrapper title={strings().label_input}>
        <Skeleton.Input loading={loading}>
          <FormInput placeholder={strings().placeholder_input} />
        </Skeleton.Input>
      </CellWrapper>
      <CellWrapper title={strings().label_image}>
        <Skeleton.Image loading={loading}>{/* <Image src={imgSrc} /> */}</Skeleton.Image>
      </CellWrapper>
      <CellWrapper title={strings().label_paragraph}>
        <Skeleton.Paragraph
          loading={loading}
          paragraph={{
            rows: 2,
          }}>
          <Typography type="paragraph">
            Lorem ipsum dolor, sit amet consectetur adipisicing elit. Molestias, accusamus excepturi ipsum non commodi fugiat at nulla aut incidunt, sequi et itaque magnam dolorum labore eos. Cumque
            adipisci rerum cum.
          </Typography>
        </Skeleton.Paragraph>
      </CellWrapper>
    </Row>
  );
}, isEqual);

TrangChuContent.displayName = "TrangChuContent";

export default TrangChuContent;

import {FormInput} from "@src/components";
import {Col, Form, message, Row} from "antd";
import {Dayjs} from "dayjs";
import {isEqual} from "lodash";
import {forwardRef, memo, useImperativeHandle, useMemo, useRef} from "react";
import {FormTaoMoiHopDongBaoHiemXeCoGioi, listHDVipSelect, listKieuHopDongSelect, ruleRequired, ThongTinHopDongXeStepProps} from "../Constant";
import ModalCanBoQuanLy, {IModalCanBoQuanLyRef} from "../ModalCanBoQuanLy";
import ModalDaiLyKhaiThac, {IModalDaiLyKhaiThacRef} from "../ModalDaiLyKhaiThac";
import ModalThongTinKhachHang, {IModalThongTinKhachHangRef} from "../ModalThongTinKhachHang";
import {NHOM_XCG, optionNghiepVuSelect} from "../../index.configs";
import {useBaoHiemXeCoGioiContext} from "../../index.context";

const {ma_doi_tac_ql, ma_chi_nhanh_ql, phong_ql, ma_kh, kieu_hd, so_hd_g, ma_cb_ql, ngay_cap, gio_hl, ngay_hl, gio_kt, ngay_kt, ma_sp, nv, ma_ctbh, pt_kt, daily_kt, ma_nha_tpa, vip, so_hd} =
  FormTaoMoiHopDongBaoHiemXeCoGioi;
const ThongTinHopDongXeStepComponent = forwardRef<any, ThongTinHopDongXeStepProps>(
  ({formNhapHopDongXe, khachHangSelected, setKhachHangSelected, daiLyKhaiThacSelected, setDaiLyKhaiThacSelected, canBoSeleceted, setCanBoSelected}: ThongTinHopDongXeStepProps, ref) => {
    useImperativeHandle(ref, () => ({
      resetForm: () => {},
    }));

    const {listDoiTac, listChiNhanh, listPhongBan, listPhuongThucKhaiThac, listSanPham, listDonViBoiThuong, listChuongTrinhBaoHiem, chiTietHopDongBaoHiemXe} = useBaoHiemXeCoGioiContext();
    let refModalThongTinKhachHang = useRef<IModalThongTinKhachHangRef>(null);
    let refModalDaiLyKhaiThac = useRef<IModalDaiLyKhaiThacRef>(null);
    let refModalCanBoQuanLy = useRef<IModalCanBoQuanLyRef>(null);

    //watch
    const watchDoiTacCapDon = Form.useWatch("ma_doi_tac_ql", formNhapHopDongXe);
    const watchChiNhanhCapDon = Form.useWatch("ma_chi_nhanh_ql", formNhapHopDongXe);
    const watchKieuHopDong = Form.useWatch("kieu_hd", formNhapHopDongXe);
    const formNhapHopDongXeValues = Form.useWatch([], formNhapHopDongXe);

    // Filter theo nghiệp vụ và đối tác được chọn
    const listSanPhamFiltered = useMemo(() => {
      const selectedNghiepVu = formNhapHopDongXeValues?.nv;
      const selectedDoiTac = formNhapHopDongXeValues?.ma_doi_tac_ql;
      if (listSanPham.length > 0 && selectedDoiTac && selectedNghiepVu) {
        return listSanPham.filter(item => item.ma_doi_tac_ql === selectedDoiTac && item.nv === selectedNghiepVu);
      }
      return [];
    }, [formNhapHopDongXeValues?.nv, formNhapHopDongXeValues?.ma_doi_tac_ql, listSanPham]);
    // Filter theo nghiệp vụ và đối tác được chọn
    const listChuongTrinhBaoHiemFiltered = useMemo(() => {
      const selectedDoiTac = formNhapHopDongXeValues?.ma_doi_tac_ql;

      if (listChuongTrinhBaoHiem.length > 0 && selectedDoiTac) {
        return listChuongTrinhBaoHiem.filter(item => item.ma_doi_tac_ql === selectedDoiTac && item.nv === chiTietHopDongBaoHiemXe.nv);
      }
      return [];
    }, [formNhapHopDongXeValues?.ma_doi_tac_ql, listChuongTrinhBaoHiem]);
    // Filter theo đối tác được chọn
    const listChiNhanhFiltered = useMemo(() => {
      const selectedDoiTac = formNhapHopDongXeValues?.ma_doi_tac_ql;
      if (listChiNhanh.length > 0 && selectedDoiTac) {
        return listChiNhanh.filter(item => item.ma_doi_tac === selectedDoiTac);
      }
      return [];
    }, [formNhapHopDongXeValues?.ma_doi_tac_ql, listChiNhanh]);
    // Filter theo đối tác được chọn
    const listPhuongThucKhaiThacFiltered = useMemo(() => {
      const selectedDoiTac = formNhapHopDongXeValues?.ma_doi_tac_ql;

      if (listPhuongThucKhaiThac.length > 0 && selectedDoiTac) {
        return listPhuongThucKhaiThac.filter(item => item.ma_doi_tac_ql === selectedDoiTac);
      }
      return [];
    }, [formNhapHopDongXeValues?.ma_doi_tac_ql, listPhuongThucKhaiThac]);

    const listDonViBoiThuongTPAFiltered = useMemo(() => {
      const selectedDoiTac = formNhapHopDongXeValues?.ma_doi_tac_ql;

      if (listDonViBoiThuong.length > 0 && selectedDoiTac) {
        return listDonViBoiThuong.filter(item => item.ma_doi_tac_ql === selectedDoiTac && item.nv === chiTietHopDongBaoHiemXe.nv);
      }
      return [];
    }, [formNhapHopDongXeValues?.ma_doi_tac_ql, listDonViBoiThuong]);

    const listPhongBanFiltered = useMemo(() => {
      const selectedDoiTac = formNhapHopDongXeValues?.ma_doi_tac_ql;
      const selectedChiNhanh = formNhapHopDongXeValues?.ma_chi_nhanh_ql;

      if (listPhongBan.length > 0 && selectedDoiTac && selectedChiNhanh) {
        return listPhongBan.filter(item => item.ma_doi_tac === selectedDoiTac && item.ma_chi_nhanh === selectedChiNhanh);
      }
      return [];
    }, [formNhapHopDongXeValues?.ma_doi_tac_ql, formNhapHopDongXeValues?.ma_chi_nhanh_ql, listPhongBan]);

    //BẤM ĐỂ MỞ MODAL KHÁCH HÀNG
    const onClickInputKhachHang = () => {
      if (!watchChiNhanhCapDon || watchChiNhanhCapDon === "") {
        message.error("Vui lòng chọn chi nhánh cấp đơn!");
        return;
      }
      refModalThongTinKhachHang.current?.open(undefined, watchChiNhanhCapDon, watchDoiTacCapDon);
    };

    //BẤM ĐỂ MỞ MODAL CÁN BỘ QUẢN LÝ
    const onClickInputCanBoQuanLy = () => {
      if (!watchDoiTacCapDon || watchDoiTacCapDon === "") {
        message.error("Vui lòng chọn chi nhánh cấp đơn!");
        return;
      }
      refModalCanBoQuanLy.current?.open(watchChiNhanhCapDon, watchDoiTacCapDon);
    };

    //BẤM ĐỂ MỞ MODAL ĐẠI LÝ KHAI THẮC
    const onClickInputDaiLyKhaiThac = () => {
      if (!watchDoiTacCapDon || watchDoiTacCapDon === "") {
        message.error("Vui lòng chọn đối tác cấp đơn!");
        return;
      }
      refModalDaiLyKhaiThac.current?.open(undefined, watchDoiTacCapDon);
    };

    //Change Select Đối tác
    const onChangeMaDoiTac = (maDoiTac: string) => {
      formNhapHopDongXe.setFieldValue("ma_chi_nhanh_ql", undefined);
      formNhapHopDongXe.setFieldValue("phong_ql", undefined);
      formNhapHopDongXe.setFieldValue("pt_kt", undefined);
    };

    //Change Select Chi nhánh
    const onChangeMaChiNhanh = (maChiNhanh: string) => {
      formNhapHopDongXe.setFieldValue("phong_ql", undefined);
    };

    // RENDER
    const renderFormInputColum = (props?: any, span = 6) => {
      return (
        <Col span={span}>
          <FormInput {...props} />
        </Col>
      );
    };

    const renderForm = () => (
      <Form form={formNhapHopDongXe} layout="vertical" className="mt-5">
        {/* gutter : khoảng cách giữa các ô */}
        <Row gutter={16}>
          {renderFormInputColum({
            ...ma_doi_tac_ql,
            options: listDoiTac,
            fieldNames: {label: "ten_tat", value: "ma"},
            onChange: (value: string) => {
              onChangeMaDoiTac(value);
            },
          })}
          {renderFormInputColum({
            ...ma_chi_nhanh_ql,
            options: listChiNhanhFiltered,
            fieldNames: {label: "ten_tat", value: "ma"},
            onChange: (value: string) => {
              onChangeMaChiNhanh(value);
            },
          })}
          {renderFormInputColum({...phong_ql, options: listPhongBanFiltered})}
          {renderFormInputColum({...ma_kh, options: [{ten: khachHangSelected?.ten, ma: khachHangSelected?.ma}], onClick: () => onClickInputKhachHang()})}
        </Row>

        <Row gutter={16}>
          {renderFormInputColum({
            ...so_hd,
          })}
          {renderFormInputColum({
            ...kieu_hd,
            options: listKieuHopDongSelect,
            onChange: (value: string) => {
              formNhapHopDongXe.setFieldValue("so_hd_g", undefined);
            },
          })}
          {renderFormInputColum({...so_hd_g, disabled: watchKieuHopDong === "G" ? true : false, rules: watchKieuHopDong === "G" ? [] : [ruleRequired]}, 3)}
          {renderFormInputColum({...vip, options: listHDVipSelect}, 3)}
          {renderFormInputColum({...ma_cb_ql, options: [{ten: canBoSeleceted?.ten, ma: canBoSeleceted?.ma || ""}], onClick: () => onClickInputCanBoQuanLy()})}

          {renderFormInputColum(
            {
              ...ngay_cap,
              onChange: (date: Dayjs) => {
                //ngày hiệu lực = ngày cấp đơn, ngày kết thúc = ngày hiệu lực + 1 (năm)
                formNhapHopDongXe.setFields([
                  {name: "ngay_hl", value: date},
                  {name: "ngay_kt", value: date.add(1, "y")},
                ]);
              },
            },
            3,
          )}
          {renderFormInputColum({...gio_hl}, 3)}
          {renderFormInputColum(
            {
              ...ngay_hl,
              onChange: (date: Dayjs) => {
                //ngày kết thúc = ngày hiệu lực + 1(năm)
                formNhapHopDongXe.setFields([{name: "ngay_kt", value: date.add(1, "y")}]);
              },
            },
            3,
          )}
          {renderFormInputColum({...gio_kt}, 3)}
          {renderFormInputColum({...ngay_kt}, 3)}
          {renderFormInputColum(
            {
              ...nv,
              options: optionNghiepVuSelect,
              fieldNames: {ten: "label", ma: "value"},
              onChange: () => {
                formNhapHopDongXe.setFields([{name: "ma_sp", value: undefined}]);
              },
            },
            3,
          )}
          {renderFormInputColum({...ma_sp, options: listSanPhamFiltered})}
          {renderFormInputColum({...ma_ctbh, options: listChuongTrinhBaoHiemFiltered})}
          {renderFormInputColum({...pt_kt, options: listPhuongThucKhaiThacFiltered})}
          {renderFormInputColum({
            ...daily_kt,
            options: [{ten: daiLyKhaiThacSelected?.ten, ma: daiLyKhaiThacSelected?.ma}],
            onClick: () => onClickInputDaiLyKhaiThac(),
          })}
          {renderFormInputColum({...ma_nha_tpa, options: listDonViBoiThuongTPAFiltered})}
        </Row>
      </Form>
    );

    return (
      <>
        {/* Form nhập thông tin hợp đồng xe */}
        {renderForm()}
        {/* Modal */}
        <>
          <ModalThongTinKhachHang
            ref={refModalThongTinKhachHang}
            onSelectKhachHang={dataKhachang => {
              formNhapHopDongXe.setFieldValue("ma_kh", dataKhachang?.ma);
              setKhachHangSelected(dataKhachang || {ten: "", ma: ""});
            }}
          />
          <ModalDaiLyKhaiThac
            ref={refModalDaiLyKhaiThac}
            onSelectDaiLy={data => {
              formNhapHopDongXe.setFieldValue("daily_kt", data?.ma);
              setDaiLyKhaiThacSelected(data || {ten: "", ma: ""});
            }}
          />
          <ModalCanBoQuanLy
            ref={refModalCanBoQuanLy}
            onSelectCanBo={data => {
              formNhapHopDongXe.setFieldValue("ma_cb_ql", data?.ma);
              setCanBoSelected(data || {ten: "", ma: ""});
            }}
          />
        </>
      </>
    );
  },
);

ThongTinHopDongXeStepComponent.displayName = "ThongTinHopDongStepComponent";
export const ThongTinHopDongXeStep = memo(ThongTinHopDongXeStepComponent, isEqual);

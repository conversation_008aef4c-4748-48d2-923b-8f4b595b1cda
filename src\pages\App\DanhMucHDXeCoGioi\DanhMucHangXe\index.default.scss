#DANH_MUC_HANG_XE {
    .ant-table-row:hover td {
        background-color: #e8f5e9 !important; //test
    }

    .header-cell-custom {
        background-color: #96bf49 !important;
        // background: linear-gradient(to right, #96bf49, #009a55) !important;
        color: #fff !important;
        font-weight: bold !important;
        text-align: center !important;
        border-radius: 0 !important;
    }

    /* 🔒 Ẩn cả thanh cuộn dọc và ngang */
    .antd-table-hide-scroll .ant-table-body {
        scrollbar-width: none;
        /* Firefox */
        -ms-overflow-style: none;
        /* IE 10+ */
    }

    .antd-table-hide-scroll .ant-table-body::-webkit-scrollbar {
        display: none;
        /* Chrome, Safari */
    }
}

.custom-step-container {
    display: flex;
    align-items: center;

    .step {
        display: flex;
        align-items: center;
        padding: 6px 12px;
        background: #f5f5f5;
        color: #000;
        border-radius: 4px;
        position: relative;
        font-size: 14px;
        margin-right: 8px;

        svg {
            margin-right: 6px;
        }

        &.active {
            background: #8bc34a;
            color: #fff;
        }

        &.active .arrow-right {
            border-left: 10px solid #8bc34a;
        }

        .arrow-right {
            position: absolute;
            right: -10px;
            top: 50%;
            transform: translateY(-50%);
            width: 0;
            height: 0;
            border-top: 14px solid transparent;
            border-bottom: 14px solid transparent;
            border-left: 10px solid #f5f5f5;
        }
    }
}
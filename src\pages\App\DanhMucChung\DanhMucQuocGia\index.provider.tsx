/**
 * Tác dụng: <PERSON><PERSON>ản lý state và logic nghiệp vụ cho module danh mục quốc gia
 */
import React, {memo, useCallback, useEffect, useState, useRef} from "react";
import {message} from "antd";
import {isEqual} from "lodash";

import {ReactQuery} from "@src/@types";
import {ACTION_CODE} from "@src/constants";
import {useCommonExecute} from "@src/services/react-queries";
import {defaultPaginationTableProps} from "@src/hooks";

import {DanhMucQuocGiaContext} from "./index.context";
import {IDanhMucQuocGiaProvider} from "./index.model";

interface IDanhMucQuocGiaProviderProps {
  children: React.ReactNode;
}

const DanhMucQuocGiaProviderComponent: React.FC<IDanhMucQuocGiaProviderProps> = memo(({children}) => {
  //Danh sách quốc gia hiển thị trong bảng chính
  const [listQuocGia, setListQuocGia] = useState<CommonExecute.Execute.IDanhMucQuocGia[]>([]);
  //Danh sách châu lục cho dropdown filter và form
  const [listChauLuc, setListChauLuc] = useState<CommonExecute.Execute.IDanhMucChauLuc[]>([]);
  //Danh sách khu vực cho dropdown filter và form (phụ thuộc vào châu lục được chọn)
  const [listKhuVuc, setListKhuVuc] = useState<CommonExecute.Execute.IDanhMucKhuVuc[]>([]);
  //Tổng số bản ghi để hiển thị phân trang
  const [tongSoDong, setTongSoDong] = useState<number>(0);
  //Tham số filter và phân trang hiện tại
  const [filterParams, setFilterParams] = useState<ReactQuery.ITimKiemPhanTrangDanhMucQuocGiaParams & ReactQuery.IPhanTrang>({
    ma_chau_luc: "",
    ma_khu_vuc: "",                 
    ma: "",
    ten: "",
    trang_thai: "",
    trang: 1,
    so_dong: defaultPaginationTableProps.defaultPageSize,
  });

  //===== COMMON EXECUTE HOOK =====
  const {mutate: commonExecute, mutateAsync: mutateAsync, isLoading: loading} = useCommonExecute();

  //Tác dụng: Load tất cả châu lục để hiển thị trong dropdown filter và form modal
  const getListChauLuc = useCallback(async () => {
    try {
      const response = await mutateAsync({
        ma: "",
        ten: "",
        trang_thai: "",
        trang: 1,
        so_dong: 100,
        actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_DANH_MUC_CHAU_LUC
      }as any);
      
      const responseData = (response.data as any);
      setListChauLuc(responseData.data || []);
    } catch (error) {
      setListChauLuc([]);
    }
  }, [mutateAsync]);

  //Tác dụng: Load khu vực theo châu lục được chọn để hiển thị trong dropdown
  const getListKhuVuc = useCallback(async (params?: {ma_chau_luc?: string}) => {
    try {
      const response = await mutateAsync({
        ma: "",
        ten: "",
        trang_thai: "",
        ma_chau_luc: params?.ma_chau_luc || "",
        trang: 1,
        so_dong: 1000,
        actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_DANH_MUC_KHU_VUC
      }as any);
      
      const responseData = (response.data as any);
      setListKhuVuc(responseData.data || []);
    } catch (error) {
      setListKhuVuc([]);
    }
  }, [mutateAsync]);

  //Tác dụng: Load danh sách quốc gia theo điều kiện filter và phân trang để hiển thị trong bảng
  const getListQuocGia = useCallback(
    async (params?: ReactQuery.ITimKiemPhanTrangDanhMucQuocGiaParams & ReactQuery.IPhanTrang) => {
      try {
        const searchParams = params || filterParams;
        
        const response = await mutateAsync({
          ...(searchParams as any),
          actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_DANH_MUC_QUOC_GIA,
        });

        //Response structure: data.data[] + data.tong_so_dong (giống DanhMucKhuVuc)
        const responseData = response.data;
        if (responseData?.data && Array.isArray(responseData.data)) {
          setListQuocGia(responseData.data);
          setTongSoDong(responseData.tong_so_dong || responseData.data.length);
        }
      } catch (error) {
        message.error("Có lỗi xảy ra khi tải danh sách quốc gia!");
      }
    },
    [mutateAsync, filterParams]
  );

  //Tác dụng: Lấy thông tin chi tiết của 1 quốc gia khi user click vào row để hiển thị trong modal edit
  const getChiTietQuocGia = useCallback(
    async (data: {ma: string;}) => { 
      try {
        const response = await mutateAsync({
          ma: data.ma,
          actionCode: ACTION_CODE.CHI_TIET_DANH_MUC_QUOC_GIA,
        } as any);
        
        //Response structure: data.lke[0] (confirmed from other modules)
        const responseData = response.data as any;
        if (responseData?.lke && Array.isArray(responseData.lke) && responseData.lke.length > 0) {
          return responseData.lke[0] as CommonExecute.Execute.IDanhMucQuocGia;
        }
        
        return {} as CommonExecute.Execute.IDanhMucQuocGia;
      } catch (error) {
        message.error("Không thể tải chi tiết quốc gia");
        return {} as CommonExecute.Execute.IDanhMucQuocGia;
      }
    },
    [mutateAsync],
  );

  //Tác dụng: Lưu thông tin quốc gia mới hoặc cập nhật thông tin quốc gia đã có
  const capNhatChiTietQuocGia = useCallback(
    async (params: ReactQuery.ICapNhatDanhMucQuocGiaParams, isUpdate: boolean = false): Promise<any> => {
      try {
        const response = await mutateAsync({
          ...params,
          actionCode: ACTION_CODE.UPDATE_DANH_MUC_QUOC_GIA,
        } as any);
        
        //API trả về -1 là thành công
        if (response?.data === -1) {
          const successMessage = isUpdate ? "Cập nhật quốc gia thành công!" : "Thêm mới quốc gia thành công!";
          message.success(successMessage);
          return response.data;
        } else {
          const errorMessage = isUpdate ? "Có lỗi xảy ra khi cập nhật quốc gia!" : "Có lỗi xảy ra khi thêm mới quốc gia!";
          message.error(errorMessage);
          throw new Error(`API returned failure. Response data: ${response?.data}`);
        }
      } catch (error) {
        const errorMessage = isUpdate ? "Có lỗi xảy ra khi cập nhật quốc gia!" : "Có lỗi xảy ra khi thêm mới quốc gia!";
        message.error(errorMessage);
        throw error;
      }
    },
    [mutateAsync]
  );

  //===== EFFECTS =====
  //Load danh sách châu lục khi component mount
  useEffect(() => {
    getListChauLuc();
    //Load tất cả khu vực ban đầu (không filter theo tỉnh)
    getListKhuVuc();
  }, [getListChauLuc, getListKhuVuc]);

  //Load dữ liệu quốc gia khi filterParams thay đổi
  useEffect(() => {
    getListQuocGia();
  }, [filterParams, getListQuocGia]);

  //===== CONTEXT VALUE =====
  const contextValue: IDanhMucQuocGiaProvider = {
    listQuocGia,
    listChauLuc,
    listKhuVuc,
    tongSoDong,
    loading,
    filterParams,
    getListQuocGia,
    getListChauLuc,
    getListKhuVuc,
    getChiTietQuocGia,
    capNhatChiTietQuocGia,
    setFilterParams,
  };

  return <DanhMucQuocGiaContext.Provider value={contextValue}>{children}</DanhMucQuocGiaContext.Provider>;
});

DanhMucQuocGiaProviderComponent.displayName = "DanhMucQuocGiaProvider";
export default DanhMucQuocGiaProviderComponent;

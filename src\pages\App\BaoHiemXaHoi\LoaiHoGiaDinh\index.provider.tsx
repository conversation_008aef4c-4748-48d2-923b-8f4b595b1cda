import React, {PropsWithChildren, useCallback, useEffect, useMemo, useState} from "react";

import {QuanLyLoaiHoGiaDinhContext} from "./index.context";
import {IQuanLyLoaiHoGiaDinhContextProps} from "./index.model";
import {ACTION_CODE} from "@src/constants";
import {ReactQuery} from "@src/@types";
import {TableLoaiHoGiaDinhColumnDataType} from "./index.configs";
import {useCommonExecute} from "@src/services/react-queries";
import {message} from "antd";

const QuanLyLoaiHoGiaDinhProvider: React.FC<PropsWithChildren> = props => {
  const {children} = props;
  const mutateUseCommonExecute = useCommonExecute();

  const [listLoaiHoGiaDinh, setListLoaiHoGiaDinh] = useState<Array<CommonExecute.Execute.ILoaiHoGiaDinh>>([]);
  const [tongSoDong, setTongSoDong] = useState<number>(0);
  const [filterParams, setFilterParams] = useState<ReactQuery.ITimKiemPhanTrangLoaiHoGiaDinhParams & ReactQuery.IPhanTrang>({ma: "", ten: "", trang_thai: "", trang: 1, so_dong: 20});
  
  //khởi tạo dữ liệu ban đàu
  useEffect(() => {//nè
    initData();
  }, []);

  const getChiTietLoaiHoGiaDinh = useCallback(
    async (data: TableLoaiHoGiaDinhColumnDataType) => {
      try {
        console.log("[Provider] getChiTietLoaiHoGiaDinh được gọi với data:", data);
        
        if (!data.ma) {
          console.log("[Provider] Không có mã để tìm kiếm, data.ma =", data.ma);
          return {} as CommonExecute.Execute.ILoaiHoGiaDinh;
        }
      
        console.log(" [Provider] Gọi API với params:", {
          ma: data.ma,
          actionCode: ACTION_CODE.CHI_TIET_LOAI_HO_GIA_DINH_BHXH
        });
        
        const response = await mutateUseCommonExecute.mutateAsync({
          ma: data.ma,
          actionCode: ACTION_CODE.CHI_TIET_LOAI_HO_GIA_DINH_BHXH,
        } as any);
        
        console.log("[Provider] Raw response từ API:", response);
        console.log("[Provider] Response.data:", response.data);
        
        // Xử lý structure response.data.lke
        const responseData = response.data as any; // Type casting để truy cập lke
        if (responseData && responseData.lke && Array.isArray(responseData.lke) && responseData.lke.length > 0) {
          const result = responseData.lke[0] as CommonExecute.Execute.ILoaiHoGiaDinh;
          console.log("[Provider] Trả về data từ lke[0]:", result);
          return result;
        } else {
          console.log("[Provider] Không tìm thấy data trong response.data.lke");
          return {} as CommonExecute.Execute.ILoaiHoGiaDinh;
        }
      } catch (error) {
        console.log("[Provider] getChiTietLoaiHoGiaDinh error:", error);
        return {} as CommonExecute.Execute.ILoaiHoGiaDinh;
      }
    },
    [mutateUseCommonExecute],
  );

  const getListLoaiHoGiaDinh = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        ...filterParams,
        actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_LOAI_HO_GIA_DINH_BHXH,
      } as any);
      if (response.data) {
        setListLoaiHoGiaDinh(response.data.data);
        setTongSoDong(response.data.tong_so_dong);
      }
    } catch (error) {
      console.log("getListLoaiHoGiaDinh error ", error);
    }
  }, [mutateUseCommonExecute]);

  useEffect(() => {
    getListLoaiHoGiaDinh();
  }, [filterParams]);

  const capNhatChiTietLoaiHoGiaDinh = useCallback(
    async (data: ReactQuery.ICapNhatLoaiHoGiaDinhParams, isEditMode: boolean = false) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...data,
          actionCode: ACTION_CODE.UPDATE_LOAI_HO_GIA_DINH_BHXH,
        } as any);
        message.success(isEditMode ? "Cập nhật loại hộ gia đình  thành công" : "Thêm mới loại hộ gia đình  thành công");
        return response.data;
      } catch (error) {
        message.error(isEditMode ? "Có lỗi xảy ra khi cập nhật loại hộ gia đình " : "Có lỗi xảy ra khi thêm mới loại hộ gia đình ");
        console.log("capNhatChiTietLoaiHoGiaDinh err", error);
        // return {} as CommonExecute.Execute.ILoaiHoGiaDinh;
      }
    },
    [mutateUseCommonExecute],
  );

  //initData chỉ dùng trong useEffect 1 lần, không truyền đi đâu cả, nên không cần memo hoá
  const initData = () => {};

  const value = useMemo<IQuanLyLoaiHoGiaDinhContextProps>(
    () => ({
      listLoaiHoGiaDinh,
      tongSoDong,
      loading: mutateUseCommonExecute.isLoading,
      filterParams,
      getListLoaiHoGiaDinh,
      getChiTietLoaiHoGiaDinh,
      capNhatChiTietLoaiHoGiaDinh,
      setFilterParams,
    }),
    [listLoaiHoGiaDinh, tongSoDong, mutateUseCommonExecute, filterParams, getListLoaiHoGiaDinh, getChiTietLoaiHoGiaDinh, capNhatChiTietLoaiHoGiaDinh],
  );

  return <QuanLyLoaiHoGiaDinhContext.Provider value={value}>{children}</QuanLyLoaiHoGiaDinhContext.Provider>;
};

export default QuanLyLoaiHoGiaDinhProvider;

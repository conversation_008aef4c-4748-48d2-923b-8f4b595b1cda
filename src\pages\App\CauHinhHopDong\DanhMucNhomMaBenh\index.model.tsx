import {ReactQuery} from "@src/@types";
import {TableDanhMucNhomMaBenhColumnDataType} from "./index.configs";

export interface IQuanLyDanhMucNhomMaBenhContextProps {
  listDanhMucNhomMaBenh: Array<CommonExecute.Execute.IDanhMucNhomMaBenh>;
  tongSoDong: number;
  loading: boolean;
  filterParams: ReactQuery.ITimKiemPhanTrangDanhMucNhomMaBenhParams & ReactQuery.IPhanTrang;
  getListDanhMucNhomMaBenh: (params?: ReactQuery.ITimKiemPhanTrangDanhMucNhomMaBenhParams & ReactQuery.IPhanTrang) => Promise<void>;
  getChiTietDanhMucNhomMaBenh: (params: TableDanhMucNhomMaBenhColumnDataType) => Promise<CommonExecute.Execute.IDanhMucNhomMaBenh>;
  capNhatChiTietDanhMucNhomMaBenh: (params: ReactQuery.ICapNhatDanhMucNhomMaBenhParams, isEditMode?: boolean) => Promise<any>;
  setFilterParams: React.Dispatch<React.SetStateAction<ReactQuery.ITimKiemPhanTrangDanhMucNhomMaBenhParams & ReactQuery.IPhanTrang>>;
}

// Custom styles for <PERSON><PERSON>uan <PERSON>
// .danh-muc-quan-huyen {
//   &__search-form {
//     .ant-form-item {
//       margin-bottom: 0;
//     }
//   }

//   &__table {
//     .ant-table-thead th {
//       background-color: #fafafa;
//       font-weight: 600;
//     }

//     .ant-table-row {
//       &:hover {
//         cursor: pointer;
//       }
//     }
//   }

//   &__empty-row {
//     cursor: default !important;
    
//     &:hover {
//       background-color: transparent !important;
//     }
//   }
// }

// // Table styling
// .table-quan-huyen {
//   .ant-table-thead > tr > th {
//     background-color: #fafafa;
//     border-bottom: 1px solid #f0f0f0;
//     font-weight: 600;
//     color: #262626;
//   }

//   .ant-table-tbody > tr {
//     &:hover > td {
//       background-color: #f5f5f5;
//     }
//   }
// }

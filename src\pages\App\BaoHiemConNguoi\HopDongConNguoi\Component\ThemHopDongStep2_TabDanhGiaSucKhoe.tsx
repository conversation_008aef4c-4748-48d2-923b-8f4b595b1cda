import {FormInput, Highlighter} from "@src/components";
import {defaultTableProps, fillRowTableEmpty} from "@src/hooks";
import {Checkbox, Input, Radio, Table} from "antd";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState} from "react";
import {useHopDongConNguoiContext} from "../index.context";
import {
  IDanhGiaSucKhoeCauHoi,
  IDanhGiaSucKhoeCauTraLoi,
  tableDanhGiaSucKhoeColumns,
  TableDanhGiaSucKhoeColumnDataType,
  ThemHopDongStep2_TabDanhGiaSucKhoeProps,
  ThemHopDongStep2_TabDanhGiaSucKhoeRef,
} from "./Constant";

const PAGE_SIZE = 10;
const TabDanhGiaSucKhoeComponent = forwardRef<ThemHopDongStep2_TabDanhGiaSucKhoeRef, ThemHopDongStep2_TabDanhGiaSucKhoeProps>(
  ({setIsChangeData, tabActive}: ThemHopDongStep2_TabDanhGiaSucKhoeProps, ref) => {
    useImperativeHandle(ref, () => ({
      getDanhSachCauHoiDanhGiaSucKhoe: () => {
        return listCauHoi;
      },
      getDanhSachCauTraLoiDanhGiaSucKhoe: () => {
        return listCauTraLoi;
      },
      // Helper function để collect dữ liệu cho API lưu
      getDataForSave: () => {
        return collectDataForSave();
      },
      // Validate dữ liệu trước khi lưu
      validateData: () => {
        let hasError = false;

        listCauHoi.forEach(cauHoi => {
          if (cauHoi.bat_buoc === "C" && cauHoi.ma) {
            const cauTraLoi = listCauTraLoi.find(ctl => ctl.ma === cauHoi.ma);
            if (!cauTraLoi?.cau_tra_loi || cauTraLoi.cau_tra_loi.trim() === "") {
              hasError = true;
            }
          }
        });

        return {
          isValid: !hasError,
          errors: hasError ? ["Vui lòng trả lời đầy đủ các câu hỏi bắt buộc"] : [],
        };
      },
      // Method để refresh data sau khi lưu thành công
      refreshData: () => {
        handleLoadDanhSachCauHoi();
      },
    }));

    const {chiTietNguoiDuocBaoHiem, layDanhSachCauHoiDanhGiaSucKhoe} = useHopDongConNguoiContext();

    const [listCauHoi, setListCauHoi] = useState<IDanhGiaSucKhoeCauHoi[]>([]);
    const [listCauTraLoi, setListCauTraLoi] = useState<IDanhGiaSucKhoeCauTraLoi[]>([]);
    const [giaTriData, setGiaTriData] = useState<any[]>([]);
    const [loading, setLoading] = useState<boolean>(false);

    // Ref để lưu trữ giá trị input hiện tại cho NUMBER và TEXTAREA
    const currentInputValues = useRef<{[key: string]: string}>({});

    // Gọi API khi có thông tin người được bảo hiểm
    useEffect(() => {
      if (chiTietNguoiDuocBaoHiem?.so_id && chiTietNguoiDuocBaoHiem?.so_id_dt && tabActive === "5") {
        handleLoadDanhSachCauHoi();
      }
    }, [chiTietNguoiDuocBaoHiem?.so_id, chiTietNguoiDuocBaoHiem?.so_id_dt, tabActive]);

    const handleLoadDanhSachCauHoi = useCallback(async () => {
      if (!chiTietNguoiDuocBaoHiem?.so_id || !chiTietNguoiDuocBaoHiem?.so_id_dt) return;

      try {
        setLoading(true);
        const response = await layDanhSachCauHoiDanhGiaSucKhoe({
          so_id: Number(chiTietNguoiDuocBaoHiem.so_id),
          so_id_dt: Number(chiTietNguoiDuocBaoHiem.so_id_dt),
        });

        // Kiểm tra cấu trúc dữ liệu từ API
        const cauHoiData = (response as any)?.data?.cau_hoi || response?.cau_hoi;
        const giaTriDataFromApi = (response as any)?.data?.gia_tri || response?.gia_tri;

        // Lưu giaTriData vào state để sử dụng trong render
        if (giaTriDataFromApi && Array.isArray(giaTriDataFromApi)) {
          setGiaTriData(giaTriDataFromApi);
        }

        if (cauHoiData && Array.isArray(cauHoiData)) {
          setListCauHoi(cauHoiData);

          // Khởi tạo danh sách câu trả lời với logic map giữa cauHoiData và giaTriData
          const initialCauTraLoi = cauHoiData.map((cauHoi: IDanhGiaSucKhoeCauHoi) => {
            // Xử lý ma là string hoặc mảng
            const maCauHoi = Array.isArray(cauHoi.ma) ? cauHoi.ma[0] : cauHoi.ma;

            // Tìm các giá trị tương ứng trong giaTriData dựa trên ma_cau_hoi
            const giaTriItems = giaTriDataFromApi?.filter((item: any) => item.ma_cau_hoi === maCauHoi) || [];

            let cauTraLoi = "";

            // Đối với TEXTAREA và NUMBER, ưu tiên lấy trực tiếp từ dap_an
            if (cauHoi.kieu_chon === "TEXTAREA" || cauHoi.kieu_chon === "NUMBER") {
              if (cauHoi.dap_an) {
                const dapAn = Array.isArray(cauHoi.dap_an) ? cauHoi.dap_an[0] : cauHoi.dap_an;
                cauTraLoi = dapAn;
              }
            } else if (giaTriItems.length > 0) {
              // Xử lý dap_an từ cauHoi
              const dapAn = Array.isArray(cauHoi.dap_an) ? cauHoi.dap_an[0] : cauHoi.dap_an;
              // TH1: Nếu có dap_an và khác null/rỗng
              if (dapAn && dapAn !== "" && dapAn !== null) {
                // Parse dap_an - có thể là single value hoặc multiple values với dấu pipe
                let dapAnValues: string[] = [];
                if (dapAn.includes("|")) {
                  dapAnValues = dapAn.split("|").map(v => v.trim());
                } else {
                  dapAnValues = [dapAn];
                }
                // Tìm các item trong giaTriData có gia_tri match với dapAnValues
                const matchedItems = giaTriItems.filter((item: any) => dapAnValues.includes(item.gia_tri));

                if (matchedItems.length > 0) {
                  // Sử dụng comma để join (internal format)
                  cauTraLoi = matchedItems.map((item: any) => item.gia_tri).join(",");
                } else {
                  // Nếu không tìm thấy match nào, tìm các item có mac_dinh = "C"
                  const defaultItems = giaTriItems.filter((item: any) => item.mac_dinh === "C");
                  if (defaultItems.length > 0) {
                    cauTraLoi = defaultItems.map((item: any) => item.gia_tri || item.mac_dinh).join(",");
                  }
                }
              } else {
                // TH2: Nếu dap_an null/rỗng, tìm các item có mac_dinh = "C"
                const defaultItems = giaTriItems.filter((item: any) => item.mac_dinh === "C");
                if (defaultItems.length > 0) {
                  cauTraLoi = defaultItems.map((item: any) => item.gia_tri || item.mac_dinh).join(",");
                } else {
                  // Nếu không có mac_dinh = "C", lấy các giá trị đã được chọn (có gia_tri không rỗng)
                  const selectedItems = giaTriItems.filter((item: any) => item.gia_tri && item.gia_tri !== "");
                  if (selectedItems.length > 0) {
                    cauTraLoi = selectedItems.map((item: any) => item.gia_tri).join(",");
                  }
                }
              }
            } else {
              // Fallback về dap_an nếu không có trong giaTriData (cho TEXTAREA, NUMBER, INPUT)
              if (cauHoi.dap_an) {
                const dapAn = Array.isArray(cauHoi.dap_an) ? cauHoi.dap_an[0] : cauHoi.dap_an;
                cauTraLoi = dapAn;
              }
            }

            return {
              ma: maCauHoi,
              cau_tra_loi: cauTraLoi,
              ghi_chu: "",
            };
          });

          setListCauTraLoi(initialCauTraLoi);

          // Cập nhật ref cho các câu hỏi TEXTAREA và NUMBER
          initialCauTraLoi.forEach(item => {
            const cauHoi = cauHoiData.find((ch: any) => {
              const maCauHoi = Array.isArray(ch.ma) ? ch.ma[0] : ch.ma;
              const itemMa = Array.isArray(item.ma) ? item.ma[0] : item.ma;
              return maCauHoi === itemMa;
            });

            if (cauHoi && (cauHoi.kieu_chon === "TEXTAREA" || cauHoi.kieu_chon === "NUMBER") && item.cau_tra_loi) {
              const maCauHoi = Array.isArray(item.ma) ? item.ma[0] : item.ma;
              currentInputValues.current[maCauHoi] = item.cau_tra_loi;
            }
          });
        }
      } catch (error) {
        console.error("Lỗi khi tải danh sách câu hỏi đánh giá sức khoẻ:", error);
      } finally {
        setLoading(false);
      }
    }, [chiTietNguoiDuocBaoHiem, layDanhSachCauHoiDanhGiaSucKhoe]);

    // Xử lý thay đổi câu trả lời
    const handleCauTraLoiChange = useCallback(
      (maCauHoi: string, cauTraLoi: string) => {
        setListCauTraLoi(prev => {
          const newList = [...prev];
          const index = newList.findIndex(item => item.ma === maCauHoi);
          if (index >= 0) {
            newList[index] = {...newList[index], cau_tra_loi: cauTraLoi};
          } else {
            newList.push({ma: maCauHoi, cau_tra_loi: cauTraLoi, ghi_chu: ""});
          }
          return newList;
        });
        setIsChangeData(true);
      },
      [setIsChangeData],
    );

    // Dữ liệu cho table
    const dataTableDanhGiaSucKhoe = useMemo<Array<TableDanhGiaSucKhoeColumnDataType>>(() => {
      try {
        const tableData = listCauHoi.map((item, index) => {
          // Xử lý ma là string hoặc mảng
          const maCauHoi = Array.isArray(item.ma) ? item.ma[0] : item.ma;
          const cauTraLoi = listCauTraLoi.find(ctl => {
            const ctlMa = Array.isArray(ctl.ma) ? ctl.ma[0] : ctl.ma;
            return ctlMa === maCauHoi;
          });
          return {
            key: maCauHoi || index.toString(),
            stt: index + 1,
            ma: maCauHoi,
            ten: item.ten,
            kieu_chon: item.kieu_chon,
            bat_buoc: item.bat_buoc,
            do_rong: item.do_rong,
            trang_thai: item.trang_thai,
            cau_tra_loi: cauTraLoi?.cau_tra_loi || "",
            dap_an: Array.isArray(item.dap_an) ? item.dap_an[0] : item.dap_an,
          };
        });

        const arrEmptyRow = fillRowTableEmpty(tableData.length, PAGE_SIZE);
        return [...tableData, ...arrEmptyRow];
      } catch (error) {
        console.error("Lỗi khi tạo dữ liệu table:", error);
        const arrEmptyRow = fillRowTableEmpty(0, PAGE_SIZE);
        return [...arrEmptyRow];
      }
    }, [listCauHoi, listCauTraLoi]);

    // Helper function để lấy options cho Radio/Checkbox từ giaTriData
    const getOptionsForQuestion = useCallback(
      (maCauHoi: string) => {
        // Tìm tất cả items có ma_cau_hoi trùng với maCauHoi
        const options = giaTriData.filter((item: any) => item.ma_cau_hoi === maCauHoi);
        return options.map((option: any) => ({
          value: option.gia_tri || option.mac_dinh || "",
          label: option.ten_gia_tri || option.gia_tri || option.mac_dinh || "",
          rawData: option,
        }));
      },
      [giaTriData],
    );

    // Helper function để lấy giá trị hiện tại của câu hỏi (multiple values cho checkbox)
    const getCurrentValuesForQuestion = useCallback(
      (maCauHoi: string) => {
        const cauTraLoi = listCauTraLoi.find(ctl => ctl.ma === maCauHoi);
        const currentValue = cauTraLoi?.cau_tra_loi || "";
        // Nếu có multiple values, split bằng dấu phẩy (internal format) hoặc dấu pipe (từ server)
        if (currentValue) {
          if (currentValue.includes("|")) {
            // Dữ liệu từ server với dấu pipe
            return currentValue.split("|").map(v => v.trim());
          } else if (currentValue.includes(",")) {
            // Dữ liệu internal với dấu phẩy
            return currentValue.split(",").map(v => v.trim());
          } else {
            // Single value
            return [currentValue];
          }
        }
        return [];
      },
      [listCauTraLoi],
    );

    // Helper function để collect dữ liệu cho API lưu
    const collectDataForSave = useCallback(() => {
      const dgsk: Array<{ma?: string; dap_an?: string}> = [];

      listCauHoi.forEach(cauHoi => {
        if (cauHoi.ma) {
          // Xử lý ma là string hoặc mảng
          const maCauHoi = Array.isArray(cauHoi.ma) ? cauHoi.ma[0] : cauHoi.ma;
          const cauTraLoi = listCauTraLoi.find(ctl => {
            const ctlMa = Array.isArray(ctl.ma) ? ctl.ma[0] : ctl.ma;
            return ctlMa === maCauHoi;
          });

          // Lấy giá trị trực tiếp từ ref cho NUMBER và TEXTAREA
          let currentInputValue = cauTraLoi?.cau_tra_loi || "";

          if (cauHoi.kieu_chon === "NUMBER" || cauHoi.kieu_chon === "TEXTAREA") {
            // Lấy giá trị từ ref nếu có
            const refValue = currentInputValues.current[maCauHoi];
            if (refValue !== undefined) {
              currentInputValue = refValue;
            }
          }

          // Map câu trả lời: xử lý multiple values với dấu pipe (|)
          let mappedAnswer = "";
          if (currentInputValue) {
            // Nếu có multiple values (separated by comma), convert thành pipe-separated
            if (currentInputValue.includes(",")) {
              const values = currentInputValue.split(",").map(v => v.trim());
              const mappedValues = values.map(value => {
                if (value === "Có") return "C";
                if (value === "Không") return "K";
                return value; // Giữ nguyên nếu là text input hoặc giá trị khác
              });
              mappedAnswer = mappedValues.join("|"); // Sử dụng dấu pipe thay vì comma
            } else {
              // Single value
              if (currentInputValue === "Có") {
                mappedAnswer = "C";
              } else if (currentInputValue === "Không") {
                mappedAnswer = "K";
              } else {
                mappedAnswer = currentInputValue; // Giữ nguyên nếu là text input
              }
            }
          }

          // Thêm object {ma: '', dap_an: ''}
          dgsk.push({
            ma: maCauHoi,
            dap_an: mappedAnswer,
          });
        }
      });
      return {dgsk};
    }, [listCauHoi, listCauTraLoi]);

    // Cấu hình cột table - sử dụng columns từ Constant.tsx và override render cho cột "Câu trả lời"
    const finalTableColumns = useMemo(() => {
      return (
        tableDanhGiaSucKhoeColumns?.map(column => {
          // Override render cho cột "Câu trả lời"
          if (column.key === "cau_tra_loi") {
            return {
              ...column,
              render: (_: any, record: TableDanhGiaSucKhoeColumnDataType) => {
                if (record.key && record.key.toString().includes("empty")) return "";

                // Lấy giá trị câu trả lời từ state để đảm bảo sync với dữ liệu thực tế
                const cauTraLoi = listCauTraLoi.find(ctl => ctl.ma === record.ma);
                let currentValue = cauTraLoi?.cau_tra_loi || record.cau_tra_loi || "";

                // Đối với TEXTAREA và NUMBER, ưu tiên lấy từ ref nếu có
                if ((record.kieu_chon === "TEXTAREA" || record.kieu_chon === "NUMBER") && currentInputValues.current[record.ma || ""]) {
                  currentValue = currentInputValues.current[record.ma || ""];
                }

                // Render input dựa trên kiểu chọn
                switch (record.kieu_chon) {
                  case "RADIO":
                    const radioOptions = getOptionsForQuestion(record.ma || "");

                    return (
                      <Radio.Group
                        value={currentValue}
                        onChange={e => {
                          handleCauTraLoiChange(record.ma || "", e.target.value);
                        }}
                        size="small">
                        {radioOptions.map(option => (
                          <Radio key={option.value} value={option.value}>
                            {option.label}
                          </Radio>
                        ))}
                      </Radio.Group>
                    );

                  case "CHECKBOX":
                    const checkboxOptions = getOptionsForQuestion(record.ma || "");
                    const currentValues = getCurrentValuesForQuestion(record.ma || "");

                    return (
                      <div style={{display: "flex", flexDirection: "row", gap: "4px", justifyContent: "center"}}>
                        {checkboxOptions.map(option => (
                          <Checkbox
                            key={option.value}
                            checked={currentValues.includes(option.value)}
                            onChange={e => {
                              let newValues = [...currentValues];
                              if (e.target.checked) {
                                // Thêm giá trị vào danh sách
                                if (!newValues.includes(option.value)) {
                                  newValues.push(option.value);
                                }
                              } else {
                                // Xóa giá trị khỏi danh sách
                                newValues = newValues.filter(v => v !== option.value);
                              }
                              const newValue = newValues.join(",");
                              handleCauTraLoiChange(record.ma || "", newValue);
                            }}>
                            {option.label}
                          </Checkbox>
                        ))}
                      </div>
                    );

                  case "TEXTAREA":
                    return (
                      <FormInput
                        {...({
                          component: "input",
                          value: currentValue,
                          placeholder: "Nhập câu trả lời chi tiết...",
                          onChange: (e: any) => {
                            // Lưu giá trị vào ref để lấy khi gọi API
                            currentInputValues.current[record.ma || ""] = e.target.value;
                            handleCauTraLoiChange(record.ma || "", e.target.value);
                          },
                          className: "!mb-0",
                        } as any)}
                      />
                    );

                  case "NUMBER":
                    return (
                      <FormInput
                        {...({
                          component: "input",
                          type: "number",
                          value: currentValue,
                          placeholder: "Nhập số...",
                          onChange: (e: any) => {
                            const inputValue = e.target.value;
                            // Chỉ cho phép nhập số (bao gồm số thập phân và số âm)
                            const numericRegex = /^-?[0-9]*\.?[0-9]*$/;
                            if (inputValue === "" || numericRegex.test(inputValue)) {
                              currentInputValues.current[record.ma || ""] = inputValue;
                              handleCauTraLoiChange(record.ma || "", inputValue);
                            }
                          },
                          onKeyPress: (e: any) => {
                            // Ngăn chặn nhập ký tự không phải số
                            const char = String.fromCharCode(e.which);
                            if (!/[0-9\.\-]/.test(char)) {
                              e.preventDefault();
                            }
                          },
                          className: "!mb-0",
                          min: 0,
                          step: "any",
                        } as any)}
                      />
                    );

                  default:
                    // Fallback cho INPUT hoặc các kiểu khác
                    return (
                      <Input
                        value={currentValue}
                        placeholder="Nhập câu trả lời..."
                        onChange={e => {
                          handleCauTraLoiChange(record.ma || "", e.target.value);
                        }}
                        style={{width: "100%"}}
                        size="small"
                      />
                    );
                }
              },
            };
          }

          // Override render cho các cột khác để thêm Highlighter
          if (column.key === "stt" || column.key === "ma") {
            return {
              ...column,
              render: (text: any, record: any) => {
                if (record.key && record.key.toString().includes("empty")) return "";
                return <Highlighter searchWords={[]} textToHighlight={text?.toString() || ""} />;
              },
            };
          }

          return column;
        }) || []
      );
    }, [handleCauTraLoiChange, listCauTraLoi, getOptionsForQuestion, getCurrentValuesForQuestion]);

    return (
      <div>
        <Table<TableDanhGiaSucKhoeColumnDataType>
          {...defaultTableProps}
          style={{borderBottom: "1px solid #f0f0f0"}}
          className="table-quyen-loi no-header-border-radius"
          columns={finalTableColumns}
          dataSource={dataTableDanhGiaSucKhoe}
          loading={loading}
          rowKey="ma"
          pagination={false}
          sticky
          title={() => <></>}
          onRow={record => ({
            style: record.key && record.key.toString().includes("empty") ? {height: "34px"} : undefined,
          })}
        />
      </div>
    );
  },
);

export const ThemHopDongStep2_TabDanhGiaSucKhoe = memo(TabDanhGiaSucKhoeComponent);

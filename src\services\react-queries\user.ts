// import {useQuery} from "react-query";
// import {ReactQuery, User} from "@src/@types";
// import {DEFAULT_PARAMS, QUERY_KEYS} from "@src/constants";
// import {getUserList} from "../axios/endpoints/user";

// const {GET_USER_LIST} = QUERY_KEYS.USER;

// export const useGetUserList = (params: ReactQuery.Queries = DEFAULT_PARAMS, options?: ReactQuery.Options) => {
//   return useQuery<User.GetUserList.Response>([GET_USER_LIST, params], () => getUserList(params), options);
// };

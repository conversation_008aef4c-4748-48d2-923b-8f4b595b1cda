import {useMemo, useRef, useState} from "react";
import {useDanhMucChiNhanhNganHangContext} from "./index.context";
import {FormTimKiemChiNhanhNganHang, getColumns, TableChiNhanhNganHangDataType, trangThaiOptions, TRANG_THAI_CHI_NHANH_NGAN_HANG} from "./index.configs";
import {Col, Form, Input, InputRef, Row, Table, TableColumnType, Tag, Tooltip, Flex} from "antd";
import {Button, FormInput, Highlighter} from "@src/components";
import {COLOR_PALETTE, ID_PAGE} from "@src/constants";
import {IFormInput, ReactQuery} from "@src/@types";
import {SearchOutlined, ClearOutlined, PlusCircleOutlined} from "@ant-design/icons";
import "./index.default.scss";
import {ModalChiTietChiNhanhNganHang, IModalChiTietChiNhanhNganHangRef} from "./Component";
import {defaultPaginationTableProps, defaultTableProps, fillRowTableEmpty} from "@src/hooks";
// Type cho các column có thể search được trong bảng
type DataIndex = keyof TableChiNhanhNganHangDataType;

// Destructure các field config từ form tìm kiếm để dễ sử dụng
const {ten, ma_ngan_hang, trang_thai} = FormTimKiemChiNhanhNganHang;

const DanhMucChiNhanhNganHangContent: React.FC = () => {
  // Lấy tất cả state và functions từ Provider thông qua Context
  const {
    danhSachChiNhanhNganHang, // Danh sách chi nhánh từ API (hiển thị trên bảng)
    loading, // Trạng thái loading (hiển thị spinner)
    tongSoDong, // Tổng số record để tính pagination
    listNganHang, // Danh sách ngân hàng cho dropdown
    layDanhSachChiNhanhNganHang, // Function search API
    layChiTietChiNhanhNganHang, // Function lấy chi tiết 1 record
    defaultFormValue, // Giá trị mặc định cho form search
  } = useDanhMucChiNhanhNganHangContext();

  // State cho form search API (gửi lên server)
  const [searchParams, setSearchParams] = useState<ReactQuery.ITimKiemPhanTrangChiNhanhNganHangParams>(defaultFormValue);

  // State cho pagination (tách riêng để dễ quản lý)
  const [page, setPage] = useState(1); // Trang hiện tại
  const [pageSize, setPageSize] = useState(defaultPaginationTableProps.defaultPageSize); // Số dòng mỗi trang - thay đổi từ 20 → 15

  // State cho column search trong bảng (search local, không gửi API)
  const [searchText, setSearchText] = useState(""); // Text đang search
  const [searchedColumn, setSearchedColumn] = useState(""); // Column nào đang được search

  // Ref cho input search trong dropdown column (để focus và select)
  const searchInput = useRef<InputRef>(null);

  // Ref cho modal chi tiết (để control open/close từ bên ngoài)
  const refModalChiTiet = useRef<IModalChiTietChiNhanhNganHangRef>(null);

  const normalizeVietnamese = (text: string) => {
    if (!text) return "";
    return text
      .normalize("NFD") // Unicode Normalization: tách dấu khỏi chữ
      .replace(/[\u0300-\u036f]/g, "") // Xóa tất cả dấu tiếng Việt
      .toLowerCase() // Chuyển về chữ thường
      .trim(); // Xóa space đầu/cuối
  };

  const onSearchApi = (values: ReactQuery.ITimKiemPhanTrangChiNhanhNganHangParams & ReactQuery.IPhanTrang) => {
    // Clean và validate dữ liệu từ form
    const cleanedValues = {
      ...values,
      ma_ngan_hang: values.ma_ngan_hang ?? "", // Nếu null/undefined thì set ""
      ten: values.ten ?? "",
      trang_thai: values.trang_thai ?? "",
      trang: values.trang ?? 1,
      so_dong: values.so_dong ?? 13, // Thay đổi từ 20 → 15
    };

    // Lưu search params để dùng cho pagination sau này
    setSearchParams(cleanedValues);

    // Reset về trang 1 khi search mới (quan trọng!)
    setPage(1);

    // Gọi API search với params đã clean
    layDanhSachChiNhanhNganHang({...cleanedValues, trang: 1, so_dong: pageSize});
  };

  const onChangePage = (page: number, pageSize: number) => {
    // Update local state
    setPage(page);
    setPageSize(pageSize);

    // Gọi API với search params cũ + pagination mới
    layDanhSachChiNhanhNganHang({...searchParams, trang: page, so_dong: pageSize});
  };

  const handleSearch = (selectedKeys: string[], confirm: any, dataIndex: DataIndex) => {
    confirm(); // Đóng dropdown
    setSearchText(selectedKeys[0]); // Lưu text đang search
    setSearchedColumn(dataIndex); // Lưu column đang search (để highlight)
  };

  /**
   * XỬ LÝ RESET SEARCH TRONG COLUMN
   *
   * Clear search text và reset filter cho column đó
   */
  const handleReset = (clearFilters: any, confirm: any, dataIndex: DataIndex) => {
    clearFilters(); // Clear filter của column
    setSearchText(""); // Clear search text
    setSearchedColumn(dataIndex); // Update searched column
  };

  const dataTableWithSTT = useMemo(() => {
    
    try {
      // Map data từ API, thêm key và STT
      const tableData =
        danhSachChiNhanhNganHang?.map((item, index) => ({
          ...item, // Spread tất cả field từ API
          key: item.ma || `row-${index}`, // Key unique (dùng mã hoặc fallback)
          sott: (page - 1) * (pageSize || 13) + index + 1, // STT theo pagination với fallback
        })) || [];


      // Thêm empty rows để table luôn có đủ 15 dòng (thay đổi từ 20 → 15)
      const arrEmptyRow = fillRowTableEmpty(tableData.length, pageSize);
      

      const finalData = [...tableData, ...arrEmptyRow]; // Merge real data + empty rows
      
      return finalData;
    } catch (error) {
      // Nếu có lỗi trong quá trình transform, log và return empty
      console.log("dataTableWithSTT error", error);
      return [];
    }
  }, [danhSachChiNhanhNganHang, page, pageSize]);

  const nganHangOptions = useMemo(() => {
    // Kiểm tra data hợp lệ
    if (!listNganHang || !Array.isArray(listNganHang)) {
      return [];
    }

    // Transform từng item
    return listNganHang.map((item, index) => ({
      ten: item.ten || item.label || `Ngân hàng ${index + 1}`, // Label hiển thị
      ma: item.ma || item.value || "", // Value submit
    }));
  }, [listNganHang]);

  const getColumnSearchProps = (dataIndex: DataIndex, title: string): TableColumnType<TableChiNhanhNganHangDataType> => ({
    // ===== SEARCH DROPDOWN UI =====
    filterDropdown:
      dataIndex !== "trang_thai_ten" // Column trạng thái dùng dropdown select, không dùng input
        ? ({setSelectedKeys, selectedKeys, confirm, clearFilters}) => (
            <div onKeyDown={e => e.stopPropagation()} className="flex flex-row items-center p-2">
              {/* Input search */}
              <Input
                ref={searchInput} // Ref để focus
                placeholder={`Nhập ${title}`} // Placeholder động
                value={selectedKeys[0]} // Value từ Ant Design filter
                onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])} // Update filter value
                onPressEnter={() => handleSearch(selectedKeys as string[], confirm, dataIndex)} // Enter = search
                className="mr-2 flex p-2"
              />

              {/* Button tìm kiếm */}
              <Tooltip title="Tìm kiếm">
                <Button type="primary" shape="circle" icon={<SearchOutlined />} onClick={() => handleSearch(selectedKeys as string[], confirm, dataIndex)} className="mr-2" />
              </Tooltip>

              {/* Button xóa */}
              <Tooltip title="Xoá">
                <Button type="primary" shape="circle" icon={<ClearOutlined />} onClick={() => clearFilters && handleReset(clearFilters, confirm, dataIndex)} />
              </Tooltip>
            </div>
          )
        : undefined, // Column trạng thái không có filterDropdown

    // Icon search bên cạnh title column, đổi màu khi đang filter
    filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} />,

    onFilter: (value, record) => {
      const recordValue = record[dataIndex]?.toString() || ""; // Giá trị trong cell
      const searchValue = (value as string) || ""; // Giá trị search

      // So sánh bản gốc (có dấu) - case insensitive
      const originalMatch = recordValue.toLowerCase().includes(searchValue.toLowerCase());

      // So sánh bản normalize (không dấu)
      const normalizedRecord = normalizeVietnamese(recordValue);
      const normalizedSearch = normalizeVietnamese(searchValue);
      const normalizedMatch = normalizedRecord.includes(normalizedSearch);

      // Return true nếu match ở bất kỳ cách nào
      return originalMatch || normalizedMatch;
    },

    filterDropdownProps: {
      onOpenChange(open) {
        if (open) {
          // Khi mở dropdown, focus và select all text trong input (UX tốt)
          setTimeout(() => searchInput.current?.select(), 100);
        }
      },
    },

    // Chỉ column trạng thái mới có filter options (Đang sử dụng / Ngừng sử dụng)
    filters: dataIndex === "trang_thai_ten" ? trangThaiOptions.map(opt => ({text: opt.label, value: opt.label})) : undefined,

    // ===== RENDER CELL CONTENT =====
    /**
     * HÀM RENDER NỘI DUNG TỪNG CELL
     *
     * Xử lý cách hiển thị data trong từng ô của bảng:
     * - Column trạng thái: hiển thị Tag màu
     * - Column đang search: highlight search term
     * - Column bình thường: hiển thị text + tooltip
     */
    render: (text, record) => {
      // ===== XỬ LÝ COLUMN TRẠNG THÁI =====
      if (dataIndex === "trang_thai_ten") {
        // Chọn màu tag based on trạng thái
        const color = text === "Đang sử dụng" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];

        // Không hiển thị gì cho empty rows
        if (record.key.toString().includes("empty")) return "";

        return (
          <Tag color={color} className="text-[11px]">
            {text}
          </Tag>
        );
      }

      // ===== XỬ LÝ CÁC COLUMN KHÁC =====
      const displayText = text ? text.toString() : "";

      // Nếu đây là column đang được search, highlight search term
      return searchedColumn === dataIndex ? (
        <Highlighter
          searchWords={[searchText]} // Từ khóa cần highlight
          textToHighlight={displayText} // Text gốc
          highlightClassName="text-[11px]" // Màu highlight cam nhạt
        />
      ) : (
        // Column bình thường: hiển thị text với tooltip và truncate
        <span
          className="block max-w-full truncate text-ellipsis text-[11px]"
          title={displayText} // Tooltip hiển thị full text
        >
          {displayText || "\u00A0"}
        </span>
      );
    },
  });

  /**
   * HÀM RENDER MỘT FORM INPUT COLUMN
   *
   * Helper function để render một column input trong form search
   * Wrap FormInput trong Col và Form.Item với config chuẩn
   */
  const renderFormInputColumn = (props: IFormInput, span = 4) => (
    <Col span={span} key={props.name}>
      <Form.Item>
        <FormInput {...props} />
      </Form.Item>
    </Col>
  );

  /**
   *
   * Render toàn bộ form tìm kiếm phía trên bảng bao gồm:
   * - Input tên chi nhánh
   * - Dropdown ngân hàng
   * - Dropdown trạng thái
   * - Button tìm kiếm và tạo mới
   */
  const renderHeaderTable = () => {
    return (
      <Form
        // initialValues={{trang_thai: TRANG_THAI_CHI_NHANH_NGAN_HANG[0]?.ma || ""}} // Set trạng thái mặc định
        layout="vertical" // Layout form dọc
        className="[&_.ant-form-item]:!mb-0" // Thêm mb-6 để tạo khoảng cách giữa 2 phần với bảng
        onFinish={onSearchApi} // Submit handler
      >
        {/* <div className="flex flex-col gap-5"> */}
        <Row gutter={16} align={"bottom"}>
          {/* Input tên chi nhánh - giảm từ span 5 → 4 */}
          {renderFormInputColumn(ten)}

          {}
          {renderFormInputColumn({...ma_ngan_hang, options: nganHangOptions})}

          {}
          {renderFormInputColumn({...trang_thai, options: TRANG_THAI_CHI_NHANH_NGAN_HANG})}

          {}
          <Col span={3}>
            {/* <Form.Item>
                <Flex wrap="wrap" gap="small" className="w-full"> */}
            <Button
              // className="w-full"
              htmlType="submit"
              type="primary"
              icon={<SearchOutlined />}
              loading={loading}
              block>
              Tìm kiếm
            </Button>
            {/* </Flex>
              </Form.Item> */}
          </Col>

          <Col span={3}>
            {/* <Form.Item>
                <Flex wrap="wrap" gap="small" className="w-full"> */}
            <Button
              // className="w-full"
              type="primary"
              icon={<PlusCircleOutlined />}
              onClick={() => refModalChiTiet.current?.open()}
              loading={loading}
              block>
              Tạo mới
            </Button>
            {/* </Flex>
              </Form.Item> */}
          </Col>
        </Row>
        {/* </div> */}
      </Form>
    );
  };

  /**
   * XỬ LÝ CLICK VÀO DÒNG TRONG BẢNG
   *
   * Khi user click vào dòng, sẽ lấy chi tiết và mở modal edit
   * Bỏ qua nếu:
   * - Là empty row
   * - Đang loading
   */
  const handleRowClick = async (record: TableChiNhanhNganHangDataType) => {
    // Bỏ qua empty rows và khi đang loading
    if (record.key.toString().includes("empty") || loading) return;

    try {
      // Gọi API lấy chi tiết với mã chi nhánh và mã ngân hàng
      const response = await layChiTietChiNhanhNganHang({
        ma: record.ma,
        ma_ngan_hang: record.ma_ngan_hang || "",
      });

      // Nếu có data, mở modal với data chi tiết
      if (response?.ma) {
        refModalChiTiet.current?.open(response); // Truyền data để modal biết đây là edit mode
      }
    } catch (error) {
      console.error("Error loading chi tiết:", error);
    }
  };

  // ===== MAIN RENDER =====
  return (
    <div id={ID_PAGE.DANH_MUC_CHI_NHANH_NGAN_HANG}>
      {/* BẢNG CHÍNH */}
      <Table<TableChiNhanhNganHangDataType>
        {...defaultTableProps} // Spread default props cho table
        dataSource={dataTableWithSTT} // Data source đã có STT và empty rows
        // ===== TABLE COLUMNS =====
        columns={(getColumns() || []).map(item => {
          return {
            ...item, // Spread column config từ getColumns()
            // Thêm search props cho tất cả column trừ STT
            ...(item.key === "sott" ? {} : getColumnSearchProps(item.key as keyof TableChiNhanhNganHangDataType, item.title as string)),
          };
        })}
        loading={loading} // Hiển thị loading spinner
        // ===== PAGINATION CONFIG =====
        pagination={{
          ...defaultPaginationTableProps, // Default pagination props
          total: tongSoDong, // Tổng số record từ API
          current: page, // Trang hiện tại
          pageSize: pageSize, // Số dòng mỗi trang
          onChange: onChangePage, // Handler khi đổi trang
          // showSizeChanger: false, // Tắt dropdown chọn pageSize
          // showQuickJumper: true, // Cho phép nhảy trang nhanh
          // showTotal: (total, range) => `${range[0]}-${range[1]} của ${total} bản ghi`, // Text hiển thị info
        }}
        title={renderHeaderTable} // Form search ở phía trên bảng
        // ===== ROW BEHAVIOR =====
        onRow={record => ({
          style: {cursor: loading ? "progress" : "pointer"}, // Đổi cursor khi hover
          onClick: () => handleRowClick(record), // Click handler
        })}
        scroll={{x: 1200}} // Horizontal scroll khi cần
        // size="small" // Size nhỏ để fit nhiều data
      />

      {/* MODAL CHI TIẾT */}
      <ModalChiTietChiNhanhNganHang
        ref={refModalChiTiet} // Ref để control từ bên ngoài
        listChiNhanhNganHang={danhSachChiNhanhNganHang} // Pass data list để modal validate trùng
      />
    </div>
  );
};

export default DanhMucChiNhanhNganHangContent;

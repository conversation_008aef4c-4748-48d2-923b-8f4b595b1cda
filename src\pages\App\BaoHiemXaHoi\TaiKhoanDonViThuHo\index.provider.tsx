import React, {PropsWithChildren, useCallback, useEffect, useMemo, useState} from "react";

import {QuanLyTaiKhoanDonViThuHoContext} from "./index.context";
import {IQuanLyTaiKhoanDonViThuHoContextProps} from "./index.model";
import {ACTION_CODE} from "@src/constants";
import {ReactQuery} from "@src/@types";
import {TableTaiKhoanDonViThuHoColumnDataType} from "./index.configs";
import {useCommonExecute} from "@src/services/react-queries";
import {message} from "antd";

const QuanLyTaiKhoanDonViThuHoProvider: React.FC<PropsWithChildren> = props => {
  const {children} = props;
  const mutateUseCommonExecute = useCommonExecute();
  const [listTaiKhoanDonViThuHo, setListTaiKhoanDonViThuHo] = useState<Array<CommonExecute.Execute.ITaiKhoanDonViThuHo>>([]);
  const [listDonVi, setListDonVi] = useState<Array<CommonExecute.Execute.IDanhSachDonViBHXH>>([]);
  const [tongSoDong, setTongSoDong] = useState<number>(0);
  const [filterParams, setFilterParams] = useState<ReactQuery.ITimKiemPhanTrangTaiKhoanDonViThuHoParams & ReactQuery.IPhanTrang>({ma: "", ten: "", trang_thai: "", trang: 1, so_dong: 20});

  //khởi tạo dữ liệu ban đàu
  useEffect(() => {
    initData();
  }, []);

  //Tác dụng: Lấy danh sách đơn vị để hiển thị trong dropdown filter
  const getListDonVi = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        ma: "",
        ten: "",
        trang_thai: "",
        trang: 1,
        so_dong: 100,
        actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_DON_VI_THU_HO_BHXH,
      } as any);

      if (response.data) {
        console.log("getListDonVi response.data:", response.data);
        //Xử lý response structure để đảm bảo tương thích
        if (Array.isArray(response.data)) {
          setListDonVi(response.data);
        } else if (response.data && typeof response.data === "object") {
          const responseData = response.data as any;
          if (responseData.data && Array.isArray(responseData.data)) {
            setListDonVi(responseData.data);
          } else {
            setListDonVi([]);
          }
        }
      } else {
        console.log("response.data is null/undefined");
      }
    } catch (error) {
      console.log("[Provider] getListDonVi error:", error);
      setListDonVi([]);
    }
  }, [mutateUseCommonExecute]);

  const getChiTietTaiKhoanDonViThuHo = useCallback(
    async (data: TableTaiKhoanDonViThuHoColumnDataType) => {
      try {
        console.log("[Provider] getChiTietTaiKhoanDonViThuHo được gọi với data:", data);

        if (!data.ma) {
          console.log("[Provider] Không có mã để tìm kiếm, data.ma =", data.ma);
          return {} as CommonExecute.Execute.ITaiKhoanDonViThuHo;
        }

        console.log(" [Provider] Gọi API với params:", {
          ma: data.ma,
          actionCode: ACTION_CODE.CHI_TIET_TAI_KHOAN_DON_VI_THU_HO_BHXH,
        });

        const response = await mutateUseCommonExecute.mutateAsync({
          ma: data.ma,
          actionCode: ACTION_CODE.CHI_TIET_TAI_KHOAN_DON_VI_THU_HO_BHXH,
        } as any);

        console.log("[Provider] Raw response từ API:", response);
        console.log("[Provider] Response.data:", response.data);

        // Xử lý structure response.data.lke
        const responseData = response.data as any; // Type casting để truy cập lke
        if (responseData && responseData.lke && Array.isArray(responseData.lke) && responseData.lke.length > 0) {
          const result = responseData.lke[0] as CommonExecute.Execute.ITaiKhoanDonViThuHo;
          console.log("[Provider] Trả về data từ lke[0]:", result);
          return result;
        } else {
          console.log("[Provider] Không tìm thấy data trong response.data.lke");
          return {} as CommonExecute.Execute.ITaiKhoanDonViThuHo;
        }
      } catch (error) {
        console.log("[Provider] getChiTietTaiKhoanDonViThuHo error:", error);
        return {} as CommonExecute.Execute.ITaiKhoanDonViThuHo;
      }
    },
    [mutateUseCommonExecute],
  );

  const getListTaiKhoanDonViThuHo = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        ...filterParams,
        actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_TAI_KHOAN_DON_VI_THU_HO_BHXH,
      } as any);
      if (response.data) {
        setListTaiKhoanDonViThuHo(response.data.data);
        setTongSoDong(response.data.tong_so_dong);
      }
    } catch (error) {
      console.log("getListTaiKhoanDonViThuHo error ", error);
    }
  }, [mutateUseCommonExecute]);

  useEffect(() => {
    getListTaiKhoanDonViThuHo();
  }, [filterParams]);

  const capNhatChiTietTaiKhoanDonViThuHo = useCallback(
    async (data: ReactQuery.ICapNhatTaiKhoanDonViThuHoParams, isEditMode: boolean = false) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...data,
          actionCode: ACTION_CODE.UPDATE_TAI_KHOAN_DON_VI_THU_HO_BHXH,
        } as any);
        message.success(isEditMode ? "Cập nhật Tài khoản đơn vị thu hộ  thành công" : "Thêm mới Tài khoản đơn vị thu hộ  thành công");
        return response.data;
      } catch (error) {
        message.error(isEditMode ? "Có lỗi xảy ra khi cập nhật Tài khoản đơn vị thu hộ " : "Có lỗi xảy ra khi thêm mới Tài khoản đơn vị thu hộ ");
        console.log("capNhatChiTietTaiKhoanDonViThuHo err", error);
        // return {} as CommonExecute.Execute.ITaiKhoanDonViThuHo;
      }
    },
    [mutateUseCommonExecute],
  );
  const layDanhSachTaiKhoanQuanLy = useCallback(
    async (maTaiKhoanCha: string) => {
      try {
        if (!maTaiKhoanCha) return [];

        // Tìm thông tin tài khoản cha để lấy ma_dvi
        const taiKhoanCha = listTaiKhoanDonViThuHo.find(tk => tk.ma === maTaiKhoanCha);
        if (!taiKhoanCha) {
          console.log("[Provider] Không tìm thấy thông tin tài khoản cha:", maTaiKhoanCha);
          return [];
        }
        // Truyền ts
        const res = await mutateUseCommonExecute.mutateAsync({
          actionCode: ACTION_CODE.LIET_KE_TAI_KHOAN_QUAN_LY_THU_HO_BHXH,
          ma: maTaiKhoanCha,
          ma_dvi: taiKhoanCha.ma_dvi,
        } as any);

        // API`data: []`
        const arr = Array.isArray(res?.data) ? res.data : [];
        const list = arr.filter((x: any) => x?.ma && x.ma !== maTaiKhoanCha);
        return list;
      } catch (e) {
        console.error("[QLThuHo] layDanhSachTaiKhoanQuanLy error:", e);
        return [];
      }
    },
    [mutateUseCommonExecute, listTaiKhoanDonViThuHo],
  );

  const capNhatDanhSachTaiKhoanQuanLy = useCallback(
    async (data: {ma: string; ma_dvi?: string; ds: Array<{ma: string}>}) => {
      // Suy ra ma_dvi nếu caller chưa truyền
      const _ma_dvi = data.ma_dvi ?? listTaiKhoanDonViThuHo.find(tk => tk.ma === data.ma)?.ma_dvi;

      if (!_ma_dvi) {
        message.error("Thiếu mã đơn vị (ma_dvi) của tài khoản cha");
        throw new Error("Missing ma_dvi");
      }

      const res = await mutateUseCommonExecute.mutateAsync({
        actionCode: ACTION_CODE.UPDATE_TAI_KHOAN_QUAN_LY_THU_HO_BHXH,
        ma: data.ma,
        ma_dvi: _ma_dvi,
        ds: data.ds,
      } as any);

      message.success("Cập nhật danh sách tài khoản quản lý thành công");
      return res.data;
    },
    [mutateUseCommonExecute, listTaiKhoanDonViThuHo],
  );
  //initData chỉ dùng trong useEffect 1 lần, không truyền đi đâu cả, nên không cần memo hoá
  const initData = () => {
    getListDonVi();
  };
  const value = useMemo<IQuanLyTaiKhoanDonViThuHoContextProps>(
    () => ({
      listTaiKhoanDonViThuHo,
      listDonVi,
      tongSoDong,
      loading: mutateUseCommonExecute.isLoading,
      filterParams,
      getListTaiKhoanDonViThuHo,
      getChiTietTaiKhoanDonViThuHo,
      capNhatChiTietTaiKhoanDonViThuHo,
      layDanhSachTaiKhoanQuanLy,
      capNhatDanhSachTaiKhoanQuanLy,
      setFilterParams,
    }),
    [
      listTaiKhoanDonViThuHo,
      listDonVi,
      tongSoDong,
      mutateUseCommonExecute.isLoading,
      filterParams,
      getListTaiKhoanDonViThuHo,
      getChiTietTaiKhoanDonViThuHo,
      capNhatChiTietTaiKhoanDonViThuHo,
      layDanhSachTaiKhoanQuanLy,
      capNhatDanhSachTaiKhoanQuanLy,
    ],
  );

  return <QuanLyTaiKhoanDonViThuHoContext.Provider value={value}>{children}</QuanLyTaiKhoanDonViThuHoContext.Provider>;
};

export default QuanLyTaiKhoanDonViThuHoProvider;

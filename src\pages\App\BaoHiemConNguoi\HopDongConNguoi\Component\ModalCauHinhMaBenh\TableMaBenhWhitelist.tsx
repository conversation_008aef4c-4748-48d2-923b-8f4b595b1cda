import {FormInput} from "@src/components";
import {defaultPaginationTableProps, defaultTableProps, fillRowTableEmpty, useAsyncAction} from "@src/hooks";
import {Checkbox, Space, Table} from "antd";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState} from "react";
import {useHopDongConNguoiContext} from "../../index.context";
import {HINH_THUC_AP_DUNG_OPTIONS, ITableMaBenhWhitelistRef, maBenhColumns, PAGE_SIZE, TableMaBenhDataType, TableMaBenhProps} from "./Constant";
import {SearchMaBenh} from "./SearchMaBenh";

const TableMaBenhWhitelistComponent = memo(
  forwardRef<ITableMaBenhWhitelistRef, TableMaBenhProps>(({soId, soIdDt, loaiApDung, onDataChange}, ref) => {
    // Context
    const {timKiemMaBenhPhanTrangHopDongConNguoi, layDanhSachMaBenhDaLuuHopDongConNguoi} = useHopDongConNguoiContext();

    // State management
    const [data, setData] = useState<TableMaBenhDataType[]>([]);
    const [masterCheckboxState, setMasterCheckboxState] = useState<{
      checked: boolean;
      indeterminate: boolean;
    }>({checked: false, indeterminate: false});

    // Global selections across all pages - key: ma, value: {is_selected, hinh_thuc_ap_dung}
    const [globalSelections, setGlobalSelections] = useState<Map<string, {is_selected: boolean; hinh_thuc_ap_dung: string}>>(new Map());

    // Master checkbox state for "Hình thức áp dụng" column - simplified
    const [masterHinhThucApDungChecked, setMasterHinhThucApDungChecked] = useState<boolean>(false);

    const [masterHinhThucApDung, setMasterHinhThucApDung] = useState<string>("");
    const loadedRef = useRef<{soId?: number; soIdDt?: number; loaiApDung?: string; currentPage?: number; pageSize?: number}>({});

    // Pagination state
    const [currentPage, setCurrentPage] = useState<number>(1);
    const [pageSize, setPageSize] = useState<number>(PAGE_SIZE);
    const [tongSoDongWhitelist, setTongSoDongWhitelist] = useState<number>(0);

    // Refresh trigger để force reload data
    const [refreshTrigger, setRefreshTrigger] = useState<number>(0);

    // Search state
    const [searchTerm, setSearchTerm] = useState<string>("");
    const [searchResultCount, setSearchResultCount] = useState<number>(0);

    // Sử dụng ref để state mới cập nhật sử dụng được luôn
    const masterHinhThucApDungCheckboxRef = useRef(masterHinhThucApDungChecked);
    const dataRef = useRef(data);
    const globalSelectionsRef = useRef(globalSelections);

    useEffect(() => {
      masterHinhThucApDungCheckboxRef.current = masterHinhThucApDungChecked;
    }, [masterHinhThucApDungChecked]);

    useEffect(() => {
      dataRef.current = data;
    }, [data]);

    useEffect(() => {
      globalSelectionsRef.current = globalSelections;
    }, [globalSelections]);

    // Update master checkbox state based on current page data only
    const updateMasterCheckboxState = useCallback((currentData: TableMaBenhDataType[]) => {
      // Only consider items in current page (visible items)
      const validItems = currentData.filter(item => !item.key.includes("empty"));
      const selectedItems = validItems.filter(item => item.is_selected);

      if (selectedItems.length === 0) {
        setMasterCheckboxState({checked: false, indeterminate: false});
      } else if (selectedItems.length === validItems.length) {
        setMasterCheckboxState({checked: true, indeterminate: false});
      } else {
        setMasterCheckboxState({checked: false, indeterminate: true});
      }
    }, []);

    // Helper function to create selected items from globalSelections
    const createSelectedItemsFromGlobalSelections = useCallback(() => {
      const allSelectedItems: TableMaBenhDataType[] = [];
      globalSelections.forEach((selection, ma) => {
        if (selection.is_selected) {
          // Try to find full data from current data state first
          const existingItem = data.find(item => item.ma === ma);

          if (existingItem) {
            // Use existing data with updated selection state
            allSelectedItems.push({
              ...existingItem,
              is_selected: true,
              hinh_thuc_ap_dung: selection.hinh_thuc_ap_dung,
            });
          } else {
            // Fallback to minimal data structure (for items from other pages)
            allSelectedItems.push({
              key: `ma-benh-${ma}`,
              ma: ma,
              is_selected: true,
              hinh_thuc_ap_dung: selection.hinh_thuc_ap_dung,
            });
          }
        }
      });
      return allSelectedItems;
    }, [globalSelections, data]);

    // Expose methods via ref
    useImperativeHandle(
      ref,
      () => ({
        getData: createSelectedItemsFromGlobalSelections,
        setData: (newData: TableMaBenhDataType[]) => {
          setData(newData);
          updateMasterCheckboxState(newData);
        },
        refreshData: () => {
          // Reset cache để force reload data
          loadedRef.current = {};
          // Trigger useEffect bằng cách thay đổi refreshTrigger
          setRefreshTrigger(prev => prev + 1);
        },
        resetSelections: () => {
          // Reset tất cả selections tạm thời, giữ nguyên data từ API
          setGlobalSelections(new Map());
          // Reset master checkbox state
          setMasterCheckboxState({checked: false, indeterminate: false});
          // Reset master hình thức áp dụng
          setMasterHinhThucApDungChecked(false);
          setMasterHinhThucApDung("");
          // Force refresh data để load lại từ API
          loadedRef.current = {};
          setRefreshTrigger(prev => prev + 1);
        },
      }),
      [createSelectedItemsFromGlobalSelections, updateMasterCheckboxState],
    );

    // Handle individual checkbox change
    const handleCheckboxChange = useCallback(
      (key: string, checked: boolean) => {
        const newData = data.map(item => {
          if (item.key === key) {
            const updatedItem = {...item, is_selected: checked};
            // Update globalSelections
            setGlobalSelections(prev => {
              const newMap = new Map(prev);
              if (checked) {
                newMap.set(String(item.ma), {
                  is_selected: true,
                  hinh_thuc_ap_dung: item.hinh_thuc_ap_dung || "",
                });
              } else {
                newMap.delete(String(item.ma));
              }
              return newMap;
            });
            return updatedItem;
          }
          return item;
        });
        setData(newData);
        updateMasterCheckboxState(newData);
        onDataChange?.(newData);
      },
      [data, onDataChange, updateMasterCheckboxState],
    );

    // Handle master checkbox change - áp dụng cho trang hiện tại
    const handleMasterCheckboxChange = useCallback(
      (checked: boolean) => {
        // data đã là current page data từ API, không cần slice thêm
        const validData = data.filter(item => !item.key.includes("empty"));

        // Update globalSelections cho tất cả items trong page hiện tại
        setGlobalSelections(prev => {
          const newMap = new Map(prev);

          validData.forEach(item => {
            if (checked) {
              newMap.set(String(item.ma), {
                is_selected: true,
                hinh_thuc_ap_dung: item.hinh_thuc_ap_dung || "",
              });
            } else {
              newMap.delete(String(item.ma));
            }
          });

          return newMap;
        });

        // Update local data state
        const newData = data.map(item => {
          if (item.key.includes("empty")) return item;
          return {...item, is_selected: checked};
        });

        setData(newData);
        updateMasterCheckboxState(newData);
        onDataChange?.(newData);
      },
      [data, currentPage, pageSize, onDataChange, updateMasterCheckboxState],
    );

    // Search handlers
    const handleSearch = useCallback((term: string) => {
      setSearchTerm(term);
      setCurrentPage(1); // Reset to first page when searching
      // Reset cache to force reload with new search term
      loadedRef.current = {};
      setRefreshTrigger(prev => prev + 1);
    }, []);

    const handleClearSearch = useCallback(() => {
      setSearchTerm("");
      setCurrentPage(1); // Reset to first page
      setSearchResultCount(0);
      // Reset cache to force reload without search term
      loadedRef.current = {};
      setRefreshTrigger(prev => prev + 1);
    }, []);

    // Handle individual hinh thuc ap dung change
    const handleHinhThucApDungChange = useCallback(
      (key: string, value: string) => {
        setData(prev => {
          const newData = [...prev];

          // Sử dụng ref để lấy giá trị mới nhất
          if (masterHinhThucApDungCheckboxRef.current) {
            // Cập nhật tất cả các item được chọn (is_selected = true)
            const selectedItems = newData.filter(item => item.is_selected && !item.key.includes("empty"));
            selectedItems.forEach(selectedItem => {
              const itemIndex = newData.findIndex(item => item.key === selectedItem.key);
              if (itemIndex !== -1) {
                newData[itemIndex] = {
                  ...newData[itemIndex],
                  hinh_thuc_ap_dung: value,
                };
                // Update globalSelections
                setGlobalSelections(prev => {
                  const newMap = new Map(prev);
                  if (newData[itemIndex].is_selected) {
                    newMap.set(String(newData[itemIndex].ma), {
                      is_selected: true,
                      hinh_thuc_ap_dung: value,
                    });
                  }
                  return newMap;
                });
              }
            });
          } else {
            // Cập nhật chỉ item được chọn
            const itemIndex = newData.findIndex(item => item.key === key);
            if (itemIndex !== -1) {
              newData[itemIndex] = {
                ...newData[itemIndex],
                hinh_thuc_ap_dung: value,
              };
              // Update globalSelections
              setGlobalSelections(prev => {
                const newMap = new Map(prev);
                if (newData[itemIndex].is_selected) {
                  newMap.set(String(newData[itemIndex].ma), {
                    is_selected: true,
                    hinh_thuc_ap_dung: value,
                  });
                }
                return newMap;
              });
            }
          }

          onDataChange?.(newData);
          return newData;
        });
      },
      [onDataChange],
    );

    // Handle master hinh thuc ap dung change
    const handleMasterHinhThucApDungChange = useCallback(
      (value: string) => {
        setMasterHinhThucApDung(value);
        const newData = data.map(item => (item.key.includes("empty") ? item : {...item, hinh_thuc_ap_dung: value}));
        setData(newData);
        onDataChange?.(newData);
      },
      [data, onDataChange],
    );

    // Handle master checkbox change for "Hình thức áp dụng" column
    const handleMasterHinhThucApDungCheckboxChange = useCallback(
      (checked: boolean) => {
        setMasterHinhThucApDungChecked(checked);

        // Reset value về "" cho các select bị disabled (row không được chọn)
        const updatedData = data.map(item => ({
          ...item,
          hinh_thuc_ap_dung: item.is_selected ? item.hinh_thuc_ap_dung : "", // Reset về "" nếu row không được chọn
        }));

        onDataChange?.(updatedData);
      },
      [data, onDataChange],
    );

    // Async function for loading data
    const loadDataAsyncFn = useCallback(async () => {
      if (!soId || !soIdDt) {
        return;
      }

      // Check if we already loaded data for this combination (including pagination)
      const currentKey = `${soId}-${soIdDt}-${loaiApDung}-${currentPage}-${pageSize}`;
      const loadedKey = `${loadedRef.current.soId}-${loadedRef.current.soIdDt}-${loadedRef.current.loaiApDung}-${loadedRef.current.currentPage}-${loadedRef.current.pageSize}`;
      if (currentKey === loadedKey) {
        return;
      }

      // First, load ma benh for current page
      const searchParams: {
        so_id: number;
        so_id_dt: number;
        loai_ad: "WL" | "BL";
        trang: number;
        so_dong: number;
        ten?: string;
      } = {
        so_id: soId,
        so_id_dt: soIdDt,
        loai_ad: loaiApDung,
        trang: currentPage,
        so_dong: pageSize,
      };

      // Add search term if provided
      if (searchTerm.trim()) {
        searchParams.ten = searchTerm.trim();
      }

      const searchResult = await timKiemMaBenhPhanTrangHopDongConNguoi(searchParams);

      // Log và lưu tổng số dòng
      if (searchResult.tong_so_dong) {
        setTongSoDongWhitelist(searchResult.tong_so_dong);
        setSearchResultCount(searchResult.tong_so_dong);
      }

      // Then, load saved ma benh to pre-check them
      const savedResult = await layDanhSachMaBenhDaLuuHopDongConNguoi({
        so_id: soId,
        so_id_dt: soIdDt,
        loai_ad: loaiApDung,
      });

      // Create Map for efficient lookup of saved ma benh
      // Key insight: search results use 'ma' field, saved results use 'ma_benh' field (different from bv which uses 'ma_bv')
      // Handle different possible response structures
      const savedData = savedResult?.data || savedResult || [];
      const savedMaBenhMap = new Map(savedData.map((item: any) => [String(item.ma_benh), item]));

      // Populate globalSelections with saved data
      // For first load of new object, start fresh. For pagination, preserve existing selections
      const isNewObjectLoad = loadedRef.current.soId !== soId || loadedRef.current.soIdDt !== soIdDt;
      const newGlobalSelections = isNewObjectLoad ? new Map() : new Map(globalSelectionsRef.current);

      savedData.forEach((savedItem: any) => {
        if (savedItem.ma_benh) {
          // For new object load, always add saved data. For pagination, only add if not already exists
          if (isNewObjectLoad || !newGlobalSelections.has(String(savedItem.ma_benh))) {
            newGlobalSelections.set(String(savedItem.ma_benh), {
              is_selected: true,
              hinh_thuc_ap_dung: savedItem.hinh_thuc_ad || "",
            });
          }
        }
      });
      setGlobalSelections(newGlobalSelections);

      const mergedData = (searchResult.data || []).map((item: any, index: number) => {
        // Match search result 'ma' field with saved result 'ma_benh' field
        const savedItem = savedMaBenhMap.get(String(item.ma));
        const globalSelection = newGlobalSelections.get(String(item.ma));
        const isSelected = !!savedItem || !!globalSelection;
        const hinhThucApDung = (savedItem as any)?.hinh_thuc_ad || globalSelection?.hinh_thuc_ap_dung || "";

        return {
          key: `ma-benh-${item.ma || index}`,
          stt: index + 1,
          id: item.ma, // Use 'ma' as id
          ma: item.ma,
          ten: item.ten,
          dia_chi: item.dia_chi,
          dien_thoai: item.dien_thoai,
          email: item.email,
          trang_thai: item.trang_thai,
          trang_thai_ten: item.trang_thai_ten,
          is_selected: isSelected,
          hinh_thuc_ap_dung: hinhThucApDung,
        };
      });

      // Fill with empty rows if needed
      const emptyRows = fillRowTableEmpty(mergedData.length, 10);
      const finalData = [...mergedData, ...emptyRows];

      setData(finalData);
      updateMasterCheckboxState(finalData);

      // Mark as loaded (including pagination)
      loadedRef.current = {soId, soIdDt, loaiApDung, currentPage, pageSize};
    }, [soId, soIdDt, loaiApDung, currentPage, pageSize, searchTerm, timKiemMaBenhPhanTrangHopDongConNguoi, layDanhSachMaBenhDaLuuHopDongConNguoi, updateMasterCheckboxState]);

    // Use async action for data loading with separate loading state
    const [loadData, isLoadingData] = useAsyncAction(loadDataAsyncFn);

    // Column configuration with custom renders - memoized for performance
    const columns = useMemo(() => {
      return (
        maBenhColumns?.map(column => {
          if ("dataIndex" in column && column.dataIndex === "is_selected") {
            return {
              ...column,
              title: (
                <Checkbox checked={masterCheckboxState.checked} indeterminate={masterCheckboxState.indeterminate} onChange={e => handleMasterCheckboxChange(e.target.checked)}>
                  Chọn
                </Checkbox>
              ),
              render: (_: any, record: TableMaBenhDataType) => {
                if (record.key.includes("empty")) return null;
                return (
                  <Checkbox
                    checked={record.is_selected || false}
                    disabled={false}
                    style={{pointerEvents: "auto"}}
                    onChange={e => {
                      handleCheckboxChange(record.key, e.target.checked);
                    }}
                  />
                );
              },
            };
          }

          if ("dataIndex" in column && column.dataIndex === "hinh_thuc_ap_dung") {
            return {
              ...column,
              title: (
                <span className="flex w-full items-center justify-center gap-1">
                  <Checkbox
                    checked={masterHinhThucApDungChecked}
                    onChange={e => {
                      handleMasterHinhThucApDungCheckboxChange(e.target.checked);
                    }}
                  />
                  Hình thức áp dụng
                </span>
              ),
              render: (_: any, record: TableMaBenhDataType) => {
                if (record.key.includes("empty")) return null;
                return (
                  <FormInput
                    {...({
                      component: "select",
                      value: record.hinh_thuc_ap_dung,
                      onChange: (value: string) => handleHinhThucApDungChange(record.key, value),
                      options: HINH_THUC_AP_DUNG_OPTIONS,
                      placeholder: "Chọn hình thức",
                      className: "!mb-0",
                      disabled: !record.is_selected, // Disable khi row không được check
                    } as any)}
                  />
                );
              },
            };
          }

          return column;
        }) || []
      );
    }, [masterCheckboxState, masterHinhThucApDung, masterHinhThucApDungChecked, handleMasterCheckboxChange, handleCheckboxChange, handleMasterHinhThucApDungChange, handleHinhThucApDungChange]);

    // Load data when component mounts or soId changes
    useEffect(() => {
      loadData();
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [soId, soIdDt, loaiApDung, currentPage, pageSize, refreshTrigger, searchTerm]); // Remove loadData from deps to avoid infinite loop

    // Update master checkbox state when data changes
    useEffect(() => {
      updateMasterCheckboxState(data);
    }, [data, updateMasterCheckboxState]);

    // Reset search when soId or soIdDt changes
    useEffect(() => {
      setSearchTerm("");
      setSearchResultCount(0);
      setCurrentPage(1);
    }, [soId, soIdDt]);

    // Reset globalSelections when soId or soIdDt changes (new object selected)
    useEffect(() => {
      setGlobalSelections(new Map());
      setMasterCheckboxState({checked: false, indeterminate: false});
      setMasterHinhThucApDungChecked(false);
      setMasterHinhThucApDung("");
      // Reset cache to force reload data
      loadedRef.current = {};
    }, [soId, soIdDt]);

    return (
      <Space direction="vertical" size="small" style={{width: "100%"}}>
        <SearchMaBenh
          placeholder="Tìm kiếm theo tên mã bệnh..."
          onSearch={handleSearch}
          onClear={handleClearSearch}
          loading={isLoadingData}
          resultCount={searchResultCount}
          showResultCount={searchTerm.trim().length > 0}
          initialValue={searchTerm}
        />

        <Table<TableMaBenhDataType>
          {...defaultTableProps}
          loading={isLoadingData}
          dataSource={data}
          columns={columns}
          rowClassName={record => {
            // Thêm class cho empty rows để tăng chiều cao
            return record.key.includes("empty") ? "empty-row" : "";
          }}
          pagination={{
            ...defaultPaginationTableProps,
            current: currentPage,
            pageSize: pageSize,
            total: tongSoDongWhitelist, // Sử dụng tổng số dòng từ API
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} của ${total} mục`,
            onChange: (page, size) => {
              setCurrentPage(page);
              setPageSize(size || PAGE_SIZE);
            },
          }}
          className="table-ma-benh-whitelist no-header-border-radius"
          size="small"
        />
      </Space>
    );
  }),
);

TableMaBenhWhitelistComponent.displayName = "TableMaBenhWhitelistComponent";
export const TableMaBenhWhitelist = TableMaBenhWhitelistComponent;

import { IFormInput } from "@src/@types";

const ruleRequired = {
  required: true,
  message: "Thông tin bắt buộc",
};

export interface IFormChiTietDanhMucLoaiXeFieldsConfig {
  ma: IFormInput;
  ten: IFormInput;
  nv: IFormInput;
  stt: IFormInput;
  trang_thai: IFormInput;
}

const FormChiTietDanhMucLoaiXe: IFormChiTietDanhMucLoaiXeFieldsConfig = {
  ma: {
    component: "input",
    name: "ma",
    label: "Mã loại xe",
    placeholder: "Mã loại xe",
    rules: [ruleRequired, { max: 50, message: "Mã loại xe không được vượt quá 50 ký tự!" }],
  },
  ten: {
    component: "input",
    name: "ten",
    label: "Tên loại xe",
    placeholder: "Tên loại xe",
    rules: [ruleRequired, { max: 200, message: "Tên loại xe không được vượt quá 200 ký tự!" }],
  },
  nv: {
    component: "select",
    name: "nv",
    label: "<PERSON><PERSON><PERSON><PERSON> vụ",
    placeholder: "Chọn nghiệp vụ",
    rules: [
      ruleRequired,
      {
        validator: (_: any, value: string) => {
          if (!value) return Promise.resolve();
          if (!["XE", "XE_MAY"].includes(value)) {
            return Promise.reject(new Error("Nghiệp vụ phải là 'XE' hoặc 'XE_MAY'!"));
          }
          return Promise.resolve();
        },
      },
    ],
  },
  stt: {
    component: "input",
    name: "stt",
    label: "Số thứ tự",
    placeholder: "Số thứ tự",
    rules: [
      ruleRequired,
      {
        validator: (_: any, value: string | number) => {
          if (!value) return Promise.resolve();
          const numValue = typeof value === 'string' ? parseInt(value, 10) : value;
          if (isNaN(numValue) || numValue <= 0) {
            return Promise.reject(new Error("Số thứ tự phải là số nguyên dương!"));
          }
          if (!Number.isInteger(numValue)) {
            return Promise.reject(new Error("Số thứ tự phải là số nguyên!"));
          }
          return Promise.resolve();
        },
      },
    ],
  },
  trang_thai: {
    component: "select",
    name: "trang_thai",
    label: "Trạng thái",
    placeholder: "Chọn trạng thái",
    rules: [ruleRequired],
  },
};

export const TRANG_THAI_CHI_TIET_LOAI_XE = [
  { ten: "Đang sử dụng", ma: "D" },
  { ten: "Ngừng sử dụng", ma: "K" },
];

export const NGHIEP_VU_OPTIONS = [
  { ten: "Bảo hiểm ô tô", ma: "XE" },
  { ten: "Bảo hiểm xe máy", ma: "XE_MAY" }
];

export default FormChiTietDanhMucLoaiXe;

export interface Props {
  onAfterSave?: () => void;
}

export interface IModalChiTietDanhMucLoaiXeRef {
  open: (data?: CommonExecute.Execute.IChiTietDanhMucLoaiXe | null) => void;
  close: () => void;
}

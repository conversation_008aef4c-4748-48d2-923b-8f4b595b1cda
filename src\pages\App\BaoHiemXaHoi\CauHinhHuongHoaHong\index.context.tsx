import {createContext, useContext} from "react";

import {ICauHinhHuongHoaHongContextProps} from "./index.model";

//khởi tạo giá trị mặc định
export const CauHinhHuongHoaHongContext = createContext<ICauHinhHuongHoaHongContextProps>({
  listTaiKhoanDonViThuHo: [],
  listDonVi: [],
  listLoaiHoGiaDinh: [],
  tongSoDong: 0,
  tongSoDongCT: 0,
  loading: false,
  filterParams: {},
  chiTietTaiKhoanDonViThuHo: {} as CommonExecute.Execute.ITaiKhoanDonViThuHo,
  danhSachCauHinhHuongHoaHongNgayApDung: [],
  danhSachCauHinhHuongHoaHong: [],
  chiTietCauHinhHuongHoaHong: {} as CommonExecute.Execute.ICauHinhHuongHoaHong,
  listDonViThuHo: [],
  ngayAdMoiTao: null,
  setDanhSachCauHinhHuongHoaHong: () => {},
  setNgayAdMoiTao: () => {},
  xoaCauHinhHuongHoaHong: async () => Promise.resolve(false),
  layChiTietCauHinhHuongHoaHong: async () => Promise.resolve({} as CommonExecute.Execute.ICauHinhHuongHoaHong),
  layDanhSachCauHinhHuongHoaHong: async () => Promise.resolve([]),
  layDanhSachCauHinhHuongHoaHongNgayApDung: async () => Promise.resolve([]),
  updateCauHinhHuongHoaHongNgayApDung: async () => Promise.resolve(false),
  getListTaiKhoanDonViThuHo: async () => Promise.resolve(),
  getChiTietTaiKhoanDonViThuHo: async () => Promise.resolve({} as CommonExecute.Execute.ITaiKhoanDonViThuHo),
  xoaNgayApDungCauHinhHuongHoaHong: async () => Promise.resolve(false),
  setFilterParams: () => {},
  capNhatCauHinhHuongHoaHong: async () => Promise.resolve(false),
});

export const useCauHinhHuongHoaHongContext = () => useContext(CauHinhHuongHoaHongContext);

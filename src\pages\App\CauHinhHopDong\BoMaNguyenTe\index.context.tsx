import {createContext, useContext} from "react";
import {BoMaNguyenteProps} from "./index.model";
//craeteContext : khai báo 1 context mà component có thể cung cấp hoặc đọc
export const BoMaNguyenteContext = createContext<BoMaNguyenteProps>({
  listDoiTac: [],
  //khỏi tạo các giá trị mặc định, trong context này có giá trị là onsubmit
  danhSachBoMaNguyenTe: [],
  loading: false,
  layDanhSachBoMaNguyenTe: params => {},
  tongSoDong: 0,
  layChiTietBoMaNguyenTe: () => Promise.resolve(null),
  defaultFormValue: {},
  updateBoMaNguyenTe: () => Promise.resolve(null),
  getListDoiTac: () => Promise.resolve(),
});

//useContext(someContext): là react Hook cho phép bạn đọc và subscribe context từ component của mình
// file .context muốn truy câp context thì sử dụng useLoginContext : const {onSubmit} = useLoginContext();
export const useChiTietBoMaNguyenTeContext = () => useContext(BoMaNguyenteContext);

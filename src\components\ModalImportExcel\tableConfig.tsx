import {TableProps} from "antd";
import {defaultTableColumnsProps} from "@src/hooks";
import {InputCellTable} from "@src/components";
import {IExcelRowData, COLUMN_LABELS, REQUIRED_FIELDS} from "./constants";

// Type for table column configuration
export type ExcelImportTableColumn = TableProps<IExcelRowData>["columns"];

// Column types matching FormInput components
type ColumnType =
  | "input" // Regular text input
  | "input" // Number input
  | "input-price" // Price/currency input
  | "input" // Time picker
  | "input" // Date picker
  | "input" // Dropdown input
  | "textarea" // Multi-line text
  | "readonly"; // Non-editable display

// Helper function to create editable column with specific input type
const createEditableColumn = (dataIndex: keyof IExcelRowData, width: number, columnType: ColumnType, onChange: (index: number, dataIndex: string, value: string) => void) => {
  const isRequired = REQUIRED_FIELDS.includes(dataIndex);

  return {
    ...defaultTableColumnsProps,
    title: COLUMN_LABELS[dataIndex],
    dataIndex,
    key: dataIndex,
    width,
    align: (columnType === "readonly" ? "center" : "left") as "center" | "left",
    // onHeaderCell: () => ({
    //   className: `!p-2 !font-bold !text-center !text-[#FFF] ${isRequired ? "!bg-[#ff7875]" : "!bg-[#96bf49]"}`,
    // }),
    render: (value: string, _record: IExcelRowData, index: number) => {
      const hasError = isRequired && (!value || value.trim() === "");

      // Readonly column (like STT)
      if (columnType === "readonly") {
        return <div style={{textAlign: "center", padding: "4px 8px"}}>{value || ""}</div>;
      }

      // Use the column type directly as the component name
      return <InputCellTable component={columnType} value={value || ""} onChange={onChange} index={index} dataIndex={dataIndex} placeholder={""} />;
    },
  };
};

// Create table columns configuration
export const createExcelImportColumns = (onChange: (index: number, dataIndex: string, value: string) => void): ExcelImportTableColumn => [
  // STT column - readonly, center aligned
  createEditableColumn("stt", 80, "readonly", onChange),

  // Thông tin chủ xe
  createEditableColumn("ten_chu_xe", 200, "input", onChange),
  createEditableColumn("dia_chi", 250, "textarea", onChange), // Address - use textarea for longer text

  // Thông tin xe
  createEditableColumn("bien_xe", 120, "input", onChange),
  createEditableColumn("so_khung", 150, "input", onChange),
  createEditableColumn("so_may", 150, "input", onChange),
  createEditableColumn("so_gcn", 120, "input", onChange),
  createEditableColumn("nam_sx", 100, "input", onChange), // Year
  createEditableColumn("so_lai_phu_xe", 150, "input", onChange),

  // Thông tin sử dụng
  createEditableColumn("muc_dich_su_dung", 180, "input", onChange), // Purpose - dropdown
  createEditableColumn("gia_tri_xe", 150, "input-price", onChange), // Vehicle value - price input
  createEditableColumn("loai_xe", 120, "input", onChange), // Vehicle type - dropdown
  createEditableColumn("hang_xe", 120, "input", onChange),
  createEditableColumn("hieu_xe", 120, "input", onChange),
  createEditableColumn("so_cho", 100, "input", onChange), // Seats
  createEditableColumn("trong_tai", 120, "input", onChange), // Weight
  createEditableColumn("vip", 80, "input", onChange), // VIP status - dropdown

  // Thông tin thời gian
  createEditableColumn("ngay_cap", 120, "input", onChange), // Issue date
  createEditableColumn("gio_hieu_luc", 120, "input", onChange), // Start time
  createEditableColumn("ngay_hieu_luc", 130, "input", onChange), // Start date
  createEditableColumn("gio_ket_thuc", 120, "input", onChange), // End time
  createEditableColumn("ngay_ket_thuc", 130, "input", onChange), // End date

  // Thông tin bảo hiểm
  createEditableColumn("ma_dk_dkbs", 150, "input", onChange),
  createEditableColumn("so_tien_bh", 150, "input-price", onChange), // Insurance amount
  createEditableColumn("tien_mien_thuong", 160, "input-price", onChange), // Deductible amount

  // Thông tin phí
  createEditableColumn("phi_bh_chua_vat", 160, "input-price", onChange), // Fee before VAT
  createEditableColumn("thue_vat", 120, "input-price", onChange), // VAT
  createEditableColumn("phi_giam_chua_vat", 170, "input-price", onChange), // Discount fee
  createEditableColumn("thue_giam_vat", 140, "input-price", onChange), // Discount VAT
];

// Validation helper functions
export const validateRowData = (data: IExcelRowData): string[] => {
  const errors: string[] = [];

  REQUIRED_FIELDS.forEach(field => {
    if (field !== "key" && (!data[field] || data[field].toString().trim() === "")) {
      errors.push(`${COLUMN_LABELS[field]} không được để trống`);
    }
  });

  return errors;
};

export const validateAllData = (
  data: IExcelRowData[],
): {
  isValid: boolean;
  errors: Array<{row: number; errors: string[]}>;
  totalErrors: number;
} => {
  const allErrors: Array<{row: number; errors: string[]}> = [];
  let totalErrors = 0;

  data.forEach((row, index) => {
    const rowErrors = validateRowData(row);
    if (rowErrors.length > 0) {
      allErrors.push({
        row: index + 1,
        errors: rowErrors,
      });
      totalErrors += rowErrors.length;
    }
  });

  return {
    isValid: allErrors.length === 0,
    errors: allErrors,
    totalErrors,
  };
};

// Helper to generate empty row
export const createEmptyRow = (index: number): IExcelRowData => ({
  key: `empty-${index}`,
  stt: "",
  ten_chu_xe: "",
  dia_chi: "",
  bien_xe: "",
  so_khung: "",
  so_may: "",
  so_gcn: "",
  nam_sx: "",
  so_lai_phu_xe: "",
  muc_dich_su_dung: "",
  gia_tri_xe: "",
  loai_xe: "",
  hang_xe: "",
  hieu_xe: "",
  so_cho: "",
  trong_tai: "",
  vip: "",
  ngay_cap: "",
  gio_hieu_luc: "",
  ngay_hieu_luc: "",
  gio_ket_thuc: "",
  ngay_ket_thuc: "",
  ma_dk_dkbs: "",
  so_tien_bh: "",
  tien_mien_thuong: "",
  phi_bh_chua_vat: "",
  thue_vat: "",
  phi_giam_chua_vat: "",
  thue_giam_vat: "",
});

// Helper to add keys to data
export const addKeysToData = (data: Partial<IExcelRowData>[]): IExcelRowData[] => {
  return data.map((row, index) => ({
    ...createEmptyRow(index),
    ...row,
    key: row.key || `row-${index}`,
  }));
};

diff --git a/node_modules/@jest/expect/build/index.d.ts b/node_modules/@jest/expect/build/index.d.ts
index 1b69646..992633f 100644
--- a/node_modules/@jest/expect/build/index.d.ts
+++ b/node_modules/@jest/expect/build/index.d.ts
@@ -14,6 +14,7 @@ import {Matchers} from 'expect';
 import {MatcherState} from 'expect';
 import {MatcherUtils} from 'expect';
 import type {SnapshotMatchers} from 'jest-snapshot';
+import { TestingLibraryMatchers } from '@testing-library/jest-dom/matchers';
 
 export {AsymmetricMatchers};
 
@@ -36,7 +37,7 @@ export declare type JestExpect = {
 export declare const jestExpect: JestExpect;
 
 declare type JestMatchers<R extends void | Promise<void>, T> = Matchers<R, T> &
-  SnapshotMatchers<R, T>;
+  SnapshotMatchers<R, T> & TestingLibraryMatchers<typeof expect.stringContaining, R>;
 
 export {MatcherContext};
 

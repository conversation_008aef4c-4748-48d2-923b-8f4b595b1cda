import {ReactQuery} from "@src/@types";
import React from "react";

export interface IQuanLyDonViChiNhanhContextProps {
  listDoiTac: Array<CommonExecute.Execute.IDoiTac>;
  listDonViChiNhanh: Array<CommonExecute.Execute.IChiNhanh>;
  tongSoDong: number;
  loading: boolean;
  filterParams: ReactQuery.ILayDanhSachDonViChiNhanhParams & ReactQuery.IPhanTrang;
  searchDonViChiNhanh: () => Promise<void>;
  getChiTietDonViChiNhanh: (params: ReactQuery.IChiTietDonViChiNhanhParams) => Promise<CommonExecute.Execute.IChiNhanh>;
  capNhatChiTietDonViChiNhanh: (params: ReactQuery.IUpdateDoiTacParams) => Promise<any>;
  setFilterParams: React.Dispatch<React.SetStateAction<ReactQuery.ILayDanhSachDonViChiNhanhParams & ReactQuery.IPhanTrang>>;
}

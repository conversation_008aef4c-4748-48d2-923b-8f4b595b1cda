import React from "react";
import {TableColumnsType, TableProps} from "antd";

import {IFormInput, ReactQuery} from "@src/@types";
import {colWidthByKey, defaultTableColumnsProps, ruleInputMessage, validationRules} from "@src/hooks";

//Kiểm tra value có phải là empty hay không (null, undefined, chuỗi rỗng)
const isEmptyValue = (value: any): boolean => {
  return value === null || value === undefined || value === '' || (typeof value === 'string' && value.trim() === '');
};



//===== FORM TÌM KIẾM =====
export interface IFormTimKiemQuocGiaFieldsConfig {
  ma_chau_luc: IFormInput;
  ma_khu_vuc: IFormInput;            
  ma: IFormInput;
  ten: IFormInput;
  trang_thai: IFormInput;
}

//C<PERSON>u hình form tìm kiếm quốc gia ở header table
export const FormTimKiemDanhMucQuocGia: IFormTimKiemQuocGiaFieldsConfig = {
  ma_chau_luc: {
    component: "select",
    name: "ma_chau_luc",
    label: "Châu lục",
    placeholder: "Chọn châu lục",
    className: "!mb-0",
  },
  ma_khu_vuc: {                      
    component: "select",
    name: "ma_khu_vuc",             
    label: "Khu vực",
    placeholder: "Chọn khu vực",
    className: "!mb-0",
  },
  ma: {
    component: "input",
    name: "ma",
    label: "Mã quốc gia",
    placeholder: "Nhập mã quốc gia",
    className: "!mb-0",
  },
  ten: {
    component: "input",
    name: "ten",
    label: "Tên quốc gia",
    placeholder: "Nhập tên quốc gia",
    className: "!mb-0",
  },
  trang_thai: {
    component: "select",
    name: "trang_thai",
    label: "Trạng thái",
    placeholder: "Chọn trạng thái",
    className: "!mb-0",
  },
};

//===== FORM TẠO MỚI/CHỈNH SỬA =====
export interface IFormTaoMoiQuocGiaFieldsConfig {
  ma_chau_luc: IFormInput;
  ma_khu_vuc: IFormInput;            
  ma: IFormInput;
  ten: IFormInput;
  stt: IFormInput;
  trang_thai: IFormInput;
}

//Cấu hình form tạo mới/chỉnh sửa quốc gia trong modal
export const FormTaoMoiQuocGia: IFormTaoMoiQuocGiaFieldsConfig = {
  ma_chau_luc: {
    component: "select",
    label: "Châu lục",
    name: "ma_chau_luc",
    placeholder: "Chọn châu lục",
    rules: [ruleInputMessage.required],
  },
  ma_khu_vuc: {                     
    component: "select",
    label: "Khu vực",
    name: "ma_khu_vuc",             
    placeholder: "Chọn khu vực",
    rules: [ruleInputMessage.required],
  },
  ma: {
    component: "input",
    label: "Mã quốc gia",
    name: "ma",
    placeholder: "Nhập mã quốc gia",
    rules: [ruleInputMessage.required],
  },
  ten: {
    component: "input",
    label: "Tên quốc gia",
    name: "ten",
    placeholder: "Nhập tên quốc gia",
    rules: [ruleInputMessage.required],
  },
  stt: {
    component: "input",
    label: "Số thứ tự",
    name: "stt",
    placeholder: "Nhập số thứ tự",
    rules: [],
  },
  trang_thai: {
    component: "select",
    label: "Trạng thái",
    name: "trang_thai",
    placeholder: "Chọn trạng thái",
    rules: [ruleInputMessage.required],
  },
};

//===== RADIO ITEMS - TRẠNG THÁI =====
//Options cho dropdown trạng thái trong form tìm kiếm
export const radioItemTrangThaiQuocGiaSelect: Array<{ma: string; ten: string}> = [
  {ma: "", ten: "Tất cả"},
  {ma: "D", ten: "Đang sử dụng"},
  {ma: "K", ten: "Ngưng sử dụng"},
];

export const radioItemTrangThaiQuocGiaTable = [
  {value: "Đang sử dụng", text: "Đang sử dụng"},
  {value: "Ngưng sử dụng", text: "Ngưng sử dụng"},
];

//===== TRẠNG THÁI TẠO MỚI =====
export const TRANG_THAI_TAO_MOI_QUOC_GIA = [
  {ma: "D", ten: "Đang sử dụng"},
  {ma: "K", ten: "Ngưng sử dụng"},
];


//===== KIỂU DỮ LIỆU TABLE =====
export interface TableQuocGiaColumnDataType {
  key: string;
  sott?: number;
  ma?: string;
  ten?: string;
  ma_chau_luc?: string;
  ten_chau_luc?: string;
  ma_khu_vuc?: string;
  ten_khu_vuc?: string;
  stt?: number;
  trang_thai?: string;
  ngay_tao?: string;
  nguoi_tao?: string;
  ngay_cap_nhat?: string;
  nguoi_cap_nhat?: string;
  trang_thai_ten?: string;
}

//===== CỘT TABLE =====
//Cấu hình các cột hiển thị trong bảng danh sách quốc gia
export const tableQuocGiaColumn: TableProps<TableQuocGiaColumnDataType>["columns"] = [
  {
    title: "STT",
    dataIndex: "sott",
    key: "sott",
    align: "center",
    width: colWidthByKey.sott,
    ...defaultTableColumnsProps,
  },
  {
    title: "Mã quốc gia",
    dataIndex: "ma",
    key: "ma",
    align: "center",
    width: 100,
    ...defaultTableColumnsProps,
  },
  {
    title: "Tên quốc gia",
    dataIndex: "ten",
    key: "ten",
    ...defaultTableColumnsProps,
  },
  {
    title: "Tên khu vực",
    dataIndex: "ten_khu_vuc",
    key: "ten_khu_vuc",
    width: 120,
    ...defaultTableColumnsProps,
  },
  {
    title: "Tên châu lục",
    dataIndex: "ten_chau_luc",
    key: "ten_chau_luc",
    width: 120,
    ...defaultTableColumnsProps,
  },
  {
    title: "Ngày tạo",
    dataIndex: "ngay_tao",
    key: "ngay_tao",
    width: colWidthByKey.ngay_tao,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Người tạo",
    dataIndex: "nguoi_tao",
    key: "nguoi_tao",
    width: colWidthByKey.nguoi_tao,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Ngày cập nhật",
    dataIndex: "ngay_cap_nhat",
    key: "ngay_cap_nhat",
    width: colWidthByKey.ngay_cap_nhat,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Người cập nhật",
    dataIndex: "nguoi_cap_nhat",
    key: "nguoi_cap_nhat",
    width: colWidthByKey.nguoi_cap_nhat,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Trạng thái",
    dataIndex: "trang_thai_ten",
    key: "trang_thai_ten",
    width: 130,
    align: "center",
    ...defaultTableColumnsProps,
  },
];

//keyof: return ra key của interface TableQuocGiaColumnDataType;
export type TableQuocGiaColumnDataIndex = keyof TableQuocGiaColumnDataType;

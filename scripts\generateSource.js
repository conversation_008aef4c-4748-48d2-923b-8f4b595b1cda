console.log("Generating...");
const Hjson = require("hjson");
const fs = require("fs");

const imgFolders = ["backgrounds", "banners", "icons"];

function genStringResource() {
  try {
    const data = fs.readFileSync("./src/configs/i18n/translations/en.ts", "utf8");
    const json = Hjson.parse(data.replace("export default", "").replace(";", ""));
    const stringName = Object.keys(json);
    fs.writeFileSync(
      "./src/assets/strings.ts",
      `import i18n from "@src/configs/i18n";
import "@i18n";

function strings() {
  const { t } = i18n;
  return {${stringName.map((string, index) => {
    let path = "";
    if (typeof json[string] === "string") {
      path = `
    ${string}: t("${string}", { defaultValue: "${json[string]}" })`;
    } else {
      const keys = Object.keys(json[string]);
      keys.map((val, i) => {
        path += `
    ${string}_${val}: t("${string}.${val}", { defaultValue: "" })${i !== keys.length - 1 ? "," : ""}`;
      });
    }
    return path;
  })}
  };
}
export default strings;
        `,
    );
    console.log(`============== Linked ${stringName.length} string ==============`);
  } catch (err) {
    console.error(err);
  }
}

function genImgs(folderName) {
  fs.readdir(`./src/assets/img/${folderName}/`, function (err, fileName) {
    if (err) {
      console.log(err);
      return;
    }
    fs.writeFileSync(
      `./src/assets/${folderName}.ts`,
      `${fileName
        .map(file => {
          const [name] = file.split(".");

          return `import ${name.split("-").join("_")} from './img/${folderName}/${file}'`;
        })
        .join(";\n")}
      
const ${folderName} = {${fileName.map(file => {
        const [name] = file.split(".");
        const path = `
  ${name.split("-").join("_")}`;
        return path;
      })}
}
export default ${folderName}`,
      {encoding: "utf8", flag: "w"},
    );
    console.log(`============== Linked ${fileName.length} images ==============`);
  });
}
genStringResource();
// imgFolders.map((item) => genImgs(item))

// fs.watch("./src/i18n/translation/en.ts", (eventType, filename) => {
//   if (filename) {
//     genStringResource()
//   }
// })

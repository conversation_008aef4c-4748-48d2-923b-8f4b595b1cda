import React, { memo, useCallback, useContext } from "react";
import { InfoCircleOutlined } from "@ant-design/icons";
import { ConfigProvider, Space } from "antd";

import { Typography } from "..";
import { isEqual } from "lodash";

export interface IFormMessage {
  message: string | React.ReactNode;
  status?: "info" | "error" | "warning";
}

const FormMessageComponent: React.FC<IFormMessage> = (props) => {
  const { message, status = "info" } = props;
  const { theme } = useContext(ConfigProvider.ConfigContext);

  const getColor = useCallback(() => {
    switch (status) {
      case "error":
        return theme?.token?.colorError;
      case "warning":
        return theme?.token?.colorWarning;
      case "info":
      default:
        return theme?.token?.colorInfo;
    }
  }, [theme?.token, status]);

  const getType = useCallback(() => {
    switch (status) {
      case "error":
        return "danger";
      case "warning":
        return "warning";
      case "info":
      default:
        return undefined;
    }
  }, [status]);

  return (
    <Space>
      <InfoCircleOutlined
        style={{
          color: getColor(),
        }}
      />
      {typeof message === "string" ? (
        <Typography
          type="text"
          textProps={{
            color: getColor(),
            type: getType(),
          }}
        >
          {message}
        </Typography>
      ) : (
        message
      )}
    </Space>
  );
};

const FormMessage = memo(FormMessageComponent, isEqual);

export default FormMessage;

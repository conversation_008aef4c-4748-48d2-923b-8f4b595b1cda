import {ReactQuery} from "@src/@types";

//Interface định nghĩa contract cho DanhMucQuocGiaProvider
export interface IDanhMucQuocGiaProvider {
  // Dữ liệu hiển thị
  listQuocGia: CommonExecute.Execute.IDanhMucQuocGia[]; // Danh sách quốc gia hiển thị trong bảng
  listChauLuc: CommonExecute.Execute.IDanhMucChauLuc[]; // Danh sách châu lục cho dropdown
  listKhuVuc: CommonExecute.Execute.IDanhMucKhuVuc[]; // Danh sách khu vực cho dropdown
  tongSoDong: number; // Tổng số bản ghi để hiển thị pagination
  loading: boolean; // Trạng thái loading cho UI

  // Tham số filter và phân trang
  filterParams: ReactQuery.ITimKiemPhanTrangDanhMucQuocGiaParams & ReactQuery.IPhanTrang; // Tham số filter và phân trang

  // Actions
  getListQuocGia: (params?: ReactQuery.ITimKiemPhanTrangDanhMucQuocGiaParams & ReactQuery.IPhanTrang) => Promise<void>; // Tìm kiếm danh sách quốc gia
  getListChauLuc: () => Promise<void>; // Load danh sách châu lục
  getListKhuVuc: (params?: {ma_chau_luc?: string}) => Promise<void>; // Load danh sách khu vực theo châu lục
  getChiTietQuocGia: (data: {ma: string;}) => Promise<CommonExecute.Execute.IDanhMucQuocGia>; // Lấy chi tiết quốc gia
  capNhatChiTietQuocGia: (data: ReactQuery.ICapNhatDanhMucQuocGiaParams, isUpdate?: boolean) => Promise<any>; // Tạo mới hoặc cập nhật quốc gia
  setFilterParams: React.Dispatch<React.SetStateAction<ReactQuery.ITimKiemPhanTrangDanhMucQuocGiaParams & ReactQuery.IPhanTrang>>; // Cập nhật tham số filter
}

import React, {memo, useEffect, useState} from "react";
import FormInput from "../FormInput";
import {isEqual} from "lodash";
import {twMerge} from "tailwind-merge";

/*
  Tối ưu để không bị render lại, chỉ render lại khi value, index, dataIndex thay đổi
  React.memo(...) là HOC (Higher-Order Component) giúp ghi nhớ component, chỉ re-render khi props thay đổi.
*/
const InputCellTableMemo = React.memo(
  ({
    component = "input",
    value,
    onChange,
    index,
    dataIndex,
    disabled = false,
    className = "",
    ...restProps
  }: {
    component: any;
    value: any;
    onChange: (index: number, dataIndex: string, value: string) => void;
    index: number;
    dataIndex: string;
    disabled?: boolean;
    className?: string;
    [key: string]: any;
  }) => {
    /*
    phải có 1 biến innerValue để ngay lập tức lưu giá trị user gõ vào input. nếu lấy giá value={value} được truyền vào 
    thì phải đợi update LIST của TABLE xong (có độ trễ) thì mới hiển thị value -> dẫn đến deplay
    --> dùng biến innerValue để lưu giá trị người dùng nhập vào
    */
    const [innerValue, setInnerValue] = useState(value);

    useEffect(() => {
      if (value !== innerValue) setInnerValue(value); // đồng bộ từ cha khi giá trị thay đổi
    }, [value]);

    const handleChange = (event: any) => {
      const newVal = event.target.value;
      setInnerValue(newVal); // cập nhật ngay cho input
      onChange(index, dataIndex, newVal); // gửi về cha
    };
    return (
      <FormInput
        component={component}
        value={innerValue}
        className={twMerge("border-b-1 !mb-0 !h-[20px] !border-t-0 border-b-gray-50 text-right" + " " + className)}
        allowClear={false}
        variant="underlined"
        onChange={handleChange}
        disabled={disabled}
        {...restProps}
      />
    );
  },
  (
    prevProps: {
      component: any;
      value: any;
      onChange: (index: number, dataIndex: string, value: string) => void;
      index: number;
      dataIndex: string;
      disabled?: boolean;
      [key: string]: any;
    },
    nextProps: {
      component: any;
      value: any;
      onChange: (index: number, dataIndex: string, value: string) => void;
      index: number;
      dataIndex: string;
      disabled?: boolean;
      [key: string]: any;
    },
  ) => prevProps.value === nextProps.value && prevProps.index === nextProps.index && prevProps.dataIndex === nextProps.dataIndex && prevProps.disabled === nextProps.disabled,
);
InputCellTableMemo.displayName = "InputCellTableMemo";

export default memo(InputCellTableMemo, isEqual);

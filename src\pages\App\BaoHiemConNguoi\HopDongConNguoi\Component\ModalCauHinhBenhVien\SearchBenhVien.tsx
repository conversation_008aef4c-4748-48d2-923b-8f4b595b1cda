import React, {useState, useCallback, useEffect} from "react";
import {Input, Button, Space, Typography} from "antd";
import {SearchOutlined, ClearOutlined} from "@ant-design/icons";
import {debounce} from "lodash";

const {Text} = Typography;

interface SearchBenhVienProps {
  placeholder?: string;
  onSearch: (searchTerm: string) => void;
  onClear: () => void;
  loading?: boolean;
  resultCount?: number;
  showResultCount?: boolean;
  className?: string;
  disabled?: boolean;
  initialValue?: string;
}

export const SearchBenhVien: React.FC<SearchBenhVienProps> = ({
  placeholder = "Tìm kiếm theo tên bệnh viện...",
  onSearch,
  onClear,
  loading = false,
  resultCount,
  showResultCount = true,
  className = "",
  disabled = false,
  initialValue = "",
}) => {
  const [searchValue, setSearchValue] = useState(initialValue);
  const [isSearching, setIsSearching] = useState(false);

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce((value: string) => {
      setIsSearching(false);
      onSearch(value.trim());
    }, 400),
    [onSearch],
  );

  // Handle input change
  const handleInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      setSearchValue(value);
      setIsSearching(true);

      debouncedSearch(value);
    },
    [debouncedSearch],
  );

  // Handle clear
  const handleClear = useCallback(() => {
    setSearchValue("");
    setIsSearching(false);
    debouncedSearch.cancel(); // Cancel pending debounced calls
    onClear();
  }, [onClear, debouncedSearch]);

  // Handle Enter key
  const handlePressEnter = useCallback(() => {
    debouncedSearch.cancel(); // Cancel debounced call
    setIsSearching(false);
    onSearch(searchValue.trim());
  }, [searchValue, onSearch, debouncedSearch]);

  // Update internal state when initialValue changes
  useEffect(() => {
    setSearchValue(initialValue);
  }, [initialValue]);

  // Cleanup debounced function on unmount
  useEffect(() => {
    return () => {
      debouncedSearch.cancel();
    };
  }, [debouncedSearch]);

  const showLoading = loading || isSearching;
  const hasSearchValue = searchValue.trim().length > 0;

  return (
    <div className={`search-benh-vien ${className}`}>
      <Space direction="vertical" size="small">
        <Space.Compact style={{width: "30%"}}>
          <Input
            placeholder={placeholder}
            value={searchValue}
            onChange={handleInputChange}
            onPressEnter={handlePressEnter}
            prefix={<SearchOutlined />}
            disabled={disabled}
            allowClear={false} // We'll use custom clear button
          />
          {hasSearchValue && <Button icon={<ClearOutlined />} onClick={handleClear} disabled={disabled || showLoading} title="Xóa tìm kiếm" />}
        </Space.Compact>
      </Space>
    </div>
  );
};

export default SearchBenhVien;

import React, {memo, PropsWithChildren} from "react";
import {Skeleton as AntSkeleton} from "antd";
import {SkeletonInputProps} from "antd/es/skeleton/Input";
import {twMerge} from "tailwind-merge";
import {isEqual} from "lodash";

export interface InputProps extends SkeletonInputProps {
  loading?: boolean;
}

const InputSkeletonComponent: React.FC<PropsWithChildren<InputProps>> = props => {
  const {className = "", loading = true, children, active = true, ...etc} = props;

  return <React.Fragment>{loading ? <AntSkeleton.Input className={twMerge("custom-input-skeleton", className)} active={active} {...etc} /> : children}</React.Fragment>;
};

const InputSkeleton = memo(InputSkeletonComponent, isEqual);

export default InputSkeleton;

import {IFormInput, ReactQuery} from "@src/@types";
import {colWidthByKey, defaultTableColumnsProps} from "@src/hooks";
import {TableProps} from "antd";
export interface TableBoMaNguyenTeDataType {
  key: number | string;
  stt?: number;
  ma?: string;
  ma_doi_tac_ql?: string;
  ten?: string;
  doi_tac?: string;
  ngay_tao?: number;
  nguoi_tao?: string;
  ngay_cap_nhat?: string;
  nguoi_cap_nhat?: string;
  trang_thai?: string;
  trang_thai_ten?: string;
  doi_tac_ql_ten_tat?: string;
  ma_doi_tac?: string;
  nghiep_vu?: string;
}

// ĐỊNH NGHĨA Column RENDER RA TỪNG Ô TRONG CỘT
//đọc https://ant.design/components/table#column để biết thêm về column
export const BoMaNguyenTeColumns: TableProps<TableBoMaNguyenTeDataType>["columns"] = [
  {title: "STT", dataIndex: "stt", width: colWidthByKey.sott, align: "center", ...defaultTableColumnsProps},
  {title: "Mã nguyên tệ", dataIndex: "ma", key: "ma", width: 120, align: "center", ...defaultTableColumnsProps},
  {title: "Nguyên tệ", dataIndex: "ten", key: "ten", align: "left", ...defaultTableColumnsProps},
  {title: "Đối tác", dataIndex: "doi_tac_ql_ten_tat", key: "doi_tac_ql_ten_tat", width: 200, align: "center", ...defaultTableColumnsProps},
  {title: "Ngày tạo", dataIndex: "ngay_tao", key: "ngay_tao", width: 130, align: "center", ...defaultTableColumnsProps},
  {title: "Người tạo", dataIndex: "nguoi_tao", key: "nguoi_tao", width: 110, align: "center", ...defaultTableColumnsProps},
  {title: "Ngày cập nhật", dataIndex: "ngay_cap_nhat", key: "ngay_cap_nhat", width: 130, align: "center", ...defaultTableColumnsProps},
  {title: "Người cập nhật", dataIndex: "nguoi_cap_nhat", key: "nguoi_cap_nhat", width: 120, align: "center", ...defaultTableColumnsProps},
  {title: "Trạng thái", dataIndex: "trang_thai_ten", key: "trang_thai_ten", width: 130, align: "center", ...defaultTableColumnsProps},
];

export const setFormFields = (form: any, chiTietHopDong: any) => {
  if (chiTietHopDong) {
    form.setFields([
      {
        name: "ten",
        value: chiTietHopDong.ten || "",
      },
      {
        name: "ma",
        value: chiTietHopDong.ma || "",
      },
      {
        name: "loai",
        value: chiTietHopDong.loai || "",
      },
      {
        name: "kieu_ad",
        value: chiTietHopDong.kieu_ad || "",
      },
    ]);
  }
};

export const radioItemTrangThaiHopDongTable = [
  {value: "Đang sử dụng", text: "Đang sử dụng"},
  {value: "Ngưng sử dụng", text: "Ngưng sử dụng"},
];

//option select trạng thái
export const optionTrangThaiHopDongSelect = [
  {ma: "D", ten: "Đang sử dụng"},
  {ma: "K", ten: "Ngưng sử dụng"},
];
//Form
export interface IFormTimKiemBoMaNguyenTeFieldsConfig {
  ma_doi_tac_ql: IFormInput;
  ma: IFormInput;
  ten: IFormInput;
  trang_thai: IFormInput;
}

export const FormTimKiemBoMaNguyenTe: IFormTimKiemBoMaNguyenTeFieldsConfig = {
  ma: {
    component: "input",
    name: "ma",
    label: "Mã nguyên tệ",
    placeholder: "Chọn mã nguyên tệ",
    className: "!mb-0",
  },
  ma_doi_tac_ql: {
    component: "select",
    name: "ma_doi_tac_ql",
    label: "Đối tác",
    placeholder: "Chọn đối tác",
    className: "!mb-0",
  },
  ten: {
    component: "input",
    name: "ten",
    label: "Nguyên tệ",
    placeholder: "Chọn nguyên tệ",
    className: "!mb-0",
  },
  trang_thai: {
    component: "select",
    name: "trang_thai",
    label: "Trạng thái",
    placeholder: "Chọn trạng thái",
    className: "!mb-0",
  },
};
// keyof: return ra kay của interface TableBoMaNguyenTeColumDataType;
export type TableBoMaNguyenTeDataIndex = keyof TableBoMaNguyenTeDataType;
// defaultFormValue tìm kiếm phân trang bộ mã nguyên tệ
export const defaultFormValue: ReactQuery.ITimKiemPhanTrangBoMaNguyenTeParams = {
  ma_doi_tac_ql: "",
  ma: "",
  ten: "",
  trang_thai: "",
  // trang: 1,
  // so_dong: 10,
};

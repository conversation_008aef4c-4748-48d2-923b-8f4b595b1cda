import Footer from "@src/components/Footer";
import Header from "@src/components/Header";
import Sidenav from "@src/components/Sidenav";
import {useProfile} from "@src/hooks";
import {Affix, Drawer, Layout} from "antd";
import {isEqual} from "lodash";
import {memo, useCallback, useState, useEffect} from "react";
import {useLocation} from "react-router-dom";
import "./index.default.scss";
import {useMenuNguoiDung} from "@src/hooks/menuNguoiDungStore";

const {Header: AntHeader, Content, Sider} = Layout;
interface MainProps {
  children: any;
}
const Main = ({children}: MainProps) => {
  const {profile} = useProfile();
  const {menuNguoiDung} = useMenuNguoiDung();
  const [visible, setVisible] = useState(false);
  const [sidenavColor, setSidenavColor] = useState("#1890ff");
  const [sidenavType, setSidenavType] = useState("transparent");
  const [fixed, setFixed] = useState(false);
  const [sidebarHovered, setSidebarHovered] = useState(false);

  const openDrawer = () => setVisible(!visible);
  // const handleSidenavType = type => setSidenavType(type);
  // const handleSidenavColor = color => setSidenavColor(color);
  // const handleFixedNavbar = type => setFixed(type);

  const handleSidebarHover = (isHovered: boolean) => {
    // Only apply hover effect on desktop (screen width >= 992px)
    if (window.innerWidth >= 992) {
      setSidebarHovered(isHovered);
    }
  };

  // Handle window resize to reset sidebar state on mobile
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 992) {
        setSidebarHovered(true); // Always expanded on mobile
      } else {
        setSidebarHovered(false); // Start collapsed on desktop
      }
    };

    // Set initial state
    handleResize();

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  let {pathname} = useLocation();
  pathname = pathname.replace("/", "");

  //lấy ra title của Route theo url
  const getRouteTitleUrl = useCallback((): string => {
    try {
      let routerTitle = "";
      menuNguoiDung.map(item => {
        if (item.url === "/" + pathname) {
          routerTitle = item.ten || "";
          return;
        }
      });

      // Fallback title for Dashboard when accessing root path
      if (!routerTitle && pathname === "") {
        routerTitle = "Dashboard";
      }

      return routerTitle;
    } catch (error) {
      console.log("getRouteNameByUrl err", error);
      return "";
    }
  }, [menuNguoiDung, pathname]);

  return (
    <Layout
      className={`layout-dashboard ${sidebarHovered ? "sidebar-expanded" : "sidebar-collapsed"} ${pathname === "admin/tai-khoan" ? "layout-profile" : ""} ${pathname === "rtl" ? "layout-dashboard-rtl" : ""}`}>
      {profile?.token && menuNguoiDung.length > 1 && pathname !== "dang-nhap" && (
        <Drawer
          title={false}
          placement={"left"}
          closable={false}
          onClose={() => setVisible(false)}
          open={visible}
          key={"left"}
          width={350} //set width của side menu
          className={`drawer-sidebar ${pathname === "rtl" ? "drawer-sidebar-rtl" : ""} `}>
          <Layout className={`layout-dashboard ${pathname === "rtl" ? "layout-dashboard-rtl" : ""}`}>
            <Sider
              trigger={null}
              width={350} //set width của side menu
              theme="light"
              className={`sider-primary ant-layout-sider-primary ${sidenavType === "#fff" ? "active-route" : ""}`}
              style={{background: sidenavType}}>
              <Sidenav color={sidenavColor} />
            </Sider>
          </Layout>
        </Drawer>
      )}

      {profile?.token && menuNguoiDung.length > 1 && pathname !== "dang-nhap" && (
        <Sider
          breakpoint="lg"
          collapsedWidth="0"
          onCollapse={(collapsed, type) => {
            // console.log(collapsed, type);
          }}
          trigger={null}
          width={sidebarHovered ? 350 : 80} //set width của side menu
          theme="light"
          className={`sider-primary ant-layout-sider-primary ant-layout-sider-primary-custom ${sidenavType === "#fff" ? "active-route" : ""}`}
          style={{background: sidenavType}}
          onMouseEnter={() => handleSidebarHover(true)}
          onMouseLeave={() => handleSidebarHover(false)}>
          <Sidenav color={sidenavColor} isCollapsed={!sidebarHovered} />
        </Sider>
      )}

      {profile?.token && menuNguoiDung.length > 1 && pathname !== "dang-nhap" && (
        <Layout>
          {fixed ? (
            <Affix>
              <AntHeader className={`${fixed ? "ant-header-fixed" : ""}`}>
                <Header
                  onPress={openDrawer}
                  name={getRouteTitleUrl()}
                  // subName={pathname}
                  // handleSidenavColor={handleSidenavColor}
                  // handleSidenavType={handleSidenavType}
                  // handleFixedNavbar={handleFixedNavbar}
                />
              </AntHeader>
            </Affix>
          ) : (
            <AntHeader className={`${fixed ? "ant-header-fixed" : ""}`}>
              <Header
                onPress={openDrawer}
                name={getRouteTitleUrl()}
                // subName={pathname}
                // handleSidenavColor={handleSidenavColor}
                // handleSidenavType={handleSidenavType}
                // handleFixedNavbar={handleFixedNavbar}
              />
            </AntHeader>
          )}
          <Content className="content-ant">{children}</Content>
          <Footer />
        </Layout>
      )}
      {(!profile?.token || pathname == "dang-nhap") && <Content className="content-ant">{children}</Content>}
    </Layout>
  );
};

export default memo(Main, isEqual);

import React, {PropsWith<PERSON><PERSON>dren, useCallback, useEffect, useMemo, useState} from "react";
import {DashboardContext} from "./index.context";
import {IDashboardContextProps, IDashboardData} from "./index.model";
import {DASHBOARD_REFRESH_INTERVAL, MOCK_DATA_CONFIG} from "./index.configs";

// Mock Data Generator
const generateMockData = (): IDashboardData => {
  return {
    stats: {
      totalRevenue: 1540000510382,
      retainedRevenue: 540000510382,
      totalCompanies: 100000000,
      targetAmount: 195000000,
      completionRate: 78.5,
    },
    monthlyStats: MOCK_DATA_CONFIG.MONTHS.map((month, index) => ({
      month,
      currentYear: Math.floor(Math.random() * 50000000) + 150000000,
      previousYear: Math.floor(Math.random() * 40000000) + 120000000,
      target: Math.floor(Math.random() * 50000000) + 150000000,
    })),
    insuranceCategories: [
      {
        id: "1",
        name: "<PERSON><PERSON><PERSON> hiể<PERSON> xe ô tô",
        revenue: 200000000000,
        claims: 100000000000,
        claimsRate: 50,
      },
      {
        id: "2",
        name: "B<PERSON>o hiểm xe máy",
        revenue: 200000000000,
        claims: 100000000000,
        claimsRate: 50,
      },
      {
        id: "3",
        name: "Bảo hiểm con người",
        revenue: 200000000000,
        claims: 100000000000,
        claimsRate: 50,
      },
      {
        id: "4",
        name: "Bảo hiểm tài sản",
        revenue: 200000000000,
        claims: 100000000000,
        claimsRate: 50,
      },
      {
        id: "5",
        name: "Bảo hiểm du lịch",
        revenue: 200000000000,
        claims: 100000000000,
        claimsRate: 50,
      },
      {
        id: "6",
        name: "Bảo hiểm khác",
        revenue: 200000000000,
        claims: 100000000000,
        claimsRate: 50,
      },
    ],
    topClients: [
      {
        id: 1,
        name: "ESCS - Hà Thành",
        revenue: 20000000000,
        trend: "up" as const,
        trendValue: 2,
      },
      {
        id: 2,
        name: "ESCS - Đông Đô",
        revenue: 19000000000,
        trend: "up" as const,
        trendValue: 1,
      },
      {
        id: 3,
        name: "ESCS - Thăng Long",
        revenue: 2000000000,
        trend: "down" as const,
        trendValue: 3,
      },
      {
        id: 4,
        name: "ESCS - Bến Thành",
        revenue: 2000000000,
        trend: "up" as const,
        trendValue: 1,
      },
      {
        id: 5,
        name: "ESCS - Hội Phòng",
        revenue: 2000000000,
        trend: "neutral" as const,
      },
    ],
    topSellers: [
      {
        id: 1,
        name: "Nguyễn Công Kỳ Anh",
        revenue: 2000000000,
        trend: "up" as const,
        trendValue: 2,
      },
      {
        id: 2,
        name: "Nguyễn Xuân Thành",
        revenue: 2000000000,
        trend: "up" as const,
        trendValue: 1,
      },
      {
        id: 3,
        name: "Vũ Văn Giang",
        revenue: 2000000000,
        trend: "down" as const,
        trendValue: 3,
      },
      {
        id: 4,
        name: "Vương Ngọc Hiếu",
        revenue: 2000000000,
        trend: "up" as const,
        trendValue: 1,
      },
      {
        id: 5,
        name: "Đặng Quốc Đạt",
        revenue: 2000000000,
        trend: "neutral" as const,
      },
    ],
    revenueByChannel: [
      {
        channel: "App",
        amount: 620000000,
        percentage: 40.3,
      },
      {
        channel: "Website",
        amount: 465000000,
        percentage: 30.2,
      },
      {
        channel: "Đại lý",
        amount: 310000000,
        percentage: 20.1,
      },
      {
        channel: "Khác",
        amount: 145000000,
        percentage: 9.4,
      },
    ],
    barLineChartData: {
      labels: ["T1", "T2", "T3", "T4", "T5", "T6", "T7", "T8", "T9", "T10", "T11", "T12"],
      barValues: [180000000000, 195000000000, 210000000000, 185000000000, 220000000000, 235000000000, 250000000000, 240000000000, 260000000000, 275000000000, 290000000000, 310000000000],
      lineValues: [5, 8, 7, -2, 12, 15, 18, 10, 20, 22, 25, 28],
    },
  };
};

const DashboardProvider: React.FC<PropsWithChildren> = ({children}) => {
  const [data, setData] = useState<IDashboardData>(generateMockData());
  const [loading, setLoading] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState<"week" | "month" | "year">("month");

  const refreshData = useCallback(() => {
    setLoading(true);
    // Simulate API call
    setTimeout(() => {
      setData(generateMockData());
      setLoading(false);
    }, 1000);
  }, []);

  // Auto refresh data
  useEffect(() => {
    const interval = setInterval(refreshData, DASHBOARD_REFRESH_INTERVAL);
    return () => clearInterval(interval);
  }, [refreshData]);

  const contextValue: IDashboardContextProps = useMemo(
    () => ({
      data,
      loading,
      selectedPeriod,
      setSelectedPeriod,
      refreshData,
    }),
    [data, loading, selectedPeriod, refreshData],
  );

  return <DashboardContext.Provider value={contextValue}>{children}</DashboardContext.Provider>;
};

DashboardProvider.displayName = "DashboardProvider";

export default DashboardProvider;

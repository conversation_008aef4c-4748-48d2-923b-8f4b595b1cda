import {CheckCircleOutlined, ClearOutlined, SearchOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {Button, FormInput, Highlighter,TableFilterDropdown} from "@src/components";
import {defaultPaginationTableProps, defaultTableProps, fillRowTableEmpty} from "@src/hooks";
import {Checkbox, Col, Flex, Form, Input, InputRef, Modal, Row, Table, TableColumnType, Tag, Tooltip} from "antd";
import {FilterDropdownProps} from "antd/es/table/interface";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useImperativeHandle, useMemo, useRef, useState} from "react";
import {useQuanLyTaiKhoanDonViThuHoContext} from "../index.context";
import "../index.default.scss";

// Types
export interface IModalChonTaiKhoanQuanLyRef {
  open: (maTaiKhoanCha: string) => void;
  close: () => void;
}

export interface ModalChonTaiKhoanQuanLyProps {
  onSelectTaiKhoanQuanLy: (danhSachMaChon: string[]) => void;
}

export interface TableTaiKhoanQuanLyDataType {
  key: string;
  ma_doi_tac?: string;
  ma_dvi?: string;
  ma?: string;
  ten?: string;
  email?: string;
  dthoai?: string;
  chon?: number; // 0 = chưa chọn, 1 = đã chọn
}
// Columns definition
const taiKhoanQuanLyColumns: TableColumnType<TableTaiKhoanQuanLyDataType>[] = [
  {
    title: "Chọn",
    dataIndex: "chon",
    key: "chon",
    width: 60,
    align: "center",
    render: (chon: any, record) => {
      if (record.key.toString().includes("empty")) return "";
      return (
        <Checkbox
          checked={Number(chon) === 1}
          onChange={() => {}} // click được xử lý ở onRow
        />
      );
    },
  },
  {title: "Mã", dataIndex: "ma", key: "ma", width: 120, align: "center"},
  {title: "Tên", dataIndex: "ten", key: "ten", width: 200, align: "center"},
  {title: "Email", dataIndex: "email", key: "email", width: 180, align: "center"},
  {title: "Điện thoại", dataIndex: "dthoai", key: "dthoai", width: 120, align: "center"},
];

type DataIndex = keyof TableTaiKhoanQuanLyDataType;

const ModalChonTaiKhoanQuanLyComponent = forwardRef<IModalChonTaiKhoanQuanLyRef, ModalChonTaiKhoanQuanLyProps>(({onSelectTaiKhoanQuanLy}: ModalChonTaiKhoanQuanLyProps, ref) => {
  useImperativeHandle(ref, () => ({
    open: (maTaiKhoanCha: string) => {
      setMaTaiKhoanCha(maTaiKhoanCha);
      setIsOpen(true);
      initData(maTaiKhoanCha);
    },
    close: () => setIsOpen(false),
  }));

  const {loading, layDanhSachTaiKhoanQuanLy, capNhatDanhSachTaiKhoanQuanLy,listTaiKhoanDonViThuHo} = useQuanLyTaiKhoanDonViThuHoContext();
  const [isOpen, setIsOpen] = useState(false);
  const [maTaiKhoanCha, setMaTaiKhoanCha] = useState<string>("");
  const [danhSachTaiKhoan, setDanhSachTaiKhoan] = useState<TableTaiKhoanQuanLyDataType[]>([]);
  const [danhSachMaChon, setDanhSachMaChon] = useState<string[]>([]);
  const [searchText, setSearchText] = useState("");
  const [searchedColumn, setSearchedColumn] = useState<DataIndex | "">("");
  const refSearchInputTable = useRef<InputRef>(null);

  const initData = async (maTaiKhoanCha: string) => {
  try {
    const listAll = await layDanhSachTaiKhoanQuanLy(maTaiKhoanCha);
    const tableData = (Array.isArray(listAll) ? listAll : []).map((item:any, i:number) => ({
      key: String(i),
      ma_doi_tac: item.ma_doi_tac,
      ma_dvi: item.ma_dvi,
      ma: item.ma,
      ten: item.ten,
      email: item.email,
      dthoai: item.dthoai,
      chon: Number(item?.chon) === 1 ? 1 : 0,
    }));
    setDanhSachTaiKhoan(tableData);
    setDanhSachMaChon(tableData.filter(r => r.chon === 1 && r.ma).map(r => r.ma!));
  } catch (e) {
    setDanhSachTaiKhoan([]); setDanhSachMaChon([]);
  }
};

  const dataTableTaiKhoanQuanLy = useMemo<TableTaiKhoanQuanLyDataType[]>(() => {
    try {
      const tableData = danhSachTaiKhoan.map((item, index) => ({
        ...item,
        key: item.key || index.toString(),
      }));

      const arrEmptyRow: TableTaiKhoanQuanLyDataType[] = fillRowTableEmpty(tableData.length, defaultPaginationTableProps.defaultPageSize);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      console.log("dataTableTaiKhoanQuanLy error", error);
      return [];
    }
  }, [danhSachTaiKhoan]);

  const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: DataIndex) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);

  const handleReset = useCallback(
    (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: DataIndex) => {
      clearFilters();
      setSearchText("");
      handleSearch([""], confirm, dataIndex);
    },
    [handleSearch],
  );

  const getColumnSearchProps = (
  dataIndex: DataIndex,
  title: string
): TableColumnType<TableTaiKhoanQuanLyDataType> => ({
  // dùng component TableFilterDropdown giống trang danh sách
  filterDropdown:
    dataIndex !== "chon"
      ? (props) => (
          <TableFilterDropdown
            ref={refSearchInputTable}
            title={title}
            dataIndex={dataIndex as any}
            handleSearch={(selectedKeys, confirm) => {
              confirm();
              setSearchText(selectedKeys[0]);
              setSearchedColumn(dataIndex);
            }}
            handleReset={(clearFilters, confirm) => {
              clearFilters?.();
              setSearchText("");
              confirm();
              setSearchedColumn("");
            }}
            {...props}
          />
        )
      : undefined,

  // hiện icon tìm kiếm trên header cột
  filterIcon: (filtered) => (
    <SearchOutlined style={{ opacity: filtered ? 1 : 0.45 }} />
  ),

  onFilter: (value, record) =>
    record[dataIndex]?.toString().toLowerCase()
      .includes((value as string).toLowerCase()) || false,

  filterDropdownProps: {
    onOpenChange(open) {
      if (open) setTimeout(() => refSearchInputTable.current?.select(), 100);
    },
  },

  render: (text, record) => {
    if (dataIndex === "chon") {
      return record.key.toString().includes("empty") ? (
        ""
      ) : (
        <Checkbox
          checked={danhSachMaChon.includes(record.ma || "")}
          onChange={() => {}}
        />
      );
    }
    return searchedColumn === dataIndex
      ? <Highlighter searchWords={[searchText]} textToHighlight={text ? text.toString() : ""} />
      : (text !== undefined
          ? text
          : <Tag color={"transparent"} className="!text-white text-[11px]">{'\u00A0'}</Tag>);
  },
});

  const handleRowClick = (record: TableTaiKhoanQuanLyDataType) => {
    if (record.key.toString().includes("empty") || !record.ma) return;

    const ma = record.ma;
    const isCurrentlySelected = danhSachMaChon.includes(ma);

    let newDanhSachMaChon: string[];
    if (isCurrentlySelected) {
      // Bỏ chọn
      newDanhSachMaChon = danhSachMaChon.filter(item => item !== ma);
    } else {
      // Chọn thêm
      newDanhSachMaChon = [...danhSachMaChon, ma];
    }
    setDanhSachMaChon(newDanhSachMaChon);
    // Cập nhật state hiển thị
    setDanhSachTaiKhoan(prev => prev.map(item => (item.ma === ma ? {...item, chon: isCurrentlySelected ? 0 : 1} : item)));
  };
//handler cho nút “Chọn (n)” trong modal
  const onPressXacNhan = async () => {
  try {
    const ds = danhSachMaChon.map(ma => ({ma}));
    const tkCha = listTaiKhoanDonViThuHo.find(tk => tk.ma === maTaiKhoanCha);
    await capNhatDanhSachTaiKhoanQuanLy({
      ma: maTaiKhoanCha,
      ma_dvi: tkCha?.ma_dvi,  
      ds
    });
    onSelectTaiKhoanQuanLy(danhSachMaChon);
    setIsOpen(false);
  } catch {}
};

  const renderFooter = () => {
    return (
      <Form.Item>
        <Button type="default" onClick={() => setIsOpen(false)} className="mr-2">
          Hủy
        </Button>
        <Button type="primary" onClick={onPressXacNhan} icon={<CheckCircleOutlined />} iconPosition="end">
          Chọn ({danhSachMaChon.length})
        </Button>
      </Form.Item>
    );
  };

  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        maskClosable={false}
        title="Chọn tài khoản quản lý"
        className="modal-chon-tai-khoan-quan-ly"
        open={isOpen}
        onOk={() => setIsOpen(false)}
        onCancel={() => setIsOpen(false)}
        width={{
          xs: "65vw",
          sm: "65vw",
          md: "65vw",
          lg: "65vw",
          xl: "65vw",
          xxl: "65vw",
        }}
        styles={{
          body: {
            maxHeight: '70vh',                      //phần body tối đa chiếm 70% chiều cao màn hình
            overflowY: 'auto',                      //có scroll dọc nếu nội dung dài
            paddingTop: 8,
            paddingBottom: 16,
          },
          content: { borderRadius: 10 },            
        }}
        footer={renderFooter()}>
        <Table<TableTaiKhoanQuanLyDataType>
          {...defaultTableProps}
          size="small"  
          className="tai-khoan-quan-ly"
          loading={loading}
          onRow={record => ({
            style: {cursor: loading ? "progress" : "pointer"},
            onClick: () => handleRowClick(record),
          })}
          columns={taiKhoanQuanLyColumns.map(col =>
            col.key === "chon"
              ? {
                  ...col,
                  render: (_: any, record) =>
                    record.key.toString().includes("empty") ? (
                      ""
                    ) : (
                      <Checkbox
                        checked={danhSachMaChon.includes(record.ma || "")}
                        onChange={() => {}} // click xử lý ở onRow
                      />
                    ),
                }
              : {
                  ...col,
                  ...(col.key && typeof col.title === "string" && col.key !== "chon" ? getColumnSearchProps(col.key as keyof TableTaiKhoanQuanLyDataType, col.title as string) : {}),
                },
          )}
          dataSource={dataTableTaiKhoanQuanLy}
          pagination={{
            ...defaultPaginationTableProps,
            total: danhSachTaiKhoan.length,
            showSizeChanger: false,
          }}
        />
      </Modal>
    </Flex>
  );
});

ModalChonTaiKhoanQuanLyComponent.displayName = "ModalChonTaiKhoanQuanLyComponent";
export const ModalChonTaiKhoanQuanLy = memo(ModalChonTaiKhoanQuanLyComponent, isEqual);



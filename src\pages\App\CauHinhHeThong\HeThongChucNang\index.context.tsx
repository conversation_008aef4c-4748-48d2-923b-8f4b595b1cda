import { createContext, useContext } from "react";
import { HeThongChucNangContextProps } from "./index.model";

//createContext : khai báo 1 context mà component có thể cung cấp hoặc đọc
export const HeThongChucNangContext = createContext<HeThongChucNangContextProps>({
  //khởi tạo các giá trị mặc định, trong context này có giá trị là onSubmit
  danhSachHeThongChucNang: [],
  loading: false,
  onUpdateHeThongChucNang: () => Promise.resolve(),
  layDanhSachChucNangPhanTrang: () => Promise.resolve(),
  tongSoDong: 0,
  layChiTietChucNang: () => Promise.resolve(null),
  defaultFormValue: {},
  onSyncChucNang: () => Promise.resolve(),
});

//useContext(someContext): là React Hook cho phép bạn đọc và subscribe context từ component của mình.
// file .context muốn truy câp context thì sử dụng useLoginContext : const {onSubmit} = useLoginContext();
export const useHeThongChucNangContext = () => useContext(HeThongChucNangContext);

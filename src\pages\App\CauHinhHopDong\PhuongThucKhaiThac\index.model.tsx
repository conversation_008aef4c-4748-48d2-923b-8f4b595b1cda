import {ReactQuery} from "@src/@types";

// khai báo interface props Context của Login
export interface PhuongThucKhaiThacProps {
  listDoiTac: Array<CommonExecute.Execute.IDoiTac>;
  danhSachPhuongThucKhaiThac: Array<CommonExecute.Execute.IDanhSachPhuongThucKhaiThac>;
  loading: boolean;
  LayDanhSachPhuongThuckhaiThac: (params: ReactQuery.ITimKiemPhanTrangPhuongThucKhaiThacParams) => void;
  tongSoDong: number;
  layChiTietPhuongThucKhaiThac: (params: ReactQuery.IlayChiTietPhuongThucKhaiThacParams) => Promise<CommonExecute.Execute.IChiTietPhuongThucKhaiThac | null>;
  defaultFormValue: object;
  updatePhuongThucKhaiThac: (params: ReactQuery.IUpdatePhuongThucKhaiThacParams) => void;
  getListDoiTac: () => Promise<void>;
}

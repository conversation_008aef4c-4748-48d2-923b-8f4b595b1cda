import React, {memo} from "react";
import strings from "@src/assets/strings";
import {ThemeMode, useSetting} from "@src/hooks";
import Switch from "../Switch";
import {isEqual} from "lodash";

const LanguageControlComponent: React.FC = () => {
  const {themeMode, setThemeMode} = useSetting();

  return (
    <Switch
      defaultChecked={themeMode === ThemeMode.dark}
      onChange={e => {
        setThemeMode(e ? ThemeMode.dark : ThemeMode.light);
      }}
      title={strings().label_dark_mode}
      labelDirection="horizontal"
    />
  );
};

const LanguageControl = memo(LanguageControlComponent, isEqual);

export default LanguageControl;

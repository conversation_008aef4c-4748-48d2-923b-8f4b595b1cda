// Dashboard Styles
.dashboard-content {
  min-height: calc(100vh - 64px);
  // padding: 20px;
  background-color: #f5f5f5;

  // Stats Cards
  .ant-statistic {
    .ant-statistic-title {
      font-size: 14px;
      font-weight: 500;
      color: #8c8c8c;
      margin-bottom: 8px;
    }

    .ant-statistic-content {
      font-size: 24px;
      font-weight: 600;
      color: #262626;

      .ant-statistic-content-value {
        font-size: 24px;
      }

      .ant-statistic-content-prefix {
        margin-right: 8px;
      }

      .ant-statistic-content-suffix {
        margin-left: 8px;
        font-size: 14px;
      }
    }
  }

  // Stats Cards New Design
  .stats-card {
    border-radius: 12px;
    border: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    padding: 0;
    overflow: hidden;
    height: 100%;
    .ant-card {
      height: 100%;
    }
    .ant-card-body {
      padding: 8px;
      display: flex;
      align-items: center;
      gap: 16px;
      height: 100%;
    }

    .stats-icon {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      color: white;
    }

    .stats-content {
      flex: 1;
    }

    .stats-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 20px;
      color: #000000;
      margin-bottom: 4px;
      font-weight: 700;
    }

    .stats-value {
      font-size: 20px;
      font-weight: 600;
      color: #262626;
      margin-bottom: 4px;
      line-height: 1.2;
    }

    .stats-subtitle {
      font-size: 12px;
      color: #8c8c8c;
      font-weight: 400;
    }

    // Card specific colors
    &.stats-card-revenue .stats-icon {
      background: linear-gradient(135deg, #52c41a, #73d13d);
    }

    &.stats-card-retained .stats-icon {
      background: linear-gradient(135deg, #722ed1, #9254de);
    }

    &.stats-card-sales .stats-icon {
      background: linear-gradient(135deg, #13c2c2, #36cfc9);
    }

    &.stats-card-target .stats-icon {
      background: linear-gradient(135deg, #faad14, #ffc53d);
    }
  }

  // Card styling
  .ant-card {
    height: 100%;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #f0f0f0;

    .ant-card-head {
      border-bottom: 1px solid #f0f0f0;

      .ant-card-head-title {
        font-size: 20px;
        font-weight: 700;
        color: #262626;
      }
    }

    .ant-card-body {
      padding: 8px;
    }

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      transition: box-shadow 0.3s ease;
    }
  }

  .stats-chart-card.ant-card .ant-card-body {
    display: flex !important;
    flex-direction: row;
  }
  // Chart containers
  .line-chart-container,
  .pie-chart-container,
  .bar-chart-container {
    width: 100%;
    height: 100%;

    .apexcharts-canvas {
      margin: 0 auto;
    }

    .apexcharts-legend {
      justify-content: center !important;
    }
  }

  // Table styling
  .ant-table {
    .ant-table-thead > tr > th {
      background-color: #fafafa;
      font-weight: 600;
      color: #262626;
      border-bottom: 1px solid #f0f0f0;
    }

    .ant-table-tbody > tr > td {
      border-bottom: 1px solid #f5f5f5;
    }

    .ant-table-tbody > tr:hover > td {
      background-color: #f5f5f5;
    }
  }

  // Progress bar styling
  .ant-progress {
    .ant-progress-text {
      font-size: 12px;
      color: #8c8c8c;
    }
  }

  // Avatar styling
  .ant-avatar {
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
  }

  // Button styling
  .ant-btn {
    border-radius: 6px;
    font-weight: 500;

    &.ant-btn-primary {
      background-color: #1890ff;
      border-color: #1890ff;

      &:hover {
        background-color: #40a9ff;
        border-color: #40a9ff;
      }
    }
  }

  // Select styling
  .ant-select {
    .ant-select-selector {
      border-radius: 6px;
    }
  }

  // Header controls styling
  .dashboard-header-controls {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 12px;
    margin-bottom: 24px;
  }

  // Stats Chart Card
  .stats-chart-card {
    .ant-card-body {
      padding: 20px;
      display: flex;
      flex-direction: column;
      height: 100%;
    }

    .chart-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }

    .chart-title {
      font-size: 16px;
      font-weight: 600;
      color: #262626;
    }

    .chart-tabs {
      display: flex;
      gap: 8px;

      .ant-btn {
        border-radius: 20px;
        font-size: 12px;
        height: 28px;
        padding: 0 16px;
      }
    }

    .chart-values {
      display: flex;
      gap: 24px;
      margin-bottom: 16px;
      padding: 16px;
      background: #f8f9fa;
      border-radius: 8px;
    }

    .value-item {
      flex: 1;

      .value-label {
        font-size: 12px;
        color: #8c8c8c;
        margin-bottom: 4px;
        text-transform: uppercase;
      }

      .value-amount {
        font-size: 16px;
        font-weight: 600;
        color: #262626;
      }

      &.primary .value-amount {
        color: #52c41a;
      }

      &.secondary .value-amount {
        color: #faad14;
      }
    }

    .chart-container {
      flex: 1;
      position: relative;
    }
  }

  // Top Tables Cards
  .top-clients-card,
  .top-sellers-card {
    .ant-card-head {
      border-bottom: 1px solid #f0f0f0;
      padding: 8px 8px;

      .ant-card-head-title {
        font-size: 20px;
        font-weight: 700;
        color: #262626;
      }
    }

    .ant-card-body {
      padding: 0;
    }

    .ant-table {
      .ant-table-thead > tr > th {
        background: #fafafa;
        border-bottom: 1px solid #f0f0f0;
        padding: 8px 8px;
        font-size: 12px;
        font-weight: 600;
        color: #8c8c8c;
        text-transform: uppercase;

        &:first-child {
          padding-left: 20px;
        }

        &:last-child {
          padding-right: 20px;
        }
      }

      .ant-table-tbody > tr > td {
        padding: 8px 8px;
        border-bottom: 1px solid #f5f5f5;
        vertical-align: middle;

        &:first-child {
          padding-left: 20px;
        }

        &:last-child {
          padding-right: 20px;
        }
      }

      .ant-table-tbody > tr:hover > td {
        background: #fafafa;
      }

      .ant-table-tbody > tr:last-child > td {
        border-bottom: none;
      }

      // Custom styling for ranking numbers
      .ant-table-tbody > tr > td:first-child {
        width: 40px;
        text-align: center;
      }

      // Ensure proper alignment for trend indicators
      .ant-table-tbody > tr > td:nth-child(2) {
        .ant-avatar {
          flex-shrink: 0;
        }
      }
    }
  }

  // Time Statistics Wrapper
  .time-statistics-wrapper {
    padding: 16px 0;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 20px;

    .period-tabs {
      display: flex;
      gap: 8px;
      justify-content: center;

      .ant-btn {
        border-radius: 6px;
        font-size: 12px;
        height: 28px;
        padding: 0 12px;
      }
    }

    .current-period {
      text-align: center;
      padding: 16px 0;
      border-bottom: 1px solid #f0f0f0;

      .period-label {
        font-size: 14px;
        color: #8c8c8c;
        margin-bottom: 4px;
      }

      .period-date {
        font-size: 12px;
        color: #bfbfbf;
        margin-bottom: 8px;
      }

      .period-value {
        font-size: 20px;
        font-weight: 700;
        color: #262626;
      }
    }

    .period-comparison {
      // display: flex;
      // flex-direction: column;
      gap: 12px;
      .ant-row {
        margin: 20px 0;
      }

      .comparison-item {
        // display: flex;
        justify-content: space-between;
        align-items: center;
        text-align: center;

        .period-btn {
          border-radius: 4px;
          font-size: 11px;
          height: 24px;
          padding: 0 8px;

          &.current {
            background-color: #52c41a;
            border-color: #52c41a;
            color: white;
          }

          &.week {
            background-color: #f0f0f0;
            border-color: #d9d9d9;
            color: #000000;
          }
          &.target {
            background-color: #ffd54b;
            border-color: #d9d9d9;
            color: #ffffff;
          }
          &.previous {
            background-color: #cdfd97;
            border-color: #d9d9d9;
            color: #000000;
          }
        }

        .comparison-value,
        .period-date {
          font-size: 15px;
          font-weight: 700;
          color: #262626;
        }
      }
    }

    // Responsive design
    @media (max-width: 768px) {
      .period-tabs {
        .ant-btn {
          font-size: 11px;
          height: 26px;
          padding: 0 8px;
        }
      }

      .current-period {
        .period-value {
          font-size: 18px;
        }
      }

      .period-comparison {
        .comparison-item {
          .comparison-value,
          .period-date {
            font-size: 15px;
            font-weight: 700;
          }
        }
      }
    }
  }

  // Time Period Card (Legacy - keep for compatibility)
  .time-period-card {
    .ant-card-body {
      padding: 20px;
      display: flex;
      flex-direction: column;
      height: 100%;
    }

    .time-period-header {
      margin-bottom: 20px;
    }

    .period-tabs {
      display: flex;
      gap: 8px;

      .ant-btn {
        border-radius: 20px;
        font-size: 12px;
        height: 28px;
        padding: 0 16px;
      }
    }

    .current-period {
      text-align: center;
      margin-bottom: 30px;

      .period-label {
        font-size: 14px;
        color: #8c8c8c;
        margin-bottom: 8px;
      }

      .period-date {
        font-size: 16px;
        font-weight: 600;
        color: #262626;
        margin-bottom: 12px;
      }

      .period-value {
        font-size: 24px;
        font-weight: 700;
        color: #52c41a;
      }
    }

    .period-comparison {
      // display: flex;
      // justify-content: space-between;
      gap: 16px;

      .comparison-item {
        flex: 1;
        text-align: center;

        .period-btn {
          width: 100%;
          margin-bottom: 12px;
          border-radius: 8px;
          font-size: 12px;

          &.current {
            background-color: #3db700;
            border-color: #52c41a;
            color: white;
          }

          &.week {
            background-color: #f0f0f0;
            border-color: #d9d9d9;
            color: #000000;
          }
          &.target {
            background-color: #ffd54b;
            border-color: #d9d9d9;
            color: #ffffff;
          }
          &.previous {
            background-color: #cdfd97;
            border-color: #d9d9d9;
            color: #000000;
          }
        }

        .comparison-value,
        .period-date {
          font-size: 16px;
          font-weight: 600;
          color: #262626;
        }
      }
    }
  }

  // Insurance Table Card
  .insurance-table-card {
    .insurance-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 8px;

      @media (max-width: 768px) {
        grid-template-columns: 1fr;
      }
    }

    .insurance-item {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      padding: 16px;
      border: 1px solid #f0f0f0;
      border-radius: 8px;
      background: #fafafa;

      &:hover {
        border-color: #d9d9d9;
        background: #f5f5f5;
      }
    }

    .insurance-icon {
      .icon-bg {
        width: 20px;
        height: 20px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        font-weight: 600;
        color: white;

        &.icon-1 {
          background: linear-gradient(135deg, #52c41a, #73d13d);
        }
        &.icon-2 {
          background: linear-gradient(135deg, #1890ff, #40a9ff);
        }
        &.icon-3 {
          background: linear-gradient(135deg, #fa8c16, #ffa940);
        }
        &.icon-4 {
          background: linear-gradient(135deg, #722ed1, #9254de);
        }
        &.icon-5 {
          background: linear-gradient(135deg, #13c2c2, #36cfc9);
        }
        &.icon-6 {
          background: linear-gradient(135deg, #eb2f96, #f759ab);
        }
      }
    }

    .insurance-content {
      flex: 1;
    }

    .insurance-name {
      font-size: 14px;
      font-weight: 600;
      color: #262626;
      margin-bottom: 8px;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .insurance-stats {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }

    .stat-item {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .stat-label {
        font-size: 12px;
        color: #8c8c8c;
      }

      .stat-value {
        font-size: 12px;
        font-weight: 500;
        color: #262626;

        &.rate {
          color: #52c41a;
          font-weight: 600;
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 1200px) {
  .dashboard-content {
    .ant-card-body {
      padding: 16px;
    }

    .ant-statistic-content {
      font-size: 20px;

      .ant-statistic-content-value {
        font-size: 20px;
      }
    }
  }
}

@media (max-width: 992px) {
  .dashboard-content {
    // padding: 16px !important;

    .ant-card {
      margin-bottom: 16px;

      .ant-card-body {
        padding: 12px;
      }
    }

    .ant-statistic-content {
      font-size: 18px;

      .ant-statistic-content-value {
        font-size: 18px;
      }
    }

    // Stack charts vertically on mobile
    .line-chart-container,
    .pie-chart-container,
    .bar-chart-container {
      height: 250px;
    }
  }
}

@media (max-width: 768px) {
  .dashboard-content {
    padding: 12px !important;

    h2 {
      font-size: 20px !important;
    }

    .ant-card {
      .ant-card-head {
        .ant-card-head-title {
          font-size: 14px;
        }
      }

      .ant-card-body {
        padding: 8px;
      }
    }

    .ant-statistic {
      .ant-statistic-title {
        font-size: 12px;
      }

      .ant-statistic-content {
        font-size: 16px;

        .ant-statistic-content-value {
          font-size: 16px;
        }
      }
    }

    .ant-table {
      font-size: 12px;

      .ant-table-thead > tr > th,
      .ant-table-tbody > tr > td {
        padding: 8px 4px;
      }
    }

    // Header controls
    .dashboard-header-controls {
      flex-direction: column;
      gap: 8px;

      .ant-select {
        width: 100% !important;
      }

      .ant-btn {
        width: 100%;
      }
    }
  }
}

@media (max-width: 576px) {
  .dashboard-content {
    .ant-row {
      margin: 0 -4px;

      .ant-col {
        padding: 0 4px;
      }
    }

    .line-chart-container,
    .pie-chart-container,
    .bar-chart-container {
      height: 200px;
    }
  }
}

// Dark theme support
.dark {
  .dashboard-content {
    background-color: #141414;

    .ant-card {
      background-color: #1f1f1f;
      border-color: #303030;

      .ant-card-head {
        border-bottom-color: #303030;

        .ant-card-head-title {
          color: #ffffff;
        }
      }
    }

    .ant-statistic {
      .ant-statistic-title {
        color: #a6a6a6;
      }

      .ant-statistic-content {
        color: #ffffff;
      }
    }

    .ant-table {
      .ant-table-thead > tr > th {
        background-color: #262626;
        color: #ffffff;
        border-bottom-color: #303030;
      }

      .ant-table-tbody > tr > td {
        background-color: #1f1f1f;
        color: #ffffff;
        border-bottom-color: #303030;
      }

      .ant-table-tbody > tr:hover > td {
        background-color: #262626;
      }
    }
  }
}

import React, {PropsWithChildren} from "react";
import {message} from "antd";
import {MessageInstance} from "antd/es/message/interface";
export interface IMessageContextProps {
  noti: MessageInstance;
}
export const MessageContext = React.createContext<IMessageContextProps>({} as any);

export const useMessageContext = () => React.useContext<IMessageContextProps>(MessageContext);

export const MessageProvider: React.FC<PropsWithChildren> = props => {
  const {children} = props;
  const [api, contextHolder] = message.useMessage();

  return (
    <MessageContext.Provider value={{noti: api}}>
      {contextHolder}
      {children}
    </MessageContext.Provider>
  );
};

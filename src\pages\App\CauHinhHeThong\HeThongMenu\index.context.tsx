import {createContext, useContext} from "react";
import {HeThongMenuContextProps} from "./index.model";
// import {HeThongMenuContextProps} from "./index.model";
//createContext : khai báo 1 context mà component có thể cung cấp hoặc đọc
export const HeThongMenuContext = createContext<HeThongMenuContextProps>({
  //khởi tạo các giá trị mặc định, trong context này có giá trị là onSubmit
  danhSachMenu: [],
  danhSachMenuCha: [],
  loading: false,
  filterParams: {},
  setFilterParams: () => {},
  layDanhSachMenuPhanTrang: params => {},
  tongSoDong: 0,
  layChiTietMenu: params => Promise.resolve(null),
  onUpdateHeThongMenu: () => Promise.resolve(null),
  layDanhSachMenuCha: () => Promise.resolve(),
});

//useContext(someContext): là React Hook cho phép bạn đọc và subscribe context từ component của mình.
// file .context muốn truy câp context thì sử dụng useLoginContext : const {onSubmit} = useLoginContext();
export const useHeThongMenuContext = () => useContext(HeThongMenuContext);

import {ReactQuery} from "@src/@types";

export interface DanhMucNghiepVuContextProps {
  loading: boolean;
  tongSoDong: number;
  // defaultFormValue: ReactQuery.ITimKiemPhanTrangDanhSachNghiepVuParams;
  filterParams: ReactQuery.ITimKiemPhanTrangDanhSachNghiepVuParams & ReactQuery.IPhanTrang;
  setFilterParams: React.Dispatch<React.SetStateAction<ReactQuery.ITimKiemPhanTrangDanhSachNghiepVuParams & ReactQuery.IPhanTrang>>;
  onUpdateDanhMucNghiepVu: (item: ReactQuery.IUpdateDanhMucNghiepVuParams) => Promise<number | null | undefined>;
  layDanhSachNghiepVuPhanTrang: (params: ReactQuery.ITimKiemPhanTrangDanhSachNghiepVuParams) => void;
  danhSachNghiepVuPhanTrang: Array<CommonExecute.Execute.IDanhMucNghiepVu>;
  layChiTietNghiepVu: (params: ReactQuery.IChiTietDanhMucNghiepVuParams) => Promise<CommonExecute.Execute.IDanhMucNghiepVu | null>;
  listDoiTac: Array<CommonExecute.Execute.IDoiTac>;
  getListDoiTac: () => Promise<void>;
}

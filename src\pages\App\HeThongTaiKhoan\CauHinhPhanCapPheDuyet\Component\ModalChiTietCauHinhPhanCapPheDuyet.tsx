import {CloseOutlined, PlusCircleOutlined} from "@ant-design/icons";
import {Button, FormInput, HeaderModal, Highlighter, Popcomfirm, TableFilterDropdown} from "@src/components";
import {defaultTableProps, fillRowTableEmpty, usePhongBan} from "@src/hooks";
import {useChucDanh} from "@src/hooks/chucDanhStore";
import {Col, Dropdown, Form, InputRef, Modal, Row, Space, Table, TableColumnType, Tag} from "antd";
import {forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState} from "react";
import {useCauHinhPhanCapPheDuyetContext} from "../index.context";
import {
  cauHinhPhanCapPheDuyetChiTietColumns,
  cauHinhPhanCapPheDuyetColumns,
  DataIndexCauHinhPhanCapPheDuyet,
  DataIndexCauHinhPhanCapPheDuyetCT,
  FormChiTietCauHinhPhanCapPheDuyet,
  FormThemNgayApDung,
  IModalThemCauHinhPhanCapPheDuyetRef,
  initFormFields,
  TableCauHinhPhanCapPheDuyetChiTietDataType,
  TableCauHinhPhanCapPheDuyetDataType,
} from "./index.configs";
import {FilterDropdownProps} from "antd/es/table/interface";
import dayjs from "dayjs";
import isSameOrBefore from "dayjs/plugin/isSameOrBefore";

dayjs.extend(isSameOrBefore);
import {ModalThemCauHinhPhanCapPheDuyet} from "./ModalThemChiTietCauHinhPhanCapPheDuyet";
import {formatCurrencyUS} from "@src/utils";
interface Props {}

export interface IModalChiTietCauHinhPhanCapPheDuyetRef {
  open: (data?: CommonExecute.Execute.IChiTietNguoiSuDung) => void;
  close: () => void;
}

const ModalChiTietCauHinhPhanCapPheDuyet = forwardRef<IModalChiTietCauHinhPhanCapPheDuyetRef, Props>((Props, ref) => {
  const listPhongBan = usePhongBan();
  const listChucDanh = useChucDanh();
  const {
    loading,
    listDoiTac,
    listChiNhanh,
    danhSachCauHinhPhanCapPheDuyet,
    danhSachCauHinhPhanCapPheDuyetCT,
    layChiTietTaiKhoanNguoiDung,
    layChiTietCauHinhPhanCapPheDuyetCT,
    getListChiNhanhTheoDoiTac,
    onDeleteCauHinhPhanCapPheDuyetCT,
    layDanhSachCauHinhPhanCapPheDuyet,
    layDanhSachCauHinhPhanCapPheDuyetCT,
    setDanhSachCauHinhPhanCapPheDuyet,
    setDanhSachCauHinhPhanCapPheDuyetCT,
    onUpdateCauHinhPhanCapPheDuyet,
    onDeleteCauHinhPhanCapPheDuyet,
  } = useCauHinhPhanCapPheDuyetContext();

  const {ten, phong, ma_chi_nhanh, ma, ma_doi_tac} = FormChiTietCauHinhPhanCapPheDuyet;
  const {ngay_ad} = FormThemNgayApDung;
  const [formChiTietTaiKhoanNguoiDung] = Form.useForm();
  const [formThemNgayApDung] = Form.useForm();
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const formValues = Form.useWatch([], formChiTietTaiKhoanNguoiDung);
  const [chiTietNguoiSuDung, setChiTietNguoiSuDung] = useState<CommonExecute.Execute.IChiTietNguoiSuDung | null>(null);
  const [filteredPhongBan, setFilteredPhongBan] = useState<Array<CommonExecute.Execute.IDanhMucPhongBan>>([]);
  const [filteredChiNhanh, setFilteredChiNhanh] = useState<Array<CommonExecute.Execute.IChiNhanh>>([]);
  const [searchedColumn, setSearchedColumn] = useState("");
  const [searchText, setSearchText] = useState("");

  const [searchedColumnCT, setSearchedColumnCT] = useState("");
  const [searchTextCT, setSearchTextCT] = useState("");
  const searchInput = useRef<InputRef>(null);
  const searchInputCT = useRef<InputRef>(null);
  const [pageSize, setPageSize] = useState(10);
  const [selectePhanCapPheDuyet, setSelectePhanCapPheDuyet] = useState<number | null>(null); // Theo dõi vai trò được chọn
  // const [rawPhanCapPheDuyetCTData, setRawPhanCapPheDuyetCTData] = useState<any[]>([]);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const refModalThemCauHinhPhanCapPheDuyet = useRef<IModalThemCauHinhPhanCapPheDuyetRef>(null);
  const [ngayAdMoiTao, setNgayAdMoiTao] = useState<string | null>(null);
  useImperativeHandle(ref, () => ({
    open: (dataChiTietNguoiSuDung?: CommonExecute.Execute.IChiTietNguoiSuDung) => {
      setIsOpen(true);
      if (dataChiTietNguoiSuDung) setChiTietNguoiSuDung(dataChiTietNguoiSuDung);
      setDanhSachCauHinhPhanCapPheDuyet([]); // Reset danh sách về rỗng khi đóng modal
      setDanhSachCauHinhPhanCapPheDuyetCT([]);
      setSelectePhanCapPheDuyet(null);
      setNgayAdMoiTao(null); // Reset lại khi mở modal
    },
    close: () => {
      setIsOpen(false);
      setDanhSachCauHinhPhanCapPheDuyet([]); // Reset danh sách về rỗng khi đóng modal
      setDanhSachCauHinhPhanCapPheDuyetCT([]);
      setSelectePhanCapPheDuyet(null);
      setNgayAdMoiTao(null); // Reset lại khi đóng modal
    },
  }));
  // init form data gọi vào index.configs
  useEffect(() => {
    initFormFields(formChiTietTaiKhoanNguoiDung, chiTietNguoiSuDung);
  }, [chiTietNguoiSuDung, formChiTietTaiKhoanNguoiDung]);

  // Lọc danh sách phòng ban theo mã chi nhánh
  useEffect(() => {
    if (chiTietNguoiSuDung?.ma_chi_nhanh_ql) {
      const filtered = listPhongBan.listPhongBan.filter(pb => pb.ma_chi_nhanh === chiTietNguoiSuDung.ma_chi_nhanh_ql);
      setFilteredPhongBan(filtered);
    } else {
      setFilteredPhongBan([]);
    }
  }, [chiTietNguoiSuDung?.ma_chi_nhanh_ql, listPhongBan.listPhongBan]);
  useEffect(() => {
    if (chiTietNguoiSuDung?.ma) {
      layDanhSachCauHinhPhanCapPheDuyet({nsd_duyet: chiTietNguoiSuDung.ma});
    }
  }, [chiTietNguoiSuDung]);

  // Tự động chọn dòng cuối cùng khi danh sách thay đổi
  useEffect(() => {
    if (danhSachCauHinhPhanCapPheDuyet.length > 0) {
      let selected;
      if (ngayAdMoiTao) {
        // Tìm ngày vừa tạo
        selected = danhSachCauHinhPhanCapPheDuyet.find(item => dayjs(item.ngay_ad, "DD/MM/YYYY").isSame(dayjs(ngayAdMoiTao, "DD/MM/YYYY")));
        setNgayAdMoiTao(null); // Reset lại sau khi đã select
      } else {
        // Tìm ngày gần hôm nay nhất (không vượt quá hôm nay)
        const today = dayjs();
        const validDates = danhSachCauHinhPhanCapPheDuyet.filter(item => dayjs(item.ngay_ad, "DD/MM/YYYY").isBefore(today) || dayjs(item.ngay_ad, "DD/MM/YYYY").isSame(today));

        if (validDates.length > 0) {
          selected = validDates.reduce((prev, curr) => {
            const prevDiff = today.diff(dayjs(prev.ngay_ad, "DD/MM/YYYY"), "day");
            const currDiff = today.diff(dayjs(curr.ngay_ad, "DD/MM/YYYY"), "day");
            return currDiff < prevDiff ? curr : prev;
          });
        } else {
          // Fallback: nếu không có ngày nào <= hôm nay, chọn ngày gần nhất
          selected = danhSachCauHinhPhanCapPheDuyet.reduce((prev, curr) => {
            const prevDiff = Math.abs(today.diff(dayjs(prev.ngay_ad, "DD/MM/YYYY"), "day"));
            const currDiff = Math.abs(today.diff(dayjs(curr.ngay_ad, "DD/MM/YYYY"), "day"));
            return currDiff < prevDiff ? curr : prev;
          });
        }
      }
      if (selected && selected.bt !== undefined && selected.bt !== null) {
        setSelectePhanCapPheDuyet(Number(selected.bt));
        layDanhSachCauHinhPhanCapPheDuyetCT({bt_phan_cap: Number(selected.bt)});
      }
    } else {
      setSelectePhanCapPheDuyet(null);
      setDanhSachCauHinhPhanCapPheDuyetCT([]);
    }
  }, [danhSachCauHinhPhanCapPheDuyet]);
  //xử lý validate form
  // useEffect(() => {
  //   formChiTietTaiKhoanNguoiDung
  //     .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
  //     .then(() => {
  //       setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
  //     })
  //     .catch(() => {
  //       setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
  //     });
  // }, [formChiTietTaiKhoanNguoiDung, formValues]);
  useEffect(() => {
    if (selectePhanCapPheDuyet !== null) {
      setDisableSubmit(false);
    } else {
      setDisableSubmit(true);
    }
  }, [selectePhanCapPheDuyet]);
  // Dữ liệu bảng vai trò (bên trái)
  const dataTableListNgayApDung = useMemo<Array<TableCauHinhPhanCapPheDuyetDataType>>(() => {
    try {
      console.log("danh sách phân cấp phê duyệt", danhSachCauHinhPhanCapPheDuyet);

      const mappedData = danhSachCauHinhPhanCapPheDuyet.map((item: any, index: number) => ({
        stt: item.sott ?? index + 1,
        ngay_ad: item.ngay_ad || "",
        bt: item.bt,
        ngay_cap_nhat: item.ngay_cap_nhat,
        nguoi_cap_nhat: item.nguoi_cap_nhat,
        key: index.toString(),
        hanh_dong: () => renderDeleteButton(chiTietNguoiSuDung?.ten, item?.bt, item.ngay_ad),
      }));
      console.log("mappData danh sách cấu hình cấp phê duyệt", mappedData);
      const arrEmptyRow = fillRowTableEmpty(mappedData.length, pageSize);
      return [...mappedData, ...arrEmptyRow];
    } catch (error) {
      console.error("Lỗi khi xử lý dữ liệu bảng ngày áp dụng:", error);
      return [];
    }
  }, [danhSachCauHinhPhanCapPheDuyet, pageSize]);
  const dataTableListChiTietCauHinhPhanCapPheDuyetCT = useMemo<Array<TableCauHinhPhanCapPheDuyetChiTietDataType>>(() => {
    try {
      // Tạm thời sử dụng danhSachCauHinhPhanCapPheDuyet cho bảng chi tiết
      const mappedData = danhSachCauHinhPhanCapPheDuyetCT.map((item: any, index: number) => ({
        stt: item.sott ?? index + 1,
        nhom_duyet: item.nhom_duyet,
        ma_doi_tac_ql: item.ma_doi_tac_ql,
        ma_chi_nhanh_ql: item.ma_chi_nhanh_ql,
        nv: item.nv,
        ma_sp: item.ma_sp,
        tien_tu: item.tien_tu,
        tien_toi: item.tien_toi,
        ten_sp: item.ten_sp,
        doi_tac_ql_ten_tat: item.doi_tac_ql_ten_tat,
        chi_nhanh_ql_ten_tat: item.chi_nhanh_ql_ten_tat,
        ten_nhom: item.ten_nhom,
        key: index.toString(),
        bt_phan_cap: item.bt_phan_cap,
        bt: item.bt,
        hanh_dong: () => renderDeleteButtonCT(item.ma_doi_tac_ql, item.nhom_duyet, item?.bt, item.bt_phan_cap),
      }));
      console.log("mappedData danh sách cấu hình phê duyệt chi tiết", mappedData);
      const arrEmptyRow = fillRowTableEmpty(mappedData.length, pageSize);
      return [...mappedData, ...arrEmptyRow];
    } catch (error) {
      console.error("Lỗi khi xử lý dữ liệu bảng Vai Trò:", error);
      return [];
    }
  }, [danhSachCauHinhPhanCapPheDuyetCT, pageSize]);

  const handleChangeDoiTac = (maDoiTac: string) => {
    console.log("hàm chang đối tác");
    getListChiNhanhTheoDoiTac();
    const filtered = listChiNhanh.filter(pb => pb.ma_doi_tac === maDoiTac);
    setFilteredChiNhanh(filtered);
  };
  const optionsChucDanh = listChucDanh.listChucDanh.map((item, idx) => ({
    ...item,
    value: item.ma,
    label: item.label || item.ten,
  }));

  // Xử lý khi chọn chi nhánh
  const handleChangeChiNhanh = (maChiNhanh: string) => {
    // Lọc danh sách phòng ban theo mã chi nhánh
    const filtered = listPhongBan.listPhongBan.filter(pb => pb.ma_chi_nhanh === maChiNhanh);
    setFilteredPhongBan(filtered);
    // Lấy giá trị phòng ban hiện tại từ chiTietNguoiSuDung
    const currentPhong = chiTietNguoiSuDung?.phong;
    // Kiểm tra phòng ban hiện tại có trong filtered không
    const found = filtered.find(pb => pb.value === currentPhong);
    if (found) {
      formChiTietTaiKhoanNguoiDung.setFieldsValue({phong: currentPhong}); // Nếu có thì set lại giá trị
    } else {
      formChiTietTaiKhoanNguoiDung.setFieldsValue({phong: null}); // Nếu không có thì reset
    }
  };
  const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: DataIndexCauHinhPhanCapPheDuyet) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);
  const handleReset = useCallback(
    (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: DataIndexCauHinhPhanCapPheDuyet) => {
      clearFilters();
      setSearchedColumn("");
      handleSearch([""], confirm, dataIndex);
    },
    [handleSearch],
  );
  const handleSearchCT = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: DataIndexCauHinhPhanCapPheDuyetCT) => {
    confirm();
    setSearchTextCT(selectedKeys[0]);
    setSearchedColumnCT(dataIndex);
  }, []);
  const handleResetCT = useCallback(
    (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: DataIndexCauHinhPhanCapPheDuyetCT) => {
      clearFilters();
      setSearchedColumnCT("");
      handleSearchCT([""], confirm, dataIndex);
    },
    [handleSearchCT],
  );
  // Xử lý nhấp vào hàng ngày áp dụng
  const handlePhanCapPheDuyetRowClick = async (record: TableCauHinhPhanCapPheDuyetDataType) => {
    // Nếu là hàng trống (key chứa "empty" hoặc không có bt), set null
    if (record.key?.toString().includes("empty") || record.bt === undefined || record.bt === null) {
      setSelectePhanCapPheDuyet(null);
      setDanhSachCauHinhPhanCapPheDuyetCT([]);
      return;
    }
    setSelectePhanCapPheDuyet(record.bt);
    try {
      const phanCapPheDuyetCTData = await layDanhSachCauHinhPhanCapPheDuyetCT({bt_phan_cap: record.bt});
      // setDanhSachCauHinhPhanCapPheDuyetCT(phanCapPheDuyetCTData);
    } catch (error) {
      setDanhSachCauHinhPhanCapPheDuyetCT([]);
    }
  };
  const handleSubmit = async () => {
    try {
      const values = await formThemNgayApDung.validateFields();
      console.log("Giá trị ngày áp dụng:", values.ngay_ad);

      if (!values.ngay_ad) {
        console.log("Vui lòng chọn ngày áp dụng");
        return;
      }

      await onUpdateCauHinhPhanCapPheDuyet({
        nsd_duyet: chiTietNguoiSuDung?.ma,
        ngay_ad: Number(dayjs(values.ngay_ad).format("YYYYMMDD")),
      });
      setDropdownOpen(false); // Đóng dropdown sau khi lưu thành công
      formThemNgayApDung.resetFields();
      setNgayAdMoiTao(values.ngay_ad); // Lưu lại ngày vừa tạo

      if (chiTietNguoiSuDung?.ma) {
        layDanhSachCauHinhPhanCapPheDuyet({nsd_duyet: chiTietNguoiSuDung.ma});
      }
    } catch (error) {
      console.log("Lỗi khi submit:", error);
      // Không đóng dropdown nếu có lỗi để người dùng có thể sửa
    }
  };
  const handleDelete = async (nsd_duyet: string, bt: number, ngay_ad: number) => {
    try {
      await onDeleteCauHinhPhanCapPheDuyet({
        bt: Number(bt),
        nsd_duyet: chiTietNguoiSuDung?.ma,
        ngay_ad: Number(dayjs(ngay_ad, "DD/MM/YYYY").format("YYYYMMDD")),
      });

      console.log("Xóa ngày áp dụng thành công");

      // Reset selection sau khi xóa
      setSelectePhanCapPheDuyet(null);
      setDanhSachCauHinhPhanCapPheDuyetCT([]);

      // Refresh danh sách
      if (chiTietNguoiSuDung?.ma) {
        layDanhSachCauHinhPhanCapPheDuyet({nsd_duyet: chiTietNguoiSuDung.ma});
      }
    } catch (error) {
      console.log("Lỗi khi xóa ngày áp dụng:", error);
    }
  };
  const handleDeleteCT = async (ma_doi_tac_ql: string, nhom_duyet: string, bt: number, bt_phan_cap: number) => {
    try {
      await onDeleteCauHinhPhanCapPheDuyetCT({
        bt: Number(bt),
        bt_phan_cap: Number(bt_phan_cap),
        nhom_duyet,
        ma_doi_tac_ql,
      });
      await layDanhSachCauHinhPhanCapPheDuyetCT({bt_phan_cap: bt_phan_cap});
    } catch (error) {
      console.log("Lỗi khi xóa ngày áp dụng:", error);
    }
  };
  const getColumnSearchCauHinhPhanCapPheDuyetProps = (dataIndex: DataIndexCauHinhPhanCapPheDuyet, title: string): TableColumnType<TableCauHinhPhanCapPheDuyetDataType> => ({
    filterDropdown: ({setSelectedKeys, selectedKeys, confirm, clearFilters}) => (
      <TableFilterDropdown
        ref={searchInput}
        title={title}
        selectedKeys={selectedKeys}
        dataIndex={dataIndex}
        setSelectedKeys={setSelectedKeys}
        handleSearch={handleSearch}
        confirm={confirm}
        clearFilters={clearFilters}
        handleReset={handleReset}
      />
    ),
    // filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} />,
    onFilter: (value, record) =>
      record[dataIndex]
        ? record[dataIndex]
            .toString()
            .toLowerCase()
            .includes((value as string).toLowerCase())
        : false,
    filterDropdownProps: {
      onOpenChange(open) {
        if (open) {
          setTimeout(() => searchInput.current?.select(), 100);
        }
      },
    },
    // filters: dataIndex === "trang_thai_ten" ? radioItemTrangThaiNhomTable : undefined,
    render: (text, record, index) => {
      //   if (dataIndex === "trang_thai_ten") {
      //     // Xác định màu dựa vào text (trạng thái hiển thị)
      //     const color = text === "Đang sử dụng" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];
      //     if (record.key.toString().includes("empty")) return "";
      //     return <Tag color={color}>{text}</Tag>;
      //   }
      return searchedColumn === dataIndex ? (
        <Highlighter searchWords={[searchText]} textToHighlight={text ? text.toString() : ""} />
      ) : text !== undefined ? (
        text
      ) : (
        <Tag color={"transparent"} className="!text-white text-[11px]">
          {""}
        </Tag>
      );
    },
  });
  const getColumnSearchCauHinhPhanCapPheDuyetCTProps = (dataIndex: DataIndexCauHinhPhanCapPheDuyetCT, title: string): TableColumnType<TableCauHinhPhanCapPheDuyetChiTietDataType> => ({
    filterDropdown: ({setSelectedKeys, selectedKeys, confirm, clearFilters}) => (
      <TableFilterDropdown
        ref={searchInputCT}
        title={title}
        selectedKeys={selectedKeys}
        dataIndex={dataIndex}
        setSelectedKeys={setSelectedKeys}
        handleSearch={handleSearchCT}
        confirm={confirm}
        clearFilters={clearFilters}
        handleReset={handleResetCT}
      />
    ),
    // filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} />,
    onFilter: (value, record) =>
      record[dataIndex]
        ? record[dataIndex]
            .toString()
            .toLowerCase()
            .includes((value as string).toLowerCase())
        : false,
    filterDropdownProps: {
      onOpenChange(open) {
        if (open) {
          setTimeout(() => searchInputCT.current?.select(), 100);
        }
      },
    },
    render: (text, record, index) => {
      if ((dataIndex === "tien_tu" || dataIndex === "tien_toi") && typeof text === "number") {
        return formatCurrencyUS(text);
      }
      return searchedColumnCT === dataIndex ? (
        <Highlighter searchWords={[searchTextCT]} textToHighlight={text ? text.toString() : ""} />
      ) : text !== undefined ? (
        text
      ) : (
        <Tag color={"transparent"} className="!text-white text-[11px]">
          {""}
        </Tag>
      );
    },
  });
  //renderTable
  const renderTableCauHinhPhanCapPheDuyetFooter = () => {
    return (
      <div className="">
        <Form.Item className="" style={{marginTop: 16, marginRight: 8, marginBottom: 0}}>
          {/* <Space direction="vertical" > */}
          <Space className="">
            <Dropdown
              className=""
              open={dropdownOpen}
              onOpenChange={setDropdownOpen}
              trigger={["click"]}
              getPopupContainer={triggerNode => triggerNode.parentNode as HTMLElement}
              dropdownRender={() => (
                <div style={{padding: 8, display: "flex"}}>
                  <Form id="formThemNgayApDung" form={formThemNgayApDung} layout="vertical">
                    <div style={{display: "flex", justifyContent: "space-between", alignItems: "flex-end"}}>
                      {renderFormInputColum({...ngay_ad}, 24)}
                      {/* <Form.Item> */}
                      <Button type="primary" onClick={handleSubmit} style={{marginLeft: 8, marginBottom: 8}}>
                        Áp dụng
                      </Button>
                      {/* </Form.Item> */}
                    </div>
                  </Form>
                </div>
              )}
              placement="topRight">
              <Button className="w-full" type="primary" icon={<PlusCircleOutlined />} onClick={() => setDropdownOpen(true)}>
                Thêm ngày áp dụng
              </Button>
            </Dropdown>
          </Space>
          {/* </Space> */}
        </Form.Item>
        {/* <Form.Item style={{marginTop: 16, marginBottom: 0}}>
         
          <Popcomfirm
            title="Thông báo"
            onConfirm={() => handleDelete()}
            htmlType="button"
            okText="Xóa"
            description="Bạn có chắc muốn xóa ngày áp dụng đã chọn?"
            buttonTitle={"Xóa ngày áp dụng"}
            buttonColor="red"
            okButtonProps={{
              style: {
                backgroundColor: "white",
                borderColor: "red",
                color: "red",
              },
            }}
            variant="outlined"
            // className="h-auto"
            icon={<CloseOutlined />}
            buttonIcon
          />
        
        </Form.Item> */}
      </div>
    );
  };
  const renderDeleteButton = (nsd_duyet?: string, bt?: number, ngay_ad?: number) => {
    if (!nsd_duyet || !bt || !ngay_ad) return null;
    return (
      <Popcomfirm
        title="Thông báo"
        onConfirm={() => handleDelete(nsd_duyet, bt, ngay_ad)}
        htmlType="button"
        okText="Xóa"
        description="Bạn có chắc muốn xóa ngày áp dụng?"
        buttonTitle={""}
        buttonColor="red"
        okButtonProps={{
          style: {
            backgroundColor: "white",
            borderColor: "red",
            color: "red",
          },
        }}
        style={{width: "fit-content"}}
        variant="text"
        className="h-auto"
        icon={<CloseOutlined />}
        buttonIcon
      />
    );
  };
  const renderDeleteButtonCT = (ma_doi_tac_ql?: string, nhom_duyet?: string, bt?: number, bt_phan_cap?: number) => {
    if (!ma_doi_tac_ql || !bt || !nhom_duyet || !bt_phan_cap) return null;
    return (
      <span onClick={e => e.stopPropagation()}>
        <Popcomfirm
          title="Thông báo"
          onConfirm={() => handleDeleteCT(ma_doi_tac_ql, nhom_duyet, bt, bt_phan_cap)}
          htmlType="button"
          okText="Xóa"
          description="Bạn có chắc muốn xóa ngày áp dụng?"
          buttonTitle={""}
          buttonColor="red"
          onClick={e => e.stopPropagation()}
          okButtonProps={{
            style: {
              backgroundColor: "white",
              borderColor: "red",
              color: "red",
            },
          }}
          style={{width: "fit-content"}}
          variant="text"
          className="h-auto"
          icon={<CloseOutlined />}
          buttonIcon
        />
      </span>
    );
  };
  const renderTableCauHinhPhanCapPheDuyetCTFooter = () => {
    return (
      <Form.Item style={{marginTop: 16, marginBottom: 0, textAlign: "end"}}>
        <Button className="" type="primary" icon={<PlusCircleOutlined />} onClick={() => refModalThemCauHinhPhanCapPheDuyet.current?.open()} loading={loading} disabled={disableSubmit}>
          Thêm phân cấp
        </Button>
      </Form.Item>
    );
  };
  const renderTableCauHinhPhanCapPheDuyet = () => {
    return (
      <Table<TableCauHinhPhanCapPheDuyetDataType>
        className="table-nhom no-header-border-radius"
        {...defaultTableProps}
        onRow={(record, rowIndex) => {
          return {
            style: {
              cursor: "pointer",
              background: record.bt === selectePhanCapPheDuyet ? "#96BF49" : undefined, // Làm nổi bật hàng được chọn
            },
            onClick: () => handlePhanCapPheDuyetRowClick(record), // Xử lý nhấp vào hàng
          };
        }}
        title={null}
        pagination={false}
        columns={(cauHinhPhanCapPheDuyetColumns || []).map(item => {
          // Đảm bảo item.key được định nghĩa, item.title là chuỗi và item.key không phải là "stt" trước khi thêm ô tìm kiếm
          return {
            ...item,
            ...(item.key && typeof item.title === "string" && item.key !== "stt" ? getColumnSearchCauHinhPhanCapPheDuyetProps(item.key as keyof TableCauHinhPhanCapPheDuyetDataType, item.title) : {}),
          };
        })}
        dataSource={dataTableListNgayApDung}
        bordered
      />
    );
  };
  const renderTableCauHinhPhanCapPheDuyetCT = () => {
    return (
      <Table<TableCauHinhPhanCapPheDuyetChiTietDataType>
        className="table-nhom no-header-border-radius"
        {...defaultTableProps}
        pagination={false}
        title={null}
        columns={(cauHinhPhanCapPheDuyetChiTietColumns || []).map(item => {
          // Đảm bảo item.key được định nghĩa, item.title là chuỗi và item.key không phải là "stt" trước khi thêm ô tìm kiếm
          return {
            ...item,
            ...(item.key && typeof item.title === "string" && item.key !== "stt"
              ? getColumnSearchCauHinhPhanCapPheDuyetCTProps(item.key as keyof TableCauHinhPhanCapPheDuyetChiTietDataType, item.title)
              : {}),
          };
        })}
        onRow={(record, rowIndex) => {
          return {
            style: {cursor: "pointer"},
            onClick: async event => {
              if (record.key.toString().includes("empty")) return;
              const response: CommonExecute.Execute.ICauHinhPhanCapPheDuyetCT | null = await layChiTietCauHinhPhanCapPheDuyetCT({bt: Number(record.bt)});
              console.log("response", response);
              if (response) refModalThemCauHinhPhanCapPheDuyet.current?.open(response);
            }, // click row
          };
        }}
        dataSource={dataTableListChiTietCauHinhPhanCapPheDuyetCT}
        bordered
      />
    );
  };
  const renderTable = () => {
    return (
      <Row>
        <Col span={7} style={{paddingRight: 16}}>
          {renderTableCauHinhPhanCapPheDuyet()}
          {renderTableCauHinhPhanCapPheDuyetFooter()}
        </Col>
        <Col span={17}>
          {renderTableCauHinhPhanCapPheDuyetCT()}
          {renderTableCauHinhPhanCapPheDuyetCTFooter()}
        </Col>
      </Row>
    );
  };
  //renderFooter Modal
  // const renderFooter = () => {
  //   return (
  //     <Form.Item>
  //       <Button type="primary" htmlType="submit" form="formUpdatePhongBan" className="mr-2 w-40" icon={<CheckOutlined />}>
  //         Lưu
  //       </Button>
  //     </Form.Item>
  //   );
  // };
  //Render

  const renderFormInputColum = (props?: any, span = 4) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );

  return (
    <Modal
      className="modal-chi-tiet-cau-hinh-phan-cap-phe-duyet"
      title={
        <HeaderModal
          title={chiTietNguoiSuDung ? `Thông tin tài khoản ${chiTietNguoiSuDung.ma} - ${chiTietNguoiSuDung.ten} ` : ""}
          // trang_thai_ten={chiTietNguoiSuDung?.trang_thai_ten}
          // trang_thai={chiTietNguoiSuDung?.trang_thai}
        />
      }
      style={{top: 0}}
      centered
      open={isOpen}
      onOk={() => setIsOpen(false)}
      onCancel={() => {
        formChiTietTaiKhoanNguoiDung.resetFields();
        setIsOpen(false);
        setChiTietNguoiSuDung(null);
        setDanhSachCauHinhPhanCapPheDuyet([]); // Reset danh sách về rỗng khi đóng modal
      }}
      footer={null}
      closable
      maskClosable={false}
      width="100vw"
      // style={{
      //   top: 20,
      //   left: 0,
      //   padding: 0,
      // }}
      // styles={{
      //   body: {
      //     height: "60vh",
      //   },
      // }}
      // className="custom-full-modal m-2"
    >
      <Form id="formUpdateTaiKhoanNguoiDung" form={formChiTietTaiKhoanNguoiDung} layout="vertical" autoComplete="on">
        <Row gutter={16}>
          {renderFormInputColum({...ma_doi_tac, options: listDoiTac, onChange: (value: string) => handleChangeDoiTac(value), disabled: chiTietNguoiSuDung ? true : false}, 6)}
          {renderFormInputColum({...ma_chi_nhanh, options: filteredChiNhanh, disabled: chiTietNguoiSuDung ? true : false, onChange: handleChangeChiNhanh})}
          {renderFormInputColum({...phong, options: filteredPhongBan, disabled: chiTietNguoiSuDung ? true : false, onChange: () => {}})}
          {renderFormInputColum({...ma, disabled: chiTietNguoiSuDung ? true : false})}
          {renderFormInputColum({...ten, disabled: chiTietNguoiSuDung ? true : false}, 6)}
        </Row>
      </Form>
      {renderTable()}
      <ModalThemCauHinhPhanCapPheDuyet ref={refModalThemCauHinhPhanCapPheDuyet} PhanCapPheDuyet={selectePhanCapPheDuyet}></ModalThemCauHinhPhanCapPheDuyet>
    </Modal>
  );
});
ModalChiTietCauHinhPhanCapPheDuyet.displayName = "ModalChiTietCauHinhPhanCapPheDuyet";
export default ModalChiTietCauHinhPhanCapPheDuyet;

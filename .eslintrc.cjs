module.exports = {
  env: {
    browser: true, // cho phép dùng các global của trình duyệt như window, document.
    es2021: true, // hỗ trợ cú pháp ES2021.
    node: true, // cho phép dùng các global của Node.js như require, process.
    jest: true, //để ESLint hiểu các hàm test như describe, it, expect.
  },
  //Kế thừa nhiều cấu hình gợi ý:
  extends: [
    "eslint:recommended", // quy tắc chuẩn từ ESLint.
    "plugin:react/recommended", //quy tắc cho React.
    "plugin:@typescript-eslint/recommended", // cho TypeScript.
    "plugin:react-hooks/recommended", // kiểm tra đúng quy tắc React Hooks.
    "prettier", // vô hiệu hóa các rule ESLint gây xung đột với Prettier.
  ],
  parser: "@typescript-eslint/parser", // Dùng parser của TypeScript để hiểu file .ts và .tsx.
  parserOptions: {
    ecmaVersion: "latest",
    sourceType: "module", // cho phép sử dụng import/export.
  },
  plugins: [
    "react",
    "@typescript-eslint",
    "unused-imports", //xoá import không dùng.
    "simple-import-sort", // sắp xếp import.
    "react",
    "react-hooks",
    "prettier", // tích hợp với Prettier để bắt lỗi format.
  ],
  rules: {
    'react/prop-types': 'off', // 👈 Tắt rule này vì bạn đã dùng TypeScript
    "no-empty": "off",
    "no-empty-function": "off", // không bắt lỗi khi function trống
    "no-empty-pattern": "off", // không bắt lỗi khi pattern trống
    "no-unused-vars": "off", // không bắt lỗi khi biến không dùng
    "unused-imports/no-unused-imports-ts": "error", // Báo lỗi khi có import không dùng.
    "@typescript-eslint/no-unused-vars": "off",
    "@typescript-eslint/no-empty-function": "off",
    // "@typescript-eslint/no-unused-vars": [
    //   "warn",
    //   {
    //     vars: "all",
    //     varsIgnorePattern: "^_",
    //     args: "after-used",
    //     argsIgnorePattern: "^_",
    //   },
    // ],
    "sort-imports": [
      // Cảnh báo nếu import không được sắp xếp đúng (chỉ nếu bật).
      "warn",
      {
        ignoreDeclarationSort: true,
        ignoreMemberSort: true,
      },
    ],
    "react/jsx-uses-react": "error",
    "react/jsx-uses-vars": "error",
    "react/react-in-jsx-scope": "off",
    "@typescript-eslint/no-explicit-any": "off", // không bắt lỗi khi dùng any
    "@typescript-eslint/no-empty-interface": "off", // không bắt lỗi khi interface rỗng
    "simple-import-sort/exports": "off",
    "prettier/prettier": "warn", // Cảnh báo nếu code không đúng format của Prettier.
    "react-hooks/exhaustive-deps": "warn", // Cảnh báo nếu thiếu dependencies trong useEffect, useCallback,...
    "simple-import-sort/imports": "off", //Đang tắt sắp xếp import tự động.
    // "simple-import-sort/imports": [
    //   "error",
    //   {
    //     groups: [
    //       // Packages `react` related packages come first.
    //       [
    //         "^react",
    //         // ant* packages
    //         "^(@|antd)",
    //         // tailwind
    //         "^(@|tailwind)",
    //         // Internal packages.
    //         "^(@|components)(/.*|$)",
    //         // Side effect imports.
    //         "^\\u0000",
    //         // Parent imports. Put `..` last.
    //         "^\\.\\.(?!/?$)",
    //         // Other relative imports. Put same-folder imports and `.` last.
    //         "^\\./(?=.*/)(?!/?$)",
    //         // Style imports.
    //         "^.+\\.?(css)$",
    //       ],
    //     ],
    //   },
    // ],
  },
};

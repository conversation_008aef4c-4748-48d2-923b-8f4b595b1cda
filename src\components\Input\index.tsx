import {DEBOUNCE_TIME} from "@src/constants";
import {Input as AntInput, InputProps as AntInputProps} from "antd";
import lodash, {isEqual} from "lodash";
import React, {memo, NamedExoticComponent, useCallback} from "react";
import {twMerge} from "tailwind-merge";
import InputPassword from "@components/Input/password";

interface InputComposition {
  Password: typeof InputPassword;
  TextArea: typeof AntInput.TextArea;
}

export interface InputProps extends AntInputProps {
  // only enable isDebounceFunc when input not controlled by Ant Form
  // if using Form controlled, use debounce on onValuesChange function
  isDebounceFunc?: boolean;
}

const InputComponent: React.FC<InputProps> = (props: InputProps) => {
  const {className = "", value, onChange, suffix, allowClear = true, isDebounceFunc = false, size = "middle", ...etc} = props;

  const handleChange: React.ChangeEventHandler<HTMLInputElement> | undefined = useCallback(
    lodash.debounce((e: React.ChangeEvent<HTMLInputElement>) => {
      console.log("DEBOUNCE VALUE: ", e.target.value);
      onChange?.(e);
    }, DEBOUNCE_TIME),
    [],
  );

  return <AntInput size={size} className={twMerge("custom-input", className)} value={value} onChange={isDebounceFunc ? handleChange : onChange} suffix={suffix} allowClear={allowClear} {...etc} />;
};

const Input = memo(InputComponent, isEqual) as NamedExoticComponent<InputProps> & InputComposition;

Input.Password = InputPassword;
Input.TextArea = AntInput.TextArea;

export default Input;

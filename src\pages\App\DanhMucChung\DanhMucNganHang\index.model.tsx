import {ReactQuery} from "@src/@types";

export interface DanhMucNganHangContextProps {
  loading: boolean;
  tongSoDong: number;

  filterParams: ReactQuery.ITimKiemPhanTrangDanhMucNganHangParams & ReactQuery.IPhanTrang;
  setFilterParams: React.Dispatch<React.SetStateAction<ReactQuery.ITimKiemPhanTrangDanhMucNganHangParams & ReactQuery.IPhanTrang>>;
  onUpdateDanhMucNganHang: (item: ReactQuery.ICapNhatDanhMucNganHangParams) => Promise<number | null | undefined>;
  layDanhSachNganHangPhanTrang: (params: ReactQuery.ITimKiemPhanTrangDanhMucNganHangParams) => void;
  danhSachNganHangPhanTrang: Array<CommonExecute.Execute.IDanhMucNganHang>;
  layChiTietNganHang: (params: ReactQuery.IChiTietDanhMucNganHangParams) => Promise<CommonExecute.Execute.IDanhMucNganHang | null>;
  listDoiTac: Array<CommonExecute.Execute.IDoiTac>;
  getListDoiTac: () => Promise<void>;
}

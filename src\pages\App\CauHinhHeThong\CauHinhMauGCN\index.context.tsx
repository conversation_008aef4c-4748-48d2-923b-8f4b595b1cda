/**
 * Chức năng: <PERSON><PERSON><PERSON> nghĩa React Context để chia sẻ state và functions giữa các components
 */

import {createContext} from "react";
import {ICauHinhMauGCNProvider} from "./index.model";

/**
 * Context để chia sẻ state và methods cho các component con của CauHinhMauGCN
 */
const CauHinhMauGCNContext = createContext<ICauHinhMauGCNProvider>({
  // DEFAULT STATE VALUES 
  danhSachCauHinhMauGCN: [],
  listSanPham: [],
  tongSoDong: 0,
  loading: false,
  filterParams: {
    ma_doi_tac_ql: "",
    ma_sp: "",
    ten: "",
    nv: "",
    trang_thai: "",
    ngay_ad: undefined,
    trang: 1,
    so_dong: 20,
  },

  // DEFAULT API FUNCTIONS 
  searchCauHinhMauGCN: async () => {},
  getChiTietCauHinhMauGCN: async () => ({} as CommonExecute.Execute.ICauHinhMauGCN),
  capNhatChiTietCauHinhMauGCN: async () => ({}),
  getListSanPham: async () => {},
  
  // DEFAULT STATE SETTERS 
  setFilterParams: () => {},
});

export default CauHinhMauGCNContext;

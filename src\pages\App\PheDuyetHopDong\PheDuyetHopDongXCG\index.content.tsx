import {SearchOutlined} from "@ant-design/icons";
import {IFormInput, ReactQuery} from "@src/@types";
import {Button, FormInput, Highlighter, TableFilterDropdown} from "@src/components";
import {COLOR_PALETTE} from "@src/constants";
import {defaultPaginationTableProps, defaultTableProps, fillRowTableEmpty} from "@src/hooks";
import {formatCurrencyUS, formatDateTimeToNumber} from "@src/utils";
import {Col, Form, InputRef, Row, Table, TableColumnType, Tag} from "antd";
import {FilterDropdownProps} from "antd/es/table/interface";
import {isEqual} from "lodash";
import React, {memo, useCallback, useEffect, useMemo, useRef, useState} from "react";
import {IModalChiTietThongTinTrinhDuyetRef} from "./Component/Constant";
import {
  currentDate,
  defaultValueFormTimKiemPhanTrang,
  FormTimKiemHopDongTrinhDuyetXCG,
  ngay<PERSON>auThang,
  NHOM_XCG,
  optionNghiepVuSelect,
  optionTrangThaiDuyet,
  PAGE_SIZE,
  radioItemTrangThaiDuyetTable,
  TableHopDongTrinhDuyetColumnDataIndex,
  tableHopDongTrinhDuyetXCGColumn,
  TableHopDongTrinhDuyetXCGColumnDataType,
} from "./index.configs";
import {useHopDongTrinhDuyetContext} from "./index.context";
import "./index.default.scss";
import {ModalChiTietThongTinTrinhDuyet} from "./Component";

const {tu_ngay, den_ngay, so_hd, ten_kh, trang_thai, nv} = FormTimKiemHopDongTrinhDuyetXCG;

const PheDuyetHopDongXCGContent: React.FC = memo(() => {
  const {danhSachHopDongTrinhDuyetXCG, loading, tongSoDong, timKiemPhanTrangHopDongTrinhDuyetXCG, xemChiTietHopDongTrinhDuyet} = useHopDongTrinhDuyetContext();

  const [formTimKiemHopDongTrinhDuyetXCG] = Form.useForm();
  const refModalChiTietDoiTac = useRef<IModalChiTietThongTinTrinhDuyetRef>(null);
  const refSearchInputTable = useRef<InputRef>(null);
  const [searchTextTable, setSearchTextTable] = useState<string>(""); //lưu textsearch để hiển thị vào cell
  const [searchedColumn, setSearchedColumn] = useState<TableHopDongTrinhDuyetColumnDataIndex | "">(""); //key column đang được search
  const [page, setPage] = useState(1);

  useEffect(() => {
    timKiemPhanTrangHopDongTrinhDuyetXCG(defaultValueFormTimKiemPhanTrang);
  }, []);

  useEffect(() => {
    formTimKiemHopDongTrinhDuyetXCG.setFieldsValue({
      tu_ngay: ngayDauThang,
      den_ngay: currentDate,
    });
  }, []);

  const dataTableHopDongTrinhDuyetXCG = useMemo<Array<TableHopDongTrinhDuyetXCGColumnDataType>>(() => {
    try {
      const tableData = danhSachHopDongTrinhDuyetXCG.map((item, index) => {
        return {
          ...item,
          key: index.toString(),
        };
      });
      const arrEmptyRow: Array<TableHopDongTrinhDuyetXCGColumnDataType> = fillRowTableEmpty(tableData.length, PAGE_SIZE);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      console.log("dataTableHopDongTrinhDuyetXCG error", error);
      return [];
    }
  }, [danhSachHopDongTrinhDuyetXCG]);

  const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: TableHopDongTrinhDuyetColumnDataIndex) => {
    confirm();
    setSearchTextTable(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);

  const handleReset = useCallback(
    (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: TableHopDongTrinhDuyetColumnDataIndex) => {
      clearFilters();
      setSearchTextTable("");
      handleSearch([""], confirm, dataIndex);
    },
    [handleSearch],
  );
  // chức năng search
  const onSearchApi = (values: ReactQuery.ITimKiemPhanTrangHopDongTrinhDuyetParams & ReactQuery.IPhanTrang) => {
    const cleanedValues = {
      ...values,
      tu_ngay: values.tu_ngay ? formatDateTimeToNumber(values.tu_ngay) : 0,
      den_ngay: values.den_ngay ? formatDateTimeToNumber(values.den_ngay) : 0,
      nv: values.nv || "",
      nhom: NHOM_XCG,
      so_hd: values.so_hd || "",
      ten_kh: values.ten_kh || "",
      trang_thai: values.trang_thai || "",
      trang: values.trang || 1,
      so_dong: values.so_dong || PAGE_SIZE,
    };
    timKiemPhanTrangHopDongTrinhDuyetXCG(cleanedValues);
  };

  const onChangePage = (page: number, pageSize: number) => {
    setPage(page);
    timKiemPhanTrangHopDongTrinhDuyetXCG(); // setFilterParams({...filterParams, trang: page, so_dong: pageSize});
    // layDanhSachChuongTrinhBaoHiemPhanTrang({...searchParams, trang: page, so_dong: pageSize});
  };

  // RENDER
  const renderFormInputColum = (props: IFormInput, span = 3) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );

  const renderHeaderTable = () => {
    return (
      <>
        <Form form={formTimKiemHopDongTrinhDuyetXCG} initialValues={{}} layout="vertical" className="[&_.ant-form-item]:mb-0" onFinish={onSearchApi}>
          <Row gutter={16} align={"bottom"}>
            {renderFormInputColum({...tu_ngay})}
            {renderFormInputColum({...den_ngay})}
            {renderFormInputColum({...so_hd}, 4)}
            {renderFormInputColum({...ten_kh}, 4)}
            {renderFormInputColum({...nv, options: optionNghiepVuSelect})}
            {renderFormInputColum({...trang_thai, options: optionTrangThaiDuyet})}
            <Col>
              <Button type="primary" htmlType="submit" loading={loading} icon={<SearchOutlined />} block>
                Tìm kiếm
              </Button>
            </Col>
          </Row>
        </Form>
      </>
    );
  };

  // dataIndex : là các key của column, title : tiêu đề của column
  const getColumnSearchProps = (dataIndex: TableHopDongTrinhDuyetColumnDataIndex, title: string): TableColumnType<TableHopDongTrinhDuyetXCGColumnDataType> => ({
    /**
     *  filterDropdown: UI tuỳ chỉnh của ô filter (component render ra trong dropdown filter)
     * @param param0
     * @returns
     * dataIndex !== "trang_thai_ten"
     */
    filterDropdown:
      dataIndex !== "trang_thai_ten"
        ? filterDropdownParams => (
            <TableFilterDropdown
              ref={refSearchInputTable}
              title={title}
              dataIndex={dataIndex}
              handleSearch={(selectedKeys, confirm, dataIndex) => handleSearch(selectedKeys, confirm, dataIndex)}
              handleReset={(clearFilters, confirm, dataIndex) => clearFilters && handleReset(clearFilters, confirm, dataIndex)}
              {...filterDropdownParams}
            />
          )
        : undefined,
    /**
     * onFilter: hàm filter dữ liệu tại client-side (bắt buộc phải khai báo để filter hoạt động)
     * do hàm onFilter luôn phải trả về boolean, nên thêm || false vào để k bị lỗi
     * @param value : giá trị filter người dùng nhập vào
     * @param record : từng bản ghi trong dataSource
     * @returns
     */
    onFilter: (value, record) => {
      return (
        record[dataIndex]
          ?.toString()
          .toLowerCase()
          .includes((value as string).toLowerCase()) || false
      );
    },
    /**
     * filterDropdownProps: thuộc tính tuì chỉnh hành vi dropdown
     */
    filterDropdownProps: {
      // onOpenChange: callback khi dropdown mở/ tắt thì sẽ focus vào input
      onOpenChange(open) {
        if (open) {
          setTimeout(() => refSearchInputTable.current?.select(), 100); // Focus/ select input khi mở filter
        }
      },
    },
    filters: dataIndex === "trang_thai_ten" ? radioItemTrangThaiDuyetTable : undefined,
    render: (
      text,
      record,
      //  index
    ) => {
      if (text && dataIndex === "tong_phi") {
        return formatCurrencyUS(text);
      }
      if (dataIndex === "trang_thai_ten") {
        const getStatusTagColor = (text: string) => {
          let color = COLOR_PALETTE.gray[70];
          if (text === "Đã duyệt") color = COLOR_PALETTE.green[100];
          else if (text === "Chưa duyệt") color = COLOR_PALETTE.gray[70];
          else if (text === "Từ chối duyệt") color = COLOR_PALETTE.red[50];
          return color;
        };
        if (record?.key?.toString().includes("empty")) return "";
        return (
          <Tag color={getStatusTagColor(text)} className="text-[11px]">
            {text}
          </Tag>
        );
      }
      return searchedColumn === dataIndex ? (
        <Highlighter searchWords={[searchTextTable]} textToHighlight={text ? text.toString() : ""} />
      ) : text !== undefined ? (
        text
      ) : (
        <Tag color={"transparent"} className="!text-white text-[11px]">
          {"\u00A0"}
        </Tag>
      );
    },
  });

  return (
    <div id={"PHE_DUYET_HD_XCG"} className="[&_.ant-space]:w-full">
      <Table<TableHopDongTrinhDuyetXCGColumnDataType>
        {...defaultTableProps}
        dataSource={dataTableHopDongTrinhDuyetXCG} //mảng dữ liệu record được hiển thị
        columns={
          tableHopDongTrinhDuyetXCGColumn?.map(item => {
            //Nếu là cột stt thì không hiển thị search
            return {...item, ...(item.key === "sott" ? {} : getColumnSearchProps(item.key as keyof TableHopDongTrinhDuyetXCGColumnDataType, item.title as string))};
          }) || []
        } //định nghĩa cột của table
        loading={loading} //hiển thị loading khi đang gọi API để loading data
        pagination={{
          ...defaultPaginationTableProps,
          total: tongSoDong,
          onChange: (page, pageSize) => {
            onChangePage(page, pageSize);
          },
        }}
        title={renderHeaderTable}
        onRow={record => {
          return {
            style: {cursor: loading ? "progress" : "pointer"}, //  thể hiện vị trí con trỏ là button(👈)
            onClick: async () => {
              if (record?.key?.toString().includes("empty")) return;
              const response = await xemChiTietHopDongTrinhDuyet({bt: record.bt});
              if (response?.lke) refModalChiTietDoiTac.current?.open(response?.lke);
            }, // click row
          };
        }}
      />
      <ModalChiTietThongTinTrinhDuyet ref={refModalChiTietDoiTac} />
    </div>
  );
}, isEqual);

PheDuyetHopDongXCGContent.displayName = "PheDuyetHopDongXCGContent";

export default PheDuyetHopDongXCGContent;

import {createContext, useContext} from "react";
import {DanhMucChiNhanhNganHangContextProps} from "./index.model";

// Tạo giá trị mặc định cho Context
// Giá trị này sẽ được sử dụng khi Context chưa được khởi tạo hoặc component không nằm trong Provider
const defaultContextValue: DanhMucChiNhanhNganHangContextProps = {
  // Danh sách chi nhánh ngân hàng hiện tại
  danhSachChiNhanhNganHang: [],
  // Trạng thái loading khi gọi API
  loading: false,
  // Tổng số dòng để phân trang
  tongSoDong: 0,
  // Danh sách ngân hàng để hiển thị trong dropdown
  listNganHang: [],
  // Function lấy danh sách chi nhánh ngân hàng
  layDanhSachChiNhanhNganHang: async () => {},
  // Function lấy chi tiết một chi nhánh ngân hàng
  layChiTietChiNhanhNganHang: async () => null,
  // Function cập nhật (thêm/sửa) chi nhánh ngân hàng
  capNhatChiNhanhNganHang: async () => false,
  // Function lấy danh sách ngân hàng
  getListNganHang: async () => {},
  // Giá trị mặc định cho form tìm kiếm
  defaultFormValue: {
    ten: "",
    ma_ngan_hang: "",
    trang_thai: "",
    trang: 1,
    so_dong: 10,
  },
};

// Tạo Context với giá trị mặc định
// Context này sẽ chứa tất cả state và functions của module
export const DanhMucChiNhanhNganHangContext = createContext<DanhMucChiNhanhNganHangContextProps>(defaultContextValue);

// Custom hook để sử dụng Context một cách dễ dàng
// Các component con sẽ gọi hook này để lấy dữ liệu từ Context
export const useDanhMucChiNhanhNganHangContext = () => useContext(DanhMucChiNhanhNganHangContext);
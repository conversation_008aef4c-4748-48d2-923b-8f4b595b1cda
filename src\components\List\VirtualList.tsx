import { memo } from "react";
import { List, ListProps as AntListProps } from "antd";
import { twMerge } from "tailwind-merge";
import "./index.default.scss";

import { isEqual } from "lodash";
import {
  default as AntVirtualList,
  ListProps as VirtualListProps,
} from "rc-virtual-list";

const VirtualListComponent = <T,>(
  props: Omit<VirtualListProps<T>, "children"> & AntListProps<T>,
): React.ReactNode => {
  const {
    data,
    itemKey,
    className = "",
    height,
    itemHeight,
    loading,
    loadMore,
    renderItem,
    onScroll,
  } = props;

  const hanldeOnScroll = (e: React.UIEvent<HTMLElement, UIEvent>) => {
    if (e.currentTarget.scrollHeight - e.currentTarget.scrollTop === height) {
      onScroll?.(e);
    }
  };

  return (
    <List className="custom-list" loading={loading} loadMore={loadMore}>
      <AntVirtualList
        data={data}
        itemKey={itemKey}
        className={twMerge("custom-virtual-list", className)}
        height={height}
        itemHeight={itemHeight}
        onScroll={hanldeOnScroll}
      >
        {(item: T, index: number) => (
          <List.Item key={item[itemKey as keyof typeof item] as any}>
            {renderItem?.(item, index)}
          </List.Item>
        )}
      </AntVirtualList>
    </List>
  );
};

const VirtualList = memo(
  VirtualListComponent,
  isEqual,
) as typeof VirtualListComponent;

export default VirtualList;

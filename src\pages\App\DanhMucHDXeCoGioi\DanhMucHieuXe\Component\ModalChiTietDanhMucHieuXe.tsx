import {ArrowLeftOutlined, CheckOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {FormInput, HeaderModal, Popcomfirm} from "@src/components";
import {COLOR_PALETTE} from "@src/constants";
import {<PERSON><PERSON>, Col, Flex, Form, Modal, Row} from "antd";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useState} from "react";
import {useChiTietDanhMucHieuXeContext} from "../index.context";
import {FormChiTietDanhMucHieuXe, NGHIEP_VU_HIEU_XE, TRANG_THAI_HIEU_XE} from "./index.configs";

const {ma, ten, trang_thai, stt, nv, hang_xe} = FormChiTietDanhMucHieuXe;
interface Props {
  listHangXe: Array<CommonExecute.Execute.IDoiTac>;
}

//định ngh<PERSON>a c<PERSON> h<PERSON>, biến mà modal sẽ expose để component bên ngoài sử dụng thông qua ref
export interface IModalChiTietDanhMuchieuXeRef {
  open: (data?: CommonExecute.Execute.IChiTietDanhMucHieuXe) => void;
}

const ModalChiTietDanhMucHieuXeComponent = forwardRef<IModalChiTietDanhMuchieuXeRef, Props>(({listHangXe}: Props, ref) => {
  useImperativeHandle(ref, () => ({
    open: (dataDoiTac?: CommonExecute.Execute.IChiTietDanhMucHieuXe) => {
      setIsOpen(true);
      if (dataDoiTac) setChiTietDanhMucHieuXe(dataDoiTac);
    },
    close: () => setIsOpen(false),
  }));
  const [chiTietDanhMucHieuXe, setChiTietDanhMucHieuXe] = useState<CommonExecute.Execute.IChiTietDanhMucHieuXe | null>(null);
  const [isOpen, setIsOpen] = useState(false);

  const [form] = Form.useForm();

  const {loading, onUpdateDanhMucHieuXe} = useChiTietDanhMucHieuXeContext();
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const formValues = Form.useWatch([], form);
  console.log("form2", form.getFieldsValue());

  useEffect(() => {
    if (chiTietDanhMucHieuXe) {
      const arrFormData = [];
      for (const key in chiTietDanhMucHieuXe) {
        arrFormData.push({
          name: key as keyof CommonExecute.Execute.IChiTietDanhMucHieuXe,
          value: chiTietDanhMucHieuXe[key as keyof CommonExecute.Execute.IChiTietDanhMucHieuXe],
        });
      }
      form.setFields(arrFormData);
    }
  }, [chiTietDanhMucHieuXe]);

  //xử lý validate form
  useEffect(() => {
    form
      .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
      .then(() => {
        setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
      })
      .catch(() => {
        setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
      });
  }, [form, formValues]);

  const closeModal = useCallback(() => {
    setIsOpen(false);
    setChiTietDanhMucHieuXe(null);
    form.resetFields();
  }, []);

  //Bấm Update
  const onConfirm = async () => {
    try {
      const values: ReactQuery.IUpdateDanhMucHieuXeParams = form.getFieldsValue(); //lấy ra values của form
      //với trường hợp tạo mới -> check mã đối tác đã tồn tại

      values.stt = Number(values.stt);

      const params = {
        ...values,
      };

      const response = await onUpdateDanhMucHieuXe(params);
      console.log("check respon", response);
      if (response === -1) {
        console.log("cập nhật thành công");
        closeModal();
      } else {
        console.log("cập nhật thất bại");
      }

      // closeModal();
    } catch (error) {
      console.log("onConfirm", error);
    }
  };

  // RENDER
  //get màu trạng thái sử dụng
  const getStatusColor = (status?: string) => {
    let color = COLOR_PALETTE.gray[70];
    if (status === "D") color = COLOR_PALETTE.green[100];
    else if (status === "K") color = COLOR_PALETTE.red[50];
    return color;
  };

  //FOOTER
  const renderFooter = () => {
    return (
      <Form.Item>
        <Button type="default" onClick={() => closeModal()} className="mr-2" icon={<ArrowLeftOutlined />}>
          Quay lại
        </Button>
        <Popcomfirm
          title="Thông báo"
          onConfirm={onConfirm}
          okText="Lưu"
          description="Bạn có chắc muốn lưu thông tin?"
          buttonTitle={"Lưu"}
          buttonDisable={disableSubmit}
          buttonIcon={<CheckOutlined />}
          iconPosition="end"
          loading={loading}
        />
      </Form.Item>
    );
  };
  const renderFormInputColum = (props: any, span?: number) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  const renderForm = () => (
    <Form form={form} layout="vertical" initialValues={{trang_thai: TRANG_THAI_HIEU_XE[0].ma}}>
      {/* MÃ */}
      <Row gutter={16}>
        {renderFormInputColum({...ma, disabled: chiTietDanhMucHieuXe ? true : false}, 8)}
        {renderFormInputColum({...ten}, 8)}
        {renderFormInputColum({...hang_xe, disabled: chiTietDanhMucHieuXe ? true : false, options: listHangXe}, 8)}
      </Row>

      {/* gutter : khoảng cách giữa các ô */}
      <Row gutter={16}>
        {/* 3 cột có width = nhau  */}
        {renderFormInputColum({...nv, disabled: chiTietDanhMucHieuXe ? true : false, options: NGHIEP_VU_HIEU_XE}, 8)}
        {renderFormInputColum({...stt}, 8)}
        {renderFormInputColum({...trang_thai, options: TRANG_THAI_HIEU_XE}, 8)}
      </Row>
    </Form>
  );
  //Render
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        title={
          <HeaderModal
            title={chiTietDanhMucHieuXe ? `Chi tiết hiệu xe ${chiTietDanhMucHieuXe.ten}` : "Tạo mới hiệu xe"}
            trang_thai_ten={chiTietDanhMucHieuXe?.trang_thai_ten}
            trang_thai={chiTietDanhMucHieuXe?.trang_thai}
          />
        }
        // centered
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width={{
          xs: "50%",
          sm: "50%",
          md: "50%",
          lg: "50%",
          xl: "50%",
          xxl: "50%",
        }}
        footer={renderFooter}>
        {renderForm()}
      </Modal>
    </Flex>
  );
});

ModalChiTietDanhMucHieuXeComponent.displayName = "ModalChiTietDanhMucHieuXeComponent";
export const ModalChiTietDanhMucHieuXe = memo(ModalChiTietDanhMucHieuXeComponent, isEqual);

import {ArrowLeftOutlined, CheckOutlined} from "@ant-design/icons";
import {IFormInput, ReactQuery} from "@src/@types";
import {Button, FormInput, HeaderModal, Popcomfirm} from "@src/components";
import {validationRules} from "@src/hooks/formHook";
import {Col, Form, Modal, Row} from "antd";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useState, useMemo} from "react";
import {useDanhMucChiNhanhNganHangContext} from "../index.context";

// Import config từ index.configs chính
import {TRANG_THAI_CHI_NHANH_NGAN_HANG} from "../index.configs";

// ===== FORM FIELD CONFIGURATIONS =====
/**
 * CẤU HÌNH CÁC FIELD TRONG MODAL FORM
 *
 * Định nghĩa cách hiển thị và validation cho từng input field.
 * Sử dụng validation rules từ formHook để đảm bảo nhất quán.
 */
const FormChiTietChiNhanhNganHangConfigs = {
  /** Field mã chi nhánh - sẽ disabled khi edit */
  ma: {
    component: "input" as const,
    name: "ma",
    label: "Mã chi nhánh",
    placeholder: "Mã chi nhánh",
    rules: validationRules.ma, // Sử dụng rules từ formHook
  },
  /** Field tên chi nhánh */
  ten: {
    component: "input" as const,
    name: "ten",
    label: "Tên chi nhánh",
    placeholder: "Tên chi nhánh",
    rules: validationRules.ten, // Sử dụng rules từ formHook
  },
  /** Field dropdown chọn ngân hàng */
  ma_ngan_hang: {
    component: "select" as const,
    name: "ma_ngan_hang",
    label: "Ngân hàng",
    placeholder: "Chọn ngân hàng",
    rules: validationRules.ma_ngan_hang, // Sử dụng rules từ formHook
  },
  /** Field số thứ tự */
  stt: {
    component: "input" as const,
    name: "stt",
    label: "Số thứ tự",
    placeholder: "Số thứ tự",
    rules: validationRules.stt, // Sử dụng rules từ formHook
  },
  /** Field dropdown chọn trạng thái */
  trang_thai: {
    component: "select" as const,
    name: "trang_thai",
    label: "Trạng thái",
    placeholder: "Chọn trạng thái",
    rules: validationRules.trang_thai, // Sử dụng rules từ formHook
  },
};

// Destructure các field configs
const {ma, ten, ma_ngan_hang, stt, trang_thai} = FormChiTietChiNhanhNganHangConfigs;

// ===== INTERFACES =====
/**
 * INTERFACE CHO REF CỦA MODAL
 *
 * Định nghĩa các methods mà parent component có thể gọi thông qua ref.
 */
export interface IModalChiTietChiNhanhNganHangRef {
  /**
   * Mở modal
   * @param data - Dữ liệu chi nhánh để edit (undefined = tạo mới)
   */
  open: (data?: CommonExecute.Execute.IChiTietChiNhanhNganHang) => void;

  /**
   * Đóng modal
   */
  close: () => void;
}

/**
 * INTERFACE CHO PROPS CỦA MODAL COMPONENT
 */
interface ChiTietChiNhanhNganHangProps {
  /** Danh sách chi nhánh hiện tại để kiểm tra trùng mã khi tạo mới */
  listChiNhanhNganHang: Array<CommonExecute.Execute.IDanhSachChiNhanhNganHangPhanTrang>;
}

// ===== MAIN COMPONENT =====
/**
 * COMPONENT MODAL CHI TIẾT CHI NHÁNH NGÂN HÀNG
 *
 * Component modal được forwardRef để parent có thể điều khiển thông qua ref.
 * Hỗ trợ cả tạo mới và chỉnh sửa chi nhánh ngân hàng.
 */
const ModalChiTietChiNhanhNganHangComponent = forwardRef<IModalChiTietChiNhanhNganHangRef, ChiTietChiNhanhNganHangProps>(({listChiNhanhNganHang}: ChiTietChiNhanhNganHangProps, ref) => {
  // ===== EXPOSE METHODS THROUGH REF =====
  /**
   * EXPOSE METHODS CHO PARENT COMPONENT
   *
   * Parent component có thể gọi các methods này thông qua ref:
   * - open(): Mở modal với hoặc không có dữ liệu
   * - close(): Đóng modal
   */
  useImperativeHandle(ref, () => ({
    open: (dataChiNhanh?: CommonExecute.Execute.IChiTietChiNhanhNganHang) => {
      setIsOpen(true);

      form.resetFields();
      if (dataChiNhanh) setChiTietChiNhanh(dataChiNhanh);
    },
    close: () => setIsOpen(false),
  }));

  // ===== LOCAL STATE =====
  /** Dữ liệu chi nhánh để edit (null = tạo mới) */
  const [chiTietChiNhanh, setChiTietChiNhanh] = useState<CommonExecute.Execute.IChiTietChiNhanhNganHang | null>(null);
  /** Trạng thái hiển thị modal */
  const [isOpen, setIsOpen] = useState(false);

  const {capNhatChiNhanhNganHang, layDanhSachChiNhanhNganHang, defaultFormValue, loading, listNganHang} = useDanhMucChiNhanhNganHangContext();

  // ===== FORM STATE =====
  /** Form instance của Ant Design */
  const [form] = Form.useForm();
  /** Trạng thái disable submit button */
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  /** Watch form values để trigger validation */
  const formValues = Form.useWatch([], form);

  // ===== FORM INITIALIZATION =====
  /**
   * KHỞI TẠO DỮ LIỆU FORM KHI CÓ DATA EDIT
   *
   * Khi có dữ liệu chi nhánh để edit, tự động fill vào form.
   * Chạy mỗi khi chiTietChiNhanh thay đổi.
   */
  useEffect(() => {
    if (chiTietChiNhanh) {
      const arrFormData = [];
      for (const key in chiTietChiNhanh) {
        arrFormData.push({
          name: key,
          value: chiTietChiNhanh[key as keyof CommonExecute.Execute.IChiTietChiNhanhNganHang],
        });
      }
      form.setFields(arrFormData);
    }
  }, [chiTietChiNhanh, form]);

  // ===== FORM VALIDATION =====
  /**
   * REALTIME VALIDATION FORM
   *
   * Kiểm tra form validation mỗi khi formValues thay đổi.
   * Disable submit button nếu form invalid.
   */
  useEffect(() => {
    form
      .validateFields({validateOnly: true})
      .then(() => {
        setDisableSubmit(false);
      })
      .catch(() => {
        setDisableSubmit(true);
      });
  }, [form, formValues]);

  // ===== MODAL ACTIONS =====
  /**
   * ĐÓNG MODAL VÀ RESET FORM
   *
   * Function để đóng modal và clean up state.
   * Reset form về trạng thái ban đầu.
   */
  const closeModal = useCallback(() => {
    setIsOpen(false);
    setChiTietChiNhanh(null);
    form.resetFields();
  }, [form]);

  /**
   * XỬ LÝ SUBMIT FORM
   *
   * Function chính để xử lý việc lưu dữ liệu chi nhánh ngân hàng.
   * Bao gồm:
   * - Kiểm tra trùng mã khi tạo mới
   * - Gọi API cập nhật
   * - Refresh danh sách nếu thành công
   * - Đóng modal
   */
  const onConfirm = async () => {
    try {
      const values: ReactQuery.ICapNhatChiNhanhNganHangParams = form.getFieldsValue();

      // KIỂM TRA TRÙNG MÃ KHI TẠO MỚI
      // if (!chiTietChiNhanh) {
      //   for (let i = 0; i < listChiNhanhNganHang.length; i++) {
      //     if (listChiNhanhNganHang[i].ma === values.ma && listChiNhanhNganHang[i].ma_ngan_hang === values.ma_ngan_hang) {
      //       form.setFields([
      //         {
      //           name: "ma",
      //           errors: ["Mã chi nhánh đã tồn tại!"],
      //         },
      //       ]);
      //       return;
      //     }
      //   }
      // }

      // GỌI API CẬP NHẬT
      const success = await capNhatChiNhanhNganHang(values);
      if (success) {
        // REFRESH DANH SÁCH VÀ ĐÓNG MODAL
        await layDanhSachChiNhanhNganHang(defaultFormValue);
        closeModal();
      }
    } catch (error) {
      console.log("onConfirm", error);
    }
  };

  // ===== RENDER FUNCTIONS =====
  /**
   * RENDER FOOTER CỦA MODAL
   *
   * Hiển thị các button action:
   * - Button "Quay lại": Đóng modal
   * - Button "Lưu": Submit form với confirmation
   */
  const renderFooter = () => {
    return (
      <Form.Item>
        <Button type="default" onClick={() => closeModal()} className="mr-2" icon={<ArrowLeftOutlined />}>
          Quay lại
        </Button>
        <Popcomfirm
          title={disableSubmit ? "Vui lòng nhập đầy đủ thông tin" : ""}
          onConfirm={onConfirm}
          okText="Lưu"
          description="Bạn có chắc muốn lưu thông tin?"
          buttonTitle={"Lưu"}
          buttonDisable={disableSubmit}
          buttonClassName="w-40"
          buttonIcon={<CheckOutlined />}
          iconPosition="end"
          loading={loading}
        />
      </Form.Item>
    );
  };

  /**
   * RENDER FORM COLUMN COMPONENT
   *
   * Function helper để render các input field với cấu hình chung.
   * Thêm class CSS để giảm margin bottom của Form.Item.
   */
  const renderFormColumn = (props: IFormInput) => (
    <div className="form-item-compact">
      <FormInput {...props} />
    </div>
  );

  // ===== COMPUTED VALUES =====
  /**
   * FORMAT DANH SÁCH NGÂN HÀNG THÀNH OPTIONS CHO DROPDOWN
   *
   * Transform dữ liệu từ API thành format phù hợp cho FormInput select.
   * Bao gồm debug logging để track data flow.
   */
  const nganHangOptions = useMemo(() => {
    if (!listNganHang || !Array.isArray(listNganHang)) {
      return [];
    }

    // Format theo structure {ten, ma} để phù hợp với fieldNames của FormInput
    const options = listNganHang.map(item => ({
      ten: item.ten || item.label || "Không xác định",
      ma: item.ma || item.value || "",
    }));

    return options;
  }, [listNganHang]);

  /**
   * LẤY OPTIONS TRẠNG THÁI CHO MODAL (LOẠI BỎ "TẤT CẢ")
   *
   * Sử dụng config từ file chính nhưng loại bỏ option "Tất cả"
   * vì trong modal phải chọn trạng thái cụ thể.
   */
  const trangThaiOptionsForModal = useMemo(() => {
    return TRANG_THAI_CHI_NHANH_NGAN_HANG.filter(item => item.ma !== "");
  }, []);

  const renderForm = () => (
    <Form
      form={form}
      layout="vertical"
      className="mt-2"
      style={
        {
          // CSS để giảm khoảng cách giữa các Form.Item
          "--form-item-margin-bottom": "12px",
        } as React.CSSProperties
      }>
      {/* Row 1: Số thứ tự + Mã chi nhánh */}
      <Row gutter={16} style={{marginBottom: "8px"}}>
        <Col span={9}>{renderFormColumn({...ma, disabled: chiTietChiNhanh ? true : false})}</Col>
        <Col span={15}>{renderFormColumn({...ten})}</Col>
      </Row>
      {/* Row 2: Ngân hàng + Trạng thái */}
      <Row style={{marginBottom: "8px"}}>
        <Col span={24}>{renderFormColumn({...ma_ngan_hang, options: nganHangOptions})}</Col>
      </Row>

      {/* Row 3: Tên chi nhánh (full width) */}
      <Row gutter={16} style={{marginBottom: "8px"}}>
        <Col span={9}>{renderFormColumn({...stt})}</Col>
        <Col span={15}>{renderFormColumn({...trang_thai, options: trangThaiOptionsForModal})}</Col>
      </Row>
    </Form>
  );

  // ===== MAIN RENDER =====
  /**
    
     */
  return (
    <Modal
      title={
        <HeaderModal title={chiTietChiNhanh ? `${chiTietChiNhanh.ten}` : "Tạo mới chi nhánh ngân hàng"} trang_thai_ten={chiTietChiNhanh?.trang_thai_ten} trang_thai={chiTietChiNhanh?.trang_thai} />
      }
      open={isOpen}
      onOk={() => closeModal()}
      onCancel={() => closeModal()}
      maskClosable={false}
      width={700}
      styles={{
        body: {
          paddingTop: "8px", // Giảm padding top
          paddingBottom: "16px", // Padding bottom vừa phải
        },
      }}
      footer={renderFooter()}>
      {renderForm()}
    </Modal>
  );
});

// ===== COMPONENT SETUP =====
/**
 * THIẾT LẬP COMPONENT EXPORT
 *
 * Đặt displayName và export component với memo để optimize performance.
 */
ModalChiTietChiNhanhNganHangComponent.displayName = "ModalChiTietChiNhanhNganHangComponent";
export const ModalChiTietChiNhanhNganHang = memo(ModalChiTietChiNhanhNganHangComponent, isEqual);

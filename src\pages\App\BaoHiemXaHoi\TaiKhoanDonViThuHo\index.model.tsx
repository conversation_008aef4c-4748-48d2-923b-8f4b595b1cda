import {ReactQuery} from "@src/@types";
import {TableTaiKhoanDonViThuHoColumnDataType} from "./index.configs";

export interface IQuanLyTaiKhoanDonViThuHoContextProps {
  listTaiKhoanDonViThuHo: Array<CommonExecute.Execute.ITaiKhoanDonViThuHo>;
  listDonVi: Array<CommonExecute.Execute.IDanhSachDonViBHXH>;
  tongSoDong: number;
  loading: boolean;
  filterParams: ReactQuery.ITimKiemPhanTrangTaiKhoanDonViThuHoParams & ReactQuery.IPhanTrang;
  getListTaiKhoanDonViThuHo: (params?: ReactQuery.ITimKiemPhanTrangTaiKhoanDonViThuHoParams & ReactQuery.IPhanTrang) => Promise<void>;
  getChiTietTaiKhoanDonViThuHo: (params: TableTaiKhoanDonViThuHoColumnDataType) => Promise<CommonExecute.Execute.ITaiKhoanDonViThuHo>;
  capNhatChiTietTaiKhoanDonViThuHo: (params: ReactQuery.ICapNhatTaiKhoanDonViThuHoParams, isEditMode?: boolean) => Promise<any>;
  // layDanhSachTaiKhoanQuanLy: (maTaiKhoanCha: string) => Promise<any[]>;
  layDanhSachTaiKhoanQuanLy: (maTaiKhoanCha: string, maDviOptional?: string) => Promise<any[]>;
  capNhatDanhSachTaiKhoanQuanLy: (data: {ma: string, ma_dvi?: string, ds: Array<{ma: string}>}) => Promise<any>;
  setFilterParams: React.Dispatch<React.SetStateAction<ReactQuery.ITimKiemPhanTrangTaiKhoanDonViThuHoParams & ReactQuery.IPhanTrang>>;
}

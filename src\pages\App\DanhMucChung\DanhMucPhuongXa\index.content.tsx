/**
 * T<PERSON><PERSON> dụng: <PERSON><PERSON><PERSON> thị giao diện bảng danh sách phường xã với tìm kiếm, phân trang, CRUD
 */
import React, {memo, useCallback, useMemo, useRef, useState} from "react";
import {Col, Form, InputRef, Row, Table, TableColumnType, Tag} from "antd";
import {FilterDropdownProps} from "antd/es/table/interface";
import {PlusCircleOutlined, SearchOutlined} from "@ant-design/icons";

import {Button, FormInput, Highlighter, TableFilterDropdown} from "@src/components";
import {COLOR_PALETTE, ID_PAGE} from "@src/constants";
import {IFormInput, ReactQuery} from "@src/@types";
import {defaultPaginationTableProps, defaultTableProps, fillRowTableEmpty} from "@src/hooks";

import {useDanhMucPhuongXaContext} from "./index.context";
import {FormTimKiemDanhMucPhuong<PERSON>a, table<PERSON><PERSON><PERSON>a<PERSON>olumn, TablePhuongXaColumnDataType, radioItemTrangThaiPhuongXaSelect, radioItemNgayApDungPhuongXaSelect} from "./index.configs";
import {ModalChiTietPhuongXa, IModalChiTietPhuongXaRef} from "./Component";
import "./index.default.scss";

type DataIndex = keyof TablePhuongXaColumnDataType;

//Destructuring form search values
const {ma_tinh, ma_quan, ngay_ad, ma, ten, trang_thai} = FormTimKiemDanhMucPhuongXa;

const DanhMucPhuongXaContent: React.FC = memo(() => {
  const {listPhuongXa, listTinhThanh, listQuanHuyen, loading, tongSoDong, filterParams, getListQuanHuyen, getChiTietPhuongXa, setFilterParams} = useDanhMucPhuongXaContext();

  //Quản lý từ khóa tìm kiếm trong cột và cột đang được tìm kiếm
  const [searchText, setSearchText] = useState("");
  const [searchedColumn, setSearchedColumn] = useState<DataIndex | "">("");

  //State để lưu current search parameters
  const [currentSearchParams, setCurrentSearchParams] = useState<ReactQuery.ITimKiemPhanTrangDanhMucPhuongXaParams>({});

  //===== REFS =====
  const searchInput = useRef<InputRef>(null);
  const modalChiTietRef = useRef<IModalChiTietPhuongXaRef>(null);

  //Tác dụng: Tạo danh sách tỉnh thành cho dropdown filter, chỉ hiển thị các tỉnh đang hoạt động
  const dropdownOptionsTinhThanh = useMemo(() => {
    if (!listTinhThanh || !Array.isArray(listTinhThanh)) {
      return [{ma: "", ten: "Chọn tỉnh/thành phố"}];
    }

    const allOption = [{ma: "", ten: "Tất cả"}];
    const validProvinces = listTinhThanh
      .filter(item => {
        const hasValidData = item && item.ma && item.ten;
        if (!hasValidData) return false;

        const trangThai = item.trang_thai?.toString();
        const isActive = trangThai === "1" || trangThai === "D" || trangThai === "Đang sử dụng" || !trangThai;

        return hasValidData && isActive;
      })
      .map(item => ({
        ma: item.ma?.toString() || "",
        ten: item.ten?.toString() || "",
      }));

    return [...allOption, ...validProvinces];
  }, [listTinhThanh]);

  //Tác dụng: Tạo danh sách quận huyện cho dropdown filter, chỉ hiển thị các quận huyện đang hoạt động
  const dropdownOptionsQuanHuyen = useMemo(() => {
    if (!listQuanHuyen || !Array.isArray(listQuanHuyen)) {
      return [{ma: "", ten: "Chọn quận/huyện"}];
    }

    const allOption = [{ma: "", ten: "Tất cả"}];
    const validDistricts = listQuanHuyen
      .filter(item => {
        const hasValidData = item && item.ma && item.ten;
        if (!hasValidData) return false;

        const trangThai = item.trang_thai?.toString();
        const isActive = trangThai === "1" || trangThai === "D" || trangThai === "Đang sử dụng" || !trangThai;

        return hasValidData && isActive;
      })
      .map(item => ({
        ma: item.ma?.toString() || "",
        ten: item.ten?.toString() || "",
      }));

    return [...allOption, ...validDistricts];
  }, [listQuanHuyen]);

  //Tác dụng: Tạo data table với STT và xử lý ngày áp dụng
  const dataTableWithSTT = useMemo(() => {
    try {
      const currentPage = filterParams?.trang || 1;
      const currentPageSize = (filterParams?.so_dong || defaultPaginationTableProps.defaultPageSize) as number;

      const tableData =
        listPhuongXa?.map((item, index) => {
          //Xử lý ngày áp dụng: convert string "01/01/1900" về number 19000101 để tương thích với API
          let ngayAdNumber = 19000101; //default
          if (item.ngay_ad) {
            if (typeof item.ngay_ad === "string") {
              //Convert "01/01/1900" -> 19000101 hoặc "01/07/2025" -> 20250701
              if (item.ngay_ad === "01/01/1900") {
                ngayAdNumber = 19000101;
              } else if (item.ngay_ad === "01/07/2025") {
                ngayAdNumber = 20250701;
              } else {
                //Nếu format khác, giữ default
                ngayAdNumber = 19000101;
              }
            } else if (typeof item.ngay_ad === "number") {
              ngayAdNumber = item.ngay_ad;
            }
          }

          return {
            ...item,
            ngay_ad: ngayAdNumber, //Convert về number để tương thích với API calls
            postcode: item.postcode || "", //Đảm bảo có postcode field
            key: item.ma_tinh && item.ma_quan && item.ma ? `${item.ma_tinh}-${item.ma_quan}-${item.ma}-${ngayAdNumber}-${item.sott}` : `row-${index}`,
            sott: (currentPage - 1) * currentPageSize + index + 1,
          };
        }) || [];

      const arrEmptyRow = fillRowTableEmpty(tableData.length, currentPageSize);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      return [];
    }
  }, [listPhuongXa, filterParams?.trang, filterParams?.so_dong]);

  //Tác dụng: Xử lý submit form tìm kiếm và cập nhật filter parameters
  const onSearchApi = useCallback(
    (values: ReactQuery.ITimKiemPhanTrangDanhMucPhuongXaParams) => {
      setCurrentSearchParams(values);

      setFilterParams({
        ma_tinh: values.ma_tinh || "",
        ma_quan: values.ma_quan || "",
        ngay_ad: values.ngay_ad || "",
        ma: values.ma || "",
        ten: values.ten || "",
        trang_thai: values.trang_thai || "",
        trang: 1,
        so_dong: filterParams?.so_dong || defaultPaginationTableProps.defaultPageSize,
      });
    },
    [setFilterParams, filterParams?.so_dong],
  );

  //Tác dụng: Xử lý thay đổi trang và số dòng hiển thị trong pagination
  const onChangePage = useCallback(
    (newPage: number, newPageSize: number) => {
      setFilterParams(prev => ({
        ...prev,
        trang: newPage,
        so_dong: newPageSize,
      }));
    },
    [setFilterParams],
  );

  //Tác dụng: Xử lý thay đổi tỉnh thành trong form search để load quận huyện tương ứng
  const handleSearchTinhThanhChange = useCallback(
    (value: any) => {
      //Khi chọn tỉnh thành khác, load danh sách quận huyện tương ứng
      if (value && value !== "") {
        getListQuanHuyen({ma_tinh: value});
      } else {
        //Nếu chọn "Tất cả", clear danh sách quận huyện
        getListQuanHuyen({ma_tinh: ""});
      }
    },
    [getListQuanHuyen],
  );

  const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: DataIndex) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);

  const handleReset = useCallback(
    (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: DataIndex) => {
      clearFilters();
      setSearchText("");
      handleSearch([""], confirm, dataIndex);
    },
    [handleSearch],
  );

  //Tác dụng: Xử lý click vào row để hiển thị modal chi tiết phường xã
  const handleRowClick = useCallback(
    async (record: TablePhuongXaColumnDataType) => {
      //Bỏ qua nếu là empty row hoặc đang loading
      if (record.key.toString().includes("empty") || loading) {
        return;
      }

      try {
        if (!record.ma) {
          return;
        }

        if (!modalChiTietRef.current) {
          return;
        }

        const response = await getChiTietPhuongXa({
          ma_tinh: record.ma_tinh || "",
          ma_quan: record.ma_quan || "",
          ma: record.ma,
          ngay_ad: record.ngay_ad || 19000101,
        });

        if (response && Object.keys(response).length > 0) {
          try {
            modalChiTietRef.current.open(response);
          } catch (modalError) {
            //Xử lý lỗi thầm lặng
          }
        }
      } catch (error) {
        //Xử lý lỗi thầm lặng
      }
    },
    [getChiTietPhuongXa, loading],
  );

  //Tác dụng: Mở modal để tạo mới phường xã
  const handleThemMoi = useCallback(() => {
    modalChiTietRef.current?.open();
  }, []);

  //Tác dụng: Reload danh sách sau khi lưu thành công
  const handleAfterSave = useCallback(() => {
    setFilterParams(prev => ({...prev}));
  }, [setFilterParams]);

  const renderFormInputColumn = useCallback(
    (props: IFormInput, span = 4) => (
      <Col span={span}>
        <FormInput {...props} />
      </Col>
    ),
    [],
  );

  //Tác dụng: Render form tìm kiếm ở header table
  const renderHeaderTablePhuongXa = useCallback(
    () => (
      <Form layout="vertical" className="[&_.ant-form-item]:mb-0" onFinish={onSearchApi}>
        <Row gutter={16} align="bottom">
          {renderFormInputColumn({...ma_tinh, options: dropdownOptionsTinhThanh, onChange: handleSearchTinhThanhChange}, 3)}
          {renderFormInputColumn({...ma_quan, options: dropdownOptionsQuanHuyen}, 3)}
          {renderFormInputColumn({...ngay_ad, options: radioItemNgayApDungPhuongXaSelect}, 5)}
          {renderFormInputColumn(ma, 3)}
          {renderFormInputColumn(ten, 3)}
          {renderFormInputColumn({...trang_thai, options: radioItemTrangThaiPhuongXaSelect}, 3)}
          <Col span={2}>
            <Button type="primary" htmlType="submit" loading={loading} icon={<SearchOutlined />} block>
              Tìm kiếm
            </Button>
          </Col>
          <Col span={2}>
            <Button type="primary" block icon={<PlusCircleOutlined />} onClick={handleThemMoi}>
              Tạo mới
            </Button>
          </Col>
        </Row>
      </Form>
    ),
    [ma_tinh, ma_quan, ngay_ad, ma, ten, trang_thai, dropdownOptionsTinhThanh, dropdownOptionsQuanHuyen, loading, onSearchApi, handleThemMoi, renderFormInputColumn, handleSearchTinhThanhChange],
  );

  //Tác dụng: Tạo cấu hình search cho các cột table
  const getColumnSearchProps = useCallback(
    (dataIndex: DataIndex, title: string): TableColumnType<TablePhuongXaColumnDataType> => ({
      filterDropdown: ({setSelectedKeys, selectedKeys, confirm, clearFilters}) => (
        <TableFilterDropdown
          ref={searchInput}
          title={title}
          selectedKeys={selectedKeys}
          dataIndex={dataIndex}
          setSelectedKeys={setSelectedKeys}
          handleSearch={handleSearch}
          confirm={confirm}
          clearFilters={clearFilters}
          handleReset={handleReset}
        />
      ),
      filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? COLOR_PALETTE.blue[60] : undefined}} />,
      onFilter: (value, record) =>
        record[dataIndex]
          ?.toString()
          .toLowerCase()
          .includes((value as string).toLowerCase()) || false,
      onFilterDropdownOpenChange: visible => {
        if (visible) {
          setTimeout(() => searchInput.current?.select(), 100);
        }
      },
      render: (text, record) => {
        if (record.key?.toString().includes("empty")) return <span style={{height: "22px", display: "inline-block"}}>&nbsp;</span>;

        if (dataIndex === "trang_thai_ten") {
          if (!text) return "";
          const color = text === "Đang sử dụng" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];
          return (
            <Tag color={color} className="text-[11px]">
              {text}
            </Tag>
          );
        }

        // Xử lý đặc biệt cho cột ngày áp dụng: convert số thành text
        if (dataIndex === "ngay_ad") {
          const convertNgayApDungToText = (ngayAd: number | string | null | undefined): string => {
            if (ngayAd === null || ngayAd === undefined || ngayAd === "") {
              return "";
            }
            const ngayAdStr = String(ngayAd);
            if (ngayAdStr === "19000101") {
              return "01/01/1900";
            } else if (ngayAdStr === "20250701") {
              return "01/07/2025";
            }
            if (ngayAdStr === "01/01/1900") {
              return "Áp dụng từ ngày 01/01/1900";
            } else if (ngayAdStr === "01/07/2025") {
              return "Áp dụng từ ngày 01/07/2025";
            }
            if (ngayAdStr === "null" || ngayAdStr === "undefined") {
              return "";
            }
            return ngayAdStr;
          };

          const displayText = convertNgayApDungToText(text);
          return searchedColumn === dataIndex ? <Highlighter searchWords={[searchText]} textToHighlight={displayText} /> : displayText;
        }

        return searchedColumn === dataIndex ? (
          <Highlighter searchWords={[searchText]} textToHighlight={text ? text.toString() : ""} />
        ) : text !== undefined ? (
          text
        ) : (
          <Tag color={"transparent"} className="!text-white text-[11px]">
            {"\u00A0"}
          </Tag>
        );
      },
    }),
    [handleSearch, handleReset, searchText, searchedColumn],
  );

  //Tác dụng: Tạo columns table với tính năng search
  const columnsWithSearch = useMemo(() => {
    return tablePhuongXaColumn?.map((col: any) => {
      if (
        col.dataIndex &&
        ["ma", "ten", "ma_tinh", "ten_tinh", "ma_quan", "ten_quan", "postcode", "ngay_ad", "trang_thai_ten", "ngay_tao", "nguoi_tao", "ngay_cap_nhat", "nguoi_cap_nhat"].includes(col.dataIndex)
      ) {
        return {
          ...col,
          ...getColumnSearchProps(col.dataIndex as DataIndex, col.title as string),
        };
      }
      return col;
    });
  }, [getColumnSearchProps]);

  return (
    <div id={ID_PAGE.DANH_MUC_PHUONG_XA} className="[&_.ant-space]:w-full">
      <Table<TablePhuongXaColumnDataType>
        {...defaultTableProps}
        dataSource={dataTableWithSTT}
        columns={columnsWithSearch}
        loading={loading}
        pagination={{
          ...defaultPaginationTableProps,
          current: filterParams?.trang || 1,
          pageSize: (filterParams?.so_dong || defaultPaginationTableProps.defaultPageSize) as number,
          total: tongSoDong,
          onChange: onChangePage,
          showQuickJumper: true,
          showTotal: (total, range) => {
            return `${range[0]}-${range[1]} của ${total} bản ghi`;
          },
        }}
        title={renderHeaderTablePhuongXa}
        onRow={record => ({
          style: {cursor: loading ? "progress" : "pointer"},
          onClick: () => handleRowClick(record),
        })}
      />
      <ModalChiTietPhuongXa ref={modalChiTietRef} onAfterSave={handleAfterSave} danhSachTinhThanh={dropdownOptionsTinhThanh} danhSachQuanHuyen={dropdownOptionsQuanHuyen} />
    </div>
  );
});

DanhMucPhuongXaContent.displayName = "DanhMucPhuongXaContent";
export default DanhMucPhuongXaContent;

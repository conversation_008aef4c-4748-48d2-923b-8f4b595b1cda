import {ReactQuery} from "@src/@types";

export interface IPheDuyetHopDongXCGContextProps {
  loading: boolean;
  tongSoDong: number;
  danhSachHopDongTrinhDuyetXCG: Array<CommonExecute.Execute.IDoiTac>;
  timKiemPhanTrangHopDongTrinhDuyetXCG: (params?: ReactQuery.ITimKiemPhanTrangHopDongTrinhDuyetParams & ReactQuery.IPhanTrang) => Promise<void>;
  xemChiTietHopDongTrinhDuyet: (params: ReactQuery.IXemChiTietHopDongTrinhDuyetParams) => Promise<CommonExecute.Execute.IHopDongTrinhDuyetXCG>;
  handlePheDuyetHopDong: (params: ReactQuery.ICapNhatHopDongTrinhDuyetParams) => Promise<boolean>;
  handleGoDuyetHopDong: (params: ReactQuery.ICapNhatHopDongTrinhDuyetParams) => Promise<boolean>;
  handleTuChoiDuyetHopDong: (params: ReactQuery.ICapNhatHopDongTrinhDuyetParams) => Promise<boolean>;
}

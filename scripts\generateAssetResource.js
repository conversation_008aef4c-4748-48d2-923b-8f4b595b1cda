/* eslint-disable indent */
const Hjson = require("hjson");
const fs = require("fs");

function genStringResource() {
  try {
    let data = fs.readFileSync("./app/i18n/locales/en.ts", "utf8");
    data = data.toString().replace("import { I18nApp } from 'src/AppProvider';", "").replace("export default en;", "").replace(";", "").replace("const en: I18nApp =", "");

    const json = Hjson.parse(data);
    const stringName = Object.keys(json);
    fs.writeFileSync(
      "./app/assets/strings.ts",
      `import I18n from 'src/i18n';
function strings(){
    return{${stringName.map((string, _index) => {
      let path = "";
      if (typeof json[string] === "string") {
        path = `
        ${string}: I18n.t("${string}")`;
      } else {
        const keys = Object.keys(json[string]);
        keys.map((val, i) => {
          path += `
          ${string}_${val}: I18n.t("${string}.${val}")${i !== keys.length - 1 ? "," : ""}`;
        });
      }
      return path;
    })}
}}
export default strings
        `,
    );
    console.log(`============== Linked ${stringName.length} string ==============`);
  } catch (err) {
    console.error(err);
  }
}

function genImageResource() {
  fs.readdir("./src/assets/images/", (err, fileName) => {
    if (err) {
      console.log(err);
      return;
    }
    fs.writeFileSync(
      "./src/assets/imagesAsset.ts",
      `${fileName
        .map(iconName => {
          let iconNameFormat = iconName.replace(/\.(png|jpg|jpeg)$/, "");
          let path = `import ${iconNameFormat} from "./images/${iconName}";`;
          return path;
        })
        .join("\n")}
\nconst images = {
    ${fileName
      .map(iconName => {
        let iconNameFormat = iconName.replace(/\.(png|jpg|jpeg)$/, "");
        let path = `  ${iconNameFormat}: ${iconNameFormat},`;
        return path;
      })
      .join("\n")}
    }
\nexport default images;`,
      {encoding: "utf8", flag: "w"},
    );
    console.log(`============== Linked ${fileName.length} images ==============`);
  });
}

function genIconResource() {
  fs.readdir("./src/assets/icons/", (err, fileName) => {
    if (err) {
      console.log(err);
      return;
    }
    fs.writeFileSync(
      "./src/assets/iconsAsset.ts",
      `${fileName
        .map(iconName => {
          let iconNameFormat = iconName.replace(/\.(png|jpg|jpeg)$/, "");
          let path = `import ${iconNameFormat} from "./images/${iconName}";`;
          return path;
        })
        .join("\n")}
\nconst icons = {
    ${fileName
      .map(iconName => {
        let iconNameFormat = iconName.replace(/\.(png|jpg|jpeg)$/, "");
        let path = `  ${iconNameFormat}: ${iconNameFormat},`;
        return path;
      })
      .join("\n")}
    }
\nexport default icons;`,
      {encoding: "utf8", flag: "w"},
    );
    console.log(`============== Linked ${fileName.length} images ==============`);
  });
}

function genSvgResource() {
  fs.readdir("./src/assets/svg/", (err, fileName) => {
    if (err) {
      console.log(err);
      return;
    }
    fs.writeFileSync(
      "./src/assets/svgAsset.ts",
      `${fileName
        .map(iconName => {
          let iconNameFormat = iconName.replace(/\.(svg)$/, "");
          let path = `import ${iconNameFormat} from "./svg/${iconName}";`;
          return path;
        })
        .join("\n")}
\nconst svg = {
    ${fileName
      .map(iconName => {
        let iconNameFormat = iconName.replace(/\.(svg)$/, "");
        let path = `  ${iconNameFormat}: ${iconNameFormat},`;
        return path;
      })
      .join("\n")}
    }
\nexport default svg;`,
      {encoding: "utf8", flag: "w"},
    );
    console.log(`============== Linked ${fileName.length} images ==============`);
  });
}

genImageResource();
// genStringResource();
genIconResource();
genSvgResource();

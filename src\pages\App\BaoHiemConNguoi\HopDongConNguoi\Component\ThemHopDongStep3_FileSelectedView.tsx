import {LeftOutlined, RightOutlined} from "@ant-design/icons";
import {<PERSON><PERSON>, Worker} from "@react-pdf-viewer/core";
import "@react-pdf-viewer/core/lib/styles/index.css";
import {But<PERSON>, Image} from "antd";
import {isEqual} from "lodash";
import workerUrl from "pdfjs-dist/build/pdf.worker.js?url";
import {memo} from "react";
import {FileSelectedViewComponentProps} from "./Constant";

const FileSelectedViewComponent = ({selectedFile, getCurrentFileIndex, getTotalFiles, navigateToPrevious, navigateToNext}: FileSelectedViewComponentProps) => {
  // RENDER RA FILE
  const renderFile = (file: any) => {
    if (file.type === "image") {
      return (
        <Image
          src={file.url}
          width="100%"
          height="100%"
          style={{
            objectFit: "contain",
            border: "1px solid #eee",
            borderRadius: 8,
            boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
            display: "block",
          }}
          alt={file.name}
          preview={{visible: true}}
        />
      );
    } else if (file.type === "pdf") {
      return (
        <div
          style={{
            width: "100%",
            height: "90vh",
            border: "1px solid #eee",
            borderRadius: 8,
            boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
            display: "block",
            // overflowY: "auto",
            // justifyContent: "center",
            // alignItems: "center",
          }}>
          {/* <TransformWrapper
            wheel={{step: 0.1}} // Tùy chỉnh độ nhạy của cuộn chuột
            doubleClick={{disabled: true}}
            minScale={1}
            maxScale={2}
            initialScale={1}>
            <TransformComponent
              wrapperStyle={{width: "100%", height: 841}} // 👈 wrapper
              contentStyle={{width: "100%", height: 841}} // 👈 nội dung (PDF
            >
              <Document
                file={file.url}
                onLoadSuccess={({numPages}: {numPages: number}) => setNumPagesPdf(numPages)}
                onLoadError={data => {
                  console.log("onLoadError", data);
                }}>
                {Array.from({length: numPagesPdf}, (_, index) => (
                  <Page key={`page_${index + 1}`} pageNumber={index + 1} scale={1} height={841} />
                ))}
              </Document>
            </TransformComponent>
          </TransformWrapper> */}
          <Worker workerUrl={workerUrl}>
            <Viewer fileUrl={file.url} />
          </Worker>
        </div>
      );
    }
    return null;
  };
  return (
    <>
      <div className="relative flex min-h-[65vh] items-center justify-center overflow-hidden">
        {selectedFile ? (
          <div className="relative flex h-full w-full justify-center">
            {selectedFile.type === "image" ? (
              <Image
                src={selectedFile.url}
                width="100%"
                style={{border: "1px solid #eee"}}
                className="bg-white max-h-[65vh] max-w-full cursor-pointer rounded-lg border border-[#eee] object-contain shadow-[0_2px_8px_rgba(0,0,0,0.1)]"
                alt={selectedFile.name}
                preview={true}
              />
            ) : (
              renderFile(selectedFile)
            )}
          </div>
        ) : (
          <div
            style={{border: "1px solid #eee"}}
            className="flex h-[40vh] w-[40vw] flex-col justify-center rounded-lg bg-[#fafafa] text-center text-[16px] text-[#999] shadow-[0_2px_8px_rgba(0,0,0,0.1)]">
            <div style={{fontSize: 48, opacity: 0.5}}>📷</div>
            <div style={{marginBottom: 8}}>Chưa có file nào được chọn</div>
            <div style={{fontSize: 14, opacity: 0.7}}>Vui lòng chọn một file từ danh sách bên phải</div>
          </div>
        )}
      </div>
      {/* Navigation arrows below the image */}
      {selectedFile && getTotalFiles() > 1 && (
        <div className="mb-5 flex items-center justify-center gap-4">
          <Button type="primary" shape="circle" icon={<LeftOutlined />} onClick={navigateToPrevious} style={{background: "gray", border: "none", opacity: 0.4}} />
          <span style={{fontWeight: 500}}>
            {getCurrentFileIndex() + 1} / {getTotalFiles()}
          </span>
          <Button type="primary" shape="circle" icon={<RightOutlined />} onClick={navigateToNext} style={{background: "gray", border: "none", opacity: 0.4}} />
        </div>
      )}
    </>
  );
};

export const FileSelectedView = memo(FileSelectedViewComponent, isEqual);

// import {useQuery} from "react-query";
// import {ReactQuery} from "@src/@types";
// import {Dog} from "@src/@types/Dog";
// import {DEFAULT_PARAMS, QUERY_KEYS} from "@src/constants";
// import {DogEndpoint} from "../axios";

// const {GET_DOG_LIST} = QUERY_KEYS.DOG;

// export const useGetDogList = (params: ReactQuery.Queries = DEFAULT_PARAMS, options?: ReactQuery.Options) => {
//   return useQuery<Dog.GetDogList.Response>(
//     [GET_DOG_LIST, params], //query key : Khi bạn gọi useQuery nhiều lần với cùng key → nó sẽ dùng cache, Việc đưa params vào key giúp cache khác nhau cho mỗi bộ tham số.
//     () => DogEndpoint.getDogList(params), //query function : <PERSON><PERSON><PERSON> là hàm thực sự gọi API từ axios
//     options, //option
//   );
// };

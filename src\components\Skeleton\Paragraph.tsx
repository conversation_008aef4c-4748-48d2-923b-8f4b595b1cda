import React, { memo, PropsWithChildren } from "react";
import {
  Skeleton as AntSkeleton,
  SkeletonProps as AntSkeletonProps,
} from "antd";
import { twMerge } from "tailwind-merge";

import { isEqual } from "lodash";

export interface SkeletonProps extends AntSkeletonProps {
  loading?: boolean;
}

const ParagraphSkeletonComponent: React.FC<PropsWithChildren<SkeletonProps>> = (
  props,
) => {
  const {
    className = "",
    loading = true,
    children,
    active = true,
    ...etc
  } = props;

  return (
    <React.Fragment>
      {loading ? (
        <AntSkeleton
          className={twMerge("custom-paragraph-skeleton", className)}
          active={active}
          {...etc}
        />
      ) : (
        children
      )}
    </React.Fragment>
  );
};

const ParagraphSkeleton = memo(ParagraphSkeletonComponent, isEqual);

export default ParagraphSkeleton;

import {IFormInput, ReactQuery} from "@src/@types";
import {colWidthByKey, defaultTableColumnsProps} from "@src/hooks";
import {TableProps} from "antd";

export interface TableDanhSachDonViBHXHDataType {
  key: string;
  stt?: number;
  ma?: string;
  ten?: string;
  ma_ct?: string;
  ten_tat?: string;
  ten_e?: string;
  mst?: string;
  dchi?: string;
  dthoai?: string;
  logo?: string;
  ma_nh?: string;
  ten_nh?: string;
  so_tk?: string;
  ten_tk?: string;
  ngay_tao?: string;
  nguoi_tao?: string;
  ngay_cap_nhat?: string;
  nguoi_cap_nhat?: string;
  trang_thai_ten?: string;
}

// ĐỊNH NGHĨA Column RENDER RA TỪNG Ô TRONG CỘT
//đọc https://ant.design/components/table#column để biết thêm về column
export const danhSachDonViBHXHColumns: TableProps<TableDanhSachDonViBHXHDataType>["columns"] = [
  {title: "STT", dataIndex: "stt", key: "stt", width: colWidthByKey.sott, align: "center", ...defaultTableColumnsProps},
  {title: "Mã", dataIndex: "ma", key: "ma", width: 120, align: "center", ...defaultTableColumnsProps},
  {...defaultTableColumnsProps, title: "Tên", dataIndex: "ten", key: "ten", width: 200, align: "left"},
  {title: "Mã cấp trên", dataIndex: "ma_ct", key: "ma_ct", width: 120, align: "center", ...defaultTableColumnsProps},
  {title: "Tên tắt", dataIndex: "ten_tat", key: "ten_tat", width: 120, align: "center", ...defaultTableColumnsProps},
  {title: "Tên tiếng anh", dataIndex: "ten_e", key: "ten_e", width: 120, align: "center", ...defaultTableColumnsProps},
  {title: "MST", dataIndex: "mst", key: "mst", width: 120, align: "center", ...defaultTableColumnsProps},
  {...defaultTableColumnsProps, title: "Địa chỉ", dataIndex: "dchi", key: "dchi", width: 200, align: "left"},
  {title: "Mã ngân hàng", dataIndex: "ma_nh", key: "ma_nh", width: 120, align: "center", ...defaultTableColumnsProps},
  {title: "Tên ngân hàng", dataIndex: "ten_nh", key: "ten_nh", width: 120, align: "center", ...defaultTableColumnsProps},
  {title: "Số tài khoản", dataIndex: "so_tk", key: "so_tk", width: 120, align: "center", ...defaultTableColumnsProps},
  {title: "Tên tài khoản", dataIndex: "ten_tk", key: "ten_tk", width: 120, align: "center", ...defaultTableColumnsProps},
  {title: "Điện thoại", dataIndex: "dthoai", key: "dthoai", width: 120, align: "center", ...defaultTableColumnsProps},
  {title: "Logo", dataIndex: "logo", key: "logo", width: 120, align: "center", ...defaultTableColumnsProps},
  {title: "Ngày tạo", dataIndex: "ngay_tao", key: "ngay_tao", width: colWidthByKey.ngay_tao, align: "center", ...defaultTableColumnsProps},
  {title: "Người tạo", dataIndex: "nguoi_tao", key: "nguoi_tao", width: colWidthByKey.nguoi_tao, align: "center", ...defaultTableColumnsProps},
  {title: "Ngày cập nhật", dataIndex: "ngay_cap_nhat", key: "ngay_cap_nhat", width: colWidthByKey.ngay_cap_nhat, align: "center", ...defaultTableColumnsProps},
  {title: "Người cập nhật", dataIndex: "nguoi_cap_nhat", key: "nguoi_cap_nhat", width: colWidthByKey.nguoi_cap_nhat, align: "center", ...defaultTableColumnsProps},
  {title: "Trạng Thái", dataIndex: "trang_thai_ten", key: "trang_thai_ten", width: 130, align: "center", ...defaultTableColumnsProps},
];
//Form
export interface IFormTimKiemDanhSachDonViBHXHFieldsConfig {
  ma: IFormInput;
  ten: IFormInput;

  trang_thai: IFormInput;
}
export const FormTimKiemDanhSachDonViBHXH: IFormTimKiemDanhSachDonViBHXHFieldsConfig = {
  ma: {
    component: "input",
    name: "ma",
    label: "Mã đơn vị ",
    placeholder: "Mã đơn vị",
    width: "10%",
  },
  ten: {
    component: "input",
    name: "ten",
    label: "Tên đơn vị",
    placeholder: "Tên đơn vị",
    width: "20%",
  },

  trang_thai: {
    component: "select",
    name: "trang_thai",
    label: "Trạng thái",
    placeholder: "Chọn trạng thái",
    width: "20%",
  },
};
// defaultFormValue tìm kiếm phân trang đơn vị
export const defaultFormValue: ReactQuery.ITimKiemPhanTrangDanhSachDaiLyParams = {
  ma: "",
  ten: "",

  trang_thai: "",
  // trang: 1,
  // so_dong: 20,
};
export const TRANG_THAI_CHI_TIET_DAI_LY = [
  {ten: "Đang sử dụng", ma: "D"},
  {ten: "Ngừng sử dụng", ma: "K"},
];

export const radioItemTrangThaiDaiLyTable = [
  {value: "Đang sử dụng", text: "Đang sử dụng"},
  {value: "Ngừng sử dụng", text: "Ngừng sử dụng"},
];
export const LOAI_DAI_LY_TK = [
  {ten: "đơn vị tổ chức", ma: "T"},
  {ten: "đơn vị cá nhân", ma: "C"},
];

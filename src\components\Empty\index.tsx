import { memo } from "react";
import strings from "@src/assets/strings";
import { Empty as AntEmpty, EmptyProps } from "antd";

import { isEqual } from "lodash";

const EmptyComponent: React.FC<EmptyProps> = (props: EmptyProps) => {
  const { description = strings().label_no_data, ...etc } = props;
  return <AntEmpty description={description} {...etc} />;
};

const Empty = memo(EmptyComponent, isEqual);

export default Empty;

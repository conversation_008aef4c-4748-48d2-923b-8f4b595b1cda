/**
 * T<PERSON><PERSON> dụng: <PERSON><PERSON><PERSON> thị giao diện bảng danh sách quốc gia với tìm kiếm, phân trang, CRUD
 */
import React, {memo, useCallback, useMemo, useRef, useState, useContext} from "react";
import {Col, Form, Input, InputRef, Row, Table, TableColumnType, Tag, Tooltip} from "antd";
import {FilterDropdownProps} from "antd/es/table/interface";
import {PlusCircleOutlined, SearchOutlined, ClearOutlined, DeleteOutlined, EditOutlined, PlusOutlined} from "@ant-design/icons";
import {isEqual} from "lodash";

import {Button, FormInput, Highlighter, TableFilterDropdown} from "@src/components";
import {COLOR_PALETTE, ID_PAGE} from "@src/constants";
import {IFormInput, ReactQuery} from "@src/@types";
import {defaultPaginationTableProps, defaultTableProps, fillRowTableEmpty} from "@src/hooks";

import {useDanhMucQuocGiaContext} from "./index.context";
import {
  FormTimKiemDanhMucQuocGia, 
  tableQuocGiaColumn, 
  TableQuocGiaColumnDataType, 
  TableQuocGiaColumnDataIndex, 
  radioItemTrangThaiQuocGiaSelect,
  radioItemTrangThaiQuocGiaTable
} from "./index.configs";
import {ModalChiTietQuocGia, IModalChiTietQuocGiaRef} from "./Component";
import "./index.default.scss";

type DataIndex = keyof TableQuocGiaColumnDataType;

//Destructuring form search values
const {
  ma_chau_luc,
  ma_khu_vuc,             
  ma,
  ten,
  trang_thai,
} = FormTimKiemDanhMucQuocGia;

const DanhMucQuocGiaContent: React.FC = memo(() => {
  const {listQuocGia, listChauLuc, listKhuVuc, loading, tongSoDong, filterParams, getListKhuVuc, getChiTietQuocGia, setFilterParams} = useDanhMucQuocGiaContext();

  //Quản lý từ khóa tìm kiếm trong cột và cột đang được tìm kiếm
  const [searchText, setSearchText] = useState("");
  const [searchedColumn, setSearchedColumn] = useState<DataIndex | "">("");

  //State để lưu current search parameters
  const [currentSearchParams, setCurrentSearchParams] = useState<ReactQuery.ITimKiemPhanTrangDanhMucQuocGiaParams>({});

  //===== REFS =====
  const searchInput = useRef<InputRef>(null);
  const modalChiTietRef = useRef<IModalChiTietQuocGiaRef>(null);

  //Tác dụng: Tạo danh sách châu lục cho dropdown filter, chỉ hiển thị các tỉnh đang hoạt động
  const dropdownOptionsChauLuc = useMemo(() => {
    if (!listChauLuc || !Array.isArray(listChauLuc)) {
      return [{ma: "", ten: "Chọn tỉnh/thành phố"}];
    }
    
    const allOption = [{ma: "", ten: "Tất cả"}];
    const validProvinces = listChauLuc
      .filter(item => {
        const hasValidData = item && item.ma && item.ten;
        if (!hasValidData) return false;
        
        const trangThai = item.trang_thai?.toString();
        const isActive = trangThai === "1" || trangThai === "D" || trangThai === "Đang sử dụng" || !trangThai;
        
        return hasValidData && isActive;
      })
      .map(item => ({
        ma: item.ma?.toString() || "",
        ten: item.ten?.toString() || "",
      }));
    
    return [...allOption, ...validProvinces];
  }, [listChauLuc]);

  //Tác dụng: Tạo danh sách khu vực cho dropdown filter, chỉ hiển thị các khu vực đang hoạt động
  const dropdownOptionsKhuVuc = useMemo(() => {
    if (!listKhuVuc || !Array.isArray(listKhuVuc)) {
      return [{ma: "", ten: "Chọn quận/huyện"}];
    }
    
    const allOption = [{ma: "", ten: "Tất cả"}];
    const validDistricts = listKhuVuc
      .filter(item => {
        const hasValidData = item && item.ma && item.ten;
        if (!hasValidData) return false;
        
        const trangThai = item.trang_thai?.toString();
        const isActive = trangThai === "1" || trangThai === "D" || trangThai === "Đang sử dụng" || !trangThai;
        
        return hasValidData && isActive;
      })
      .map(item => ({
        ma: item.ma?.toString() || "",
        ten: item.ten?.toString() || "",
      }));
    
    return [...allOption, ...validDistricts];
  }, [listKhuVuc]);

  //Tác dụng: Tạo data table với STT và xử lý ngày áp dụng
  const dataTableWithSTT = useMemo(() => {
    try {
      const currentPage = filterParams?.trang || 1;
      const currentPageSize = (filterParams?.so_dong || defaultPaginationTableProps.defaultPageSize) as number;
      
      const tableData =
        listQuocGia?.map((item, index) => {
          return {
            ...item,
            key: item.ma_chau_luc && item.ma_khu_vuc && item.ma ? `${item.ma_chau_luc}-${item.ma_khu_vuc}-${item.ma}` : `row-${index}`,
            sott: (currentPage - 1) * currentPageSize + index + 1,
          };
        }) || [];
      
      const arrEmptyRow = fillRowTableEmpty(tableData.length, currentPageSize);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      return [];
    }
  }, [listQuocGia, filterParams?.trang, filterParams?.so_dong]);

  //Tác dụng: Xử lý submit form tìm kiếm và cập nhật filter parameters
  const onSearchApi = useCallback(
    (values: ReactQuery.ITimKiemPhanTrangDanhMucQuocGiaParams) => {
      setCurrentSearchParams(values);
      
      setFilterParams({
        ma_chau_luc: values.ma_chau_luc || "",
        ma_khu_vuc: values.ma_khu_vuc || "",
        ma: values.ma || "",
        ten: values.ten || "",
        trang_thai: values.trang_thai || "",
        trang: 1,
        so_dong: filterParams?.so_dong || defaultPaginationTableProps.defaultPageSize,
      });
    },
    [setFilterParams, filterParams?.so_dong],
  );

  //Tác dụng: Xử lý thay đổi trang và số dòng hiển thị trong pagination
  const onChangePage = useCallback(
    (newPage: number, newPageSize: number) => {
      setFilterParams(prev => ({
        ...prev,
        trang: newPage,
        so_dong: newPageSize,
      }));
    },
    [setFilterParams],
  );

  //Tác dụng: Xử lý thay đổi châu lục trong form search để load khu vực tương ứng
  const handleSearchChauLucChange = useCallback((value: any) => {
    //Khi chọn châu lục khác, load danh sách khu vực tương ứng
    if (value && value !== "") {
      getListKhuVuc({ma_chau_luc: value});
    } else {
      //Nếu chọn "Tất cả", clear danh sách khu vực
      getListKhuVuc({ma_chau_luc: ""});
    }
  }, [getListKhuVuc]);

  const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: DataIndex) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);

  const handleReset = useCallback(
    (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: DataIndex) => {
      clearFilters();
      setSearchText("");
      handleSearch([""], confirm, dataIndex);
    },
    [handleSearch],
  );

  //Tác dụng: Xử lý click vào row để hiển thị modal chi tiết quốc gia
  const handleRowClick = useCallback(async (record: TableQuocGiaColumnDataType) => {
    //Bỏ qua nếu là empty row hoặc đang loading
    if (record.key.toString().includes("empty") || loading) {
      return;
    }
    
    try {
      if (!record.ma) {
        return;
      }
      
      if (!modalChiTietRef.current) {
        return;
      }
      
      const response = await getChiTietQuocGia({
        ma: record.ma,
      });
      
      if (response && Object.keys(response).length > 0) {
        try {
          modalChiTietRef.current.open(response);
        } catch (modalError) {
          //Xử lý lỗi thầm lặng
        }
      }
      
    } catch (error) {
      //Xử lý lỗi thầm lặng
    }
  }, [getChiTietQuocGia, loading]);

  //Tác dụng: Mở modal để tạo mới quốc gia
  const handleThemMoi = useCallback(() => {
    modalChiTietRef.current?.open();
  }, []);

  //Tác dụng: Reload danh sách sau khi lưu thành công
  const handleAfterSave = useCallback(() => {
    setFilterParams(prev => ({...prev}));
  }, [setFilterParams]);

  const renderFormInputColumn = useCallback(
    (props: IFormInput, span = 4) => (
      <Col span={span}>
        <FormInput {...props} />
      </Col>
    ),
    [],
  );

  //Tác dụng: Render form tìm kiếm ở header table
  const renderHeaderTableQuocGia = useCallback(
    () => (
      <Form
        layout="vertical"
        className="[&_.ant-form-item]:mb-0"
        onFinish={onSearchApi}>
        <Row gutter={16} align="bottom">
          {renderFormInputColumn({...ma_chau_luc, options: dropdownOptionsChauLuc, onChange: handleSearchChauLucChange},4)}
          {renderFormInputColumn({...ma_khu_vuc, options: dropdownOptionsKhuVuc},4)}
          {renderFormInputColumn(ma,3)}
          {renderFormInputColumn(ten,4)}
          {renderFormInputColumn({...trang_thai, options: radioItemTrangThaiQuocGiaSelect},3)}
          <Col span={2}>
            <Button type="primary" htmlType="submit" loading={loading} icon={<SearchOutlined />} block>
              Tìm kiếm
            </Button>
          </Col>
          <Col span={2}>
            <Button type="primary" block icon={<PlusCircleOutlined />} onClick={handleThemMoi}>
              Tạo mới
            </Button>
          </Col>
        </Row>
      </Form>
    ),
    [ma_chau_luc, ma_khu_vuc, ma, ten, trang_thai, dropdownOptionsChauLuc, dropdownOptionsKhuVuc, loading, onSearchApi, handleThemMoi, renderFormInputColumn, handleSearchChauLucChange],
  );

  //Tác dụng: Tạo cấu hình search cho các cột table
  const getColumnSearchProps = useCallback(
    (dataIndex: DataIndex, title: string): TableColumnType<TableQuocGiaColumnDataType> => ({
      filterDropdown: ({setSelectedKeys, selectedKeys, confirm, clearFilters}) => (
        <TableFilterDropdown
          ref={searchInput}
          title={title}
          selectedKeys={selectedKeys}
          dataIndex={dataIndex}
          setSelectedKeys={setSelectedKeys}
          handleSearch={handleSearch}
          confirm={confirm}
          clearFilters={clearFilters}
          handleReset={handleReset}
        />
      ),
      filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? COLOR_PALETTE.blue[60] : undefined}} />,
      onFilter: (value, record) => record[dataIndex]?.toString().toLowerCase().includes((value as string).toLowerCase()) || false,
      onFilterDropdownOpenChange: (visible) => {
        if (visible) {
          setTimeout(() => searchInput.current?.select(), 100);
        }
      },
      render: (text, record) => {
        if (record.key?.toString().includes("empty")) return <span style={{height: '22px', display: 'inline-block'}}>&nbsp;</span>;
        
        if (dataIndex === "trang_thai_ten") {
          if (!text) return "";
          const color = text === "Đang sử dụng" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];
          return (
            <Tag color={color} className="text-[11px]">
              {text}
            </Tag>
          );
        }
        
       
        
        return searchedColumn === dataIndex ? (
          <Highlighter
            searchWords={[searchText]}
            textToHighlight={text ? text.toString() : ""}
          />
        ) : text !== undefined ? (
          text
        ) : (
          <Tag color={"transparent"} className="!text-white text-[11px]">
            {"\u00A0"}
          </Tag>
        );
      },
    }),
    [handleSearch, handleReset, searchText, searchedColumn],
  );

  //Tác dụng: Tạo columns table với tính năng search
  const columnsWithSearch = useMemo(() => {
    return tableQuocGiaColumn?.map((col: any) => {
      if (col.dataIndex && ["ma", "ten", "ma_chau_luc", "ten_chau_luc", "ma_khu_vuc", "ten_khu_vuc", "postcode", "trang_thai_ten", "ngay_tao", "nguoi_tao", "ngay_cap_nhat", "nguoi_cap_nhat"].includes(col.dataIndex)) {
        return {
          ...col,
          ...getColumnSearchProps(col.dataIndex as DataIndex, col.title as string),
        };
      }
      return col;
    });
  }, [getColumnSearchProps]);

  return (
    <div id={ID_PAGE.DANH_MUC_QUOC_GIA} className="[&_.ant-space]:w-full">
      <Table<TableQuocGiaColumnDataType>
        {...defaultTableProps}
        dataSource={dataTableWithSTT}
        columns={columnsWithSearch}
        loading={loading}
        pagination={{
          ...defaultPaginationTableProps,
          current: filterParams?.trang || 1,
          pageSize: (filterParams?.so_dong || defaultPaginationTableProps.defaultPageSize) as number,
          total: tongSoDong,
          onChange: onChangePage,
          showQuickJumper: true,
          showTotal: (total, range) => {
            return `${range[0]}-${range[1]} của ${total} bản ghi`;
          },
        }}
        title={renderHeaderTableQuocGia}
        onRow={record => ({
          style: {cursor: loading ? "progress" : "pointer"},
          onClick: () => handleRowClick(record),
        })}
      />
      <ModalChiTietQuocGia
        ref={modalChiTietRef}
        onAfterSave={handleAfterSave}
        danhSachChauLuc={dropdownOptionsChauLuc}
        danhSachKhuVuc={dropdownOptionsKhuVuc}
      />
    </div>
  );
});

DanhMucQuocGiaContent.displayName = "DanhMucQuocGiaContent";
export default DanhMucQuocGiaContent;

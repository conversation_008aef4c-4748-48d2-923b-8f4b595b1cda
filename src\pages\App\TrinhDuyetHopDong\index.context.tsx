import {createContext, useContext} from "react";
import {ITrinhDuyetHopDongContextProps} from "./index.model.tsx";

// Tạo React Context với giá trị mặc định
export const TrinhDuyetHopDongContext = createContext<ITrinhDuyetHopDongContextProps>({
  // Loading state
  loading: false,

  // Data states
  danhSachNguoiDuyetHopDong: [],

  // Functions với implementation mặc định
  getDanhSachNguoiDuyetHopDong: () => Promise.resolve([]),
  trinhPheDuyetHopDong: () => Promise.resolve(false),
  huyTrinhPheDuyetHopDong: () => Promise.resolve(false),

  // Callback functions
  onTrinhDuyetSuccess: undefined,
  onHuyTrinhDuyetSuccess: undefined,
});

// Custom hook để sử dụng TrinhDuyetHopDong context
export const useTrinhDuyetHopDongContext = () => {
  const context = useContext(TrinhDuyetHopDongContext);

  if (!context) {
    throw new Error("useTrinhDuyetHopDongContext must be used within a TrinhDuyetHopDongProvider");
  }

  return context;
};

// Provider sẽ được export từ index.ts để tránh circular import

import {createContext, useContext} from "react";

import {IQuanLyLoaiHoGiaDinhContextProps} from "./index.model";

//khởi tạo giá trị mặc định
export const QuanLyLoaiHoGiaDinhContext = createContext<IQuanLyLoaiHoGiaDinhContextProps>({
  listLoaiHoGiaDinh: [],
  tongSoDong: 0,
  loading: false,
  filterParams: {},
  getListLoaiHoGiaDinh: async () => Promise.resolve(),
  getChiTietLoaiHoGiaDinh: async () => Promise.resolve({} as CommonExecute.Execute.ILoaiHoGiaDinh),
  capNhatChiTietLoaiHoGiaDinh: async () => Promise.resolve(),
  setFilterParams: () => {},
});

export const useQuanLyLoaiHoGiaDinhContext = () => useContext(QuanLyLoaiHoGiaDinhContext);
import {DeleteOutlined, ExportOutlined, PrinterOutlined, UploadOutlined} from "@ant-design/icons";
import "@react-pdf-viewer/core/lib/styles/index.css";
import {Checkbox} from "@src/components";
import {Button, Divider, Image, Tooltip} from "antd";
import {isEqual} from "lodash";
import {memo} from "react";
import {FileCategory, ListFileViewComponentProps} from "./Constant";

const ListFileViewComponent = ({
  categories,
  selectedFile,
  selectedFiles,
  selectedCategory,
  fileInputRef,
  setSelectedFiles,
  setSelectedCategory,
  setSelectedFile,
  getCategoryByFile,
  handleUploadClick,
  handleDeleteFiles,
  handleFileUpload,
}: ListFileViewComponentProps) => {
  const renderThumbnail = (file: any) => {
    const checked = selectedFiles.includes(file.url);
    const handleCheckboxChange = (e: any) => {
      if (e.target.checked) setSelectedFiles((prev: any) => [...prev, file.url]);
      else setSelectedFiles((prev: any) => prev.filter((url: any) => url !== file.url));
    };
    const checkbox = (
      <Checkbox
        checked={checked}
        onChange={handleCheckboxChange}
        style={{position: "absolute", top: 2, left: 2, zIndex: 2, background: "rgba(255,255,255,0.7)", borderRadius: 2, padding: 0, margin: 0, width: 16, height: 16}}
      />
    );
    if (file.type === "image") {
      return (
        <div style={{position: "relative", width: 80, height: 80}}>
          {checkbox}
          <Image
            src={file.url + "&thumbnail=1"}
            width={80}
            height={80}
            style={{
              border: file.url === selectedFile?.url ? "2px solid #1890ff" : "1px solid #eee",
              borderRadius: 4,
              cursor: "pointer",
            }}
            onClick={() => {
              setSelectedFile(file);
              // Cập nhật selectedCategory khi chọn file từ hạng mục khác
              const fileCategory = getCategoryByFile(file);
              if (fileCategory && fileCategory.id !== selectedCategory) {
                setSelectedCategory(fileCategory.id);
              }
            }}
            preview={false}
          />
        </div>
      );
    } else if (file.type === "pdf") {
      return (
        <div style={{position: "relative", width: 80, height: 80}}>
          {checkbox}
          <div
            style={{
              border: file.url === selectedFile?.url ? "2px solid #1890ff" : "1px solid #eee",
            }}
            className={`flex h-[80px] w-[80px] cursor-pointer items-center justify-center rounded border bg-[#f5f5f5] text-[24px] text-[#d32f2f] ${file.url === selectedFile?.url ? "border-2 border-[#1890ff]" : "border border-[#eee]"}`}
            onClick={() => {
              setSelectedFile(file);
              // Cập nhật selectedCategory khi chọn file từ hạng mục khác
              const fileCategory = getCategoryByFile(file);
              if (fileCategory && fileCategory.id !== selectedCategory) {
                setSelectedCategory(fileCategory.id);
              }
            }}>
            📄
          </div>
        </div>
      );
    }
    return null;
  };
  return (
    <>
      <div className="mb-1 px-2 font-semibold">Hạng mục & Files</div>
      <div className="flex-1 overflow-y-auto px-2">
        {categories.map((cat: FileCategory) => {
          const allFileUrls = cat.files.map((f: {url: string}) => f.url);
          const checkedCount = allFileUrls.filter((url: string) => selectedFiles.includes(url)).length;
          const allChecked = checkedCount === allFileUrls.length && allFileUrls.length > 0;
          const indeterminate = checkedCount > 0 && checkedCount < allFileUrls.length;
          const handleCategoryCheckbox = (e: any) => {
            if (e.target.checked) {
              setSelectedFiles((prev: string[]) => Array.from(new Set([...prev, ...allFileUrls]))); // Chọn tất cả file trong hạng mục (không thêm trùng)
            } else {
              setSelectedFiles((prev: string[]) => prev.filter((url: string) => !allFileUrls.includes(url))); // Bỏ chọn tất cả file trong hạng mục
            }
          };
          return (
            <div key={cat.id} style={{marginBottom: 16}}>
              <div style={{display: "flex", alignItems: "center", marginBottom: 4, gap: 4}}>
                <Checkbox checked={allChecked} indeterminate={indeterminate} onChange={handleCategoryCheckbox} style={{marginRight: 4}} />
                <div
                  style={{
                    fontWeight: 500,
                    color: cat.id === selectedCategory ? "#1890ff" : undefined,
                    cursor: "pointer",
                  }}
                  onClick={() => {
                    // Khi bấm vào tên hạng mục, thực hiện check all hoặc uncheck all
                    if (allChecked) {
                      setSelectedFiles((prev: any) => prev.filter((url: string) => !allFileUrls.includes(url))); // Uncheck all
                    } else {
                      setSelectedFiles((prev: any) => Array.from(new Set([...prev, ...allFileUrls]))); // Check all
                    }
                    setSelectedCategory(cat.id);
                    setSelectedFile(cat.files[0]);
                  }}>
                  {cat.name}
                </div>
              </div>
              <div style={{display: "flex", gap: 8, flexWrap: "wrap"}}>
                {cat.files.map((file: {url: string; name: string}) => (
                  <div key={file.url} style={{position: "relative"}}>
                    {renderThumbnail(file)}
                    <div className="text-gray-500 mt-0.5 max-w-[80px] overflow-hidden text-ellipsis whitespace-nowrap text-center text-[10px]">{file.name}</div>
                  </div>
                ))}
              </div>
            </div>
          );
        })}
      </div>
    </>
  );
};

export const ListFileView = memo(ListFileViewComponent, isEqual);

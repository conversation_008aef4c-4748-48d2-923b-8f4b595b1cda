// Dashboard Models and Interfaces

export interface IDashboardStats {
  totalRevenue: number;
  retainedRevenue: number;
  totalCompanies: number;
  targetAmount: number;
  completionRate: number;
}

export interface IMonthlyStats {
  month: string;
  currentYear: number;
  previousYear: number;
  target?: number;
}

export interface IInsuranceCategory {
  id: string;
  name: string;
  revenue: number;
  claims: number;
  claimsRate: number;
}

export interface ITopClient {
  id: number;
  name: string;
  revenue: number;
  avatar?: string;
  trend?: "up" | "down" | "neutral";
  trendValue?: number;
}

export interface ITopSeller {
  id: number;
  name: string;
  revenue: number;
  avatar?: string;
  trend?: "up" | "down" | "neutral";
  trendValue?: number;
}

export interface IRevenueByChannel {
  channel: string;
  amount: number;
  percentage: number;
}

export interface IBarLineChartData {
  labels: string[];
  barValues: number[];
  lineValues: number[];
}

export interface IDashboardData {
  stats: IDashboardStats;
  monthlyStats: IMonthlyStats[];
  insuranceCategories: IInsuranceCategory[];
  topClients: ITopClient[];
  topSellers: ITopSeller[];
  revenueByChannel: IRevenueByChannel[];
  barLineChartData: IBarLineChartData;
}

export interface IDashboardContextProps {
  data: IDashboardData;
  loading: boolean;
  selectedPeriod: "week" | "month" | "year";
  setSelectedPeriod: (period: "week" | "month" | "year") => void;
  refreshData: () => void;
}

// Chart Configuration Types
export interface IChartConfig {
  responsive: boolean;
  maintainAspectRatio: boolean;
  plugins: {
    legend: {
      display: boolean;
      position?: "top" | "bottom" | "left" | "right";
    };
    tooltip: {
      enabled: boolean;
    };
  };
}

export interface ILineChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    borderColor: string;
    backgroundColor: string;
    tension: number;
  }[];
}

export interface IPieChartData {
  labels: string[];
  datasets: {
    data: number[];
    backgroundColor: string[];
    borderColor: string[];
    borderWidth: number;
  }[];
}

import React from "react";
import strings from "@src/assets/strings";
import { Select, Switch } from "@src/components";
import { LANGUAGE_VALUES } from "@src/constants";
import { ThemeMode, useSetting } from "@src/hooks";

const Setting: React.FC = () => {
  const { themeMode, lang, setThemeMode, setLanguage } = useSetting();

  const onLangChange = (value: string) => {
    setLanguage(value);
    window.location.reload();
  };

  return (
    <div className="flex flex-col flex-1">
      <Switch
        defaultChecked={themeMode === ThemeMode.dark}
        onChange={(e) => {
          setThemeMode(e ? ThemeMode.dark : ThemeMode.light);
        }}
        title={strings().label_dark_mode}
        containerClassName="mb-6"
      />
      <Select
        title={strings().label_language}
        options={[
          { label: strings().label_vietnamese, value: LANGUAGE_VALUES.VI },
          { label: strings().label_english, value: LANGUAGE_VALUES.EN },
        ]}
        onChange={onLangChange}
        defaultValue={lang ?? LANGUAGE_VALUES.EN}
        allowClear={false}
      />
    </div>
  );
};

export default Setting;

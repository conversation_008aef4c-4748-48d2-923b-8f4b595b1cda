import {IFormInput} from "@src/@types";
import {colWidthByKey, defaultTableColumnsProps, ruleInputMessage} from "@src/hooks";
import {TableProps} from "antd";

//ĐỊNH NGHĨA CẤU HÌNH TABLE ĐỐI TÁC GỒM NHỮNG CỘT NÀO, THỨ TỰ CỘT SẼ HIỂN THỊ THEO THỨ TỰ KHAI BÁO Ở DƯỚI
export interface TableDoiTacColumnDataType {
  key: string;
  sott?: number;
  ma?: string;
  ten?: string;
  ten_tat?: string;
  mst?: string;
  dchi?: string;
  dthoai?: string;
  ten_e?: string;
  trang_thai?: string;
  ngay_tao?: string;
  nguoi_tao?: string;
  ngay_cap_nhat?: string;
  nguoi_cap_nhat?: string;
  trang_thai_ten?: string;
}

// ĐỊNH NGHĨA Column RENDER RA TỪNG Ô TRONG CỘT
//đọc https://ant.design/components/table#column để biết thêm về column
export const tableDoiTacColumn: TableProps<TableDoiTacColumnDataType>["columns"] = [
  {
    title: "STT",
    dataIndex: "sott",
    key: "sott",
    align: "center",
    width: colWidthByKey.sott,
    ...defaultTableColumnsProps,
  },
  {
    title: "Mã",
    dataIndex: "ma",
    key: "ma",
    align: "center",
    width: 100,
    ...defaultTableColumnsProps,
  },
  {
    title: "Tên",
    dataIndex: "ten",
    key: "ten",
    width: 250,
    ...defaultTableColumnsProps,
  },
  {
    title: "MST",
    dataIndex: "mst",
    key: "mst",
    align: "center",
    width: 100,
    ...defaultTableColumnsProps,
  },
  {
    title: "Địa chỉ",
    dataIndex: "dchi",
    key: "dchi",
    width: 250,
    ...defaultTableColumnsProps,
  },
  {
    title: "Điện thoại",
    dataIndex: "dthoai",
    key: "dthoai",
    align: "center",
    width: 110,
    ...defaultTableColumnsProps,
  },
  {
    title: "Ngày tạo",
    dataIndex: "ngay_tao",
    key: "ngay_tao",
    width: colWidthByKey.ngay_tao,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Người tạo",
    dataIndex: "nguoi_tao",
    key: "nguoi_tao",
    width: colWidthByKey.nguoi_tao,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Ngày cập nhật",
    dataIndex: "ngay_cap_nhat",
    key: "ngay_cap_nhat",
    width: colWidthByKey.ngay_cap_nhat,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Người cập nhật",
    dataIndex: "nguoi_cap_nhat",
    key: "nguoi_cap_nhat",
    width: colWidthByKey.nguoi_cap_nhat,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Trạng thái",
    dataIndex: "trang_thai_ten",
    key: "trang_thai_ten",
    width: 130,
    align: "center",
    ...defaultTableColumnsProps,
  },
];

//keyof: return ra key của inteface TableDoiTacColumnDataType;
export type TableDoiTacColumnDataIndex = keyof TableDoiTacColumnDataType;

//radio trong table
export const radioItemTrangThaiDoiTacTable = [
  {value: "Đang sử dụng", text: "Đang sử dụng"},
  {value: "Ngưng sử dụng", text: "Ngưng sử dụng"},
];

//form tìm kiếm
//radio trong search
export const radioItemTrangThaiDoiTacSelect: Array<{ma: string; ten: string}> = [
  {ma: "D", ten: "Đang sử dụng"},
  {ma: "K", ten: "Ngưng sử dụng"},
];
export interface IFormTimKiemDoiTacFieldsConfig {
  ten: IFormInput;
  mst: IFormInput;
  trang_thai: IFormInput;
}
export const FormTimKiemQuanLyDoiTac: IFormTimKiemDoiTacFieldsConfig = {
  ten: {
    component: "input",
    name: "ten",
    label: "Tên đối tác",
    placeholder: "Nhập tên đối tác",
    className: "!mb-0",
  },
  mst: {
    component: "input",
    name: "mst",
    label: "Mã số thuế",
    placeholder: "Nhập mã số thuế",
    className: "!mb-0",
  },
  trang_thai: {
    component: "select",
    name: "trang_thai",
    label: "Trạng thái",
    placeholder: "Chọn trạng thái",
    className: "!mb-0",
  },
};

//form update / create
export interface IFormTaoMoiDoiTacFieldsConfig {
  ma: IFormInput;
  ten: IFormInput;
  ten_tat: IFormInput;
  ten_e: IFormInput;
  mst: IFormInput;
  dchi: IFormInput;
  dthoai: IFormInput;
  stt: IFormInput;
  trang_thai: IFormInput;
}

export const FormTaoMoiDoiTac: IFormTaoMoiDoiTacFieldsConfig = {
  ma: {
    component: "input",
    label: "Mã",
    name: "ma",
    placeholder: "Nhập mã",
    rules: [ruleInputMessage.required],
  },
  ten: {
    component: "input",
    label: "Tên",
    name: "ten",
    placeholder: "Nhập tên đối tác",
    rules: [ruleInputMessage.required],
  },
  ten_tat: {
    component: "input",
    label: "Tên tắt",
    name: "ten_tat",
    placeholder: "Nhập tên tắt",
    rules: [ruleInputMessage.required],
  },
  ten_e: {
    component: "input",
    label: "Tên tiếng anh",
    placeholder: "Nhập tên tiếng anh",
    name: "ten_e",
    rules: [ruleInputMessage.required],
  },
  mst: {
    component: "input",
    label: "Mã số thuế",
    placeholder: "Nhập mã số thuế",
    name: "mst",
    rules: [ruleInputMessage.required],
  },
  dchi: {
    component: "input",
    label: "Địa chỉ",
    placeholder: "Nhập địa chỉ",
    name: "dchi",
    rules: [ruleInputMessage.required],
  },
  dthoai: {
    component: "input",
    label: "Điện thoại",
    placeholder: "Nhập điện thoại",
    name: "dthoai",
    rules: [ruleInputMessage.required, ruleInputMessage.phone],
  },
  stt: {
    component: "input",
    label: "Số thứ tự",
    placeholder: "Nhập số thứ tự",
    name: "stt",
    rules: [ruleInputMessage.required],
  },
  trang_thai: {
    component: "select",
    label: "Trạng thái",
    name: "trang_thai",
    placeholder: "Chọn trạng thái",
    rules: [ruleInputMessage.required],
  },
};

export const TRANG_THAI_TAO_MOI_DOI_TAC = [
  {ten: "Đang sử dụng", ma: "D"},
  {ten: "Ngừng sử dụng", ma: "K"},
];
export const TAO_MOI_DOI_TAC = [
  {ten: "Công ty Cổ Phần Bảo Hiểm Petrolimex", ma: "D"},
  {ten: "Công Ty Cổ Phần Bảo Hiểm escs", ma: "K"},
];

.custom-select {
  &.ant-select {
    .ant-select-selector {
      @apply dark:bg-black-60 dark:border-pink-30;
      .ant-select-selection-placeholder {
        @apply dark:text-pink-20;
      }
      .ant-select-selection-item {
        @apply dark:text-pink-30;
      }
    }
    .ant-select-arrow,
    .ant-select-clear {
      @apply dark:text-pink-20 dark:bg-black-60;
    }
    &:hover {
      .ant-select-selector {
        @apply dark:border-pink-20 #{!important};
      }
    }
    &.ant-select-focused {
      .ant-select-selector {
        @apply dark:border-pink-20 #{!important};
      }
    }
  }
}

.custom-select-popup {
  &.ant-select-dropdown {
    @apply dark:bg-black-10;
    .ant-select-item {
      &.ant-select-item-option-selected {
        @apply dark:bg-black-30;
      }

      &.ant-select-item-option-active {
        @apply dark:bg-black-20;
      }
    }
  }
}

import {Input as AntInput, InputProps} from "antd";
import {isEqual} from "lodash";
import React, {memo} from "react";
import {twMerge} from "tailwind-merge";

const InputPasswordComponent: React.FC<InputProps> = props => {
  const {className = "", value, onChange, suffix, allowClear = true, ...etc} = props;
  return <AntInput.Password className={twMerge("custom-input-password", className)} value={value} onChange={onChange} suffix={suffix} allowClear={allowClear} {...etc} />;
};

const InputPassword = memo(InputPasswordComponent, isEqual);

export default InputPassword;

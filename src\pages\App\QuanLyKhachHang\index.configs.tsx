import {IFormInput} from "@src/@types";
import {colWidthByKey, defaultTableColumnsProps, ruleInputMessage} from "@src/hooks";
import {TableProps} from "antd";
import dayjs from "dayjs";

export const PAGE_SIZE = 13; // Số dòng mỗi trang
export const currentDate = dayjs(); // Ngày hiện tại

/**
 * ĐỊNH NGHĨA CẤU HÌNH TABLE KHÁCH HÀNG GỒM NHỮNG CỘT NÀO
 * THỨ TỰ CỘT SẼ HIỂN THỊ THEO THỨ TỰ KHAI BÁO Ở DƯỚI
 */
export interface TableKhachHangColumnDataType extends CommonExecute.Execute.IKhachHang {
  key?: string;
  sott?: number;
}

/**
 * ĐỊNH NGHĨA CÁC KEY CỘT TRONG TABLE ĐỂ SỬ DỤNG CHO SEARCH
 */
export type TableKhachHangColumnDataIndex = keyof TableKhachHangColumnDataType;

/**
 * CẤU HÌNH CÁC CỘT HIỂN THỊ TRONG TABLE KHÁCH HÀNG
 */
export const tableKhachHangColumn: TableProps<TableKhachHangColumnDataType>["columns"] = [
  {
    ...defaultTableColumnsProps,
    title: "STT",
    dataIndex: "sott",
    key: "sott",
    width: colWidthByKey.sott,
  },
  {
    ...defaultTableColumnsProps,
    title: "Mã khách hàng",
    dataIndex: "ma",
    key: "ma",
    width: 120,
  },
  {
    ...defaultTableColumnsProps,
    title: "Tên khách hàng",
    dataIndex: "ten",
    key: "ten",
    width: 250,
    align: "left",
  },
  {
    ...defaultTableColumnsProps,
    title: "Loại khách hàng",
    dataIndex: "ten_loai_kh",
    key: "ten_loai_kh",
    width: 120,
  },
  {
    ...defaultTableColumnsProps,
    title: "Điện thoại",
    dataIndex: "dthoai",
    key: "dthoai",
    width: 120,
  },
  {
    ...defaultTableColumnsProps,
    title: "Email",
    dataIndex: "email",
    key: "email",
    width: 200,
  },
  {
    ...defaultTableColumnsProps,
    title: "Địa chỉ",
    dataIndex: "dchi",
    key: "dchi",
    width: 300,
    align: "left",
  },
  {
    ...defaultTableColumnsProps,
    title: "CMND/CCCD",
    dataIndex: "cmt",
    key: "cmt",
    width: 120,
    align: "left",
  },
  {
    ...defaultTableColumnsProps,
    title: "Mã số thuế",
    dataIndex: "mst",
    key: "mst",
    width: 120,
  },
  {
    ...defaultTableColumnsProps,
    title: "Ngày tạo",
    dataIndex: "ngay_tao",
    key: "ngay_tao",
    width: colWidthByKey.ngay_tao,
  },
  {
    ...defaultTableColumnsProps,
    title: "Người tạo",
    dataIndex: "nguoi_tao",
    key: "nguoi_tao",
    width: colWidthByKey.nguoi_tao,
  },
  {
    ...defaultTableColumnsProps,
    title: "Người cập nhật",
    dataIndex: "nguoi_cap_nhat",
    key: "nguoi_cap_nhat",
    width: colWidthByKey.nguoi_cap_nhat,
  },
  {
    ...defaultTableColumnsProps,
    title: "Ngày cập nhật",
    dataIndex: "ngay_cap_nhat",
    key: "ngay_cap_nhat",
    width: colWidthByKey.ngay_cap_nhat,
  },
  {
    ...defaultTableColumnsProps,
    title: "Trạng thái",
    dataIndex: "trang_thai_ten",
    key: "trang_thai_ten",
    width: 120,
  },
];

/**
 * CẤU HÌNH FORM TÌM KIẾM KHÁCH HÀNG
 */
export const FormTimKiemKhachHang = {
  ma_doi_tac_ql: {
    name: "ma_doi_tac_ql",
    label: "Đối tác quản lý",
    component: "select",
    placeholder: "Chọn đối tác quản lý",
    allowClear: true,
    showSearch: true,
    optionFilterProp: "label",
    // rules: [ruleInputMessage.required],
  } as IFormInput,

  ma_chi_nhanh_ql: {
    name: "ma_chi_nhanh_ql",
    label: "Chi nhánh quản lý",
    component: "select",
    placeholder: "Chọn chi nhánh quản lý",
    allowClear: true,
    showSearch: true,
    optionFilterProp: "label",
    // rules: [ruleInputMessage.required],
  } as IFormInput,

  loai_kh: {
    name: "loai_kh",
    label: "Loại khách hàng",
    component: "select",
    placeholder: "Chọn loại khách hàng",
    allowClear: true,
    showSearch: true,
    optionFilterProp: "label",
  } as IFormInput,

  ma: {
    name: "ma",
    label: "Mã khách hàng",
    component: "input",
    placeholder: "Nhập mã khách hàng",
    allowClear: true,
  } as IFormInput,

  ten: {
    name: "ten",
    label: "Tên khách hàng",
    component: "input",
    placeholder: "Nhập tên khách hàng",
    allowClear: true,
  } as IFormInput,

  dthoai: {
    name: "dthoai",
    label: "Điện thoại",
    component: "input",
    placeholder: "Nhập số điện thoại",
    allowClear: true,
  } as IFormInput,

  cmt: {
    name: "cmt",
    // label: "CMND/CCCD",
    component: "input",
    placeholder: "CMND/CCCD",
    allowClear: true,
  } as IFormInput,

  mst: {
    name: "mst",
    // label: "Mã số thuế",
    component: "input",
    placeholder: "Mã số thuế",
    allowClear: true,
  } as IFormInput,

  nd_tim: {
    name: "nd_tim",
    // label: "Nội dung tìm kiếm",
    component: "input",
    placeholder: "Nhập nội dung tìm kiếm",
    allowClear: true,
  } as IFormInput,
};

/**
 * GIÁ TRỊ MẶC ĐỊNH CHO FORM TÌM KIẾM PHÂN TRANG
 */
export const defaultValueFormTimKiemPhanTrang = {
  ma_doi_tac_ql: "ESCS",
  ma_chi_nhanh_ql: "TCT",
  loai_kh: "",
  ma: "",
  ten: "",
  dthoai: "",
  cmt: "",
  mst: "",
  nd_tim: "",
  trang: 1,
  so_dong: PAGE_SIZE,
};

/**
 * OPTIONS CHO LOẠI KHÁCH HÀNG (sẽ được load từ API hoặc constants)
 */
export const LOAI_KHACH_HANG_OPTIONS = [
  {ma: "C", ten: "Cá nhân"},
  {ma: "T", ten: "Tổ chức"},
];

/**
 * OPTIONS CHO TRẠNG THÁI KHÁCH HÀNG (dùng cho client-side filtering)
 */
export const TRANG_THAI_KHACH_HANG_OPTIONS = [
  {value: "Đang sử dụng", text: "Đang sử dụng"},
  {value: "Ngưng sử dụng", text: "Ngưng sử dụng"},
];

/**
 * OPTIONS CHO TRẠNG THÁI KHÁCH HÀNG CHO MODAL (dùng cho API)
 */
export const TRANG_THAI_MODAL_KHACH_HANG_OPTIONS = [
  {ma: "D", ten: "Đang sử dụng"},
  {ma: "K", ten: "Ngừng sử dụng"},
];

/**
 * CẤU HÌNH FORM CHI TIẾT KHÁCH HÀNG CHO MODAL
 */
export const FormChiTietKhachHang = {
  ma_doi_tac_ql: {
    name: "ma_doi_tac_ql",
    label: "Đối tác quản lý",
    component: "select",
    placeholder: "Chọn đối tác quản lý",
    allowClear: true,
    showSearch: true,
    optionFilterProp: "label",
    rules: [ruleInputMessage.required],
  } as IFormInput,

  ma_chi_nhanh_ql: {
    name: "ma_chi_nhanh_ql",
    label: "Chi nhánh quản lý",
    component: "select",
    placeholder: "Chọn chi nhánh quản lý",
    allowClear: true,
    showSearch: true,
    optionFilterProp: "label",
  } as IFormInput,

  loai_kh: {
    name: "loai_kh",
    label: "Loại khách hàng",
    component: "select",
    placeholder: "Chọn loại khách hàng",
    allowClear: true,
    showSearch: true,
    optionFilterProp: "label",
  } as IFormInput,

  ma: {
    name: "ma",
    label: "Mã khách hàng",
    component: "input",
    placeholder: "Nhập mã khách hàng",
    allowClear: true,
    disabled: true,
    rules: [ruleInputMessage.required],
  } as IFormInput,

  ten: {
    name: "ten",
    label: "Tên khách hàng",
    component: "input",
    placeholder: "Nhập tên khách hàng",
    allowClear: true,
    rules: [ruleInputMessage.required],
  } as IFormInput,

  dchi: {
    name: "dchi",
    label: "Địa chỉ",
    component: "input",
    placeholder: "Nhập địa chỉ",
    allowClear: true,
  } as IFormInput,

  mst: {
    name: "mst",
    label: "Mã số thuế",
    component: "input",
    placeholder: "Nhập mã số thuế",
    allowClear: true,
  } as IFormInput,

  cmt: {
    name: "cmt",
    label: "Chứng minh thư",
    component: "input",
    placeholder: "Nhập số chứng minh thư",
    allowClear: true,
  } as IFormInput,

  dthoai: {
    name: "dthoai",
    label: "Điện thoại",
    component: "input",
    placeholder: "Nhập số điện thoại",
    allowClear: true,
    rules: [ruleInputMessage.phone],
  } as IFormInput,

  email: {
    name: "email",
    label: "Email",
    component: "input",
    placeholder: "Nhập email",
    allowClear: true,
    rules: [ruleInputMessage.email],
  } as IFormInput,

  nguoi_lhe: {
    name: "nguoi_lhe",
    label: "Người liên hệ",
    component: "input",
    placeholder: "Nhập tên người liên hệ",
    allowClear: true,
  } as IFormInput,

  dthoai_lhe: {
    name: "dthoai_lhe",
    label: "Điện thoại liên hệ",
    component: "input",
    placeholder: "Nhập số điện thoại liên hệ",
    allowClear: true,
    rules: [ruleInputMessage.phone],
  } as IFormInput,

  email_lhe: {
    name: "email_lhe",
    label: "Email liên hệ",
    component: "input",
    placeholder: "Nhập email liên hệ",
    allowClear: true,
    rules: [ruleInputMessage.email],
  } as IFormInput,

  trang_thai: {
    name: "trang_thai",
    label: "Trạng thái",
    component: "select",
    placeholder: "Chọn trạng thái",
    allowClear: true,
    showSearch: true,
    optionFilterProp: "label",
    // rules: [ruleInputMessage.required],
  } as IFormInput,
};

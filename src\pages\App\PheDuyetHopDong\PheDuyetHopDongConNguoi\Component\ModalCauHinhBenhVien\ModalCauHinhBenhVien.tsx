import {forwardRef, memo, useCallback, useImperativeHandle, useRef, useState} from "react";
// import {Modal, Tabs, Button, Flex} from "antd";
import {CloseOutlined, CheckOutlined} from "@ant-design/icons";
import {isEqual, debounce} from "lodash";

import {HeaderModal} from "@src/components/";
import {IModalCauHinhBenhVienRef, ModalCauHinhBenhVienProps, LOAI_AP_DUNG, ITableBenhVienWhitelistRef, ITableBenhVienBlacklistRef} from "./Constant";
import {TableBenhVienWhitelist, TableBenhVienBlacklist} from ".";

import "./ModalCauHinhBenhVien.scss";
import {Flex, Tabs} from "antd";
import Modal from "antd/es/modal/Modal";
import Button from "@src/components/Button";
import {useHopDongConNguoiContext} from "@src/pages/App/BaoHiemConNguoi/HopDongConNguoi/index.context";

const ModalCauHinhBenhVienComponent = forwardRef<IModalCauHinhBenhVienRef, ModalCauHinhBenhVienProps>(({}: ModalCauHinhBenhVienProps, ref) => {
  // Context
  const {luuCauHinhBenhVienHopDongConNguoi, loading} = useHopDongConNguoiContext();

  // State management
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [soId, setSoId] = useState<number | null>(null);
  const [soIdDt, setSoIdDt] = useState<number | null>(null);
  const [activeTab, setActiveTab] = useState<string>("1");

  // Refs for table components
  const refTableWhitelist = useRef<ITableBenhVienWhitelistRef>(null);
  const refTableBlacklist = useRef<ITableBenhVienBlacklistRef>(null);

  // Debounced tab change handler
  const debouncedTabChange = useCallback(
    debounce((key: string) => {
      setActiveTab(key);
    }, 150),
    [],
  );

  // Expose methods via ref
  useImperativeHandle(ref, () => ({
    open: (soIdParam?: number, soIdDtParam?: number) => {
      setIsOpen(true);
      setSoId(soIdParam || null);
      setSoIdDt(soIdDtParam || null);
      setActiveTab("1"); // Reset to first tab

      // Note: Selections will be automatically reset by useEffect in table components when soId/soIdDt changes
    },
    close: () => {
      setIsOpen(false);
      setSoId(null);
      setSoIdDt(null);
      setActiveTab("1");

      // Reset selections khi đóng modal
      // refTableWhitelist.current?.resetSelections();
      // refTableBlacklist.current?.resetSelections();
    },
  }));

  // Event handlers
  const closeModal = useCallback(() => {
    setIsOpen(false);
    setSoId(null);
    setSoIdDt(null);
    setActiveTab("1");

    // Reset selections khi đóng modal
    // refTableWhitelist.current?.resetSelections();
    // refTableBlacklist.current?.resetSelections();
  }, []);

  // Lưu cấu hình bệnh viện
  const handleSave = useCallback(async () => {
    if (!soId || !soIdDt) {
      console.error("Không có ID hợp đồng hoặc ID đối tượng");
      return;
    }

    try {
      // Xác định tab hiện tại và loại áp dụng
      const isWhitelistTab = activeTab === "1";
      const loaiApDung = isWhitelistTab ? "WL" : "BL"; // WL = Whitelist, BL = Blacklist
      const currentTableRef = isWhitelistTab ? refTableWhitelist : refTableBlacklist;

      // Lấy data từ table hiện tại
      const tableData = currentTableRef.current?.getData();
      if (!tableData) {
        console.error("Không thể lấy dữ liệu từ table");
        return;
      }

      // Lọc các bệnh viện được chọn
      const selectedHospitals = tableData
        .filter(item => !item.key.includes("empty") && item.is_selected)
        .map(item => ({
          ma: item.ma || "",
          hinh_thuc_ad: item.hinh_thuc_ap_dung || "",
        }));

      // Gọi API lưu cho hợp đồng con người
      const success = await luuCauHinhBenhVienHopDongConNguoi({
        so_id: soId,
        so_id_dt: soIdDt,
        loai_ad: loaiApDung,
        bvien: selectedHospitals,
      });

      if (success) {
        // Refresh data sau khi lưu thành công
        // Reload data cho tab hiện tại (giữ nguyên trang)
        if (isWhitelistTab && refTableWhitelist.current) {
          refTableWhitelist.current.refreshData();
        } else if (!isWhitelistTab && refTableBlacklist.current) {
          refTableBlacklist.current.refreshData();
        }
      }
    } catch (error) {
      console.error("handleSave error:", error);
    }
  }, [soId, soIdDt, activeTab, luuCauHinhBenhVienHopDongConNguoi]);

  const handleTabChange = useCallback(
    (activeKey: string) => {
      if (activeKey !== activeTab) {
        debouncedTabChange(activeKey);

        // Chỉ refresh data khi thực sự cần thiết
        setTimeout(() => {
          if (activeKey === "1" && refTableWhitelist.current) {
            refTableWhitelist.current.refreshData();
          } else if (activeKey === "2" && refTableBlacklist.current) {
            refTableBlacklist.current.refreshData();
          }
        }, 200);
      }
    },
    [activeTab, debouncedTabChange],
  );

  // Render methods
  const renderTabs = () => {
    const tabItems = [
      {
        key: "1",
        label: "Bệnh viện white list",
        children: <TableBenhVienWhitelist ref={refTableWhitelist} soId={soId || undefined} soIdDt={soIdDt || undefined} loaiApDung={LOAI_AP_DUNG.WHITELIST} />,
      },
      {
        key: "2",
        label: "Bệnh viện black list",
        children: <TableBenhVienBlacklist ref={refTableBlacklist} soId={soId || undefined} soIdDt={soIdDt || undefined} loaiApDung={LOAI_AP_DUNG.BLACKLIST} />,
      },
    ];

    return <Tabs animated={false} size="small" defaultActiveKey="1" activeKey={activeTab} onChange={handleTabChange} items={tabItems} />;
  };

  // Custom footer với căn chỉnh bên phải
  const renderFooter = () => (
    <Flex justify="flex-end" gap="small">
      <Button type="default" icon={<CloseOutlined />} onClick={closeModal}>
        Đóng
      </Button>
      <Button type="primary" icon={<CheckOutlined />} loading={loading} onClick={handleSave}>
        Lưu
      </Button>
    </Flex>
  );

  return (
    <Modal
      centered
      maskClosable={false}
      title={<HeaderModal title="Cấu hình bệnh viện" trang_thai_ten="" trang_thai="" />}
      open={isOpen}
      onCancel={closeModal}
      width={"80%"}
      styles={{
        body: {
          height: "80vh",
          // overflow: "auto",
        },
      }}
      footer={renderFooter()}
      className="modal-cau-hinh-benh-vien [&_.ant-space]:w-full">
      {renderTabs()}
    </Modal>
  );
});

ModalCauHinhBenhVienComponent.displayName = "ModalCauHinhBenhVienComponent";
export const ModalCauHinhBenhVien = memo(ModalCauHinhBenhVienComponent, isEqual);

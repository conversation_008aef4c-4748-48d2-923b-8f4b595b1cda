import {LANGUAGE_VALUES} from "@src/constants";
import {LOCAL_STORAGE_KEY} from "@src/constants/localStorage";
import {create} from "zustand";
import {createJSONStorage, persist} from "zustand/middleware";

export enum ThemeMode {
  dark = "dark",
  light = "light",
}

export interface ILanguageStore {
  lang: string;
  themeMode: ThemeMode;
  luuDangNhap: boolean;
  setLanguage: (lang: string) => void;
  setThemeMode: (flag: ThemeMode) => void;
  setLuuDangNhap: (flag: boolean) => void;
}

//create : hàm tạo store
export const useSetting = create(
  //persist : middileware để lưu trạng thái vào localStorage
  //persist<T>() có generic type ILanguageStore – kiểu dữ liệu của store gồm 3 biến lang, themeMode, luuDangNhap.
  persist<ILanguageStore, [], [], Pick<ILanguageStore, "lang" | "themeMode" | "luuDangNhap">>(
    (set, get) => ({
      lang: get()?.lang ?? LANGUAGE_VALUES.EN, //nếu đã có dữ liệu trong storage (lấy bằng hàm get()?.lang), nếu không có thì lấy mặc định là  LANGUAGE_VALUES.EN
      themeMode: get()?.themeMode ?? ThemeMode.light, //nếu đã có dữ liệu trong storage (lấy bằng hàm get()?.themeMode), nếu không có thì lấy mặc định là  ThemeMode.light
      luuDangNhap: get()?.luuDangNhap ?? false,
      setLanguage: (lang: string) =>
        set({
          lang,
        }),
      setThemeMode: (flag: ThemeMode) => set({themeMode: flag}),
      setLuuDangNhap: (luuDangNhap: boolean) => set({luuDangNhap: luuDangNhap}),
    }),
    //cấu hình persist
    {
      name: LOCAL_STORAGE_KEY.APP_SETTINGS, //key để lưu trong localStorate
      storage: createJSONStorage(() => localStorage), //nơi lưu trữ là localStorage
      //chỉ định những phần nào trong state sẽ được lưu, tránh lưu những thứ k cần thiết như các hàm set
      partialize: state => ({
        lang: state.lang,
        themeMode: state.themeMode,
        luuDangNhap: state.luuDangNhap,
      }),
    },
  ),
);

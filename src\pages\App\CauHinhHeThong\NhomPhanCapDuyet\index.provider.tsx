import {CommonExecuteResponse, useCommonExecute} from "@src/services/react-queries";
import React, {PropsWithChildren, useEffect, useMemo, useCallback} from "react";
import {useState} from "react";
import {NhomPhanCapDuyetContextProps} from "./index.model";
import {NhomPhanCapDuyetContext} from "./index.context";
import {ACTION_CODE} from "@src/constants";
import {ReactQuery} from "@src/@types";
// import {defaultFormValue} from "./index.configs";
import {message} from "antd";

const NhomPhanCapDuyetProvider: React.FC<PropsWithChildren> = props => {
  const {children} = props;
  console.log("Nhóm phân cấp duyệt PROVIDER", children);
  const mutateUseCommonExecute = useCommonExecute();
  //   const [listDoiTac, setListDoiTac] = useState<Array<CommonExecute.Execute.IDoiTac>>([]);
  const [tongSoDong, setTongSoDong] = useState<number>(0);
  const [danhSachNhomPhanCapDuyetPhanTrang, setDanhSachNhomPhanCapDuyet] = useState<Array<CommonExecute.Execute.INhomPhanCapDuyet>>([]);
  const [filterParams, setFilterParams] = useState<ReactQuery.ITimKiemPhanTrangDanhSachNhomPhanCapDuyetParams & ReactQuery.IPhanTrang>({
    ma: "",
    ten: "",

    trang_thai: "",
    // trang: 1,
    // so_dong: 20,
  });
  useEffect(() => {
    initData();
  }, []);
  const initData = () => {
    layDanhSachNhomPhanCapDuyetPhanTrang(filterParams);
    // getListDoiTac();
  };
  useEffect(() => {
    layDanhSachNhomPhanCapDuyetPhanTrang(filterParams);
  }, [filterParams]);
  /* ĐỐI TÁC */
  //   const getListDoiTac = useCallback(async () => {
  //     try {
  //       const response = await mutateUseCommonExecute.mutateAsync({
  //         actionCode: ACTION_CODE.LIET_KE_DANH_SACH_DOI_TAC,
  //       });
  //       setListDoiTac(response?.data?.map(item => ({...item, ten: item.ma + " - " + item.ten})));
  //     } catch (error) {
  //       console.log("getListDoiTac error ", error);
  //     }
  //   }, [mutateUseCommonExecute]);

  const layDanhSachNhomPhanCapDuyetPhanTrang = useCallback(
    async (body: ReactQuery.ITimKiemPhanTrangDanhSachNhomPhanCapDuyetParams & ReactQuery.IPhanTrang) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_NHOM_PHAN_CAP,
        };

        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);

        const data = response.data.data;
        console.log("data", data);
        setDanhSachNhomPhanCapDuyet(data);
        setTongSoDong(response.data.tong_so_dong);
      } catch (error: any) {
        console.log("Lấy danh sách sản phẩm error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );
  //Lấy chi tiết 1 sản phẩm
  const layChiTietNhomPhanCapDuyet = useCallback(
    async (item: ReactQuery.IChiTietNhomPhanCapDuyetParams): Promise<CommonExecute.Execute.INhomPhanCapDuyet | null> => {
      try {
        const params = {
          ...item,
          actionCode: ACTION_CODE.CHI_TIET_NHOM_PHAN_CAP,
        };
        console.log("params", params);
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        const dataArray = (responseData.data as any).lke;
        const data = Array.isArray(dataArray) && dataArray.length > 0 ? dataArray[0] : null;
        return data as CommonExecute.Execute.INhomPhanCapDuyet;
      } catch (error: any) {
        console.log("layChiTietNhomPhanCapDuyet error ", error.message || error);
        return null;
      }
    },
    [mutateUseCommonExecute],
  );
  const onUpdateNhomPhanCapDuyet = useCallback(
    async (body: ReactQuery.IUpdateNhomPhanCapDuyetParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.CAP_NHAT_NHOM_PHAN_CAP,
        };
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        if (responseData && (responseData.data as unknown as number) === -1) {
          message.success("Cập nhật thông tin thành công!");
          initData();
          return responseData.data as unknown as number;
        }
      } catch (error: any) {
        console.log("onUpdateNhomPhanCapDuyet error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );
  //khai báo giá trị của context để truyền vào Component
  const value = useMemo<NhomPhanCapDuyetContextProps>(
    () => ({
      loading: mutateUseCommonExecute.isLoading,
      tongSoDong,
      // defaultFormValue,
      filterParams,
      danhSachNhomPhanCapDuyetPhanTrang,
      //   listDoiTac,
      setFilterParams,
      layDanhSachNhomPhanCapDuyetPhanTrang,
      layChiTietNhomPhanCapDuyet,
      onUpdateNhomPhanCapDuyet,
      //   getListDoiTac,
    }),
    [
      danhSachNhomPhanCapDuyetPhanTrang,
      tongSoDong,
      mutateUseCommonExecute,
      filterParams,
      //   listDoiTac,
      setFilterParams,
      layDanhSachNhomPhanCapDuyetPhanTrang,
      layChiTietNhomPhanCapDuyet,
      onUpdateNhomPhanCapDuyet,
      //   getListDoiTac,
    ],
  );

  return <NhomPhanCapDuyetContext.Provider value={value}>{children}</NhomPhanCapDuyetContext.Provider>;
};
export default NhomPhanCapDuyetProvider;

import {ReactQuery} from "@src/@types";
import {TableLoaiHoGiaDinhColumnDataType} from "./index.configs";

export interface IQuanLyLoaiHoGiaDinhContextProps {
  listLoaiHoGiaDinh: Array<CommonExecute.Execute.ILoaiHoGiaDinh>;
  tongSoDong: number;
  loading: boolean;
  filterParams: ReactQuery.ITimKiemPhanTrangLoaiHoGiaDinhParams & ReactQuery.IPhanTrang;
  getListLoaiHoGiaDinh: (params?: ReactQuery.ITimKiemPhanTrangLoaiHoGiaDinhParams & ReactQuery.IPhanTrang) => Promise<void>;
  getChiTietLoaiHoGiaDinh: (params: TableLoaiHoGiaDinhColumnDataType) => Promise<CommonExecute.Execute.ILoaiHoGiaDinh>;
  capNhatChiTietLoaiHoGiaDinh: (params: ReactQuery.ICapNhatLoaiHoGiaDinhParams, isEditMode?: boolean) => Promise<any>;
  setFilterParams: React.Dispatch<React.SetStateAction<ReactQuery.ITimKiemPhanTrangLoaiHoGiaDinhParams & ReactQuery.IPhanTrang>>;
}

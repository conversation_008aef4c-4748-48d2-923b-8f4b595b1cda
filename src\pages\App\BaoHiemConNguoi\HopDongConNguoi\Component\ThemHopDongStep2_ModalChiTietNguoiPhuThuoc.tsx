import {ArrowLeftOutlined, CheckOutlined} from "@ant-design/icons";
import {IFormInput, ReactQuery} from "@src/@types";
import {Button, FormInput, HeaderModal} from "@src/components";
import {formatDateTimeToNumber} from "@src/utils";
import {Col, Flex, Form, message, Modal, Row} from "antd";
import dayjs from "dayjs";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useState} from "react";
import {useHopDongConNguoiContext} from "../index.context";
import {ChiTietNguoiPhuThuocProps, FormTaoMoiNguoiPhuThuoc, IModalChiTietNguoiPhuThuocRef, listGioiTinhSelect, listMoiQuanHeSelect} from "./Constant";

const {ten, ngay_sinh, gioi_tinh, so_cmt, moi_qhe, dthoai, email} = FormTaoMoiNguoiPhuThuoc;

const ModalChiTietNguoiPhuThuocComponent = forwardRef<IModalChiTietNguoiPhuThuocRef, ChiTietNguoiPhuThuocProps>(
  ({chiTietHopDong, listNguoiPhuThuoc, initListNguoiPhuThuoc}: ChiTietNguoiPhuThuocProps, ref) => {
    useImperativeHandle(ref, () => ({
      open: (dataNguoiPhuThuoc?: any) => {
        setIsOpen(true);
        if (dataNguoiPhuThuoc) setChiTietNguoiPhuThuoc(dataNguoiPhuThuoc); // nếu có dữ liệu -> set chi tiết đối tác -> là sửa
      },
      close: () => setIsOpen(false),
    }));
    const [chiTietNguoiPhuThuoc, setChiTietNguoiPhuThuoc] = useState<CommonExecute.Execute.IChiTietNguoiPhuThuoc | null>(null);
    const [isOpen, setIsOpen] = useState(false);

    const {capNhatNguoiPhuThuoc, chiTietNguoiDuocBaoHiem, loadingQuyenLoi} = useHopDongConNguoiContext();

    const [formCapNhatNguoiPhuThuoc] = Form.useForm();
    const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
    const formValues = Form.useWatch([], formCapNhatNguoiPhuThuoc);
    // init form data - load dữ liệu vào form khi sửa
    useEffect(() => {
      if (chiTietNguoiPhuThuoc) {
        const arrFormData = [];
        for (const key in chiTietNguoiPhuThuoc) {
          let value: any = chiTietNguoiPhuThuoc[key as keyof CommonExecute.Execute.IChiTietNguoiPhuThuoc];
          if (key === "ngay_sinh") value = dayjs(value + "", "YYYYMMDD").isValid() ? dayjs(value + "", "YYYYMMDD") : "";
          arrFormData.push({
            name: key,
            value,
          });
        }
        formCapNhatNguoiPhuThuoc.setFields(arrFormData);
      }
    }, [chiTietNguoiPhuThuoc]);

    //xử lý validate form
    useEffect(() => {
      formCapNhatNguoiPhuThuoc
        .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
        .then(() => {
          setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
        })
        .catch(() => {
          setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
        });
    }, [formCapNhatNguoiPhuThuoc, formValues]);

    const closeModal = useCallback(() => {
      setIsOpen(false);
      setChiTietNguoiPhuThuoc(null);
      formCapNhatNguoiPhuThuoc.resetFields();
    }, []);

    //Bấm Update
    const onClickLuuNguoiThuHuong = async () => {
      try {
        const values: ReactQuery.ICapNhatNguoiPhuThuocHopDongConNguoiParams = formCapNhatNguoiPhuThuoc.getFieldsValue(); //lấy ra values của form
        values.bt = chiTietNguoiPhuThuoc?.bt ? +chiTietNguoiPhuThuoc?.bt : 0;
        values.so_id = chiTietHopDong?.so_id || "";
        values.so_id_dt = chiTietNguoiDuocBaoHiem ? +(chiTietNguoiDuocBaoHiem.so_id_dt || -1) : -1;
        values.so_hd = chiTietHopDong?.so_hd;
        values.gcn = chiTietNguoiDuocBaoHiem?.gcn;
        values.ngay_sinh = formatDateTimeToNumber(values.ngay_sinh);
        values.stt = chiTietNguoiPhuThuoc?.stt || listNguoiPhuThuoc.length + 1;

        const response = await capNhatNguoiPhuThuoc(values); //cập nhật lại đối tác
        if (response?.output?.bt) {
          message.success(`${chiTietNguoiPhuThuoc ? "Cập nhật" : "Thêm mới"} thành công`);
          initListNguoiPhuThuoc();
          closeModal();
        }
      } catch (error) {
        console.log("onConfirm", error);
      }
    };

    // RENDER
    //FOOTER
    const renderFooter = () => {
      return (
        <div>
          <Button type="default" onClick={() => closeModal()} className="mr-2" icon={<ArrowLeftOutlined />}>
            Quay lại
          </Button>
          <Button disabled={disableSubmit} loading={loadingQuyenLoi} htmlType="submit" onClick={onClickLuuNguoiThuHuong} icon={<CheckOutlined />}>
            Lưu
          </Button>
        </div>
      );
    };
    const renderFormColum = (props: IFormInput) => (
      <Col span={6}>
        <FormInput {...props} />
      </Col>
    );
    const renderForm = () => (
      <Form form={formCapNhatNguoiPhuThuoc} layout="vertical">
        {/* MÃ */}
        <Row gutter={16}>
          {renderFormColum({...ten})}
          {renderFormColum({...ngay_sinh})}
          {renderFormColum({...gioi_tinh, options: listGioiTinhSelect})}
          {renderFormColum({...so_cmt})}
        </Row>

        {/* gutter : khoảng cách giữa các ô */}
        <Row gutter={16}>
          {renderFormColum({...moi_qhe, options: listMoiQuanHeSelect})}
          {renderFormColum({...dthoai})}
          {renderFormColum({...email})}
        </Row>
      </Form>
    );
    //Render
    return (
      <Flex vertical gap="middle" align="flex-start">
        <Modal
          title={
            <HeaderModal
              title={chiTietNguoiPhuThuoc ? `${chiTietNguoiPhuThuoc.ten}` : "Tạo mới người phụ thuộc"}
              // trang_thai_ten={chiTietNguoiPhuThuoc?.trang_thai_ten}
              // trang_thai={chiTietNguoiPhuThuoc?.trang_thai}
            />
          }
          destroyOnClose
          // centered
          maskClosable={false}
          open={isOpen}
          onOk={() => closeModal()}
          onCancel={() => closeModal()}
          width={"60%"}
          styles={{
            body: {
              height: "20vh",
              // overflowY: "auto",
              // overflowX: "hidden",
            },
          }}
          footer={renderFooter}>
          {renderForm()}
        </Modal>
      </Flex>
    );
  },
);

ModalChiTietNguoiPhuThuocComponent.displayName = "ModalChiTietNguoiPhuThuocComponent";
export const ModalChiTietNguoiPhuThuoc = memo(ModalChiTietNguoiPhuThuocComponent, isEqual);

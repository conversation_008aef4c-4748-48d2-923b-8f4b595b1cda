import {IFormInput} from "@src/@types";

export const ruleRequired = {
  required: true,
  message: "Thông tin bắt buộc",
};

// ===== INTERFACE DEFINITIONS =====
/**
 * INTERFACE CHO CẤU HÌNH FORM FIELDS
 * 
 * Định nghĩa structure của config cho từng field trong form modal.
 * Mỗi field có các thuộc tính: component, name, label, placeholder, rules...
 */
export interface IFormChiTietChiNhanhNganHangFieldsConfig {
  /** Field mã chi nhánh */
  ma: IFormInput;
  /** Field tên chi nhánh */
  ten: IFormInput;
  /** Field mã ngân hàng (dropdown) */
  ma_ngan_hang: IFormInput;
  /** Field số thứ tự */
  stt: IFormInput;
  /** Field trạng thái (dropdown) */
  trang_thai: IFormInput;
}

// ===== FORM FIELD CONFIGURATIONS =====
/**
 * CẤU HÌNH CÁC FIELD TRONG FORM MODAL
 * 
 * Định nghĩa cách hiển thị và validation cho từng field trong modal thêm/sửa.
 * Mỗi field bao gồm: component type, label, placeholder, validation rules...
 */
export const FormChiTietChiNhanhNganHangConfigs: IFormChiTietChiNhanhNganHangFieldsConfig = {
  /** Field mã chi nhánh - disabled khi edit, enabled khi tạo mới */
  ma: {
    component: "input",
    name: "ma",
    label: "Mã chi nhánh",
    placeholder: "Mã chi nhánh",
    rules: [ruleRequired],
  },
  /** Field tên chi nhánh - luôn có thể edit */
  ten: {
    component: "input",
    name: "ten",
    label: "Tên chi nhánh",
    placeholder: "Tên chi nhánh",
    rules: [ruleRequired],
  },
  /** Field dropdown chọn ngân hàng */
  ma_ngan_hang: {
    component: "select",
    name: "ma_ngan_hang",
    label: "Ngân hàng",
    placeholder: "Chọn ngân hàng",
    rules: [ruleRequired],
  },
  /** Field số thứ tự hiển thị */
  stt: {
    component: "input",
    name: "stt",
    label: "Số thứ tự",
    placeholder: "Số thứ tự",
    rules: [ruleRequired],
  },
  /** Field dropdown chọn trạng thái */
  trang_thai: {
    component: "select",
    name: "trang_thai",
    label: "Trạng thái",
    placeholder: "Chọn trạng thái",
    rules: [ruleRequired],
  },
};

// ===== OPTIONS =====
/**
 * OPTIONS TRẠNG THÁI CHO MODAL
 * 
 * Danh sách trạng thái sử dụng trong modal (không bao gồm "Tất cả").
 * Khác với config chính ở chỗ này chỉ có các trạng thái cụ thể.
 */
export const TRANG_THAI_TAO_MOI_CHI_NHANH_NGAN_HANG = [
  {ten: "Đang sử dụng", ma: "D"},
  {ten: "Ngừng sử dụng", ma: "K"},
];

// ===== REF INTERFACE =====
/**
 * INTERFACE CHO REF CỦA MODAL COMPONENT
 * 
 * Định nghĩa các methods có thể gọi từ component cha thông qua ref.
 * Cho phép parent component điều khiển modal từ bên ngoài.
 */
export interface IModalChiTietChiNhanhNganHangRef {
  /**
   * Mở modal
   * @param data - Dữ liệu chi nhánh để edit (optional - không có = tạo mới)
   */
  open: (data?: CommonExecute.Execute.IChiTietChiNhanhNganHang) => void;
  
  /**
   * Đóng modal
   */
  close: () => void;
}
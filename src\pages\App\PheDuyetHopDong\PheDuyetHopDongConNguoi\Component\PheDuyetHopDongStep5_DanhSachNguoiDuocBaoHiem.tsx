import "@react-pdf-viewer/core/lib/styles/index.css";
import {FormInput} from "@src/components";
import {defaultPaginationTableProps, fillRowTableEmpty} from "@src/hooks";
import {formatCurrencyUS} from "@src/utils";
import {Col, Form, Row, Table} from "antd";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useMemo, useState} from "react";
import {useHopDongConNguoiContext} from "@src/pages/App/BaoHiemConNguoi/HopDongConNguoi/index.context";
import {tableNguoiDuocBaoHiemColumn, TableNguoiDuocBaoHiemColumnDataType} from "./Constant";

const DanhSachNguoiDuocBaoHiemComponent = forwardRef<any, any>(({onRowClick, summaryRender, style = {}, pageSize: pageSizeProp = 10}: any, ref) => {
  useImperativeHandle(ref, () => ({}));

  const {listNguoiDuocBaoHiem, tongPhiBaoHiemFromAPI, loading, tongSoDongNguoiDuocBaoHiem, setTimKiemPhanTrangNguoiDuocBaoHiemParams, chiTietHopDong} = useHopDongConNguoiContext();
  const [formTimKiem] = Form.useForm();
  const [searchParams, setSearchParams] = useState<any>({nd_tim: ""});
  const [page, setPage] = useState(1);
  const [nguoiDuocBaoHiemSelected, setNguoiDuocBaoHiemSelected] = useState<any>(null);
  const [pageSize, setPageSize] = useState(pageSizeProp);

  useEffect(() => {
    if (pageSizeProp !== undefined && pageSizeProp !== null) {
      setPageSize(pageSizeProp);
      return;
    }
    function calculatePageSize() {
      // Giả sử header, search form, ... chiếm 300px, mỗi dòng table 48px
      const availableHeight = window.innerHeight - 300;
      const rowHeight = 48;
      const calculatedPageSize = Math.max(3, Math.floor(availableHeight / rowHeight)); // tối thiểu 3 dòng
      setPageSize(calculatedPageSize);
    }
    calculatePageSize();
    window.addEventListener("resize", calculatePageSize);
    return () => window.removeEventListener("resize", calculatePageSize);
  }, [pageSizeProp]);

  // DATA TABLE
  const dataTableListNguoiDuocBaoHiem = useMemo(() => {
    try {
      const tableData = listNguoiDuocBaoHiem.map((itemNguoiDuocBaoHiem: any) => ({
        key: itemNguoiDuocBaoHiem.so_id_dt + "",
        ten: itemNguoiDuocBaoHiem.ten + " / " + itemNguoiDuocBaoHiem.so_cmt,
        ngay_hl: itemNguoiDuocBaoHiem.ngay_hl,
        so_id: itemNguoiDuocBaoHiem.so_id + "",
        so_id_dt: itemNguoiDuocBaoHiem.so_id_dt,
        tong_phi: +(itemNguoiDuocBaoHiem.tong_phi || 0),
      }));
      if (tableData.length > 0 && !nguoiDuocBaoHiemSelected) setNguoiDuocBaoHiemSelected(tableData[0]);
      const arrEmptyRow: Array<TableNguoiDuocBaoHiemColumnDataType> = fillRowTableEmpty(tableData.length, pageSize);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      return [];
    }
  }, [listNguoiDuocBaoHiem, pageSize]);

  // Tìm kiếm và phân trang
  const handleSearchAndPagination = useCallback(
    (values?: any, pageArg?: number) => {
      const so_id = chiTietHopDong?.so_id;
      if (values) {
        const cleanedValues = {
          ...values,
          nd_tim: values.nd_tim ?? "",
          dong_tai: values.dong_tai ?? "",
          so_id,
        };
        setSearchParams(cleanedValues);
        setPage(1);
        setTimKiemPhanTrangNguoiDuocBaoHiemParams({...cleanedValues, trang: 1, so_dong: pageSize});
      } else {
        const page = pageArg || 1;
        setPage(page);
        setTimKiemPhanTrangNguoiDuocBaoHiemParams({
          ...searchParams,
          so_id,
          trang: page,
          so_dong: pageSize,
        });
      }
    },
    [chiTietHopDong, searchParams, pageSize, setTimKiemPhanTrangNguoiDuocBaoHiemParams],
  );

  // Header tìm kiếm
  const renderHeaderTable = () => (
    <div>
      <Form form={formTimKiem} layout="vertical" className="[&_.ant-form-item]:mb-0" onFinish={handleSearchAndPagination}>
        <Row gutter={8} className="items-end">
          <Col span={24}>
            <FormInput name="nd_tim" placeholder="Họ tên / CMT / Điện thoại" />
          </Col>
        </Row>
      </Form>
    </div>
  );

  // Summary mặc định
  const defaultSummary = () => (
    <Table.Summary fixed>
      <Table.Summary.Row>
        <Table.Summary.Cell index={0} colSpan={1} className="!p-[8px]">
          <div className="text-center font-medium">Tổng phí bảo hiểm</div>
        </Table.Summary.Cell>
        <Table.Summary.Cell index={1} align="right" className="!p-[8px]">
          <div className="text-right font-medium">{formatCurrencyUS(tongPhiBaoHiemFromAPI)}</div>
        </Table.Summary.Cell>
      </Table.Summary.Row>
    </Table.Summary>
  );

  return (
    <Table<TableNguoiDuocBaoHiemColumnDataType>
      className="table-danh-sach-nguoi-duoc-bao-hiem-hop-dong-con-nguoi"
      style={style}
      dataSource={dataTableListNguoiDuocBaoHiem}
      columns={tableNguoiDuocBaoHiemColumn}
      loading={loading}
      sticky
      rowClassName={(record: any) => (record.so_id_dt && record.so_id_dt === nguoiDuocBaoHiemSelected?.so_id_dt ? "table-row-active" : "")}
      pagination={{
        ...defaultPaginationTableProps,
        size: "small",
        total: tongSoDongNguoiDuocBaoHiem,
        pageSize: pageSize,
        current: page,
        onChange: (page: any) => handleSearchAndPagination(undefined, page),
        locale: {
          jump_to: "Tới trang",
          page: "",
        },
      }}
      showHeader={true}
      title={renderHeaderTable}
      bordered
      onRow={record => ({
        style: {cursor: loading ? "progress" : "pointer"},
        onClick: () => {
          onRowClick && onRowClick(record);
          setNguoiDuocBaoHiemSelected(record);
        },
      })}
      summary={summaryRender ? () => summaryRender(tongPhiBaoHiemFromAPI) : defaultSummary}
    />
  );
});

DanhSachNguoiDuocBaoHiemComponent.displayName = "DanhSachNguoiDuocBaoHiemComponent";
export const DanhSachNguoiDuocBaoHiem = memo(DanhSachNguoiDuocBaoHiemComponent, isEqual);

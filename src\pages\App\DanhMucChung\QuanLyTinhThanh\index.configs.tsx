import {IFormInput} from "@src/@types";
import {defaultTableColumnsProps, ruleInputMessage, validationRules} from "@src/hooks";
import {TableProps} from "antd";

// Hàm để convert ngày áp dụng từ số sang text
// DEPRECATED: Logic này đã được move vào content để tương thích với search
// const convertNgayApDungToText = (ngayAd: number | string | null | undefined): string => {
//   // Kiểm tra null, undefined hoặc empty string
//   if (ngayAd === null || ngayAd === undefined || ngayAd === '') {
//     return '';
//   }
//   const ngayAdStr = String(ngayAd);
//   switch (ngayAdStr) {
//     case '19000101':
//       return 'Áp dụng từ ngày 01/01/1900';
//     case '20250701':
//       return 'Áp dụng từ ngày 01/07/2025';
//     default:
//       // Nếu không match với các case trên, kiểm tra null/undefined string
//       return ngayAdStr === 'null' || ngayAdStr === 'undefined' ? '' : ngayAdStr;
//   }
// };

//ĐỊNH NGHĨA CẤU HÌNH TABLE TỈNH THÀNH GỒM NHỮNG CỘT NÀO, THỨ TỰ CỘT SẼ HIỂN THỊ THEO THỨ TỰ KHAI BÁO Ở DƯỚI
export interface TableTinhThanhColumnDataType {
  key: string;
  sott?: number;
  ngay_ad?: number;
  ngay_ad_ten?: string;
  ma?: string;
  ten?: string;
  mien?: string | null;
  ten_mien?: string;
  stt?: number;
  trang_thai?: string;
  ngay_tao?: string;
  nguoi_tao?: string;
  ngay_cap_nhat?: string;
  nguoi_cap_nhat?: string;
  trang_thai_ten?: string;
}

// ĐỊNH NGHĨA Column RENDER RA TỪNG Ô TRONG CỘT
//đọc https://ant.design/components/table#column để biết thêm về column
// Cấu hình các cột hiển thị trong bảng danh sách tỉnh thành
export const tableTinhThanhColumn: TableProps<TableTinhThanhColumnDataType>["columns"] = [
  {
    title: "STT",
    dataIndex: "sott",
    key: "sott",
    align: "center",
    // width: colWidthByKey.sott,
    width: 30,
    ...defaultTableColumnsProps,
  },

  {
    title: "Mã",
    dataIndex: "ma",
    key: "ma",
    align: "center",
    width: 30,
    ...defaultTableColumnsProps,
  },
  {
    title: "Ngày áp dụng",
    dataIndex: "ngay_ad_ten",
    key: "ngay_ad_ten",
    align: "center",
    width: 80,

    // DEPRECATED: Render function đã được move vào content để tương thích với search
    // render: (ngay_ad: number, record: TableTinhThanhColumnDataType) => {
    //   // Bỏ qua empty rows
    //   if (record.key?.toString().includes("empty")) {
    //     return "";
    //   }
    //   const result = convertNgayApDungToText(ngay_ad);
    //   return result;
    // },
    ...defaultTableColumnsProps,
  },
  {
    title: "Tên",
    dataIndex: "ten",
    key: "ten",
    width: 80,
    ...defaultTableColumnsProps,
  },
  {
    title: "Miền",
    dataIndex: "ten_mien",
    key: "ten_mien",
    align: "center",
    width: 60,
    ...defaultTableColumnsProps,
  },
  // {
  //   title: "Số thứ tự",
  //   dataIndex: "stt",
  //   key: "stt",
  //   align: "center",
  //   width: 100,
  //   ...defaultTableColumnsProps,
  // },
  {
    title: "Ngày tạo",
    dataIndex: "ngay_tao",
    key: "ngay_tao",
    width: 100,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Người tạo",
    dataIndex: "nguoi_tao",
    key: "nguoi_tao",
    width: 60,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Ngày cập nhật",
    dataIndex: "ngay_cap_nhat",
    key: "ngay_cap_nhat",
    width: 100,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Người cập nhật",
    dataIndex: "nguoi_cap_nhat",
    key: "nguoi_cap_nhat",
    width: 60,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Trạng thái",
    dataIndex: "trang_thai_ten",
    key: "trang_thai_ten",
    width: 80,
    align: "center",
    ...defaultTableColumnsProps,
  },
];

//keyof: return ra key của inteface TableTinhThanhColumnDataType;
export type TableTinhThanhColumnDataIndex = keyof TableTinhThanhColumnDataType;

//radio trong table
export const radioItemTrangThaiTinhThanhTable = [
  {value: "Đang sử dụng", text: "Đang sử dụng"},
  {value: "Ngưng sử dụng", text: "Ngưng sử dụng"},
];

//radio trong search
// Options cho dropdown trạng thái trong form tìm kiếm
export const radioItemTrangThaiTinhThanhSelect: Array<{ma: string; ten: string}> = [
  {ma: "", ten: "Tất cả"},
  {ma: "D", ten: "Đang sử dụng"},
  {ma: "K", ten: "Ngưng sử dụng"},
];

export const radioItemNgayApDungTinhThanhSelect: Array<{ma: string; ten: string}> = [
  {ma: "", ten: "Tất cả"},
  {ma: "19000101", ten: "01/01/1900"},
  {ma: "20250701", ten: "01/07/2025"},
];
// Danh sách miền Việt Nam
export const DANH_SACH_MIEN_VIET_NAM = [
  {ten: "Miền Bắc", ma: "B"},
  {ten: "Miền Trung", ma: "T"},
  {ten: "Miền Nam", ma: "N"},
];

// Danh sách miền cho tìm kiếm (có thêm "Tất cả")
export const DANH_SACH_MIEN_TIM_KIEM = [{ma: "", ten: "Tất cả"}, ...DANH_SACH_MIEN_VIET_NAM];

export interface IFormTimKiemTinhThanhFieldsConfig {
  ten: IFormInput;
  ma: IFormInput;
  ngay_ad: IFormInput;
  trang_thai: IFormInput;
}

// Cấu hình form tìm kiếm tỉnh thành ở header table
export const FormTimKiemQuanLyTinhThanh: IFormTimKiemTinhThanhFieldsConfig = {
  ten: {
    component: "input",
    name: "ten",
    label: "Tên tỉnh thành",
    placeholder: "Nhập tên tỉnh thành",
    className: "!mb-0",
  },
  ma: {
    component: "input",
    name: "ma",
    label: "Mã tỉnh thành",
    placeholder: "Nhập mã tỉnh thành",
    className: "!mb-0",
  },
  ngay_ad: {
    component: "select",
    name: "ngay_ad",
    label: "Ngày áp dụng",
    placeholder: "Chọn ngày áp dụng",
    className: "!mb-0",
  },
  trang_thai: {
    component: "select",
    name: "trang_thai",
    label: "Trạng thái",
    placeholder: "Chọn trạng thái",
    className: "!mb-0",
  },
};

//form update / create
export interface IFormTaoMoiTinhThanhFieldsConfig {
  ngay_ad: IFormInput;
  ma: IFormInput;
  ten: IFormInput;
  mien: IFormInput;
  stt: IFormInput;
  trang_thai: IFormInput;
}

// Cấu hình form tạo mới/chỉnh sửa tỉnh thành trong modal
export const FormTaoMoiTinhThanh: IFormTaoMoiTinhThanhFieldsConfig = {
  ngay_ad: {
    component: "select",
    label: "Ngày áp dụng",
    name: "ngay_ad",
    placeholder: "Chọn ngày áp dụng",
    rules: [ruleInputMessage.required],
  },
  ma: {
    component: "input",
    label: "Mã",
    name: "ma",
    placeholder: "Nhập mã",
    rules: [ruleInputMessage.required],
  },
  ten: {
    component: "input",
    label: "Tên",
    name: "ten",
    placeholder: "Nhập tên tỉnh thành",
    rules: [ruleInputMessage.required],
  },
  mien: {
    component: "select",
    label: "Miền",
    name: "mien",
    placeholder: "Chọn miền",
    rules: [ruleInputMessage.required],
  },
  stt: {
    component: "input",
    label: "Số thứ tự",
    name: "stt",
    placeholder: "Nhập số thứ tự",
    rules: validationRules.stt,
  },
  trang_thai: {
    component: "select",
    label: "Trạng thái",
    name: "trang_thai",
    placeholder: "Chọn trạng thái",
    rules: [ruleInputMessage.required],
  },
};

export const TRANG_THAI_TAO_MOI_TINH_THANH = [
  {ten: "Đang sử dụng", ma: "D"},
  {ten: "Ngừng sử dụng", ma: "K"},
];
export const NGAY_AD_TAO_MOI_TINH_THANH = [
  {ten: "01/01/1900", ma: "19000101"},
  {ten: "01/07/2025", ma: "20250701"},
];

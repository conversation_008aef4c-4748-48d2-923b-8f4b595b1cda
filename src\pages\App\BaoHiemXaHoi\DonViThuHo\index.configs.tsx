import {IFormInput} from "@src/@types";
import {colWidthByKey, defaultTableColumnsProps, ruleInputMessage} from "@src/hooks";
import {TableProps} from "antd";

//ĐỊNH NGHĨA CẤU HÌNH TABLE ĐỐI TÁC GỒM NHỮNG CỘT NÀO, THỨ TỰ CỘT SẼ HIỂN THỊ THEO THỨ TỰ KHAI BÁO Ở DƯỚI
export interface TableDonViThuHoColumnDataType {
  key: string;
  sott?: number;
  ma?: string;
  ten?: string;
  ten_tat?: string;
  ten_e?: string;
  dchi?: string;
  mst?: string;
  dthoai?: string;
  trang_thai?: string;
  ngay_tao?: string;
  nguoi_tao?: string;
  ngay_cap_nhat?: string;
  nguoi_cap_nhat?: string;
  trang_thai_ten?: string;
}

// ĐỊNH NGHĨA Column RENDER RA TỪNG Ô TRONG CỘT
//đọc https://ant.design/components/table#column để biết thêm về column
export const tableDonViThuHoColumn: TableProps<TableDonViThuHoColumnDataType>["columns"] = [
  {
    title: "STT",
    dataIndex: "sott",
    key: "sott",
    align: "center",
    width: colWidthByKey.sott,
    ...defaultTableColumnsProps,
  },
  {
    title: "Mã đơn vị thu hộ ",
    dataIndex: "ma",
    key: "ma",
    align: "center",
    width: 120,
    ...defaultTableColumnsProps,
  },
  {
    title: "Tên đơn vị thu hộ ",
    dataIndex: "ten",
    key: "ten",
    width: 180,
    ...defaultTableColumnsProps,
  },
  {
    title: "Tên tắt",
    dataIndex: "ten_tat",
    key: "ten_tat",
    width: 150,
    ...defaultTableColumnsProps,
  },
  {
    title: "Tên tiếng anh",
    dataIndex: "ten_e",
    key: "ten_e",
    width: 150,
    ...defaultTableColumnsProps,
  },
  {
    title: "Địa chỉ",
    dataIndex: "dchi",
    key: "dchi",
    width: 150,
    ...defaultTableColumnsProps,
  },
  {
    title: "Mã số thuế",
    dataIndex: "mst",
    key: "mst",
    width: 100,
    ...defaultTableColumnsProps,
  },
  {
    title: "Điện thoại",
    dataIndex: "dthoai",
    key: "dthoai",
    width: 150,
    ...defaultTableColumnsProps,
  },
  {
    title: "Ngày tạo",
    dataIndex: "ngay_tao",
    key: "ngay_tao",
    width: colWidthByKey.ngay_tao,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Người tạo",
    dataIndex: "nguoi_tao",
    key: "nguoi_tao",
    width: colWidthByKey.nguoi_tao,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Ngày cập nhật",
    dataIndex: "ngay_cap_nhat",
    key: "ngay_cap_nhat",
    width: colWidthByKey.ngay_cap_nhat,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Người cập nhật",
    dataIndex: "nguoi_cap_nhat",
    key: "nguoi_cap_nhat",
    width: colWidthByKey.nguoi_cap_nhat,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Trạng thái",
    dataIndex: "trang_thai_ten",
    key: "trang_thai_ten",
    width: 130,
    align: "center",
    ...defaultTableColumnsProps,
  },
];

//keyof: return ra key của inteface TableDonViThuHoColumnDataType;
export type TableDonViThuHoColumnDataIndex = keyof TableDonViThuHoColumnDataType;

//radio trong table
export const radioItemTrangThaiDonViThuHoTable = [
  {value: "Đang sử dụng", text: "Đang sử dụng"},
  {value: "Ngưng sử dụng", text: "Ngưng sử dụng"},
];

//form tìm kiếm
//radio trong search
export const radioItemTrangThaiDonViThuHoSelect: Array<{ma: string; ten: string}> = [
  {ma: "D", ten: "Đang sử dụng"},
  {ma: "K", ten: "Ngưng sử dụng"},
];
export interface IFormTimKiemDonViThuHoFieldsConfig {
  ten: IFormInput;
  ma: IFormInput;
  mst: IFormInput;
  trang_thai: IFormInput;
}
export const FormTimKiemQuanLyDonViThuHo: IFormTimKiemDonViThuHoFieldsConfig = {
  ten: {
    component: "input",
    name: "ten",
    label: "Tên đơn vị thu hộ ",
    placeholder: "Nhập tên đơn vị thu hộ ",
    className: "!mb-0",
  },
  ma: {
    component: "input",
    name: "ma",
    label: "Mã đơn vị thu hộ ",
    placeholder: "Nhập mã đơn vị thu hộ ",
    className: "!mb-0",
  },
  mst: {
    component: "input",
    name: "mst",
    label: "Mã số thuế",
    placeholder: "Nhập mã số thuế",
    className: "!mb-0",
  },
  trang_thai: {
    component: "select",
    name: "trang_thai",
    label: "Trạng thái",
    placeholder: "Chọn trạng thái",
    className: "!mb-0",
  },
};

//form update / create
export interface IFormTaoMoiDonViThuHoFieldsConfig {
  ma: IFormInput;
  ten: IFormInput;
  ten_tat: IFormInput;
  ten_e: IFormInput;
  dchi: IFormInput;
  dthoai: IFormInput;
  mst: IFormInput;
  stt: IFormInput;
  trang_thai: IFormInput;
  ma_nh: IFormInput;
  so_tk: IFormInput;
  ten_tk: IFormInput;
  file_qrcode: IFormInput;
  id_file_qrcode: IFormInput;
}

// const ruleRequired = {
//   required: true,
//   message: "Thông tin bắt buộc",
// };

export const FormTaoMoiDonViThuHo: IFormTaoMoiDonViThuHoFieldsConfig = {
  ma: {
    component: "input",
    label: "Mã",
    name: "ma",
    placeholder: "Nhập mã đơn vị thu hộ ",
    rules: [ruleInputMessage.required],
  },
  ten: {
    component: "input",
    label: "Tên",
    name: "ten",
    placeholder: "Nhập tên đơn vị thu hộ ",
    rules: [ruleInputMessage.required],
  },
  ten_tat: {
    component: "input",
    label: "Tên tắt",
    name: "ten_tat",
    placeholder: "Nhập tên tắt",
    // rules: [ruleInputMessage.required],
  },
  ten_e: {
    component: "input",
    label: "Tên tiếng anh",
    placeholder: "Nhập tên tiếng anh",
    name: "ten_e",
    // rules: [ruleInputMessage.required],
  },
  dchi: {
    component: "input",
    label: "Địa chỉ",
    placeholder: "Nhập địa chỉ",
    name: "dchi",
    rules: [ruleInputMessage.required],
  },
  dthoai: {
    component: "input",
    label: "Điện thoại",
    placeholder: "Nhập điện thoại",
    name: "dthoai",
    rules: [ruleInputMessage.required], //,ruleInputMessage.phone
  },
  mst: {
    component: "input",
    label: "Mã số thuế",
    placeholder: "Nhập mã số thuế",
    name: "mst",
    rules: [ruleInputMessage.required],
  },
  stt: {
    component: "input",
    label: "Số thứ tự",
    placeholder: "Nhập số thứ tự",
    name: "stt",
    rules: [ruleInputMessage.required],
  },
  trang_thai: {
    component: "select",
    label: "Trạng thái",
    name: "trang_thai",
    placeholder: "Chọn trạng thái",
    rules: [ruleInputMessage.required],
  },
  ma_nh: {
    component: "select",
    label: "Ngân hàng",
    name: "ma_nh",
    placeholder: "Chọn ngân hàng",
    // rules: [ruleInputMessage.required],
  },
  ten_tk: {
    component: "input",
    label: "Tên tài khoản",
    name: "ten_tk",
    placeholder: "Nhập tên tài khoản",
    // rules: [ruleInputMessage.required],
  },
  so_tk: {
    component: "input",
    label: "Số tài khoản",
    name: "so_tk",
    placeholder: "Nhập số tài khoản",
    // rules: [ruleInputMessage.required],
  },
  //hidden
  file_qrcode: {
    component: "input",
    name: "file_qrcode",
    className: "hidden",
  },
  id_file_qrcode: {
    component: "input",
    name: "id_file_qrcode",
    className: "hidden",
  },
};

export const TRANG_THAI_TAO_MOI_DON_VI_THU_HO = [
  {ten: "Đang sử dụng", ma: "D"},
  {ten: "Ngừng sử dụng", ma: "K"},
];

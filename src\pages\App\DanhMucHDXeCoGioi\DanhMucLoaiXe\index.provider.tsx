import React, {PropsWithChildren, useCallback, useEffect, useMemo, useState} from "react";
import {message} from "antd";
import {ReactQuery} from "@src/@types";
import {CommonExecuteResponse, useCommonExecute} from "@src/services/react-queries";
import {ACTION_CODE} from "@src/constants";
import {defaultFormValue} from "./index.configs";
import {DanhMucLoaiXeContext} from "./index.context";

const DanhMucLoaiXeProvider: React.FC<PropsWithChildren> = ({children}) => {
  // Hook để gọi API thông qua service layer
  const mutateUseCommonExecute = useCommonExecute();
  // Quản lý trạng thái loading khi gọi API (hiển thị spinner)
  const [loading, setLoading] = useState<boolean>(false);
  // Danh sách loại xe hiển thị trên bảng (từ API search)
  const [danhSachDanhMucLoaiXe, setDanhSachDanhMucLoaiXe] = useState<Array<CommonExecute.Execute.IDanhSachDanhMucLoaiXePhanTrang>>([]);
  // Tổng số dòng để tính phân trang (từ API response)
  const [tongSoDong, setTongSoDong] = useState<number>(0);
  // Function để load dữ liệu khi component mount lần đầu
  const initData = () => {
    // Load danh sách loại xe với giá trị mặc định (trang 1, 15 dòng)
    layDanhSachDanhMucLoaiXe(defaultFormValue);
  };
  useEffect(() => {
    initData();
  }, []);


  /**
   * @param params - Tham số tìm kiếm và phân trang
   */
  const layDanhSachDanhMucLoaiXe = useCallback(
    async (params: ReactQuery.ITimKiemPhanTrangDanhMucLoaiXeParams & ReactQuery.IPhanTrang) => {
      try {
        setLoading(true);


        // Gọi API search với toàn bộ tham số (search + pagination)
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync({
          ...params, 
          actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_DANH_MUC_LOAI_XE,
        });

        

        // Xử lý response data - API trả về structure: {data: {tong_so_dong: number, data: array}}
        const responseData = response.data || {};
        const data = (responseData as any).data || []; // Lấy mảng data từ response.data.data
        const tongSoDong = (responseData as any).tong_so_dong || 0; // Lấy tổng số dòng từ response.data.tong_so_dong
        
        
        
        setDanhSachDanhMucLoaiXe(data); // Set data cho bảng
        setTongSoDong(tongSoDong); 
        
       
      } catch (error) {
        setDanhSachDanhMucLoaiXe([]);
        setTongSoDong(0);
      } finally {
        setLoading(false);
      }
    },
    [mutateUseCommonExecute],
  );

  /**
   * HÀM LẤY CHI TIẾT MỘT LOẠI XE
   *
   * @param params - Tham số chứa cặp khóa chính (ma, nv)
   * @returns Chi tiết loại xe hoặc null nếu có lỗi
   */
  const layChiTietDanhMucLoaiXe = useCallback(
    async (params: ReactQuery.IChiTietDanhMucLoaiXeParams): Promise<CommonExecute.Execute.IChiTietDanhMucLoaiXe | null> => {
      try {
        // Gọi API lấy chi tiết với cặp khóa chính (ma, nv)

        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync({
          ma: params.ma,
          nv: params.nv,
          actionCode: ACTION_CODE.CHI_TIET_DANH_MUC_LOAI_XE,
        });


        // Xử lý response data (có thể có structure khác nhau)
        let dataArray: any[] = [];

        if (response.data && typeof response.data === "object" && (response.data as any).lke && Array.isArray((response.data as any).lke)) {
          dataArray = (response.data as any).lke;
        } else if (Array.isArray(response.data)) {
          dataArray = response.data;
        } else {
          return response.data as CommonExecute.Execute.IChiTietDanhMucLoaiXe;
        }

        // Tìm bản ghi có cặp khóa chính (ma, nv) khớp với input params
        const matchedRecord = dataArray.find(item => item.ma === params.ma && item.nv === params.nv);


        if (!matchedRecord) {
          return null;
        }

        // Return bản ghi đúng để modal sử dụng
        return matchedRecord as CommonExecute.Execute.IChiTietDanhMucLoaiXe;
      } catch (error) {
        // Log lỗi và return null để modal biết không có data
        console.log("layChiTietDanhMucLoaiXe error", error);
        return null;
      }
    },
    [mutateUseCommonExecute],
  );

  /**
   * HÀM CẬP NHẬT (THÊM MỚI / CHỈNH SỬA) LOẠI XE
   *
   * @param params - Dữ liệu loại xe cần cập nhật
   * @returns true nếu thành công, false nếu thất bại
   */
  const onUpdateDanhMucLoaiXe = useCallback(
    async (params: ReactQuery.ICapNhatDanhMucLoaiXeParams): Promise<boolean> => {
      try {
        // Bật loading cho nút Save trong modal
        setLoading(true);
        // Validate required fields
        if (!params.ma) {
          message.error("Mã loại xe là bắt buộc");
          return false;
        }
        if (!params.ten) {
          message.error("Tên loại xe là bắt buộc");
          return false;
        }
        if (!params.nv) {
          message.error("Nghiệp vụ là bắt buộc");
          return false;
        }
        // Gọi API cập nhật với toàn bộ data từ form
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync({
          ...params,
          actionCode: ACTION_CODE.UPDATE_DANH_MUC_LOAI_XE,
        });

        if (response.data === -1) {
          // Xác định đây là thêm mới hay chỉnh sửa
          const isEdit = params.ma && params.ma.trim() !== "";

          message.success(`${isEdit ? "Cập nhật" : "Thêm mới"} loại xe thành công`);

          layDanhSachDanhMucLoaiXe(defaultFormValue);

          return true; 
        }
        return false; // API không trả về success code
      } catch (error) {
        console.log("onUpdateDanhMucLoaiXe error", error);
        const isEdit = params.ma && params.ma.trim() !== "";
        message.error(`Có lỗi xảy ra khi ${isEdit ? "cập nhật" : "thêm mới"} loại xe`);
        return false;
      } finally {
        setLoading(false);
      }
    },
    [mutateUseCommonExecute, layDanhSachDanhMucLoaiXe],
  );

  const value = useMemo(
    () => ({
      loading,
      danhSachDanhMucLoaiXe,
      tongSoDong,

      // ===== CONFIGS =====
      defaultFormValue,

      // ===== FUNCTIONS =====
      layDanhSachDanhMucLoaiXe,
      layChiTietDanhMucLoaiXe,
      onUpdateDanhMucLoaiXe,
    }),
    [loading, danhSachDanhMucLoaiXe, tongSoDong, layDanhSachDanhMucLoaiXe, layChiTietDanhMucLoaiXe, onUpdateDanhMucLoaiXe],
  );
  /**
   * useMemo để tối ưu performance, chỉ re-render khi dependencies thay đổi
   * Các component con sẽ consume value này thông qua useContext
   */
  return <DanhMucLoaiXeContext.Provider value={value}>{children}</DanhMucLoaiXeContext.Provider>;
};

export default DanhMucLoaiXeProvider;
